# Google Chat Messaging Automation

This Python script automates Google Chat messaging by reading contact data from a CSV file and sending personalized messages to each contact.

## Features

- **CSV Data Processing**: Reads and processes contact data from CSV files
- **Automated Login**: Uses the existing Google Chat login automation
- **Incognito Mode**: Runs in Chrome's incognito mode for privacy and to avoid session conflicts
- **Modal Popup Handling**: Automatically detects and dismisses modal popups after login
- **Contact Search**: Automatically searches for contacts in Google Chat
- **Message Sending**: Sends personalized messages with phone number lists
- **Error Handling**: Comprehensive error handling and logging
- **Rate Limiting**: Built-in delays to avoid being rate-limited

## Requirements

- Python 3.7+
- Google Chrome browser
- CSV file with contact data

## Installation

1. Install required dependencies:
```bash
pip install selenium pandas
```

2. Ensure you have the CSV file with the correct format

## CSV File Format

The script expects a CSV file with the following columns:

```csv
Email,2. <PERSON>r CTV Book,3. SĐ<PERSON>,4. <PERSON><PERSON><PERSON>,5. <PERSON><PERSON><PERSON>,6. <PERSON><PERSON><PERSON>,7. <PERSON><PERSON><PERSON><PERSON>,7. <PERSON><PERSON><PERSON> demo buổi đầu,9. <PERSON><PERSON><PERSON> họ<PERSON> bu<PERSON>i đầu tiên
<EMAIL>,uyenntt6.saleclass,337348482,Thu Uyên,Lớp 2,Toán,,,7/23/2025
<EMAIL>,anhnt.saleclass,398081547,Hoàng Long,Lớp 2,Toán,,,7/23/2025
```

### Key Columns:
- **Email**: Contact's email address (used for searching in Google Chat)
- **2. User CTV Book**: Username with .saleclass suffix (used to extract nickname)
- **3. SĐT Phụ huynh**: Phone numbers (multiple rows with same email will be grouped)

## Usage

### Basic Usage

1. Update the CSV file path in the script:
```python
csv_file_path = "/path/to/your/csv/file.csv"
```

2. Run the script:
```bash
python google_chat_messaging.py
```

### Configuration Options

You can modify these settings in the `main()` function:

```python
automation = GoogleChatMessaging(
    csv_file_path="your_file.csv",
    headless=False,  # Set to True for headless mode
    incognito=True,  # Set to False to disable incognito mode
    timeout=30       # Timeout in seconds
)
```

### Incognito Mode

By default, the script runs Chrome in incognito mode, which provides several benefits:

- **Privacy**: No browsing history, cookies, or site data is saved
- **Clean Session**: Each run starts with a fresh browser session
- **Avoid Conflicts**: Prevents conflicts with existing Google account sessions
- **Security**: Reduces risk of session hijacking or data leakage

To disable incognito mode:
```python
automation = GoogleChatMessaging(
    csv_file_path="your_file.csv",
    incognito=False  # Disable incognito mode
)
```

## Message Template

The script sends messages using this template:

```
Chào {nickname},
Bạn có những số demo done sau chưa chuyển, vui lòng check CRM và chuyển số trước 14h, sau 14h L5 của bạn sẽ không được tính nữa:

📱 List số:
{phone_number_1}
{phone_number_2}
{phone_number_n}
```

## Script Flow

1. **CSV Processing**: 
   - Reads the CSV file
   - Groups data by email address
   - Extracts nicknames (removes .saleclass suffix)
   - Collects phone numbers for each contact

2. **Google Chat Login**:
   - Opens Chrome browser
   - Navigates to Google login page
   - Enters credentials automatically
   - Handles 2FA if required
   - Verifies successful login

3. **Message Sending**:
   - For each contact in the CSV:
     - Searches for the contact by email
     - Clicks on the first search result
     - Composes personalized message
     - Sends the message
     - Waits before processing next contact

## Error Handling

The script handles various scenarios:

- **Missing CSV file**: Uses sample data if main file not found
- **Contact not found**: Logs warning and continues with next contact
- **No phone numbers**: Skips contact and logs warning
- **Network issues**: Retries with different selectors
- **Rate limiting**: Built-in delays between messages

## Logging

The script provides detailed logging:

```
2025-07-29 17:30:00,123 - INFO - Reading CSV file: sample_data.csv
2025-07-29 17:30:00,456 - INFO - Processed 4 unique contacts
2025-07-29 17:30:01,789 - INFO - Starting Google Chat login process...
2025-07-29 17:30:15,234 - INFO - Google Chat interface verified successfully!
2025-07-29 17:30:16,567 - INFO - Processing contact 1/4: <EMAIL>
2025-07-29 17:30:18,890 - INFO - Successfully sent <NAME_EMAIL>
```

## Troubleshooting

### Common Issues

1. **CSV file not found**: Check the file path and ensure the file exists
2. **Login fails**: Verify credentials and check for 2FA requirements
3. **Contact not found**: Ensure the email addresses in CSV match Google Chat contacts
4. **Message not sent**: Check Google Chat interface selectors (may need updates)

### Debug Mode

For debugging, you can:

1. Set `headless=False` to see the browser actions
2. Add breakpoints in the code
3. Check the console logs for detailed information

## Security Considerations

- **Credentials**: Consider using environment variables for sensitive credentials
- **Rate Limiting**: The script includes delays to avoid being rate-limited
- **2FA**: Manual intervention required if 2FA is enabled

## Legal and Ethical Considerations

- Use this script responsibly and in compliance with Google's Terms of Service
- Ensure you have proper authorization to send automated messages
- Be mindful of spam policies and recipient consent

## Sample Output

```
2025-07-29 17:30:00,123 - INFO - Starting Google Chat messaging automation...
2025-07-29 17:30:00,456 - INFO - CSV file loaded with 4 rows
2025-07-29 17:30:00,789 - INFO - Processed 4 unique contacts
2025-07-29 17:30:01,123 - INFO - Chrome WebDriver setup completed successfully
2025-07-29 17:30:15,456 - INFO - Google Chat interface verified successfully!
2025-07-29 17:30:16,789 - INFO - Starting to send messages to all contacts...
2025-07-29 17:30:18,123 - INFO - Processing contact 1/4: <EMAIL>
2025-07-29 17:30:20,456 - INFO - Successfully sent <NAME_EMAIL>
2025-07-29 17:30:25,789 - INFO - Processing contact 2/4: <EMAIL>
2025-07-29 17:30:28,123 - INFO - Successfully sent <NAME_EMAIL>
2025-07-29 17:30:35,456 - INFO - Messaging completed. Success: 4, Failed: 0
2025-07-29 17:30:35,789 - INFO - All messaging completed successfully!
```
