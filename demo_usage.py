#!/usr/bin/env python3
"""
Demo script showing how to use the Google Chat Messaging Automation
Both programmatically and via the web interface
"""

import os
import sys
from google_chat_messaging import GoogleChatMessaging

def demo_command_line_usage():
    """Demonstrate command-line usage of the automation"""
    print("🚀 Google Chat Messaging Automation - Command Line Demo")
    print("=" * 60)
    
    # Check if sample CSV exists
    csv_file = "sample_data.csv"
    if not os.path.exists(csv_file):
        print(f"❌ Sample CSV file '{csv_file}' not found!")
        print("Please ensure the sample CSV file exists in the current directory.")
        return False
    
    print(f"📄 Using CSV file: {csv_file}")
    
    # Create automation instance
    automation = GoogleChatMessaging(
        csv_file_path=csv_file,
        headless=False,  # Set to True for headless mode
        incognito=True,  # Use incognito mode
        timeout=30
    )
    
    # You can set custom credentials if needed
    # automation.set_credentials("<EMAIL>", "your_password")
    
    print("⚙️ Configuration:")
    print(f"  - Headless mode: {automation.headless}")
    print(f"  - Incognito mode: {automation.incognito}")
    print(f"  - Timeout: {automation.timeout} seconds")
    print(f"  - Username: {automation.username}")
    
    # Load and preview data
    try:
        automation.read_csv_data()
        print(f"📊 Loaded {len(automation.contacts_data)} contacts:")
        
        for email, contact in automation.contacts_data.items():
            print(f"  • {email} ({contact['nickname']}) - {len(contact['phone_list'])} numbers")
        
        print("\n🔄 Ready to start automation!")
        print("Note: This demo will not actually run the automation to avoid unintended messages.")
        print("To run the full automation, call: automation.run_automation()")
        
        return True
        
    except Exception as e:
        print(f"❌ Error loading CSV data: {e}")
        return False

def demo_web_interface_info():
    """Show information about the web interface"""
    print("\n🌐 Web Interface Demo")
    print("=" * 60)
    
    print("The Gradio web interface provides a user-friendly way to:")
    print("✅ Upload and validate CSV files")
    print("✅ Preview data and messages before sending")
    print("✅ Configure automation settings")
    print("✅ Monitor real-time progress")
    print("✅ View detailed logs and results")
    
    print("\n🚀 To start the web interface:")
    print("1. Run: python gradio_app.py")
    print("2. Open: http://localhost:7861")
    print("3. Upload your CSV file")
    print("4. Configure settings and start automation")
    
    print("\n📋 Web Interface Features:")
    print("• File Upload & Validation")
    print("• Data Preview Table")
    print("• Message Preview")
    print("• Login Configuration")
    print("• Browser Settings (Headless/Incognito)")
    print("• Real-time Progress Tracking")
    print("• Comprehensive Logging")
    print("• Results Summary")

def create_sample_csv():
    """Create a sample CSV file for testing"""
    sample_data = """Email,2. User CTV Book,3. SĐT Phụ huynh,4. Tên học sinh,5. Lớp,6. Môn,7. Thiết bị học,7. Giờ học demo buổi đầu,9. Ngày học buổi đầu tiên
<EMAIL>,test1.saleclass,123456789,Test Student 1,Lớp 10,Toán,Laptop,14:00,2024-01-15
<EMAIL>,test2.saleclass,987654321,Test Student 2,Lớp 11,Vật lý,Tablet,15:30,2024-01-16
<EMAIL>,test3.saleclass,555666777,Test Student 3,Lớp 12,Hóa học,Desktop,16:00,2024-01-17"""
    
    with open("demo_sample.csv", "w", encoding="utf-8") as f:
        f.write(sample_data)
    
    print("📄 Created demo_sample.csv for testing")
    return "demo_sample.csv"

def main():
    """Main demo function"""
    print("🎯 Google Chat Messaging Automation - Demo")
    print("This demo shows how to use the automation tool")
    print()
    
    # Create sample CSV if it doesn't exist
    if not os.path.exists("sample_data.csv"):
        print("📄 Creating sample CSV file...")
        create_sample_csv()
    
    # Demo command line usage
    success = demo_command_line_usage()
    
    # Show web interface info
    demo_web_interface_info()
    
    print("\n" + "=" * 60)
    print("🎉 Demo completed!")
    
    if success:
        print("✅ Command-line demo successful")
    else:
        print("❌ Command-line demo had issues")
    
    print("\n💡 Next Steps:")
    print("1. Review the sample CSV format")
    print("2. Prepare your own CSV file with contact data")
    print("3. Choose between command-line or web interface")
    print("4. Configure your Google Chat credentials")
    print("5. Run the automation!")
    
    print("\n⚠️  Important Reminders:")
    print("• Test with a small dataset first")
    print("• Ensure you have permission to message all contacts")
    print("• Follow Google Chat's terms of service")
    print("• Use responsibly for legitimate business communication")

if __name__ == "__main__":
    main()
