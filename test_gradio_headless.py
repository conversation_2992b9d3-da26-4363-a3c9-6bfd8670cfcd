#!/usr/bin/env python3
"""
Test script to verify Gradio headless mode configuration
"""

from gradio_app import GradioGoogleChatApp
import tempfile
import os

def test_gradio_headless():
    """Test Gradio headless mode configuration"""
    print("🧪 Testing Gradio Headless Mode Configuration")
    print("=" * 50)
    
    # Create test CSV file
    test_csv_content = """Email,2. User CTV Book,3. SĐ<PERSON>ụ huynh,4. <PERSON><PERSON><PERSON>,5. <PERSON><PERSON><PERSON>,6. <PERSON><PERSON><PERSON>,7. <PERSON><PERSON><PERSON><PERSON> bị họ<PERSON>,7. <PERSON><PERSON><PERSON> học demo buổi đầu,9. <PERSON><PERSON><PERSON> họ<PERSON> buổi đầu tiên
<EMAIL>,test.saleclass,123456789,Test Student,Lớp 10,Toán,Laptop,14:00,2024-01-15"""
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
        f.write(test_csv_content)
        test_csv_path = f.name
    
    try:
        # Create Gradio app instance
        app = GradioGoogleChatApp()
        
        # Test 1: Headless = False (should show browser)
        print("1. Testing headless=False...")
        result = app.start_automation(
            file_path=test_csv_path,
            username="<EMAIL>",
            password="testpass",
            headless=False,  # Should show browser
            incognito=True
        )
        print(f"   Result: {result[0][:50]}...")
        
        # Test 2: Headless = True (should run in background)
        print("2. Testing headless=True...")
        result = app.start_automation(
            file_path=test_csv_path,
            username="<EMAIL>", 
            password="testpass",
            headless=True,   # Should run in background
            incognito=True
        )
        print(f"   Result: {result[0][:50]}...")
        
    except Exception as e:
        print(f"❌ Error during test: {e}")
    
    finally:
        # Cleanup
        try:
            os.unlink(test_csv_path)
        except:
            pass
    
    print("\n💡 Instructions for manual testing:")
    print("1. Open Gradio interface: http://localhost:7861")
    print("2. Upload your CSV file")
    print("3. Enter credentials")
    print("4. UNCHECK 'Headless Mode' checkbox")
    print("5. Click 'Start Automation'")
    print("6. You should see Chrome browser window appear!")
    
    print("\n🔧 If browser still doesn't appear:")
    print("- Check if Chrome is installed")
    print("- Try running without incognito mode")
    print("- Check system display settings")
    print("- Look for Chrome process in task manager")

if __name__ == "__main__":
    test_gradio_headless()
