<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="63">
            <item index="0" class="java.lang.String" itemvalue="scikit-image" />
            <item index="1" class="java.lang.String" itemvalue="pillow" />
            <item index="2" class="java.lang.String" itemvalue="numpy" />
            <item index="3" class="java.lang.String" itemvalue="easydict" />
            <item index="4" class="java.lang.String" itemvalue="tqdm" />
            <item index="5" class="java.lang.String" itemvalue="tensorboardX" />
            <item index="6" class="java.lang.String" itemvalue="torch" />
            <item index="7" class="java.lang.String" itemvalue="torchvision" />
            <item index="8" class="java.lang.String" itemvalue="opencv_python" />
            <item index="9" class="java.lang.String" itemvalue="Pillow" />
            <item index="10" class="java.lang.String" itemvalue="onnxruntime" />
            <item index="11" class="java.lang.String" itemvalue="paddle" />
            <item index="12" class="java.lang.String" itemvalue="yacs" />
            <item index="13" class="java.lang.String" itemvalue="termcolor" />
            <item index="14" class="java.lang.String" itemvalue="pydot" />
            <item index="15" class="java.lang.String" itemvalue="tensorboard" />
            <item index="16" class="java.lang.String" itemvalue="fvcore" />
            <item index="17" class="java.lang.String" itemvalue="tabulate" />
            <item index="18" class="java.lang.String" itemvalue="iopath" />
            <item index="19" class="java.lang.String" itemvalue="future" />
            <item index="20" class="java.lang.String" itemvalue="cloudpickle" />
            <item index="21" class="java.lang.String" itemvalue="omegaconf" />
            <item index="22" class="java.lang.String" itemvalue="cv2" />
            <item index="23" class="java.lang.String" itemvalue="matplotlib" />
            <item index="24" class="java.lang.String" itemvalue="pycocotools" />
            <item index="25" class="java.lang.String" itemvalue="detectron2" />
            <item index="26" class="java.lang.String" itemvalue="transformers" />
            <item index="27" class="java.lang.String" itemvalue="deepspeed" />
            <item index="28" class="java.lang.String" itemvalue="loguru" />
            <item index="29" class="java.lang.String" itemvalue="PyMuPDF" />
            <item index="30" class="java.lang.String" itemvalue="paddleocr" />
            <item index="31" class="java.lang.String" itemvalue="struct-eqtable" />
            <item index="32" class="java.lang.String" itemvalue="paddlepaddle-gpu" />
            <item index="33" class="java.lang.String" itemvalue="lmdeploy" />
            <item index="34" class="java.lang.String" itemvalue="unimernet" />
            <item index="35" class="java.lang.String" itemvalue="doclayout-yolo" />
            <item index="36" class="java.lang.String" itemvalue="ultralytics" />
            <item index="37" class="java.lang.String" itemvalue="glob2" />
            <item index="38" class="java.lang.String" itemvalue="python-time" />
            <item index="39" class="java.lang.String" itemvalue="os.path2" />
            <item index="40" class="java.lang.String" itemvalue="random2" />
            <item index="41" class="java.lang.String" itemvalue="wget" />
            <item index="42" class="java.lang.String" itemvalue="python-abc" />
            <item index="43" class="java.lang.String" itemvalue="collection" />
            <item index="44" class="java.lang.String" itemvalue="argparse" />
            <item index="45" class="java.lang.String" itemvalue="python-math" />
            <item index="46" class="java.lang.String" itemvalue="names" />
            <item index="47" class="java.lang.String" itemvalue="imageio" />
            <item index="48" class="java.lang.String" itemvalue="faker" />
            <item index="49" class="java.lang.String" itemvalue="shutils" />
            <item index="50" class="java.lang.String" itemvalue="pytest-shutil" />
            <item index="51" class="java.lang.String" itemvalue="typing" />
            <item index="52" class="java.lang.String" itemvalue="pandas" />
            <item index="53" class="java.lang.String" itemvalue="typing-extensions" />
            <item index="54" class="java.lang.String" itemvalue="pydantic-core" />
            <item index="55" class="java.lang.String" itemvalue="importlib-metadata" />
            <item index="56" class="java.lang.String" itemvalue="paddlepaddle" />
            <item index="57" class="java.lang.String" itemvalue="gradio" />
            <item index="58" class="java.lang.String" itemvalue="opencv-python" />
            <item index="59" class="java.lang.String" itemvalue="tokenizers" />
            <item index="60" class="java.lang.String" itemvalue="sentencepiece" />
            <item index="61" class="java.lang.String" itemvalue="wandb" />
            <item index="62" class="java.lang.String" itemvalue="fire" />
          </list>
        </value>
      </option>
    </inspection_tool>
  </profile>
</component>