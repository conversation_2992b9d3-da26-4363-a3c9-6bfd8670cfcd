import{SvelteComponent as Ce,init as we,safe_not_equal as Ee,element as C,space as J,text as d,create_component as ne,claim_element as w,children as O,get_svelte_dataset as te,claim_space as Y,claim_text as m,detach as f,claim_component as se,attr as D,insert_hydration as v,append_hydration as p,mount_component as ie,listen as Ve,set_data as Z,transition_in as W,transition_out as Q,destroy_component as re,create<PERSON><PERSON><PERSON><PERSON>patch<PERSON> as He,empty as fe,src_url_equal as zt,group_outros as me,check_outros as ve,noop as pe,ensure_array_like as ce,toggle_class as Ae,destroy_each as ge,set_style as he,binding_callbacks as Ne,onMount as Ht,tick as Gt,bubble as Be}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";import{C as Ut}from"./clear.D_TEXRKD.js";import{W as Jt,o as Ue,L as Ft,B as $e}from"./2.CXBv8kT_.js";function Yt(s){let e,t,l="API Docs",n,i,r,u,a,c,o,_=`To expose an API endpoint of your app in this page, set the <code>api_name</code>
		parameter of the event listener.
		<br/>
		For more information, visit the
		<a href="https://gradio.app/sharing_your_app/#api-page" target="_blank">API Page guide</a>
		. To hide the API documentation button and this page, set
		<code>show_api=False</code>
		in the
		<code>Blocks.launch()</code>
		method.`,h,k,b,T,E,A;return b=new Ut({}),{c(){e=C("div"),t=C("h1"),t.textContent=l,n=J(),i=C("p"),r=d(`No API Routes found for
		`),u=C("code"),a=d(s[0]),c=J(),o=C("p"),o.innerHTML=_,h=J(),k=C("button"),ne(b.$$.fragment),this.h()},l(g){e=w(g,"DIV",{class:!0});var P=O(e);t=w(P,"H1",{"data-svelte-h":!0}),te(t)!=="svelte-1nemy2n"&&(t.textContent=l),n=Y(P),i=w(P,"P",{class:!0});var $=O(i);r=m($,`No API Routes found for
		`),u=w($,"CODE",{class:!0});var N=O(u);a=m(N,s[0]),N.forEach(f),$.forEach(f),c=Y(P),o=w(P,"P",{"data-svelte-h":!0}),te(o)!=="svelte-2ediv8"&&(o.innerHTML=_),P.forEach(f),h=Y(g),k=w(g,"BUTTON",{class:!0});var y=O(k);se(b.$$.fragment,y),y.forEach(f),this.h()},h(){D(u,"class","svelte-e1ha0f"),D(i,"class","attention svelte-e1ha0f"),D(e,"class","wrap prose svelte-e1ha0f"),D(k,"class","svelte-e1ha0f")},m(g,P){v(g,e,P),p(e,t),p(e,n),p(e,i),p(i,r),p(i,u),p(u,a),p(e,c),p(e,o),v(g,h,P),v(g,k,P),ie(b,k,null),T=!0,E||(A=Ve(k,"click",s[2]),E=!0)},p(g,[P]){(!T||P&1)&&Z(a,g[0])},i(g){T||(W(b.$$.fragment,g),T=!0)},o(g){Q(b.$$.fragment,g),T=!1},d(g){g&&(f(e),f(h),f(k)),re(b),E=!1,A()}}}function Wt(s,e,t){const l=He();let{root:n}=e;const i=()=>l("close");return s.$$set=r=>{"root"in r&&t(0,n=r.root)},[n,l,i]}class Zt extends Ce{constructor(e){super(),we(this,e,Wt,Yt,Ee,{root:0})}}function Ge(s){let e,t;return e=new Ue({props:{size:"sm",variant:"secondary",elem_id:"start-api-recorder",$$slots:{default:[Qt]},$$scope:{ctx:s}}}),e.$on("click",s[4]),{c(){ne(e.$$.fragment)},l(l){se(e.$$.fragment,l)},m(l,n){ie(e,l,n),t=!0},p(l,n){const i={};n&64&&(i.$$scope={dirty:n,ctx:l}),e.$set(i)},i(l){t||(W(e.$$.fragment,l),t=!0)},o(l){Q(e.$$.fragment,l),t=!1},d(l){re(e,l)}}}function Qt(s){let e,t,l,n="API Recorder";return{c(){e=C("div"),t=J(),l=C("p"),l.textContent=n,this.h()},l(i){e=w(i,"DIV",{class:!0}),O(e).forEach(f),t=Y(i),l=w(i,"P",{class:!0,"data-svelte-h":!0}),te(l)!=="svelte-1ywx371"&&(l.textContent=n),this.h()},h(){D(e,"class","loading-dot self-baseline svelte-1i1gjw2"),D(l,"class","self-baseline btn-text svelte-1i1gjw2")},m(i,r){v(i,e,r),v(i,t,r),v(i,l,r)},p:pe,d(i){i&&(f(e),f(t),f(l))}}}function Xt(s){let e;return{c(){e=d("MCP Tool")},l(t){e=m(t,"MCP Tool")},m(t,l){v(t,e,l)},d(t){t&&f(e)}}}function Kt(s){let e;return{c(){e=d("API endpoint")},l(t){e=m(t,"API endpoint")},m(t,l){v(t,e,l)},d(t){t&&f(e)}}}function Je(s){let e;return{c(){e=d("s")},l(t){e=m(t,"s")},m(t,l){v(t,e,l)},d(t){t&&f(e)}}}function xt(s){let e,t,l,n,i,r,u,a,c,o,_,h,k,b,T,E,A,g,P,$,N,y,I,S=s[2]!=="mcp"&&Ge(s);function R(V,j){return V[2]!=="mcp"?Kt:Xt}let U=R(s),G=U(s),H=s[1]>1&&Je();return $=new Ut({}),{c(){e=C("h2"),t=C("img"),n=J(),i=C("div"),r=d(`API documentation
		`),u=C("div"),a=d(s[0]),c=J(),o=C("span"),S&&S.c(),_=J(),h=C("p"),k=C("span"),b=d(s[1]),T=J(),G.c(),E=fe(),H&&H.c(),A=C("br"),g=J(),P=C("button"),ne($.$$.fragment),this.h()},l(V){e=w(V,"H2",{class:!0});var j=O(e);t=w(j,"IMG",{src:!0,alt:!0,class:!0}),n=Y(j),i=w(j,"DIV",{class:!0});var F=O(i);r=m(F,`API documentation
		`),u=w(F,"DIV",{class:!0});var B=O(u);a=m(B,s[0]),B.forEach(f),F.forEach(f),c=Y(j),o=w(j,"SPAN",{class:!0});var L=O(o);S&&S.l(L),_=Y(L),h=w(L,"P",{});var q=O(h);k=w(q,"SPAN",{class:!0});var z=O(k);b=m(z,s[1]),z.forEach(f),T=Y(q),G.l(q),E=fe(),H&&H.l(q),A=w(q,"BR",{}),q.forEach(f),L.forEach(f),j.forEach(f),g=Y(V),P=w(V,"BUTTON",{class:!0});var ue=O(P);se($.$$.fragment,ue),ue.forEach(f),this.h()},h(){zt(t.src,l=Jt)||D(t,"src",l),D(t,"alt",""),D(t,"class","svelte-1i1gjw2"),D(u,"class","url svelte-1i1gjw2"),D(i,"class","title svelte-1i1gjw2"),D(k,"class","url svelte-1i1gjw2"),D(o,"class","counts svelte-1i1gjw2"),D(e,"class","svelte-1i1gjw2"),D(P,"class","svelte-1i1gjw2")},m(V,j){v(V,e,j),p(e,t),p(e,n),p(e,i),p(i,r),p(i,u),p(u,a),p(e,c),p(e,o),S&&S.m(o,null),p(o,_),p(o,h),p(h,k),p(k,b),p(h,T),G.m(h,null),p(h,E),H&&H.m(h,null),p(h,A),v(V,g,j),v(V,P,j),ie($,P,null),N=!0,y||(I=Ve(P,"click",s[5]),y=!0)},p(V,[j]){(!N||j&1)&&Z(a,V[0]),V[2]!=="mcp"?S?(S.p(V,j),j&4&&W(S,1)):(S=Ge(V),S.c(),W(S,1),S.m(o,_)):S&&(me(),Q(S,1,1,()=>{S=null}),ve()),(!N||j&2)&&Z(b,V[1]),U!==(U=R(V))&&(G.d(1),G=U(V),G&&(G.c(),G.m(h,E))),V[1]>1?H||(H=Je(),H.c(),H.m(h,A)):H&&(H.d(1),H=null)},i(V){N||(W(S),W($.$$.fragment,V),N=!0)},o(V){Q(S),Q($.$$.fragment,V),N=!1},d(V){V&&(f(e),f(g),f(P)),S&&S.d(),G.d(),H&&H.d(),re($),y=!1,I()}}}function el(s,e,t){let{root:l}=e,{api_count:n}=e,{current_language:i="python"}=e;const r=He(),u=()=>r("close",{api_recorder_visible:!0}),a=()=>r("close");return s.$$set=c=>{"root"in c&&t(0,l=c.root),"api_count"in c&&t(1,n=c.api_count),"current_language"in c&&t(2,i=c.current_language)},[l,n,i,r,u,a]}class tl extends Ce{constructor(e){super(),we(this,e,el,xt,Ee,{root:0,api_count:1,current_language:2})}}function ke(s,e,t=null){return e===void 0?t==="py"?"None":null:s===null&&t==="py"?"None":e==="string"||e==="str"?t===null?s:'"'+s+'"':e==="number"?t===null?parseFloat(s):s:e==="boolean"||e=="bool"?t==="py"?(s=String(s),s==="true"?"True":"False"):t==="js"||t==="bash"?s:s==="true":e==="List[str]"?(s=JSON.stringify(s),s):e.startsWith("Literal['")?'"'+s+'"':t===null?s===""?null:JSON.parse(s):typeof s=="string"?s===""?t==="py"?"None":"null":s:(t==="bash"&&(s=Me(s)),t==="py"&&(s=Le(s)),ll(s))}function Bt(s){if(typeof s=="object"&&s!==null&&s.hasOwnProperty("url")&&s.hasOwnProperty("meta")&&typeof s.meta=="object"&&s.meta!==null&&s.meta._type==="gradio.FileData")return!0;if(typeof s=="object"&&s!==null){for(let e in s)if(typeof s[e]=="object"&&Bt(s[e]))return!0}return!1}function Me(s){var e;return typeof s=="object"&&s!==null&&!Array.isArray(s)&&"url"in s&&s.url&&"meta"in s&&((e=s.meta)==null?void 0:e._type)==="gradio.FileData"?{path:s.url,meta:{_type:"gradio.FileData"}}:(Array.isArray(s)?s.forEach((t,l)=>{typeof t=="object"&&t!==null&&(s[l]=Me(t))}):typeof s=="object"&&s!==null&&Object.keys(s).forEach(t=>{s[t]=Me(s[t])}),s)}function Le(s){var e;return typeof s=="object"&&s!==null&&!Array.isArray(s)&&"url"in s&&s.url&&"meta"in s&&((e=s.meta)==null?void 0:e._type)==="gradio.FileData"?`handle_file('${s.url}')`:(Array.isArray(s)?s.forEach((t,l)=>{typeof t=="object"&&t!==null&&(s[l]=Le(t))}):typeof s=="object"&&s!==null&&Object.keys(s).forEach(t=>{s[t]=Le(s[t])}),s)}function ll(s){let e=JSON.stringify(s,(n,i)=>i===null?"UNQUOTEDNone":typeof i=="string"&&i.startsWith("handle_file(")&&i.endsWith(")")?`UNQUOTED${i}`:i);const t=/"UNQUOTEDhandle_file\(([^)]*)\)"/g;e=e.replace(t,(n,i)=>`handle_file(${i})`);const l=/"UNQUOTEDNone"/g;return e.replace(l,"None")}function Ye(s,e,t){const l=s.slice();return l[4]=e[t].label,l[5]=e[t].python_type,l[6]=e[t].component,l[7]=e[t].parameter_name,l[8]=e[t].parameter_has_default,l[9]=e[t].parameter_default,l[11]=t,l}function We(s){let e;return{c(){e=d("s")},l(t){e=m(t,"s")},m(t,l){v(t,e,l)},d(t){t&&f(e)}}}function nl(s){let e=(s[2][s[11]].type||"any")+"",t;return{c(){t=d(e)},l(l){t=m(l,e)},m(l,n){v(l,t,n)},p(l,n){n&4&&e!==(e=(l[2][l[11]].type||"any")+"")&&Z(t,e)},d(l){l&&f(t)}}}function sl(s){let e=s[5].type+"",t,l,n=s[8]&&s[9]===null&&Ze();return{c(){t=d(e),n&&n.c(),l=fe()},l(i){t=m(i,e),n&&n.l(i),l=fe()},m(i,r){v(i,t,r),n&&n.m(i,r),v(i,l,r)},p(i,r){r&2&&e!==(e=i[5].type+"")&&Z(t,e),i[8]&&i[9]===null?n||(n=Ze(),n.c(),n.m(l.parentNode,l)):n&&(n.d(1),n=null)},d(i){i&&(f(t),f(l)),n&&n.d(i)}}}function Ze(s){let e;return{c(){e=d(` |
							None`)},l(t){e=m(t,` |
							None`)},m(t,l){v(t,e,l)},d(t){t&&f(e)}}}function il(s){let e,t="Default: ",l,n=ke(s[9],s[5].type,"py")+"",i;return{c(){e=C("span"),e.textContent=t,l=C("span"),i=d(n),this.h()},l(r){e=w(r,"SPAN",{"data-svelte-h":!0}),te(e)!=="svelte-y3zgxh"&&(e.textContent=t),l=w(r,"SPAN",{class:!0,style:!0});var u=O(l);i=m(u,n),u.forEach(f),this.h()},h(){D(l,"class","code svelte-1yt946s"),he(l,"font-size","var(--text-sm)")},m(r,u){v(r,e,u),v(r,l,u),p(l,i)},p(r,u){u&2&&n!==(n=ke(r[9],r[5].type,"py")+"")&&Z(i,n)},d(r){r&&(f(e),f(l))}}}function rl(s){let e,t="Required";return{c(){e=C("span"),e.textContent=t,this.h()},l(l){e=w(l,"SPAN",{style:!0,"data-svelte-h":!0}),te(e)!=="svelte-1y2sdp"&&(e.textContent=t),this.h()},h(){he(e,"font-weight","bold")},m(l,n){v(l,e,n)},p:pe,d(l){l&&f(e)}}}function Qe(s){let e,t,l,n,i,r=(s[3]!=="bash"&&s[7]?s[7]:"["+s[11]+"]")+"",u,a,c,o,_,h,k,b=s[4]+"",T,E,A=s[6]+"",g,P,$=s[5].description+"",N,y;function I(V,j){return V[3]==="python"?sl:nl}let S=I(s),R=S(s);function U(V,j){return!V[8]||V[3]=="bash"?rl:il}let G=U(s),H=G(s);return{c(){e=C("hr"),t=J(),l=C("div"),n=C("p"),i=C("span"),u=d(r),a=J(),c=C("span"),R.c(),o=J(),H.c(),_=J(),h=C("p"),k=d('The input value that is provided in the "'),T=d(b),E=d('" '),g=d(A),P=d(`
				component. `),N=d($),y=J(),this.h()},l(V){e=w(V,"HR",{class:!0}),t=Y(V),l=w(V,"DIV",{style:!0});var j=O(l);n=w(j,"P",{style:!0});var F=O(n);i=w(F,"SPAN",{class:!0,style:!0});var B=O(i);u=m(B,r),B.forEach(f),a=Y(F),c=w(F,"SPAN",{class:!0,style:!0});var L=O(c);R.l(L),L.forEach(f),o=Y(F),H.l(F),F.forEach(f),_=Y(j),h=w(j,"P",{class:!0});var q=O(h);k=m(q,'The input value that is provided in the "'),T=m(q,b),E=m(q,'" '),g=m(q,A),P=m(q,`
				component. `),N=m(q,$),q.forEach(f),y=Y(j),j.forEach(f),this.h()},h(){D(e,"class","hr svelte-1yt946s"),D(i,"class","code svelte-1yt946s"),he(i,"margin-right","10px"),D(c,"class","code highlight svelte-1yt946s"),he(c,"margin-right","10px"),he(n,"white-space","nowrap"),he(n,"overflow-x","auto"),D(h,"class","desc svelte-1yt946s"),he(l,"margin","10px")},m(V,j){v(V,e,j),v(V,t,j),v(V,l,j),p(l,n),p(n,i),p(i,u),p(n,a),p(n,c),R.m(c,null),p(n,o),H.m(n,null),p(l,_),p(l,h),p(h,k),p(h,T),p(h,E),p(h,g),p(h,P),p(h,N),p(l,y)},p(V,j){j&10&&r!==(r=(V[3]!=="bash"&&V[7]?V[7]:"["+V[11]+"]")+"")&&Z(u,r),S===(S=I(V))&&R?R.p(V,j):(R.d(1),R=S(V),R&&(R.c(),R.m(c,null))),G===(G=U(V))&&H?H.p(V,j):(H.d(1),H=G(V),H&&(H.c(),H.m(n,null))),j&2&&b!==(b=V[4]+"")&&Z(T,b),j&2&&A!==(A=V[6]+"")&&Z(g,A),j&2&&$!==($=V[5].description+"")&&Z(N,$)},d(V){V&&(f(e),f(t),f(l)),R.d(),H.d()}}}function Xe(s){let e,t,l;return t=new Ft({props:{margin:!1}}),{c(){e=C("div"),ne(t.$$.fragment),this.h()},l(n){e=w(n,"DIV",{class:!0});var i=O(e);se(t.$$.fragment,i),i.forEach(f),this.h()},h(){D(e,"class","load-wrap")},m(n,i){v(n,e,i),ie(t,e,null),l=!0},i(n){l||(W(t.$$.fragment,n),l=!0)},o(n){Q(t.$$.fragment,n),l=!1},d(n){n&&f(e),re(t)}}}function al(s){let e,t,l='<div class="toggle-dot svelte-1yt946s"></div>',n,i=s[1].length+"",r,u,a,c,o,_,h,k,b=s[1].length!=1&&We(),T=ce(s[1]),E=[];for(let g=0;g<T.length;g+=1)E[g]=Qe(Ye(s,T,g));let A=s[0]&&Xe();return{c(){e=C("h4"),t=C("div"),t.innerHTML=l,n=d(`
	Accepts `),r=d(i),u=d(" parameter"),b&&b.c(),a=d(":"),c=J(),o=C("div");for(let g=0;g<E.length;g+=1)E[g].c();_=J(),A&&A.c(),h=fe(),this.h()},l(g){e=w(g,"H4",{class:!0});var P=O(e);t=w(P,"DIV",{class:!0,"data-svelte-h":!0}),te(t)!=="svelte-1pmwe5h"&&(t.innerHTML=l),n=m(P,`
	Accepts `),r=m(P,i),u=m(P," parameter"),b&&b.l(P),a=m(P,":"),P.forEach(f),c=Y(g),o=w(g,"DIV",{});var $=O(o);for(let N=0;N<E.length;N+=1)E[N].l($);$.forEach(f),_=Y(g),A&&A.l(g),h=fe(),this.h()},h(){D(t,"class","toggle-icon svelte-1yt946s"),D(e,"class","svelte-1yt946s"),Ae(o,"hide",s[0])},m(g,P){v(g,e,P),p(e,t),p(e,n),p(e,r),p(e,u),b&&b.m(e,null),p(e,a),v(g,c,P),v(g,o,P);for(let $=0;$<E.length;$+=1)E[$]&&E[$].m(o,null);v(g,_,P),A&&A.m(g,P),v(g,h,P),k=!0},p(g,[P]){if((!k||P&2)&&i!==(i=g[1].length+"")&&Z(r,i),g[1].length!=1?b||(b=We(),b.c(),b.m(e,a)):b&&(b.d(1),b=null),P&14){T=ce(g[1]);let $;for($=0;$<T.length;$+=1){const N=Ye(g,T,$);E[$]?E[$].p(N,P):(E[$]=Qe(N),E[$].c(),E[$].m(o,null))}for(;$<E.length;$+=1)E[$].d(1);E.length=T.length}(!k||P&1)&&Ae(o,"hide",g[0]),g[0]?A?P&1&&W(A,1):(A=Xe(),A.c(),W(A,1),A.m(h.parentNode,h)):A&&(me(),Q(A,1,1,()=>{A=null}),ve())},i(g){k||(W(A),k=!0)},o(g){Q(A),k=!1},d(g){g&&(f(e),f(c),f(o),f(_),f(h)),b&&b.d(),ge(E,g),A&&A.d(g)}}}function ol(s,e,t){let{is_running:l}=e,{endpoint_returns:n}=e,{js_returns:i}=e,{current_language:r}=e;return s.$$set=u=>{"is_running"in u&&t(0,l=u.is_running),"endpoint_returns"in u&&t(1,n=u.endpoint_returns),"js_returns"in u&&t(2,i=u.js_returns),"current_language"in u&&t(3,r=u.current_language)},[l,n,i,r]}class cl extends Ce{constructor(e){super(),we(this,e,ol,al,Ee,{is_running:0,endpoint_returns:1,js_returns:2,current_language:3})}}function fl(s){let e;return{c(){e=d(s[0])},l(t){e=m(t,s[0])},m(t,l){v(t,e,l)},p(t,l){l&1&&Z(e,t[0])},d(t){t&&f(e)}}}function ul(s){let e,t;return e=new Ue({props:{size:"sm",$$slots:{default:[fl]},$$scope:{ctx:s}}}),e.$on("click",s[1]),{c(){ne(e.$$.fragment)},l(l){se(e.$$.fragment,l)},m(l,n){ie(e,l,n),t=!0},p(l,[n]){const i={};n&9&&(i.$$scope={dirty:n,ctx:l}),e.$set(i)},i(l){t||(W(e.$$.fragment,l),t=!0)},o(l){Q(e.$$.fragment,l),t=!1},d(l){re(e,l)}}}function _l(s,e,t){let{code:l}=e,n="copy";function i(){navigator.clipboard.writeText(l),t(0,n="copied!"),setTimeout(()=>{t(0,n="copy")},1500)}return s.$$set=r=>{"code"in r&&t(2,l=r.code)},[n,i,l]}class ye extends Ce{constructor(e){super(),we(this,e,_l,ul,Ee,{code:2})}}function pl(s){let e,t,l,n,i,r,u,a;return t=new ye({props:{code:Re}}),{c(){e=C("div"),ne(t.$$.fragment),l=J(),n=C("div"),i=C("pre"),r=d("$ "),u=d(Re),this.h()},l(c){e=w(c,"DIV",{class:!0});var o=O(e);se(t.$$.fragment,o),o.forEach(f),l=Y(c),n=w(c,"DIV",{});var _=O(n);i=w(_,"PRE",{class:!0});var h=O(i);r=m(h,"$ "),u=m(h,Re),h.forEach(f),_.forEach(f),this.h()},h(){D(e,"class","copy svelte-hq8ezf"),D(i,"class","svelte-hq8ezf")},m(c,o){v(c,e,o),ie(t,e,null),v(c,l,o),v(c,n,o),p(n,i),p(i,r),p(i,u),a=!0},p:pe,i(c){a||(W(t.$$.fragment,c),a=!0)},o(c){Q(t.$$.fragment,c),a=!1},d(c){c&&(f(e),f(l),f(n)),re(t)}}}function hl(s){let e,t,l,n,i,r,u,a;return t=new ye({props:{code:Oe}}),{c(){e=C("div"),ne(t.$$.fragment),l=J(),n=C("div"),i=C("pre"),r=d("$ "),u=d(Oe),this.h()},l(c){e=w(c,"DIV",{class:!0});var o=O(e);se(t.$$.fragment,o),o.forEach(f),l=Y(c),n=w(c,"DIV",{});var _=O(n);i=w(_,"PRE",{class:!0});var h=O(i);r=m(h,"$ "),u=m(h,Oe),h.forEach(f),_.forEach(f),this.h()},h(){D(e,"class","copy svelte-hq8ezf"),D(i,"class","svelte-hq8ezf")},m(c,o){v(c,e,o),ie(t,e,null),v(c,l,o),v(c,n,o),p(n,i),p(i,r),p(i,u),a=!0},p:pe,i(c){a||(W(t.$$.fragment,c),a=!0)},o(c){Q(t.$$.fragment,c),a=!1},d(c){c&&(f(e),f(l),f(n)),re(t)}}}function dl(s){let e,t,l,n,i,r,u,a;return t=new ye({props:{code:qe}}),{c(){e=C("div"),ne(t.$$.fragment),l=J(),n=C("div"),i=C("pre"),r=d("$ "),u=d(qe),this.h()},l(c){e=w(c,"DIV",{class:!0});var o=O(e);se(t.$$.fragment,o),o.forEach(f),l=Y(c),n=w(c,"DIV",{});var _=O(n);i=w(_,"PRE",{class:!0});var h=O(i);r=m(h,"$ "),u=m(h,qe),h.forEach(f),_.forEach(f),this.h()},h(){D(e,"class","copy svelte-hq8ezf"),D(i,"class","svelte-hq8ezf")},m(c,o){v(c,e,o),ie(t,e,null),v(c,l,o),v(c,n,o),p(n,i),p(i,r),p(i,u),a=!0},p:pe,i(c){a||(W(t.$$.fragment,c),a=!0)},o(c){Q(t.$$.fragment,c),a=!1},d(c){c&&(f(e),f(l),f(n)),re(t)}}}function ml(s){let e,t,l,n;const i=[dl,hl,pl],r=[];function u(a,c){return a[0]==="python"?0:a[0]==="javascript"?1:a[0]==="bash"?2:-1}return~(t=u(s))&&(l=r[t]=i[t](s)),{c(){e=C("code"),l&&l.c(),this.h()},l(a){e=w(a,"CODE",{class:!0});var c=O(e);l&&l.l(c),c.forEach(f),this.h()},h(){D(e,"class","svelte-hq8ezf")},m(a,c){v(a,e,c),~t&&r[t].m(e,null),n=!0},p(a,c){let o=t;t=u(a),t===o?~t&&r[t].p(a,c):(l&&(me(),Q(r[o],1,1,()=>{r[o]=null}),ve()),~t?(l=r[t],l?l.p(a,c):(l=r[t]=i[t](a),l.c()),W(l,1),l.m(e,null)):l=null)},i(a){n||(W(l),n=!0)},o(a){Q(l),n=!1},d(a){a&&f(e),~t&&r[t].d()}}}function vl(s){let e,t;return e=new $e({props:{$$slots:{default:[ml]},$$scope:{ctx:s}}}),{c(){ne(e.$$.fragment)},l(l){se(e.$$.fragment,l)},m(l,n){ie(e,l,n),t=!0},p(l,[n]){const i={};n&3&&(i.$$scope={dirty:n,ctx:l}),e.$set(i)},i(l){t||(W(e.$$.fragment,l),t=!0)},o(l){Q(e.$$.fragment,l),t=!1},d(l){re(e,l)}}}let qe="pip install gradio_client",Oe="npm i -D @gradio/client",Re="curl --version";function gl(s,e,t){let{current_language:l}=e;return s.$$set=n=>{"current_language"in n&&t(0,l=n.current_language)},[l]}class bl extends Ce{constructor(e){super(),we(this,e,gl,vl,Ee,{current_language:0})}}function kl(s){let e,t,l,n="/"+s[0],i,r,u,a;return{c(){e=C("h3"),t=d(`API name:
	`),l=C("span"),i=d(n),r=J(),u=C("span"),a=d(s[1]),this.h()},l(c){e=w(c,"H3",{class:!0});var o=O(e);t=m(o,`API name:
	`),l=w(o,"SPAN",{class:!0});var _=O(l);i=m(_,n),_.forEach(f),r=Y(o),u=w(o,"SPAN",{class:!0});var h=O(u);a=m(h,s[1]),h.forEach(f),o.forEach(f),this.h()},h(){D(l,"class","post svelte-1y4an3z"),D(u,"class","desc svelte-1y4an3z"),D(e,"class","svelte-1y4an3z")},m(c,o){v(c,e,o),p(e,t),p(e,l),p(l,i),p(e,r),p(e,u),p(u,a)},p(c,[o]){o&1&&n!==(n="/"+c[0])&&Z(i,n),o&2&&Z(a,c[1])},i:pe,o:pe,d(c){c&&f(e)}}}function yl(s,e,t){let{api_name:l=null}=e,{description:n=null}=e;return s.$$set=i=>{"api_name"in i&&t(0,l=i.api_name),"description"in i&&t(1,n=i.description)},[l,n]}class Cl extends Ce{constructor(e){super(),we(this,e,yl,kl,Ee,{api_name:0,description:1})}}function Ke(s,e,t){const l=s.slice();return l[27]=e[t].label,l[22]=e[t].parameter_name,l[28]=e[t].type,l[20]=e[t].python_type,l[29]=e[t].component,l[21]=e[t].example_input,l[30]=e[t].serializer,l[26]=t,l}function xe(s,e,t){const l=s.slice();return l[27]=e[t].label,l[22]=e[t].parameter_name,l[28]=e[t].type,l[20]=e[t].python_type,l[29]=e[t].component,l[21]=e[t].example_input,l[30]=e[t].serializer,l[26]=t,l}function et(s,e,t){const l=s.slice();return l[29]=e[t].component,l[21]=e[t].example_input,l[26]=t,l}function tt(s,e,t){const l=s.slice();return l[20]=e[t].python_type,l[21]=e[t].example_input,l[22]=e[t].parameter_name,l[23]=e[t].parameter_has_default,l[24]=e[t].parameter_default,l[26]=t,l}function wl(s){let e,t;return e=new $e({props:{$$slots:{default:[Pl]},$$scope:{ctx:s}}}),{c(){ne(e.$$.fragment)},l(l){se(e.$$.fragment,l)},m(l,n){ie(e,l,n),t=!0},p(l,n){const i={};n[0]&3593|n[1]&8&&(i.$$scope={dirty:n,ctx:l}),e.$set(i)},i(l){t||(W(e.$$.fragment,l),t=!0)},o(l){Q(e.$$.fragment,l),t=!1},d(l){re(e,l)}}}function El(s){let e,t;return e=new $e({props:{$$slots:{default:[Il]},$$scope:{ctx:s}}}),{c(){ne(e.$$.fragment)},l(l){se(e.$$.fragment,l)},m(l,n){ie(e,l,n),t=!0},p(l,n){const i={};n[0]&287|n[1]&8&&(i.$$scope={dirty:n,ctx:l}),e.$set(i)},i(l){t||(W(e.$$.fragment,l),t=!0)},o(l){Q(e.$$.fragment,l),t=!1},d(l){re(e,l)}}}function $l(s){let e,t;return e=new $e({props:{$$slots:{default:[Sl]},$$scope:{ctx:s}}}),{c(){ne(e.$$.fragment)},l(l){se(e.$$.fragment,l)},m(l,n){ie(e,l,n),t=!0},p(l,n){const i={};n[0]&159|n[1]&8&&(i.$$scope={dirty:n,ctx:l}),e.$set(i)},i(l){t||(W(e.$$.fragment,l),t=!0)},o(l){Q(e.$$.fragment,l),t=!1},d(l){re(e,l)}}}function lt(s){let e;return{c(){e=d(",")},l(t){e=m(t,",")},m(t,l){v(t,e,l)},d(t){t&&f(e)}}}function nt(s){let e,t=ke(s[21],s[20].type,"bash")+"",l,n,i=s[26]<s[3].length-1&&lt();return{c(){e=d(`
							`),l=d(t),i&&i.c(),n=fe()},l(r){e=m(r,`
							`),l=m(r,t),i&&i.l(r),n=fe()},m(r,u){v(r,e,u),v(r,l,u),i&&i.m(r,u),v(r,n,u)},p(r,u){u[0]&8&&t!==(t=ke(r[21],r[20].type,"bash")+"")&&Z(l,t),r[26]<r[3].length-1?i||(i=lt(),i.c(),i.m(n.parentNode,n)):i&&(i.d(1),i=null)},d(r){r&&(f(e),f(l),f(n)),i&&i.d(r)}}}function Pl(s){var ue;let e,t,l,n,i,r,u,a,c,o,_=s[0].api_name+"",h,k,b="{",T,E,A,g="}",P,$,N="{",y,I,S="}",R,U,G,H,V,j=s[0].api_name+"",F,B,L;l=new ye({props:{code:(ue=s[9])==null?void 0:ue.innerText}});let q=ce(s[3]),z=[];for(let X=0;X<q.length;X+=1)z[X]=nt(Ke(s,q,X));return{c(){e=C("code"),t=C("div"),ne(l.$$.fragment),n=J(),i=C("div"),r=C("pre"),u=d("curl -X POST "),a=d(s[10]),c=d(s[11]),o=d("/call/"),h=d(_),k=d(` -s -H "Content-Type: application/json" -d '`),T=d(b),E=d(`
  "data": [`);for(let X=0;X<z.length;X+=1)z[X].c();A=d(`
]`),P=d(g),$=d(`' \\
  | awk -F'"' '`),y=d(N),I=d(" print $4"),R=d(S),U=d(`'  \\
  | read EVENT_ID; curl -N `),G=d(s[10]),H=d(s[11]),V=d("/call/"),F=d(j),B=d("/$EVENT_ID"),this.h()},l(X){e=w(X,"CODE",{class:!0});var oe=O(e);t=w(oe,"DIV",{class:!0});var le=O(t);se(l.$$.fragment,le),le.forEach(f),n=Y(oe),i=w(oe,"DIV",{});var _e=O(i);r=w(_e,"PRE",{class:!0});var K=O(r);u=m(K,"curl -X POST "),a=m(K,s[10]),c=m(K,s[11]),o=m(K,"/call/"),h=m(K,_),k=m(K,` -s -H "Content-Type: application/json" -d '`),T=m(K,b),E=m(K,`
  "data": [`);for(let ae=0;ae<z.length;ae+=1)z[ae].l(K);A=m(K,`
]`),P=m(K,g),$=m(K,`' \\
  | awk -F'"' '`),y=m(K,N),I=m(K," print $4"),R=m(K,S),U=m(K,`'  \\
  | read EVENT_ID; curl -N `),G=m(K,s[10]),H=m(K,s[11]),V=m(K,"/call/"),F=m(K,j),B=m(K,"/$EVENT_ID"),K.forEach(f),_e.forEach(f),oe.forEach(f),this.h()},h(){D(t,"class","copy svelte-114qcyq"),D(r,"class","svelte-114qcyq"),D(e,"class","svelte-114qcyq")},m(X,oe){v(X,e,oe),p(e,t),ie(l,t,null),p(e,n),p(e,i),p(i,r),p(r,u),p(r,a),p(r,c),p(r,o),p(r,h),p(r,k),p(r,T),p(r,E);for(let le=0;le<z.length;le+=1)z[le]&&z[le].m(r,null);p(r,A),p(r,P),p(r,$),p(r,y),p(r,I),p(r,R),p(r,U),p(r,G),p(r,H),p(r,V),p(r,F),p(r,B),s[18](i),L=!0},p(X,oe){var _e;const le={};if(oe[0]&512&&(le.code=(_e=X[9])==null?void 0:_e.innerText),l.$set(le),(!L||oe[0]&1024)&&Z(a,X[10]),(!L||oe[0]&2048)&&Z(c,X[11]),(!L||oe[0]&1)&&_!==(_=X[0].api_name+"")&&Z(h,_),oe[0]&8){q=ce(X[3]);let K;for(K=0;K<q.length;K+=1){const ae=Ke(X,q,K);z[K]?z[K].p(ae,oe):(z[K]=nt(ae),z[K].c(),z[K].m(r,A))}for(;K<z.length;K+=1)z[K].d(1);z.length=q.length}(!L||oe[0]&1024)&&Z(G,X[10]),(!L||oe[0]&2048)&&Z(H,X[11]),(!L||oe[0]&1)&&j!==(j=X[0].api_name+"")&&Z(F,j)},i(X){L||(W(l.$$.fragment,X),L=!0)},o(X){Q(l.$$.fragment,X),L=!1},d(X){X&&f(e),re(l),ge(z,X),s[18](null)}}}function st(s){let e,t,l,n=s[21].url+"",i,r,u=s[29]+"",a,c,o,_;return{c(){e=d(`
const response_`),t=d(s[26]),l=d(' = await fetch("'),i=d(n),r=d(`");
const example`),a=d(u),c=d(" = await response_"),o=d(s[26]),_=d(`.blob();
						`)},l(h){e=m(h,`
const response_`),t=m(h,s[26]),l=m(h,' = await fetch("'),i=m(h,n),r=m(h,`");
const example`),a=m(h,u),c=m(h," = await response_"),o=m(h,s[26]),_=m(h,`.blob();
						`)},m(h,k){v(h,e,k),v(h,t,k),v(h,l,k),v(h,i,k),v(h,r,k),v(h,a,k),v(h,c,k),v(h,o,k),v(h,_,k)},p:pe,d(h){h&&(f(e),f(t),f(l),f(i),f(r),f(a),f(c),f(o),f(_))}}}function it(s){let e,t,l;return{c(){e=d(', {auth: ["'),t=d(s[4]),l=d('", **password**]}')},l(n){e=m(n,', {auth: ["'),t=m(n,s[4]),l=m(n,'", **password**]}')},m(n,i){v(n,e,i),v(n,t,i),v(n,l,i)},p(n,i){i[0]&16&&Z(t,n[4])},d(n){n&&(f(e),f(t),f(l))}}}function Nl(s){let e,t,l=s[22]+"",n,i,r=ke(s[21],s[20].type,"js")+"",u,a;return{c(){e=d(`		
		`),t=C("span"),n=d(l),i=d(": "),u=d(r),a=d(", "),this.h()},l(c){e=m(c,`		
		`),t=w(c,"SPAN",{class:!0});var o=O(t);n=m(o,l),i=m(o,": "),u=m(o,r),o.forEach(f),a=m(c,", "),this.h()},h(){D(t,"class","example-inputs")},m(c,o){v(c,e,o),v(c,t,o),p(t,n),p(t,i),p(t,u),v(c,a,o)},p(c,o){o[0]&8&&l!==(l=c[22]+"")&&Z(n,l),o[0]&8&&r!==(r=ke(c[21],c[20].type,"js")+"")&&Z(u,r)},d(c){c&&(f(e),f(t),f(a))}}}function Dl(s){let e,t,l=s[22]+"",n,i,r=s[29]+"",u,a,c,o="";return{c(){e=d(`
				`),t=C("span"),n=d(l),i=d(": example"),u=d(r),a=d(", "),c=C("span"),c.innerHTML=o,this.h()},l(_){e=m(_,`
				`),t=w(_,"SPAN",{class:!0});var h=O(t);n=m(h,l),i=m(h,": example"),u=m(h,r),h.forEach(f),a=m(_,", "),c=w(_,"SPAN",{class:!0,"data-svelte-h":!0}),te(c)!=="svelte-1hu8bzt"&&(c.innerHTML=o),this.h()},h(){D(t,"class","example-inputs"),D(c,"class","desc svelte-114qcyq")},m(_,h){v(_,e,h),v(_,t,h),p(t,n),p(t,i),p(t,u),v(_,a,h),v(_,c,h)},p(_,h){h[0]&8&&l!==(l=_[22]+"")&&Z(n,l),h[0]&8&&r!==(r=_[29]+"")&&Z(u,r)},d(_){_&&(f(e),f(t),f(a),f(c))}}}function rt(s){let e,t;function l(r,u){return u[0]&8&&(e=null),e==null&&(e=!!r[13].includes(r[29])),e?Dl:Nl}let n=l(s,[-1,-1]),i=n(s);return{c(){i.c(),t=fe()},l(r){i.l(r),t=fe()},m(r,u){i.m(r,u),v(r,t,u)},p(r,u){n===(n=l(r,u))&&i?i.p(r,u):(i.d(1),i=n(r),i&&(i.c(),i.m(t.parentNode,t)))},d(r){r&&f(t),i.d(r)}}}function Il(s){var H;let e,t,l,n,i,r,u,a,c,o,_=(s[2]||s[1])+"",h,k,b,T,E,A=s[0].api_name+"",g,P,$,N,y;l=new ye({props:{code:(H=s[8])==null?void 0:H.innerText}});let I=ce(s[14]),S=[];for(let V=0;V<I.length;V+=1)S[V]=st(et(s,I,V));let R=s[4]!==null&&it(s),U=ce(s[3]),G=[];for(let V=0;V<U.length;V+=1)G[V]=rt(xe(s,U,V));return{c(){e=C("code"),t=C("div"),ne(l.$$.fragment),n=J(),i=C("div"),r=C("pre"),u=d(`import { Client } from "@gradio/client";
`);for(let V=0;V<S.length;V+=1)S[V].c();a=d(`
const client = await Client.connect(`),c=C("span"),o=d('"'),h=d(_),k=d('"'),R&&R.c(),b=d(`);
const result = await client.predict(`),T=C("span"),E=d('"/'),g=d(A),P=d('"'),$=d(", { ");for(let V=0;V<G.length;V+=1)G[V].c();N=d(`
});

console.log(result.data);
`),this.h()},l(V){e=w(V,"CODE",{class:!0});var j=O(e);t=w(j,"DIV",{class:!0});var F=O(t);se(l.$$.fragment,F),F.forEach(f),n=Y(j),i=w(j,"DIV",{});var B=O(i);r=w(B,"PRE",{class:!0});var L=O(r);u=m(L,`import { Client } from "@gradio/client";
`);for(let ue=0;ue<S.length;ue+=1)S[ue].l(L);a=m(L,`
const client = await Client.connect(`),c=w(L,"SPAN",{class:!0});var q=O(c);o=m(q,'"'),h=m(q,_),k=m(q,'"'),q.forEach(f),R&&R.l(L),b=m(L,`);
const result = await client.predict(`),T=w(L,"SPAN",{class:!0});var z=O(T);E=m(z,'"/'),g=m(z,A),P=m(z,'"'),z.forEach(f),$=m(L,", { ");for(let ue=0;ue<G.length;ue+=1)G[ue].l(L);N=m(L,`
});

console.log(result.data);
`),L.forEach(f),B.forEach(f),j.forEach(f),this.h()},h(){D(t,"class","copy svelte-114qcyq"),D(c,"class","token string svelte-114qcyq"),D(T,"class","api-name svelte-114qcyq"),D(r,"class","svelte-114qcyq"),D(e,"class","svelte-114qcyq")},m(V,j){v(V,e,j),p(e,t),ie(l,t,null),p(e,n),p(e,i),p(i,r),p(r,u);for(let F=0;F<S.length;F+=1)S[F]&&S[F].m(r,null);p(r,a),p(r,c),p(c,o),p(c,h),p(c,k),R&&R.m(r,null),p(r,b),p(r,T),p(T,E),p(T,g),p(T,P),p(r,$);for(let F=0;F<G.length;F+=1)G[F]&&G[F].m(r,null);p(r,N),s[17](i),y=!0},p(V,j){var B;const F={};if(j[0]&256&&(F.code=(B=V[8])==null?void 0:B.innerText),l.$set(F),j[0]&16384){I=ce(V[14]);let L;for(L=0;L<I.length;L+=1){const q=et(V,I,L);S[L]?S[L].p(q,j):(S[L]=st(q),S[L].c(),S[L].m(r,a))}for(;L<S.length;L+=1)S[L].d(1);S.length=I.length}if((!y||j[0]&6)&&_!==(_=(V[2]||V[1])+"")&&Z(h,_),V[4]!==null?R?R.p(V,j):(R=it(V),R.c(),R.m(r,b)):R&&(R.d(1),R=null),(!y||j[0]&1)&&A!==(A=V[0].api_name+"")&&Z(g,A),j[0]&8200){U=ce(V[3]);let L;for(L=0;L<U.length;L+=1){const q=xe(V,U,L);G[L]?G[L].p(q,j):(G[L]=rt(q),G[L].c(),G[L].m(r,N))}for(;L<G.length;L+=1)G[L].d(1);G.length=U.length}},i(V){y||(W(l.$$.fragment,V),y=!0)},o(V){Q(l.$$.fragment,V),y=!1},d(V){V&&f(e),re(l),ge(S,V),R&&R.d(),ge(G,V),s[17](null)}}}function Al(s){let e;return{c(){e=d(", handle_file")},l(t){e=m(t,", handle_file")},m(t,l){v(t,e,l)},d(t){t&&f(e)}}}function at(s){let e,t,l;return{c(){e=d(', auth=("'),t=d(s[4]),l=d('", **password**)')},l(n){e=m(n,', auth=("'),t=m(n,s[4]),l=m(n,'", **password**)')},m(n,i){v(n,e,i),v(n,t,i),v(n,l,i)},p(n,i){i[0]&16&&Z(t,n[4])},d(n){n&&(f(e),f(t),f(l))}}}function ot(s){let e,t=s[22]?s[22]+"=":"",l,n,i=ke(s[23]?s[24]:s[21],s[20].type,"py")+"",r,u;return{c(){e=d(`
		`),l=d(t),n=C("span"),r=d(i),u=d(",")},l(a){e=m(a,`
		`),l=m(a,t),n=w(a,"SPAN",{});var c=O(n);r=m(c,i),c.forEach(f),u=m(a,",")},m(a,c){v(a,e,c),v(a,l,c),v(a,n,c),p(n,r),v(a,u,c)},p(a,c){c[0]&8&&t!==(t=a[22]?a[22]+"=":"")&&Z(l,t),c[0]&8&&i!==(i=ke(a[23]?a[24]:a[21],a[20].type,"py")+"")&&Z(r,i)},d(a){a&&(f(e),f(l),f(n),f(u))}}}function Sl(s){var oe;let e,t,l,n,i,r,u,a="from",c,o,_="import",h,k,b,T,E=(s[2]||s[1])+"",A,g,P,$,N="predict",y,I,S,R,U=s[0].api_name+"",G,H,V,j,F="print",B,L;l=new ye({props:{code:(oe=s[7])==null?void 0:oe.innerText}});let q=s[12]&&Al(),z=s[4]!==null&&at(s),ue=ce(s[3]),X=[];for(let le=0;le<ue.length;le+=1)X[le]=ot(tt(s,ue,le));return{c(){e=C("code"),t=C("div"),ne(l.$$.fragment),n=J(),i=C("div"),r=C("pre"),u=C("span"),u.textContent=a,c=d(" gradio_client "),o=C("span"),o.textContent=_,h=d(" Client"),q&&q.c(),k=d(`

client = Client(`),b=C("span"),T=d('"'),A=d(E),g=d('"'),z&&z.c(),P=d(`)
result = client.`),$=C("span"),$.textContent=N,y=d("(");for(let le=0;le<X.length;le+=1)X[le].c();I=d(`
		api_name=`),S=C("span"),R=d('"/'),G=d(U),H=d('"'),V=d(`
)
`),j=C("span"),j.textContent=F,B=d("(result)"),this.h()},l(le){e=w(le,"CODE",{class:!0});var _e=O(e);t=w(_e,"DIV",{class:!0});var K=O(t);se(l.$$.fragment,K),K.forEach(f),n=Y(_e),i=w(_e,"DIV",{});var ae=O(i);r=w(ae,"PRE",{class:!0});var ee=O(r);u=w(ee,"SPAN",{class:!0,"data-svelte-h":!0}),te(u)!=="svelte-18n0cfl"&&(u.textContent=a),c=m(ee," gradio_client "),o=w(ee,"SPAN",{class:!0,"data-svelte-h":!0}),te(o)!=="svelte-18nlj6v"&&(o.textContent=_),h=m(ee," Client"),q&&q.l(ee),k=m(ee,`

client = Client(`),b=w(ee,"SPAN",{class:!0});var M=O(b);T=m(M,'"'),A=m(M,E),g=m(M,'"'),M.forEach(f),z&&z.l(ee),P=m(ee,`)
result = client.`),$=w(ee,"SPAN",{class:!0,"data-svelte-h":!0}),te($)!=="svelte-1qlwf3g"&&($.textContent=N),y=m(ee,"(");for(let de=0;de<X.length;de+=1)X[de].l(ee);I=m(ee,`
		api_name=`),S=w(ee,"SPAN",{class:!0});var x=O(S);R=m(x,'"/'),G=m(x,U),H=m(x,'"'),x.forEach(f),V=m(ee,`
)
`),j=w(ee,"SPAN",{class:!0,"data-svelte-h":!0}),te(j)!=="svelte-g689qk"&&(j.textContent=F),B=m(ee,"(result)"),ee.forEach(f),ae.forEach(f),_e.forEach(f),this.h()},h(){D(t,"class","copy svelte-114qcyq"),D(u,"class","highlight"),D(o,"class","highlight"),D(b,"class","token string svelte-114qcyq"),D($,"class","highlight"),D(S,"class","api-name svelte-114qcyq"),D(j,"class","highlight"),D(r,"class","svelte-114qcyq"),D(e,"class","svelte-114qcyq")},m(le,_e){v(le,e,_e),p(e,t),ie(l,t,null),p(e,n),p(e,i),p(i,r),p(r,u),p(r,c),p(r,o),p(r,h),q&&q.m(r,null),p(r,k),p(r,b),p(b,T),p(b,A),p(b,g),z&&z.m(r,null),p(r,P),p(r,$),p(r,y);for(let K=0;K<X.length;K+=1)X[K]&&X[K].m(r,null);p(r,I),p(r,S),p(S,R),p(S,G),p(S,H),p(r,V),p(r,j),p(r,B),s[16](i),L=!0},p(le,_e){var ae;const K={};if(_e[0]&128&&(K.code=(ae=le[7])==null?void 0:ae.innerText),l.$set(K),(!L||_e[0]&6)&&E!==(E=(le[2]||le[1])+"")&&Z(A,E),le[4]!==null?z?z.p(le,_e):(z=at(le),z.c(),z.m(r,P)):z&&(z.d(1),z=null),_e[0]&8){ue=ce(le[3]);let ee;for(ee=0;ee<ue.length;ee+=1){const M=tt(le,ue,ee);X[ee]?X[ee].p(M,_e):(X[ee]=ot(M),X[ee].c(),X[ee].m(r,I))}for(;ee<X.length;ee+=1)X[ee].d(1);X.length=ue.length}(!L||_e[0]&1)&&U!==(U=le[0].api_name+"")&&Z(G,U)},i(le){L||(W(l.$$.fragment,le),L=!0)},o(le){Q(l.$$.fragment,le),L=!1},d(le){le&&f(e),re(l),q&&q.d(),z&&z.d(),ge(X,le),s[16](null)}}}function Tl(s){let e,t,l,n,i,r;t=new Cl({props:{api_name:s[0].api_name,description:s[6]}});const u=[$l,El,wl],a=[];function c(o,_){return o[5]==="python"?0:o[5]==="javascript"?1:o[5]==="bash"?2:-1}return~(n=c(s))&&(i=a[n]=u[n](s)),{c(){e=C("div"),ne(t.$$.fragment),l=J(),i&&i.c(),this.h()},l(o){e=w(o,"DIV",{class:!0});var _=O(e);se(t.$$.fragment,_),l=Y(_),i&&i.l(_),_.forEach(f),this.h()},h(){D(e,"class","container svelte-114qcyq")},m(o,_){v(o,e,_),ie(t,e,null),p(e,l),~n&&a[n].m(e,null),r=!0},p(o,_){const h={};_[0]&1&&(h.api_name=o[0].api_name),_[0]&64&&(h.description=o[6]),t.$set(h);let k=n;n=c(o),n===k?~n&&a[n].p(o,_):(i&&(me(),Q(a[k],1,1,()=>{a[k]=null}),ve()),~n?(i=a[n],i?i.p(o,_):(i=a[n]=u[n](o),i.c()),W(i,1),i.m(e,null)):i=null)},i(o){r||(W(t.$$.fragment,o),W(i),r=!0)},o(o){Q(t.$$.fragment,o),Q(i),r=!1},d(o){o&&f(e),re(t),~n&&a[n].d()}}}function Vl(s,e,t){let l,n,{dependency:i}=e,{root:r}=e,{api_prefix:u}=e,{space_id:a}=e,{endpoint_parameters:c}=e,{username:o}=e,{current_language:_}=e,{api_description:h=null}=e,k,b,T,E=c.some(y=>Bt(y.example_input)),A=["Audio","File","Image","Video"],g=c.filter(y=>A.includes(y.component));function P(y){Ne[y?"unshift":"push"](()=>{k=y,t(7,k)})}function $(y){Ne[y?"unshift":"push"](()=>{b=y,t(8,b)})}function N(y){Ne[y?"unshift":"push"](()=>{T=y,t(9,T)})}return s.$$set=y=>{"dependency"in y&&t(0,i=y.dependency),"root"in y&&t(1,r=y.root),"api_prefix"in y&&t(15,u=y.api_prefix),"space_id"in y&&t(2,a=y.space_id),"endpoint_parameters"in y&&t(3,c=y.endpoint_parameters),"username"in y&&t(4,o=y.username),"current_language"in y&&t(5,_=y.current_language),"api_description"in y&&t(6,h=y.api_description)},s.$$.update=()=>{s.$$.dirty[0]&32768&&t(11,l=u||"/"),s.$$.dirty[0]&2&&t(10,n=r.replace(/\/$/,""))},[i,r,a,c,o,_,h,k,b,T,n,l,E,A,g,u,P,$,N]}class ql extends Ce{constructor(e){super(),we(this,e,Vl,Tl,Ee,{dependency:0,root:1,api_prefix:15,space_id:2,endpoint_parameters:3,username:4,current_language:5,api_description:6},null,[-1,-1])}}function ct(s,e,t){const l=s.slice();return l[20]=e[t].call,l[21]=e[t].api_name,l}function ft(s,e,t){const l=s.slice();return l[20]=e[t].call,l[21]=e[t].api_name,l}function ut(s,e,t){const l=s.slice();return l[20]=e[t].call,l[21]=e[t].api_name,l}function Ol(s){var c;let e,t,l,n,i,r;l=new ye({props:{code:(c=s[6])==null?void 0:c.innerText}});let u=ce(s[9]),a=[];for(let o=0;o<u.length;o+=1)a[o]=_t(ct(s,u,o));return{c(){e=C("code"),t=C("div"),ne(l.$$.fragment),n=J(),i=C("div");for(let o=0;o<a.length;o+=1)a[o].c();this.h()},l(o){e=w(o,"CODE",{class:!0});var _=O(e);t=w(_,"DIV",{class:!0});var h=O(t);se(l.$$.fragment,h),h.forEach(f),n=Y(_),i=w(_,"DIV",{});var k=O(i);for(let b=0;b<a.length;b+=1)a[b].l(k);k.forEach(f),_.forEach(f),this.h()},h(){D(t,"class","copy svelte-j71ub0"),D(e,"class","svelte-j71ub0")},m(o,_){v(o,e,_),p(e,t),ie(l,t,null),p(e,n),p(e,i);for(let h=0;h<a.length;h+=1)a[h]&&a[h].m(i,null);s[16](i),r=!0},p(o,_){var k;const h={};if(_&64&&(h.code=(k=o[6])==null?void 0:k.innerText),l.$set(h),_&513){u=ce(o[9]);let b;for(b=0;b<u.length;b+=1){const T=ct(o,u,b);a[b]?a[b].p(T,_):(a[b]=_t(T),a[b].c(),a[b].m(i,null))}for(;b<a.length;b+=1)a[b].d(1);a.length=u.length}},i(o){r||(W(l.$$.fragment,o),r=!0)},o(o){Q(l.$$.fragment,o),r=!1},d(o){o&&f(e),re(l),ge(a,o),s[16](null)}}}function Rl(s){var A;let e,t,l,n,i,r,u,a,c,o,_,h,k;l=new ye({props:{code:(A=s[5])==null?void 0:A.innerText}});let b=s[2]!==null&&pt(s),T=ce(s[8]),E=[];for(let g=0;g<T.length;g+=1)E[g]=dt(ft(s,T,g));return{c(){e=C("code"),t=C("div"),ne(l.$$.fragment),n=J(),i=C("div"),r=C("pre"),u=d(`import { Client } from "@gradio/client";

const app = await Client.connect(`),a=C("span"),c=d('"'),o=d(s[0]),_=d('"'),b&&b.c(),h=d(`);
					`);for(let g=0;g<E.length;g+=1)E[g].c();this.h()},l(g){e=w(g,"CODE",{class:!0});var P=O(e);t=w(P,"DIV",{class:!0});var $=O(t);se(l.$$.fragment,$),$.forEach(f),n=Y(P),i=w(P,"DIV",{});var N=O(i);r=w(N,"PRE",{class:!0});var y=O(r);u=m(y,`import { Client } from "@gradio/client";

const app = await Client.connect(`),a=w(y,"SPAN",{class:!0});var I=O(a);c=m(I,'"'),o=m(I,s[0]),_=m(I,'"'),I.forEach(f),b&&b.l(y),h=m(y,`);
					`);for(let S=0;S<E.length;S+=1)E[S].l(y);y.forEach(f),N.forEach(f),P.forEach(f),this.h()},h(){D(t,"class","copy svelte-j71ub0"),D(a,"class","token string svelte-j71ub0"),D(r,"class","svelte-j71ub0"),D(e,"class","svelte-j71ub0")},m(g,P){v(g,e,P),p(e,t),ie(l,t,null),p(e,n),p(e,i),p(i,r),p(r,u),p(r,a),p(a,c),p(a,o),p(a,_),b&&b.m(r,null),p(r,h);for(let $=0;$<E.length;$+=1)E[$]&&E[$].m(r,null);s[15](i),k=!0},p(g,P){var N;const $={};if(P&32&&($.code=(N=g[5])==null?void 0:N.innerText),l.$set($),(!k||P&1)&&Z(o,g[0]),g[2]!==null?b?b.p(g,P):(b=pt(g),b.c(),b.m(r,h)):b&&(b.d(1),b=null),P&256){T=ce(g[8]);let y;for(y=0;y<T.length;y+=1){const I=ft(g,T,y);E[y]?E[y].p(I,P):(E[y]=dt(I),E[y].c(),E[y].m(r,null))}for(;y<E.length;y+=1)E[y].d(1);E.length=T.length}},i(g){k||(W(l.$$.fragment,g),k=!0)},o(g){Q(l.$$.fragment,g),k=!1},d(g){g&&f(e),re(l),b&&b.d(),ge(E,g),s[15](null)}}}function jl(s){let e,t,l,n,i,r,u,a="from",c,o,_="import",h,k,b,T,E,A,g;l=new ye({props:{code:s[4]}});let P=s[2]!==null&&mt(s),$=ce(s[7]),N=[];for(let y=0;y<$.length;y+=1)N[y]=vt(ut(s,$,y));return{c(){e=C("code"),t=C("div"),ne(l.$$.fragment),n=J(),i=C("div"),r=C("pre"),u=C("span"),u.textContent=a,c=d(" gradio_client "),o=C("span"),o.textContent=_,h=d(` Client, file

client = Client(`),k=C("span"),b=d('"'),T=d(s[0]),E=d('"'),P&&P.c(),A=d(`)
`);for(let y=0;y<N.length;y+=1)N[y].c();this.h()},l(y){e=w(y,"CODE",{class:!0});var I=O(e);t=w(I,"DIV",{class:!0});var S=O(t);se(l.$$.fragment,S),S.forEach(f),n=Y(I),i=w(I,"DIV",{});var R=O(i);r=w(R,"PRE",{class:!0});var U=O(r);u=w(U,"SPAN",{class:!0,"data-svelte-h":!0}),te(u)!=="svelte-18n0cfl"&&(u.textContent=a),c=m(U," gradio_client "),o=w(U,"SPAN",{class:!0,"data-svelte-h":!0}),te(o)!=="svelte-18nlj6v"&&(o.textContent=_),h=m(U,` Client, file

client = Client(`),k=w(U,"SPAN",{class:!0});var G=O(k);b=m(G,'"'),T=m(G,s[0]),E=m(G,'"'),G.forEach(f),P&&P.l(U),A=m(U,`)
`);for(let H=0;H<N.length;H+=1)N[H].l(U);U.forEach(f),R.forEach(f),I.forEach(f),this.h()},h(){D(t,"class","copy svelte-j71ub0"),D(u,"class","highlight"),D(o,"class","highlight"),D(k,"class","token string svelte-j71ub0"),D(r,"class","svelte-j71ub0"),D(e,"class","svelte-j71ub0")},m(y,I){v(y,e,I),p(e,t),ie(l,t,null),p(e,n),p(e,i),p(i,r),p(r,u),p(r,c),p(r,o),p(r,h),p(r,k),p(k,b),p(k,T),p(k,E),P&&P.m(r,null),p(r,A);for(let S=0;S<N.length;S+=1)N[S]&&N[S].m(r,null);s[14](i),g=!0},p(y,I){const S={};if(I&16&&(S.code=y[4]),l.$set(S),(!g||I&1)&&Z(T,y[0]),y[2]!==null?P?P.p(y,I):(P=mt(y),P.c(),P.m(r,A)):P&&(P.d(1),P=null),I&128){$=ce(y[7]);let R;for(R=0;R<$.length;R+=1){const U=ut(y,$,R);N[R]?N[R].p(U,I):(N[R]=vt(U),N[R].c(),N[R].m(r,null))}for(;R<N.length;R+=1)N[R].d(1);N.length=$.length}},i(y){g||(W(l.$$.fragment,y),g=!0)},o(y){Q(l.$$.fragment,y),g=!1},d(y){y&&f(e),re(l),P&&P.d(),ge(N,y),s[14](null)}}}function _t(s){let e,t,l,n,i=s[21]+"",r,u,a="{",c,o,_=s[20]+"",h,k,b="}",T,E,A="{",g,P,$="}",N,y,I,S,R=s[21]+"",U,G,H,V;return{c(){e=C("pre"),t=d("curl -X POST "),l=d(s[0]),n=d("call/"),r=d(i),u=d(` -s -H "Content-Type: application/json" -d '`),c=d(a),o=d(` 
	"data": [`),h=d(_),k=d("]"),T=d(b),E=d(`' \\
  | awk -F'"' '`),g=d(A),P=d(" print $4"),N=d($),y=d(`' \\
  | read EVENT_ID; curl -N `),I=d(s[0]),S=d("call/"),U=d(R),G=d("/$EVENT_ID"),H=J(),V=C("br"),this.h()},l(j){e=w(j,"PRE",{class:!0});var F=O(e);t=m(F,"curl -X POST "),l=m(F,s[0]),n=m(F,"call/"),r=m(F,i),u=m(F,` -s -H "Content-Type: application/json" -d '`),c=m(F,a),o=m(F,` 
	"data": [`),h=m(F,_),k=m(F,"]"),T=m(F,b),E=m(F,`' \\
  | awk -F'"' '`),g=m(F,A),P=m(F," print $4"),N=m(F,$),y=m(F,`' \\
  | read EVENT_ID; curl -N `),I=m(F,s[0]),S=m(F,"call/"),U=m(F,R),G=m(F,"/$EVENT_ID"),F.forEach(f),H=Y(j),V=w(j,"BR",{}),this.h()},h(){D(e,"class","svelte-j71ub0")},m(j,F){v(j,e,F),p(e,t),p(e,l),p(e,n),p(e,r),p(e,u),p(e,c),p(e,o),p(e,h),p(e,k),p(e,T),p(e,E),p(e,g),p(e,P),p(e,N),p(e,y),p(e,I),p(e,S),p(e,U),p(e,G),v(j,H,F),v(j,V,F)},p(j,F){F&1&&Z(l,j[0]),F&512&&i!==(i=j[21]+"")&&Z(r,i),F&512&&_!==(_=j[20]+"")&&Z(h,_),F&1&&Z(I,j[0]),F&512&&R!==(R=j[21]+"")&&Z(U,R)},d(j){j&&(f(e),f(H),f(V))}}}function pt(s){let e,t,l;return{c(){e=d(', {auth: ["'),t=d(s[2]),l=d('", **password**]}')},l(n){e=m(n,', {auth: ["'),t=m(n,s[2]),l=m(n,'", **password**]}')},m(n,i){v(n,e,i),v(n,t,i),v(n,l,i)},p(n,i){i&4&&Z(t,n[2])},d(n){n&&(f(e),f(t),f(l))}}}function ht(s){let e,t=s[20]+"",l;return{c(){e=d(", "),l=d(t)},l(n){e=m(n,", "),l=m(n,t)},m(n,i){v(n,e,i),v(n,l,i)},p(n,i){i&256&&t!==(t=n[20]+"")&&Z(l,t)},d(n){n&&(f(e),f(l))}}}function dt(s){let e,t,l,n=s[21]+"",i,r,u,a=s[20]&&ht(s);return{c(){e=d(`
await client.predict(`),t=C("span"),l=d(`
  "/`),i=d(n),r=d('"'),a&&a.c(),u=d(`);
						`),this.h()},l(c){e=m(c,`
await client.predict(`),t=w(c,"SPAN",{class:!0});var o=O(t);l=m(o,`
  "/`),i=m(o,n),r=m(o,'"'),o.forEach(f),a&&a.l(c),u=m(c,`);
						`),this.h()},h(){D(t,"class","api-name svelte-j71ub0")},m(c,o){v(c,e,o),v(c,t,o),p(t,l),p(t,i),p(t,r),a&&a.m(c,o),v(c,u,o)},p(c,o){o&256&&n!==(n=c[21]+"")&&Z(i,n),c[20]?a?a.p(c,o):(a=ht(c),a.c(),a.m(u.parentNode,u)):a&&(a.d(1),a=null)},d(c){c&&(f(e),f(t),f(u)),a&&a.d(c)}}}function mt(s){let e,t,l;return{c(){e=d(', auth=("'),t=d(s[2]),l=d('", **password**)')},l(n){e=m(n,', auth=("'),t=m(n,s[2]),l=m(n,'", **password**)')},m(n,i){v(n,e,i),v(n,t,i),v(n,l,i)},p(n,i){i&4&&Z(t,n[2])},d(n){n&&(f(e),f(t),f(l))}}}function vt(s){let e,t,l,n=s[20]+"",i,r,u,a,c=s[21]+"",o,_,h;return{c(){e=d(`
client.`),t=C("span"),l=d(`predict(
`),i=d(n),r=d("  api_name="),u=C("span"),a=d('"/'),o=d(c),_=d('"'),h=d(`
)
`),this.h()},l(k){e=m(k,`
client.`),t=w(k,"SPAN",{class:!0});var b=O(t);l=m(b,`predict(
`),i=m(b,n),r=m(b,"  api_name="),u=w(b,"SPAN",{class:!0});var T=O(u);a=m(T,'"/'),o=m(T,c),_=m(T,'"'),T.forEach(f),h=m(b,`
)
`),b.forEach(f),this.h()},h(){D(u,"class","api-name svelte-j71ub0"),D(t,"class","highlight")},m(k,b){v(k,e,b),v(k,t,b),p(t,l),p(t,i),p(t,r),p(t,u),p(u,a),p(u,o),p(u,_),p(t,h)},p(k,b){b&128&&n!==(n=k[20]+"")&&Z(i,n),b&128&&c!==(c=k[21]+"")&&Z(o,c)},d(k){k&&(f(e),f(t))}}}function Ml(s){let e,t,l,n;const i=[jl,Rl,Ol],r=[];function u(a,c){return a[1]==="python"?0:a[1]==="javascript"?1:a[1]==="bash"?2:-1}return~(e=u(s))&&(t=r[e]=i[e](s)),{c(){t&&t.c(),l=fe()},l(a){t&&t.l(a),l=fe()},m(a,c){~e&&r[e].m(a,c),v(a,l,c),n=!0},p(a,c){let o=e;e=u(a),e===o?~e&&r[e].p(a,c):(t&&(me(),Q(r[o],1,1,()=>{r[o]=null}),ve()),~e?(t=r[e],t?t.p(a,c):(t=r[e]=i[e](a),t.c()),W(t,1),t.m(l.parentNode,l)):t=null)},i(a){n||(W(t),n=!0)},o(a){Q(t),n=!1},d(a){a&&f(l),~e&&r[e].d(a)}}}function Ll(s){let e,t,l;return t=new $e({props:{border_mode:"focus",$$slots:{default:[Ml]},$$scope:{ctx:s}}}),{c(){e=C("div"),ne(t.$$.fragment),this.h()},l(n){e=w(n,"DIV",{class:!0});var i=O(e);se(t.$$.fragment,i),i.forEach(f),this.h()},h(){D(e,"class","container svelte-j71ub0")},m(n,i){v(n,e,i),ie(t,e,null),l=!0},p(n,[i]){const r={};i&268436479&&(r.$$scope={dirty:i,ctx:n}),t.$set(r)},i(n){l||(W(t.$$.fragment,n),l=!0)},o(n){Q(t.$$.fragment,n),l=!1},d(n){n&&f(e),re(t)}}}function zl(s,e,t){let{dependencies:l}=e,{short_root:n}=e,{root:i}=e,{api_prefix:r=""}=e,{current_language:u}=e,{username:a}=e,c,o,_,h,{api_calls:k=[]}=e;async function b(){return await(await fetch(i.replace(/\/$/,"")+r+"/info/?all_endpoints=true")).json()}let T,E=[],A=[],g=[];function P(I,S){const R=`/${l[I.fn_index].api_name}`,G=I.data.filter(H=>typeof H<"u").map((H,V)=>{if(T[R]){const j=T[R].parameters[V];if(!j)return;const F=j.parameter_name,B=j.python_type.type;if(S==="py")return`  ${F}=${ke(H,B,"py")}`;if(S==="js")return`    ${F}: ${ke(H,B,"js")}`;if(S==="bash")return`    ${ke(H,B,"bash")}`}return`  ${ke(H,void 0,S)}`}).filter(H=>typeof H<"u").join(`,
`);if(G){if(S==="py")return`${G},
`;if(S==="js")return`{
${G},
}`;if(S==="bash")return`
${G}
`}return S==="py"?"":`
`}Ht(async()=>{T=(await b()).named_endpoints;let S=k.map(H=>P(H,"py")),R=k.map(H=>P(H,"js")),U=k.map(H=>P(H,"bash")),G=k.map(H=>l[H.fn_index].api_name||"");t(7,E=S.map((H,V)=>({call:H,api_name:G[V]}))),t(8,A=R.map((H,V)=>({call:H,api_name:G[V]}))),t(9,g=U.map((H,V)=>({call:H,api_name:G[V]}))),await Gt(),t(4,o=c.innerText)});function $(I){Ne[I?"unshift":"push"](()=>{c=I,t(3,c)})}function N(I){Ne[I?"unshift":"push"](()=>{_=I,t(5,_)})}function y(I){Ne[I?"unshift":"push"](()=>{h=I,t(6,h)})}return s.$$set=I=>{"dependencies"in I&&t(10,l=I.dependencies),"short_root"in I&&t(0,n=I.short_root),"root"in I&&t(11,i=I.root),"api_prefix"in I&&t(12,r=I.api_prefix),"current_language"in I&&t(1,u=I.current_language),"username"in I&&t(2,a=I.username),"api_calls"in I&&t(13,k=I.api_calls)},[n,u,a,c,o,_,h,E,A,g,l,i,r,k,$,N,y]}class Hl extends Ce{constructor(e){super(),we(this,e,zl,Ll,Ee,{dependencies:10,short_root:0,root:11,api_prefix:12,current_language:1,username:2,api_calls:13})}}const Ul="data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20aria-hidden='true'%20focusable='false'%20role='img'%20width='1em'%20height='1em'%20preserveAspectRatio='xMidYMid%20meet'%20viewBox='0%200%2032%2032'%20%3e%3cpath%20d='M15.84.5a16.4,16.4,0,0,0-3.57.32C9.1,1.39,8.53,2.53,8.53,4.64V7.48H16v1H5.77a4.73,4.73,0,0,0-4.7,3.74,14.82,14.82,0,0,0,0,7.54c.57,2.28,1.86,3.82,4,3.82h2.6V20.14a4.73,4.73,0,0,1,4.63-4.63h7.38a3.72,3.72,0,0,0,3.73-3.73V4.64A4.16,4.16,0,0,0,19.65.82,20.49,20.49,0,0,0,15.84.5ZM11.78,2.77a1.39,1.39,0,0,1,1.38,1.46,1.37,1.37,0,0,1-1.38,1.38A1.42,1.42,0,0,1,10.4,4.23,1.44,1.44,0,0,1,11.78,2.77Z'%20fill='%235a9fd4'%20%3e%3c/path%3e%3cpath%20d='M16.16,31.5a16.4,16.4,0,0,0,3.57-.32c3.17-.57,3.74-1.71,3.74-3.82V24.52H16v-1H26.23a4.73,4.73,0,0,0,4.7-3.74,14.82,14.82,0,0,0,0-7.54c-.57-2.28-1.86-3.82-4-3.82h-2.6v3.41a4.73,4.73,0,0,1-4.63,4.63H12.35a3.72,3.72,0,0,0-3.73,3.73v7.14a4.16,4.16,0,0,0,3.73,3.82A20.49,20.49,0,0,0,16.16,31.5Zm4.06-2.27a1.39,1.39,0,0,1-1.38-1.46,1.37,1.37,0,0,1,1.38-1.38,1.42,1.42,0,0,1,1.38,1.38A1.44,1.44,0,0,1,20.22,29.23Z'%20fill='%23ffd43b'%20%3e%3c/path%3e%3c/svg%3e",Fl="data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20aria-hidden='true'%20focusable='false'%20role='img'%20width='1em'%20height='1em'%20preserveAspectRatio='xMidYMid%20meet'%20viewBox='0%200%2032%2032'%20%3e%3crect%20width='32'%20height='32'%20fill='%23f7df1e'%3e%3c/rect%3e%3cpath%20d='M21.5,25a3.27,3.27,0,0,0,3,1.83c1.25,0,2-.63,2-1.49,0-1-.81-1.39-2.19-2L23.56,23C21.39,22.1,20,20.94,20,18.49c0-2.25,1.72-4,4.41-4a4.44,4.44,0,0,1,4.27,2.41l-2.34,1.5a2,2,0,0,0-1.93-1.29,1.31,1.31,0,0,0-1.44,1.29c0,.9.56,1.27,1.85,1.83l.75.32c2.55,1.1,4,2.21,4,4.72,0,2.71-2.12,4.19-5,4.19a5.78,5.78,0,0,1-5.48-3.07Zm-10.63.26c.48.84.91,1.55,1.94,1.55s1.61-.39,1.61-1.89V14.69h3V25c0,3.11-1.83,4.53-4.49,4.53a4.66,4.66,0,0,1-4.51-2.75Z'%20%3e%3c/path%3e%3c/svg%3e",Bl="data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20version='1.1'%20id='Layer_1'%20x='0px'%20y='0px'%20viewBox='0%200%20150%20150'%20style='enable-background:new%200%200%20150%20150;%20background-color:%20%2372a824;'%20xml:space='preserve'%3e%3cscript%20xmlns=''/%3e%3cstyle%20type='text/css'%3e%20.st0{fill:%23FFFFFF;}%20%3c/style%3e%3cg%3e%3cpath%20class='st0'%20d='M118.9,40.3L81.7,18.2c-2.2-1.3-4.7-2-7.2-2s-5,0.7-7.2,2L30.1,40.3c-4.4,2.6-7.2,7.5-7.2,12.8v44.2%20c0,5.3,2.7,10.1,7.2,12.8l37.2,22.1c2.2,1.3,4.7,2,7.2,2c2.5,0,5-0.7,7.2-2l37.2-22.1c4.4-2.6,7.2-7.5,7.2-12.8V53%20C126.1,47.8,123.4,42.9,118.9,40.3z%20M90.1,109.3l0.1,3.2c0,0.4-0.2,0.8-0.5,1l-1.9,1.1c-0.3,0.2-0.5,0-0.6-0.4l0-3.1%20c-1.6,0.7-3.2,0.8-4.3,0.4c-0.2-0.1-0.3-0.4-0.2-0.7l0.7-2.9c0.1-0.2,0.2-0.5,0.3-0.6c0.1-0.1,0.1-0.1,0.2-0.1%20c0.1-0.1,0.2-0.1,0.3,0c1.1,0.4,2.6,0.2,3.9-0.5c1.8-0.9,2.9-2.7,2.9-4.5c0-1.6-0.9-2.3-3-2.3c-2.7,0-5.2-0.5-5.3-4.5%20c0-3.3,1.7-6.7,4.4-8.8l0-3.2c0-0.4,0.2-0.8,0.5-1l1.8-1.2c0.3-0.2,0.5,0,0.6,0.4l0,3.2c1.3-0.5,2.5-0.7,3.6-0.4%20c0.2,0.1,0.3,0.4,0.2,0.7l-0.7,2.8c-0.1,0.2-0.2,0.4-0.3,0.6c-0.1,0.1-0.1,0.1-0.2,0.1c-0.1,0-0.2,0.1-0.3,0%20c-0.5-0.1-1.6-0.4-3.4,0.6c-1.9,1-2.6,2.6-2.5,3.8c0,1.5,0.8,1.9,3.3,1.9c3.4,0.1,4.9,1.6,5,5C94.7,103.4,92.9,107,90.1,109.3z%20M109.6,103.9c0,0.3,0,0.6-0.3,0.7l-9.4,5.7c-0.2,0.1-0.4,0-0.4-0.3v-2.4c0-0.3,0.2-0.5,0.4-0.6l9.3-5.5c0.2-0.1,0.4,0,0.4,0.3%20V103.9z%20M116.1,49.6L80.9,71.3c-4.4,2.6-7.6,5.4-7.6,10.7v43.4c0,3.2,1.3,5.2,3.2,5.8c-0.6,0.1-1.3,0.2-2,0.2%20c-2.1,0-4.1-0.6-5.9-1.6l-37.2-22.1c-3.6-2.2-5.9-6.2-5.9-10.5V53c0-4.3,2.3-8.4,5.9-10.5l37.2-22.1c1.8-1.1,3.8-1.6,5.9-1.6%20s4.1,0.6,5.9,1.6l37.2,22.1c3.1,1.8,5.1,5,5.7,8.5C122.1,48.4,119.3,47.7,116.1,49.6z'/%3e%3c/g%3e%3c/svg%3e";function gt(s,e,t){const l=s.slice();return l[4]=e[t].label,l[5]=e[t].type,l[6]=e[t].python_type,l[7]=e[t].component,l[8]=e[t].serializer,l[10]=t,l}function Gl(s){let e;return{c(){e=d("1 element")},l(t){e=m(t,"1 element")},m(t,l){v(t,e,l)},p:pe,d(t){t&&f(e)}}}function Jl(s){let e=s[3]=="python"?"tuple":"list",t,l,n=s[1].length+"",i,r;return{c(){t=d(e),l=d(" of "),i=d(n),r=d(`
		elements`)},l(u){t=m(u,e),l=m(u," of "),i=m(u,n),r=m(u,`
		elements`)},m(u,a){v(u,t,a),v(u,l,a),v(u,i,a),v(u,r,a)},p(u,a){a&8&&e!==(e=u[3]=="python"?"tuple":"list")&&Z(t,e),a&2&&n!==(n=u[1].length+"")&&Z(i,n)},d(u){u&&(f(t),f(l),f(i),f(r))}}}function bt(s){let e,t,l,n;return{c(){e=C("span"),t=d("["),l=d(s[10]),n=d("]"),this.h()},l(i){e=w(i,"SPAN",{class:!0});var r=O(e);t=m(r,"["),l=m(r,s[10]),n=m(r,"]"),r.forEach(f),this.h()},h(){D(e,"class","code svelte-16h224k")},m(i,r){v(i,e,r),p(e,t),p(e,l),p(e,n)},d(i){i&&f(e)}}}function Yl(s){let e=s[2][s[10]].type+"",t;return{c(){t=d(e)},l(l){t=m(l,e)},m(l,n){v(l,t,n)},p(l,n){n&4&&e!==(e=l[2][l[10]].type+"")&&Z(t,e)},d(l){l&&f(t)}}}function Wl(s){let e=s[6].type+"",t;return{c(){t=d(e)},l(l){t=m(l,e)},m(l,n){v(l,t,n)},p(l,n){n&2&&e!==(e=l[6].type+"")&&Z(t,e)},d(l){l&&f(t)}}}function kt(s){let e,t,l,n,i,r,u,a,c,o=s[4]+"",_,h,k=s[7]+"",b,T,E,A=s[1].length>1&&bt(s);function g(N,y){return N[3]==="python"?Wl:Yl}let P=g(s),$=P(s);return{c(){e=C("hr"),t=J(),l=C("div"),n=C("p"),A&&A.c(),i=J(),r=C("span"),$.c(),u=J(),a=C("p"),c=d('The output value that appears in the "'),_=d(o),h=d('" '),b=d(k),T=d(`
				component.`),E=J(),this.h()},l(N){e=w(N,"HR",{class:!0}),t=Y(N),l=w(N,"DIV",{style:!0});var y=O(l);n=w(y,"P",{});var I=O(n);A&&A.l(I),i=Y(I),r=w(I,"SPAN",{class:!0});var S=O(r);$.l(S),S.forEach(f),I.forEach(f),u=Y(y),a=w(y,"P",{class:!0});var R=O(a);c=m(R,'The output value that appears in the "'),_=m(R,o),h=m(R,'" '),b=m(R,k),T=m(R,`
				component.`),R.forEach(f),E=Y(y),y.forEach(f),this.h()},h(){D(e,"class","hr svelte-16h224k"),D(r,"class","code highlight svelte-16h224k"),D(a,"class","desc svelte-16h224k"),he(l,"margin","10px")},m(N,y){v(N,e,y),v(N,t,y),v(N,l,y),p(l,n),A&&A.m(n,null),p(n,i),p(n,r),$.m(r,null),p(l,u),p(l,a),p(a,c),p(a,_),p(a,h),p(a,b),p(a,T),p(l,E)},p(N,y){N[1].length>1?A||(A=bt(N),A.c(),A.m(n,i)):A&&(A.d(1),A=null),P===(P=g(N))&&$?$.p(N,y):($.d(1),$=P(N),$&&($.c(),$.m(r,null))),y&2&&o!==(o=N[4]+"")&&Z(_,o),y&2&&k!==(k=N[7]+"")&&Z(b,k)},d(N){N&&(f(e),f(t),f(l)),A&&A.d(),$.d()}}}function yt(s){let e,t,l;return t=new Ft({props:{margin:!1}}),{c(){e=C("div"),ne(t.$$.fragment),this.h()},l(n){e=w(n,"DIV",{class:!0});var i=O(e);se(t.$$.fragment,i),i.forEach(f),this.h()},h(){D(e,"class","load-wrap")},m(n,i){v(n,e,i),ie(t,e,null),l=!0},i(n){l||(W(t.$$.fragment,n),l=!0)},o(n){Q(t.$$.fragment,n),l=!1},d(n){n&&f(e),re(t)}}}function Zl(s){let e,t,l='<div class="toggle-dot toggle-right svelte-16h224k"></div>',n,i,r,u,a,c;function o(E,A){return E[1].length>1?Jl:Gl}let _=o(s),h=_(s),k=ce(s[1]),b=[];for(let E=0;E<k.length;E+=1)b[E]=kt(gt(s,k,E));let T=s[0]&&yt();return{c(){e=C("h4"),t=C("div"),t.innerHTML=l,n=d(`
	Returns `),h.c(),i=J(),r=C("div");for(let E=0;E<b.length;E+=1)b[E].c();u=J(),T&&T.c(),a=fe(),this.h()},l(E){e=w(E,"H4",{class:!0});var A=O(e);t=w(A,"DIV",{class:!0,"data-svelte-h":!0}),te(t)!=="svelte-1q6qbuq"&&(t.innerHTML=l),n=m(A,`
	Returns `),h.l(A),A.forEach(f),i=Y(E),r=w(E,"DIV",{});var g=O(r);for(let P=0;P<b.length;P+=1)b[P].l(g);g.forEach(f),u=Y(E),T&&T.l(E),a=fe(),this.h()},h(){D(t,"class","toggle-icon svelte-16h224k"),D(e,"class","svelte-16h224k"),Ae(r,"hide",s[0])},m(E,A){v(E,e,A),p(e,t),p(e,n),h.m(e,null),v(E,i,A),v(E,r,A);for(let g=0;g<b.length;g+=1)b[g]&&b[g].m(r,null);v(E,u,A),T&&T.m(E,A),v(E,a,A),c=!0},p(E,[A]){if(_===(_=o(E))&&h?h.p(E,A):(h.d(1),h=_(E),h&&(h.c(),h.m(e,null))),A&14){k=ce(E[1]);let g;for(g=0;g<k.length;g+=1){const P=gt(E,k,g);b[g]?b[g].p(P,A):(b[g]=kt(P),b[g].c(),b[g].m(r,null))}for(;g<b.length;g+=1)b[g].d(1);b.length=k.length}(!c||A&1)&&Ae(r,"hide",E[0]),E[0]?T?A&1&&W(T,1):(T=yt(),T.c(),W(T,1),T.m(a.parentNode,a)):T&&(me(),Q(T,1,1,()=>{T=null}),ve())},i(E){c||(W(T),c=!0)},o(E){Q(T),c=!1},d(E){E&&(f(e),f(i),f(r),f(u),f(a)),h.d(),ge(b,E),T&&T.d(E)}}}function Ql(s,e,t){let{is_running:l}=e,{endpoint_returns:n}=e,{js_returns:i}=e,{current_language:r}=e;return s.$$set=u=>{"is_running"in u&&t(0,l=u.is_running),"endpoint_returns"in u&&t(1,n=u.endpoint_returns),"js_returns"in u&&t(2,i=u.js_returns),"current_language"in u&&t(3,r=u.current_language)},[l,n,i,r]}class Xl extends Ce{constructor(e){super(),we(this,e,Ql,Zl,Ee,{is_running:0,endpoint_returns:1,js_returns:2,current_language:3})}}const Kl=""+new URL("../assets/mcp.DNm9doVd.svg",import.meta.url).href;function Ct(s,e,t){const l=s.slice();return l[29]=e[t],l}function wt(s,e,t){const l=s.slice();return l[32]=e[t],l[33]=e,l[34]=t,l}function Et(s,e,t){const l=s.slice();return l[35]=e[t][0],l[36]=e[t][1],l}function $t(s,e,t){const l=s.slice();return l[39]=e[t][0],l[40]=e[t][1],l[41]=e[t][2],l}function Pt(s){let e,t,l,n;const i=[en,xl],r=[];function u(a,c){return a[14]?0:1}return e=u(s),t=r[e]=i[e](s),{c(){t.c(),l=fe()},l(a){t.l(a),l=fe()},m(a,c){r[e].m(a,c),v(a,l,c),n=!0},p(a,c){t.p(a,c)},i(a){n||(W(t),n=!0)},o(a){Q(t),n=!1},d(a){a&&f(l),r[e].d(a)}}}function xl(s){let e,t;return e=new Zt({props:{root:s[0]}}),e.$on("close",s[23]),{c(){ne(e.$$.fragment)},l(l){se(e.$$.fragment,l)},m(l,n){ie(e,l,n),t=!0},p(l,n){const i={};n[0]&1&&(i.root=l[0]),e.$set(i)},i(l){t||(W(e.$$.fragment,l),t=!0)},o(l){Q(e.$$.fragment,l),t=!1},d(l){re(e,l)}}}function en(s){let e,t,l,n,i,r='<p style="font-size: var(--text-lg);">Choose one of the following ways to interact with the API.</p>',u,a,c,o,_,h,k,b;t=new tl({props:{root:s[3]||s[0],api_count:s[14],current_language:s[6]}}),t.$on("close",s[19]);let T=ce(s[15]),E=[];for(let N=0;N<T.length;N+=1)E[N]=Nt($t(s,T,N));const A=[ln,tn],g=[];function P(N,y){return N[5].length?0:1}_=P(s),h=g[_]=A[_](s);let $=s[6]!=="mcp"&&Rt(s);return{c(){e=C("div"),ne(t.$$.fragment),l=J(),n=C("div"),i=C("div"),i.innerHTML=r,u=J(),a=C("div"),c=C("div");for(let N=0;N<E.length;N+=1)E[N].c();o=J(),h.c(),k=J(),$&&$.c(),this.h()},l(N){e=w(N,"DIV",{class:!0});var y=O(e);se(t.$$.fragment,y),y.forEach(f),l=Y(N),n=w(N,"DIV",{class:!0});var I=O(n);i=w(I,"DIV",{class:!0,"data-svelte-h":!0}),te(i)!=="svelte-1xla08l"&&(i.innerHTML=r),u=Y(I),a=w(I,"DIV",{class:!0});var S=O(a);c=w(S,"DIV",{class:!0});var R=O(c);for(let U=0;U<E.length;U+=1)E[U].l(R);R.forEach(f),o=Y(S),h.l(S),k=Y(S),$&&$.l(S),S.forEach(f),I.forEach(f),this.h()},h(){D(e,"class","banner-wrap svelte-mko7us"),D(i,"class","client-doc svelte-mko7us"),D(c,"class","snippets svelte-mko7us"),D(a,"class","endpoint svelte-mko7us"),D(n,"class","docs-wrap svelte-mko7us")},m(N,y){v(N,e,y),ie(t,e,null),v(N,l,y),v(N,n,y),p(n,i),p(n,u),p(n,a),p(a,c);for(let I=0;I<E.length;I+=1)E[I]&&E[I].m(c,null);p(a,o),g[_].m(a,null),p(a,k),$&&$.m(a,null),b=!0},p(N,y){const I={};if(y[0]&9&&(I.root=N[3]||N[0]),y[0]&64&&(I.current_language=N[6]),t.$set(I),y[0]&32832){T=ce(N[15]);let R;for(R=0;R<T.length;R+=1){const U=$t(N,T,R);E[R]?E[R].p(U,y):(E[R]=Nt(U),E[R].c(),E[R].m(c,null))}for(;R<E.length;R+=1)E[R].d(1);E.length=T.length}let S=_;_=P(N),_===S?g[_].p(N,y):(me(),Q(g[S],1,1,()=>{g[S]=null}),ve(),h=g[_],h?h.p(N,y):(h=g[_]=A[_](N),h.c()),W(h,1),h.m(a,k)),N[6]!=="mcp"?$?($.p(N,y),y[0]&64&&W($,1)):($=Rt(N),$.c(),W($,1),$.m(a,null)):$&&(me(),Q($,1,1,()=>{$=null}),ve())},i(N){b||(W(t.$$.fragment,N),W(h),W($),b=!0)},o(N){Q(t.$$.fragment,N),Q(h),Q($),b=!1},d(N){N&&(f(e),f(l),f(n)),re(t),ge(E,N),g[_].d(),$&&$.d()}}}function Nt(s){let e,t,l,n,i=s[40]+"",r,u,a,c,o;function _(){return s[20](s[39])}return{c(){e=C("li"),t=C("img"),n=J(),r=d(i),u=J(),this.h()},l(h){e=w(h,"LI",{class:!0});var k=O(e);t=w(k,"IMG",{src:!0,alt:!0,class:!0}),n=Y(k),r=m(k,i),u=Y(k),k.forEach(f),this.h()},h(){zt(t.src,l=s[41])||D(t,"src",l),D(t,"alt",""),D(t,"class","svelte-mko7us"),D(e,"class",a="snippet "+(s[6]===s[39]?"current-lang":"inactive-lang")+" svelte-mko7us")},m(h,k){v(h,e,k),p(e,t),p(e,n),p(e,r),p(e,u),c||(o=Ve(e,"click",_),c=!0)},p(h,k){s=h,k[0]&64&&a!==(a="snippet "+(s[6]===s[39]?"current-lang":"inactive-lang")+" svelte-mko7us")&&D(e,"class",a)},d(h){h&&f(e),c=!1,o()}}}function tn(s){let e,t,l,n,i,r;const u=[rn,sn,nn],a=[];function c(_,h){return _[6]=="python"||_[6]=="javascript"?0:_[6]=="mcp"?1:2}t=c(s),l=a[t]=u[t](s);let o=s[6]!=="mcp"&&Tt(s);return{c(){e=C("p"),l.c(),n=J(),o&&o.c(),i=fe(),this.h()},l(_){e=w(_,"P",{class:!0});var h=O(e);l.l(h),h.forEach(f),n=Y(_),o&&o.l(_),i=fe(),this.h()},h(){D(e,"class","padded svelte-mko7us")},m(_,h){v(_,e,h),a[t].m(e,null),v(_,n,h),o&&o.m(_,h),v(_,i,h),r=!0},p(_,h){let k=t;t=c(_),t===k?a[t].p(_,h):(me(),Q(a[k],1,1,()=>{a[k]=null}),ve(),l=a[t],l?l.p(_,h):(l=a[t]=u[t](_),l.c()),W(l,1),l.m(e,null)),_[6]!=="mcp"?o?(o.p(_,h),h[0]&64&&W(o,1)):(o=Tt(_),o.c(),W(o,1),o.m(i.parentNode,i)):o&&(me(),Q(o,1,1,()=>{o=null}),ve())},i(_){r||(W(l),W(o),r=!0)},o(_){Q(l),Q(o),r=!1},d(_){_&&(f(e),f(n),f(i)),a[t].d(),o&&o.d(_)}}}function ln(s){let e,t,l,n,i,r=s[5].length+"",u,a,c,o,_,h,k,b,T,E,A,g=`Note: Some API calls only affect the UI, so when using the
							clients, the desired result may be achieved with only a subset of
							the recorded calls.`,P,$,N="API Documentation",y;return T=new Hl({props:{current_language:s[6],api_calls:s[5],dependencies:s[1],root:s[0],api_prefix:s[2].api_prefix,short_root:s[3]||s[0],username:s[4]}}),{c(){e=C("div"),t=C("p"),l=d("🪄 Recorded API Calls "),n=C("span"),i=d("["),u=d(r),a=d("]"),c=J(),o=C("p"),_=d(`Here is the code snippet to replay the most recently recorded API
							calls using the `),h=d(s[6]),k=d(`
							client.`),b=J(),ne(T.$$.fragment),E=J(),A=C("p"),A.textContent=g,P=J(),$=C("p"),$.textContent=N,this.h()},l(I){e=w(I,"DIV",{});var S=O(e);t=w(S,"P",{id:!0,style:!0});var R=O(t);l=m(R,"🪄 Recorded API Calls "),n=w(R,"SPAN",{class:!0});var U=O(n);i=m(U,"["),u=m(U,r),a=m(U,"]"),U.forEach(f),R.forEach(f),c=Y(S),o=w(S,"P",{});var G=O(o);_=m(G,`Here is the code snippet to replay the most recently recorded API
							calls using the `),h=m(G,s[6]),k=m(G,`
							client.`),G.forEach(f),b=Y(S),se(T.$$.fragment,S),E=Y(S),A=w(S,"P",{"data-svelte-h":!0}),te(A)!=="svelte-1nzar32"&&(A.textContent=g),S.forEach(f),P=Y(I),$=w(I,"P",{style:!0,"data-svelte-h":!0}),te($)!=="svelte-oqhm8o"&&($.textContent=N),this.h()},h(){D(n,"class","api-count svelte-mko7us"),D(t,"id","num-recorded-api-calls"),he(t,"font-size","var(--text-lg)"),he(t,"font-weight","bold"),he(t,"margin","10px 0px"),he($,"font-size","var(--text-lg)"),he($,"font-weight","bold"),he($,"margin","30px 0px 10px")},m(I,S){v(I,e,S),p(e,t),p(t,l),p(t,n),p(n,i),p(n,u),p(n,a),p(e,c),p(e,o),p(o,_),p(o,h),p(o,k),p(e,b),ie(T,e,null),p(e,E),p(e,A),v(I,P,S),v(I,$,S),y=!0},p(I,S){(!y||S[0]&32)&&r!==(r=I[5].length+"")&&Z(u,r),(!y||S[0]&64)&&Z(h,I[6]);const R={};S[0]&64&&(R.current_language=I[6]),S[0]&32&&(R.api_calls=I[5]),S[0]&2&&(R.dependencies=I[1]),S[0]&1&&(R.root=I[0]),S[0]&4&&(R.api_prefix=I[2].api_prefix),S[0]&9&&(R.short_root=I[3]||I[0]),S[0]&16&&(R.username=I[4]),T.$set(R)},i(I){y||(W(T.$$.fragment,I),y=!0)},o(I){Q(T.$$.fragment,I),y=!1},d(I){I&&(f(e),f(P),f($)),re(T)}}}function nn(s){let e;return{c(){e=d("1. Confirm that you have cURL installed on your system.")},l(t){e=m(t,"1. Confirm that you have cURL installed on your system.")},m(t,l){v(t,e,l)},p:pe,i:pe,o:pe,d(t){t&&f(e)}}}function sn(s){let e,t,l,n;const i=[on,an],r=[];function u(a,c){return a[7]?0:1}return e=u(s),t=r[e]=i[e](s),{c(){t.c(),l=fe()},l(a){t.l(a),l=fe()},m(a,c){r[e].m(a,c),v(a,l,c),n=!0},p(a,c){let o=e;e=u(a),e===o?r[e].p(a,c):(me(),Q(r[o],1,1,()=>{r[o]=null}),ve(),t=r[e],t?t.p(a,c):(t=r[e]=i[e](a),t.c()),W(t,1),t.m(l.parentNode,l))},i(a){n||(W(t),n=!0)},o(a){Q(t),n=!1},d(a){a&&f(l),r[e].d(a)}}}function rn(s){let e,t,l,n,i,r,u,a;return{c(){e=d(`1. Install the
							`),t=C("span"),l=d(s[6]),n=d(`
							client (`),i=C("a"),r=d("docs"),a=d(") if you don't already have it installed."),this.h()},l(c){e=m(c,`1. Install the
							`),t=w(c,"SPAN",{style:!0});var o=O(t);l=m(o,s[6]),o.forEach(f),n=m(c,`
							client (`),i=w(c,"A",{href:!0,target:!0,class:!0});var _=O(i);r=m(_,"docs"),_.forEach(f),a=m(c,") if you don't already have it installed."),this.h()},h(){he(t,"text-transform","capitalize"),D(i,"href",u=s[6]=="python"?Te:Se),D(i,"target","_blank"),D(i,"class","svelte-mko7us")},m(c,o){v(c,e,o),v(c,t,o),p(t,l),v(c,n,o),v(c,i,o),p(i,r),v(c,a,o)},p(c,o){o[0]&64&&Z(l,c[6]),o[0]&64&&u!==(u=c[6]=="python"?Te:Se)&&D(i,"href",u)},i:pe,o:pe,d(c){c&&(f(e),f(t),f(n),f(i),f(a))}}}function an(s){let e,t,l=".launch(mcp_server=True)",n,i,r="GRADIO_MCP_SERVER",u,a,c='"True"',o;return{c(){e=d(`This Gradio app can also serve as an MCP server, with an MCP
								tool corresponding to each API endpoint. To enable this, launch
								this Gradio app with `),t=C("code"),t.textContent=l,n=d(` or
								set the `),i=C("code"),i.textContent=r,u=d(` env variable to
								`),a=C("code"),a.textContent=c,o=d("."),this.h()},l(_){e=m(_,`This Gradio app can also serve as an MCP server, with an MCP
								tool corresponding to each API endpoint. To enable this, launch
								this Gradio app with `),t=w(_,"CODE",{class:!0,"data-svelte-h":!0}),te(t)!=="svelte-yfuruz"&&(t.textContent=l),n=m(_,` or
								set the `),i=w(_,"CODE",{class:!0,"data-svelte-h":!0}),te(i)!=="svelte-8yedgr"&&(i.textContent=r),u=m(_,` env variable to
								`),a=w(_,"CODE",{class:!0,"data-svelte-h":!0}),te(a)!=="svelte-s5r5d4"&&(a.textContent=c),o=m(_,"."),this.h()},h(){D(t,"class","svelte-mko7us"),D(i,"class","svelte-mko7us"),D(a,"class","svelte-mko7us")},m(_,h){v(_,e,h),v(_,t,h),v(_,n,h),v(_,i,h),v(_,u,h),v(_,a,h),v(_,o,h)},p:pe,i:pe,o:pe,d(_){_&&(f(e),f(t),f(n),f(i),f(u),f(a),f(o))}}}function on(s){let e,t,l,n=" ",i,r,u="Available MCP Tools",a,c,o,_,h=" ",k,b,T="SSE Transport",E,A,g=" ",P,$,N,y,I,S="STDIO Transport",R,U,G="install Node.js",H,V,j=" ",F,B,L,q,z=" ",ue,X,oe,le,_e;e=new $e({props:{$$slots:{default:[cn]},$$scope:{ctx:s}}});let K=ce(s[10]),ae=[];for(let M=0;M<K.length;M+=1)ae[M]=At(wt(s,K,M));$=new $e({props:{$$slots:{default:[_n]},$$scope:{ctx:s}}});let ee=s[13]&&St();return B=new $e({props:{$$slots:{default:[pn]},$$scope:{ctx:s}}}),{c(){ne(e.$$.fragment),t=J(),l=C("p"),l.textContent=n,i=J(),r=C("strong"),r.textContent=u,a=J(),c=C("div");for(let M=0;M<ae.length;M+=1)ae[M].c();o=J(),_=C("p"),_.textContent=h,k=J(),b=C("strong"),b.textContent=T,E=d(`: To add this MCP to clients that
								support SSE (e.g. Cursor, Windsurf, Cline), simply add the
								following configuration to your MCP config.
								`),A=C("p"),A.textContent=g,P=J(),ne($.$$.fragment),N=J(),ee&&ee.c(),y=J(),I=C("strong"),I.textContent=S,R=d(`: For clients that only support
								stdio (e.g. Claude Desktop), first
								`),U=C("a"),U.textContent=G,H=d(`. Then, you can use the following command:
								`),V=C("p"),V.textContent=j,F=J(),ne(B.$$.fragment),L=J(),q=C("p"),q.textContent=z,ue=J(),X=C("p"),oe=C("a"),le=d("Read more about MCP in the Gradio docs"),this.h()},l(M){se(e.$$.fragment,M),t=Y(M),l=w(M,"P",{"data-svelte-h":!0}),te(l)!=="svelte-9hmwf2"&&(l.textContent=n),i=Y(M),r=w(M,"STRONG",{"data-svelte-h":!0}),te(r)!=="svelte-pyixz6"&&(r.textContent=u),a=Y(M),c=w(M,"DIV",{class:!0});var x=O(c);for(let Pe=0;Pe<ae.length;Pe+=1)ae[Pe].l(x);x.forEach(f),o=Y(M),_=w(M,"P",{"data-svelte-h":!0}),te(_)!=="svelte-9hmwf2"&&(_.textContent=h),k=Y(M),b=w(M,"STRONG",{"data-svelte-h":!0}),te(b)!=="svelte-1y7xdr6"&&(b.textContent=T),E=m(M,`: To add this MCP to clients that
								support SSE (e.g. Cursor, Windsurf, Cline), simply add the
								following configuration to your MCP config.
								`),A=w(M,"P",{"data-svelte-h":!0}),te(A)!=="svelte-9hmwf2"&&(A.textContent=g),P=Y(M),se($.$$.fragment,M),N=Y(M),ee&&ee.l(M),y=Y(M),I=w(M,"STRONG",{"data-svelte-h":!0}),te(I)!=="svelte-1eu1x9e"&&(I.textContent=S),R=m(M,`: For clients that only support
								stdio (e.g. Claude Desktop), first
								`),U=w(M,"A",{href:!0,target:!0,class:!0,"data-svelte-h":!0}),te(U)!=="svelte-vjq0xs"&&(U.textContent=G),H=m(M,`. Then, you can use the following command:
								`),V=w(M,"P",{"data-svelte-h":!0}),te(V)!=="svelte-9hmwf2"&&(V.textContent=j),F=Y(M),se(B.$$.fragment,M),L=Y(M),q=w(M,"P",{"data-svelte-h":!0}),te(q)!=="svelte-9hmwf2"&&(q.textContent=z),ue=Y(M),X=w(M,"P",{});var de=O(X);oe=w(de,"A",{href:!0,target:!0,class:!0});var De=O(oe);le=m(De,"Read more about MCP in the Gradio docs"),De.forEach(f),de.forEach(f),this.h()},h(){D(c,"class","mcp-tools svelte-mko7us"),D(U,"href","https://nodejs.org/en/download/"),D(U,"target","_blank"),D(U,"class","svelte-mko7us"),D(oe,"href",mn),D(oe,"target","_blank"),D(oe,"class","svelte-mko7us")},m(M,x){ie(e,M,x),v(M,t,x),v(M,l,x),v(M,i,x),v(M,r,x),v(M,a,x),v(M,c,x);for(let de=0;de<ae.length;de+=1)ae[de]&&ae[de].m(c,null);v(M,o,x),v(M,_,x),v(M,k,x),v(M,b,x),v(M,E,x),v(M,A,x),v(M,P,x),ie($,M,x),v(M,N,x),ee&&ee.m(M,x),v(M,y,x),v(M,I,x),v(M,R,x),v(M,U,x),v(M,H,x),v(M,V,x),v(M,F,x),ie(B,M,x),v(M,L,x),v(M,q,x),v(M,ue,x),v(M,X,x),p(X,oe),p(oe,le),_e=!0},p(M,x){const de={};if(x[1]&8192&&(de.$$scope={dirty:x,ctx:M}),e.$set(de),x[0]&1024){K=ce(M[10]);let be;for(be=0;be<K.length;be+=1){const Fe=wt(M,K,be);ae[be]?ae[be].p(Fe,x):(ae[be]=At(Fe),ae[be].c(),ae[be].m(c,null))}for(;be<ae.length;be+=1)ae[be].d(1);ae.length=K.length}const De={};x[0]&2048|x[1]&8192&&(De.$$scope={dirty:x,ctx:M}),$.$set(De),M[13]?ee||(ee=St(),ee.c(),ee.m(y.parentNode,y)):ee&&(ee.d(1),ee=null);const Pe={};x[0]&4096|x[1]&8192&&(Pe.$$scope={dirty:x,ctx:M}),B.$set(Pe)},i(M){_e||(W(e.$$.fragment,M),W($.$$.fragment,M),W(B.$$.fragment,M),_e=!0)},o(M){Q(e.$$.fragment,M),Q($.$$.fragment,M),Q(B.$$.fragment,M),_e=!1},d(M){M&&(f(t),f(l),f(i),f(r),f(a),f(c),f(o),f(_),f(k),f(b),f(E),f(A),f(P),f(N),f(y),f(I),f(R),f(U),f(H),f(V),f(F),f(L),f(q),f(ue),f(X)),re(e,M),ge(ae,M),re($,M),ee&&ee.d(M),re(B,M)}}}function cn(s){let e,t,l=`<span class="status-indicator active svelte-mko7us">●</span>MCP Server
											URL (SSE)`,n,i,r,u,a,c;return a=new ye({props:{code:s[17]}}),{c(){e=C("div"),t=C("label"),t.innerHTML=l,n=J(),i=C("div"),r=C("input"),u=J(),ne(a.$$.fragment),this.h()},l(o){e=w(o,"DIV",{class:!0});var _=O(e);t=w(_,"LABEL",{class:!0,"data-svelte-h":!0}),te(t)!=="svelte-pdz3ql"&&(t.innerHTML=l),n=Y(_),i=w(_,"DIV",{class:!0});var h=O(i);r=w(h,"INPUT",{type:!0,class:!0}),u=Y(h),se(a.$$.fragment,h),h.forEach(f),_.forEach(f),this.h()},h(){D(t,"class","svelte-mko7us"),D(r,"type","text"),r.readOnly=!0,r.value=s[17],D(r,"class","svelte-mko7us"),D(i,"class","textbox svelte-mko7us"),D(e,"class","mcp-url svelte-mko7us")},m(o,_){v(o,e,_),p(e,t),p(e,n),p(e,i),p(i,r),p(i,u),ie(a,i,null),c=!0},p:pe,i(o){c||(W(a.$$.fragment,o),c=!0)},o(o){Q(a.$$.fragment,o),c=!1},d(o){o&&f(e),re(a)}}}function Dt(s){let e,t;function l(r,u){return u[0]&1024&&(t=null),t==null&&(t=Object.keys(r[32].parameters).length>0),t?un:fn}let n=l(s,[-1,-1]),i=n(s);return{c(){e=C("div"),i.c(),this.h()},l(r){e=w(r,"DIV",{class:!0});var u=O(e);i.l(u),u.forEach(f),this.h()},h(){D(e,"class","tool-content svelte-mko7us")},m(r,u){v(r,e,u),i.m(e,null)},p(r,u){n===(n=l(r,u))&&i?i.p(r,u):(i.d(1),i=n(r),i&&(i.c(),i.m(e,null)))},d(r){r&&f(e),i.d()}}}function fn(s){let e,t="Takes no input parameters";return{c(){e=C("p"),e.textContent=t},l(l){e=w(l,"P",{"data-svelte-h":!0}),te(e)!=="svelte-wp9k8h"&&(e.textContent=t)},m(l,n){v(l,e,n)},p:pe,d(l){l&&f(e)}}}function un(s){let e,t=ce(Object.entries(s[32].parameters)),l=[];for(let n=0;n<t.length;n+=1)l[n]=It(Et(s,t,n));return{c(){e=C("div");for(let n=0;n<l.length;n+=1)l[n].c();this.h()},l(n){e=w(n,"DIV",{class:!0});var i=O(e);for(let r=0;r<l.length;r+=1)l[r].l(i);i.forEach(f),this.h()},h(){D(e,"class","tool-parameters")},m(n,i){v(n,e,i);for(let r=0;r<l.length;r+=1)l[r]&&l[r].m(e,null)},p(n,i){if(i[0]&1024){t=ce(Object.entries(n[32].parameters));let r;for(r=0;r<t.length;r+=1){const u=Et(n,t,r);l[r]?l[r].p(u,i):(l[r]=It(u),l[r].c(),l[r].m(e,null))}for(;r<l.length;r+=1)l[r].d(1);l.length=t.length}},d(n){n&&f(e),ge(l,n)}}}function It(s){let e,t,l=s[35]+"",n,i,r,u,a=s[36].type+"",c,o=s[36].default!==void 0?`, default: ${JSON.stringify(s[36].default)}`:"",_,h,k,b,T=(s[36].description?s[36].description:"⚠︎ No description for this parameter in function docstring")+"",E,A;return{c(){e=C("div"),t=C("code"),n=d(l),i=J(),r=C("span"),u=d("("),c=d(a),_=d(o),h=d(")"),k=J(),b=C("p"),E=d(T),A=J(),this.h()},l(g){e=w(g,"DIV",{class:!0});var P=O(e);t=w(P,"CODE",{class:!0});var $=O(t);n=m($,l),$.forEach(f),i=Y(P),r=w(P,"SPAN",{class:!0});var N=O(r);u=m(N,"("),c=m(N,a),_=m(N,o),h=m(N,")"),N.forEach(f),k=Y(P),b=w(P,"P",{class:!0});var y=O(b);E=m(y,T),y.forEach(f),A=Y(P),P.forEach(f),this.h()},h(){D(t,"class","svelte-mko7us"),D(r,"class","parameter-type svelte-mko7us"),D(b,"class","parameter-description svelte-mko7us"),D(e,"class","parameter svelte-mko7us")},m(g,P){v(g,e,P),p(e,t),p(t,n),p(e,i),p(e,r),p(r,u),p(r,c),p(r,_),p(r,h),p(e,k),p(e,b),p(b,E),p(e,A)},p(g,P){P[0]&1024&&l!==(l=g[35]+"")&&Z(n,l),P[0]&1024&&a!==(a=g[36].type+"")&&Z(c,a),P[0]&1024&&o!==(o=g[36].default!==void 0?`, default: ${JSON.stringify(g[36].default)}`:"")&&Z(_,o),P[0]&1024&&T!==(T=(g[36].description?g[36].description:"⚠︎ No description for this parameter in function docstring")+"")&&Z(E,T)},d(g){g&&f(e)}}}function At(s){let e,t,l,n,i=s[32].name+"",r,u,a,c=(s[32].description?s[32].description:"⚠︎ No description provided in function docstring")+"",o,_,h,k=s[32].expanded?"▼":"▶",b,T,E,A,g;function P(){return s[21](s[32],s[33],s[34])}let $=s[32].expanded&&Dt(s);return{c(){e=C("div"),t=C("button"),l=C("span"),n=C("span"),r=d(i),u=d(`  
													`),a=C("span"),o=d(c),_=J(),h=C("span"),b=d(k),T=J(),$&&$.c(),E=J(),this.h()},l(N){e=w(N,"DIV",{class:!0});var y=O(e);t=w(y,"BUTTON",{class:!0});var I=O(t);l=w(I,"SPAN",{});var S=O(l);n=w(S,"SPAN",{class:!0});var R=O(n);r=m(R,i),R.forEach(f),u=m(S,`  
													`),a=w(S,"SPAN",{class:!0});var U=O(a);o=m(U,c),U.forEach(f),S.forEach(f),_=Y(I),h=w(I,"SPAN",{class:!0});var G=O(h);b=m(G,k),G.forEach(f),I.forEach(f),T=Y(y),$&&$.l(y),E=Y(y),y.forEach(f),this.h()},h(){D(n,"class","tool-name svelte-mko7us"),D(a,"class","tool-description svelte-mko7us"),D(h,"class","tool-arrow svelte-mko7us"),D(t,"class","tool-header svelte-mko7us"),D(e,"class","tool-item svelte-mko7us")},m(N,y){v(N,e,y),p(e,t),p(t,l),p(l,n),p(n,r),p(l,u),p(l,a),p(a,o),p(t,_),p(t,h),p(h,b),p(e,T),$&&$.m(e,null),p(e,E),A||(g=Ve(t,"click",P),A=!0)},p(N,y){s=N,y[0]&1024&&i!==(i=s[32].name+"")&&Z(r,i),y[0]&1024&&c!==(c=(s[32].description?s[32].description:"⚠︎ No description provided in function docstring")+"")&&Z(o,c),y[0]&1024&&k!==(k=s[32].expanded?"▼":"▶")&&Z(b,k),s[32].expanded?$?$.p(s,y):($=Dt(s),$.c(),$.m(e,E)):$&&($.d(1),$=null)},d(N){N&&f(e),$&&$.d(),A=!1,g()}}}function _n(s){let e,t,l,n,i,r,u=JSON.stringify(s[11],null,2)+"",a,c;return l=new ye({props:{code:JSON.stringify(s[11],null,2)}}),{c(){e=C("code"),t=C("div"),ne(l.$$.fragment),n=J(),i=C("div"),r=C("pre"),a=d(u),this.h()},l(o){e=w(o,"CODE",{class:!0});var _=O(e);t=w(_,"DIV",{class:!0});var h=O(t);se(l.$$.fragment,h),h.forEach(f),n=Y(_),i=w(_,"DIV",{});var k=O(i);r=w(k,"PRE",{class:!0});var b=O(r);a=m(b,u),b.forEach(f),k.forEach(f),_.forEach(f),this.h()},h(){D(t,"class","copy svelte-mko7us"),D(r,"class","svelte-mko7us"),D(e,"class","svelte-mko7us")},m(o,_){v(o,e,_),p(e,t),ie(l,t,null),p(e,n),p(e,i),p(i,r),p(r,a),c=!0},p(o,_){const h={};_[0]&2048&&(h.code=JSON.stringify(o[11],null,2)),l.$set(h),(!c||_[0]&2048)&&u!==(u=JSON.stringify(o[11],null,2)+"")&&Z(a,u)},i(o){c||(W(l.$$.fragment,o),c=!0)},o(o){Q(l.$$.fragment,o),c=!1},d(o){o&&f(e),re(l)}}}function St(s){let e,t=" ",l,n,i="Note about files",r,u,a="upload_files_to_gradio",c,o,_="UPLOAD_DIRECTORY",h,k,b="uv installed",T,E,A=" ";return{c(){e=C("p"),e.textContent=t,l=J(),n=C("em"),n.textContent=i,r=d(`: Gradio MCP servers that have files
									as inputs need the files as URLs, so the
									`),u=C("code"),u.textContent=a,c=d(`
									tool is included for your convenience. This tool can upload files
									located in the specified `),o=C("code"),o.textContent=_,h=d(`
									argument (an absolute path in your local machine) or any of its
									subdirectories to the Gradio app. You can omit this tool if you
									are fine manually uploading files yourself and providing the URLs.
									Before using this tool, you must have
									`),k=C("a"),k.textContent=b,T=d(`.
									`),E=C("p"),E.textContent=A,this.h()},l(g){e=w(g,"P",{"data-svelte-h":!0}),te(e)!=="svelte-9hmwf2"&&(e.textContent=t),l=Y(g),n=w(g,"EM",{"data-svelte-h":!0}),te(n)!=="svelte-32fwgo"&&(n.textContent=i),r=m(g,`: Gradio MCP servers that have files
									as inputs need the files as URLs, so the
									`),u=w(g,"CODE",{class:!0,"data-svelte-h":!0}),te(u)!=="svelte-1rjf6zo"&&(u.textContent=a),c=m(g,`
									tool is included for your convenience. This tool can upload files
									located in the specified `),o=w(g,"CODE",{class:!0,"data-svelte-h":!0}),te(o)!=="svelte-i0eptd"&&(o.textContent=_),h=m(g,`
									argument (an absolute path in your local machine) or any of its
									subdirectories to the Gradio app. You can omit this tool if you
									are fine manually uploading files yourself and providing the URLs.
									Before using this tool, you must have
									`),k=w(g,"A",{href:!0,target:!0,class:!0,"data-svelte-h":!0}),te(k)!=="svelte-3wsmo0"&&(k.textContent=b),T=m(g,`.
									`),E=w(g,"P",{"data-svelte-h":!0}),te(E)!=="svelte-9hmwf2"&&(E.textContent=A),this.h()},h(){D(u,"class","svelte-mko7us"),D(o,"class","svelte-mko7us"),D(k,"href","https://docs.astral.sh/uv/getting-started/installation/"),D(k,"target","_blank"),D(k,"class","svelte-mko7us")},m(g,P){v(g,e,P),v(g,l,P),v(g,n,P),v(g,r,P),v(g,u,P),v(g,c,P),v(g,o,P),v(g,h,P),v(g,k,P),v(g,T,P),v(g,E,P)},d(g){g&&(f(e),f(l),f(n),f(r),f(u),f(c),f(o),f(h),f(k),f(T),f(E))}}}function pn(s){let e,t,l,n,i,r,u=JSON.stringify(s[12],null,2)+"",a,c;return l=new ye({props:{code:JSON.stringify(s[12],null,2)}}),{c(){e=C("code"),t=C("div"),ne(l.$$.fragment),n=J(),i=C("div"),r=C("pre"),a=d(u),this.h()},l(o){e=w(o,"CODE",{class:!0});var _=O(e);t=w(_,"DIV",{class:!0});var h=O(t);se(l.$$.fragment,h),h.forEach(f),n=Y(_),i=w(_,"DIV",{});var k=O(i);r=w(k,"PRE",{class:!0});var b=O(r);a=m(b,u),b.forEach(f),k.forEach(f),_.forEach(f),this.h()},h(){D(t,"class","copy svelte-mko7us"),D(r,"class","svelte-mko7us"),D(e,"class","svelte-mko7us")},m(o,_){v(o,e,_),p(e,t),ie(l,t,null),p(e,n),p(e,i),p(i,r),p(r,a),c=!0},p(o,_){const h={};_[0]&4096&&(h.code=JSON.stringify(o[12],null,2)),l.$set(h),(!c||_[0]&4096)&&u!==(u=JSON.stringify(o[12],null,2)+"")&&Z(a,u)},i(o){c||(W(l.$$.fragment,o),c=!0)},o(o){Q(l.$$.fragment,o),c=!1},d(o){o&&f(e),re(l)}}}function Tt(s){let e,t,l,n,i,r,u,a;e=new bl({props:{current_language:s[6]}});let c=s[3]&&Vt(s);r=new Ue({props:{size:"sm",variant:"secondary",$$slots:{default:[hn]},$$scope:{ctx:s}}}),r.$on("click",s[22]);let o=s[6]=="bash"&&qt(s);return{c(){ne(e.$$.fragment),t=J(),l=C("p"),n=d(`2. Find the API endpoint below corresponding to your desired
							function in the app. Copy the code snippet, replacing the
							placeholder values with your own input data.
							`),c&&c.c(),i=d(`

							Or use the
							`),ne(r.$$.fragment),u=d(`
							to automatically generate your API requests.
							`),o&&o.c(),this.h()},l(_){se(e.$$.fragment,_),t=Y(_),l=w(_,"P",{class:!0});var h=O(l);n=m(h,`2. Find the API endpoint below corresponding to your desired
							function in the app. Copy the code snippet, replacing the
							placeholder values with your own input data.
							`),c&&c.l(h),i=m(h,`

							Or use the
							`),se(r.$$.fragment,h),u=m(h,`
							to automatically generate your API requests.
							`),o&&o.l(h),h.forEach(f),this.h()},h(){D(l,"class","padded svelte-mko7us")},m(_,h){ie(e,_,h),v(_,t,h),v(_,l,h),p(l,n),c&&c.m(l,null),p(l,i),ie(r,l,null),p(l,u),o&&o.m(l,null),a=!0},p(_,h){const k={};h[0]&64&&(k.current_language=_[6]),e.$set(k),_[3]?c?c.p(_,h):(c=Vt(_),c.c(),c.m(l,i)):c&&(c.d(1),c=null);const b={};h[1]&8192&&(b.$$scope={dirty:h,ctx:_}),r.$set(b),_[6]=="bash"?o?o.p(_,h):(o=qt(_),o.c(),o.m(l,null)):o&&(o.d(1),o=null)},i(_){a||(W(e.$$.fragment,_),W(r.$$.fragment,_),a=!0)},o(_){Q(e.$$.fragment,_),Q(r.$$.fragment,_),a=!1},d(_){_&&(f(t),f(l)),re(e,_),c&&c.d(),re(r),o&&o.d()}}}function Vt(s){let e,t,l,n,i;return{c(){e=d(`If this is a private Space, you may need to pass
								your Hugging Face token as well (`),t=C("a"),l=d("read more"),i=d(")."),this.h()},l(r){e=m(r,`If this is a private Space, you may need to pass
								your Hugging Face token as well (`),t=w(r,"A",{href:!0,class:!0,target:!0});var u=O(t);l=m(u,"read more"),u.forEach(f),i=m(r,")."),this.h()},h(){D(t,"href",n=s[6]=="python"?Te+Ie:s[6]=="javascript"?Se+Ie:ze),D(t,"class","underline svelte-mko7us"),D(t,"target","_blank")},m(r,u){v(r,e,u),v(r,t,u),p(t,l),v(r,i,u)},p(r,u){u[0]&64&&n!==(n=r[6]=="python"?Te+Ie:r[6]=="javascript"?Se+Ie:ze)&&D(t,"href",n)},d(r){r&&(f(e),f(t),f(i))}}}function hn(s){let e,t,l,n="API Recorder";return{c(){e=C("div"),t=J(),l=C("p"),l.textContent=n,this.h()},l(i){e=w(i,"DIV",{class:!0}),O(e).forEach(f),t=Y(i),l=w(i,"P",{class:!0,"data-svelte-h":!0}),te(l)!=="svelte-1ycmyh9"&&(l.textContent=n),this.h()},h(){D(e,"class","loading-dot svelte-mko7us"),D(l,"class","self-baseline svelte-mko7us")},m(i,r){v(i,e,r),v(i,t,r),v(i,l,r)},p:pe,d(i){i&&(f(e),f(t),f(l))}}}function qt(s){let e,t,l,n,i,r="2 requests",u,a,c="POST",o,_,h="GET",k,b,T="POST",E,A,g="EVENT_ID",P,$,N="GET",y,I,S="awk",R,U,G="read",H,V,j,F,B,L=s[4]!==null&&Ot();return{c(){e=C("br"),t=d(" "),l=C("br"),n=d(`Making a
								prediction and getting a result requires
								`),i=C("strong"),i.textContent=r,u=d(`: a
								`),a=C("code"),a.textContent=c,o=d(`
								and a `),_=C("code"),_.textContent=h,k=d(" request. The "),b=C("code"),b.textContent=T,E=d(` request
								returns an `),A=C("code"),A.textContent=g,P=d(`, which is used in the second
								`),$=C("code"),$.textContent=N,y=d(` request to fetch the results. In these
								snippets, we've used `),I=C("code"),I.textContent=S,R=d(" and "),U=C("code"),U.textContent=G,H=d(` to
								parse the results, combining these two requests into one command
								for ease of use. `),L&&L.c(),V=d(` See
								`),j=C("a"),F=d("curl docs"),B=d("."),this.h()},l(q){e=w(q,"BR",{}),t=m(q," "),l=w(q,"BR",{}),n=m(q,`Making a
								prediction and getting a result requires
								`),i=w(q,"STRONG",{"data-svelte-h":!0}),te(i)!=="svelte-3tg330"&&(i.textContent=r),u=m(q,`: a
								`),a=w(q,"CODE",{class:!0,"data-svelte-h":!0}),te(a)!=="svelte-12krxoq"&&(a.textContent=c),o=m(q,`
								and a `),_=w(q,"CODE",{class:!0,"data-svelte-h":!0}),te(_)!=="svelte-1r51gq6"&&(_.textContent=h),k=m(q," request. The "),b=w(q,"CODE",{class:!0,"data-svelte-h":!0}),te(b)!=="svelte-12krxoq"&&(b.textContent=T),E=m(q,` request
								returns an `),A=w(q,"CODE",{class:!0,"data-svelte-h":!0}),te(A)!=="svelte-wz35l4"&&(A.textContent=g),P=m(q,`, which is used in the second
								`),$=w(q,"CODE",{class:!0,"data-svelte-h":!0}),te($)!=="svelte-1r51gq6"&&($.textContent=N),y=m(q,` request to fetch the results. In these
								snippets, we've used `),I=w(q,"CODE",{class:!0,"data-svelte-h":!0}),te(I)!=="svelte-qg98el"&&(I.textContent=S),R=m(q," and "),U=w(q,"CODE",{class:!0,"data-svelte-h":!0}),te(U)!=="svelte-wk48ls"&&(U.textContent=G),H=m(q,` to
								parse the results, combining these two requests into one command
								for ease of use. `),L&&L.l(q),V=m(q,` See
								`),j=w(q,"A",{href:!0,target:!0,class:!0});var z=O(j);F=m(z,"curl docs"),z.forEach(f),B=m(q,"."),this.h()},h(){D(a,"class","svelte-mko7us"),D(_,"class","svelte-mko7us"),D(b,"class","svelte-mko7us"),D(A,"class","svelte-mko7us"),D($,"class","svelte-mko7us"),D(I,"class","svelte-mko7us"),D(U,"class","svelte-mko7us"),D(j,"href",ze),D(j,"target","_blank"),D(j,"class","svelte-mko7us")},m(q,z){v(q,e,z),v(q,t,z),v(q,l,z),v(q,n,z),v(q,i,z),v(q,u,z),v(q,a,z),v(q,o,z),v(q,_,z),v(q,k,z),v(q,b,z),v(q,E,z),v(q,A,z),v(q,P,z),v(q,$,z),v(q,y,z),v(q,I,z),v(q,R,z),v(q,U,z),v(q,H,z),L&&L.m(q,z),v(q,V,z),v(q,j,z),p(j,F),v(q,B,z)},p(q,z){q[4]!==null?L||(L=Ot(),L.c(),L.m(V.parentNode,V)):L&&(L.d(1),L=null)},d(q){q&&(f(e),f(t),f(l),f(n),f(i),f(u),f(a),f(o),f(_),f(k),f(b),f(E),f(A),f(P),f($),f(y),f(I),f(R),f(U),f(H),f(V),f(j),f(B)),L&&L.d(q)}}}function Ot(s){let e;return{c(){e=d(`Note: connecting to an authenticated app requires an
									additional request.`)},l(t){e=m(t,`Note: connecting to an authenticated app requires an
									additional request.`)},m(t,l){v(t,e,l)},d(t){t&&f(e)}}}function Rt(s){let e,t,l=ce(s[1]),n=[];for(let r=0;r<l.length;r+=1)n[r]=Mt(Ct(s,l,r));const i=r=>Q(n[r],1,1,()=>{n[r]=null});return{c(){for(let r=0;r<n.length;r+=1)n[r].c();e=fe()},l(r){for(let u=0;u<n.length;u+=1)n[u].l(r);e=fe()},m(r,u){for(let a=0;a<n.length;a+=1)n[a]&&n[a].m(r,u);v(r,e,u),t=!0},p(r,u){if(u[0]&863){l=ce(r[1]);let a;for(a=0;a<l.length;a+=1){const c=Ct(r,l,a);n[a]?(n[a].p(c,u),W(n[a],1)):(n[a]=Mt(c),n[a].c(),W(n[a],1),n[a].m(e.parentNode,e))}for(me(),a=l.length;a<n.length;a+=1)i(a);ve()}},i(r){if(!t){for(let u=0;u<l.length;u+=1)W(n[u]);t=!0}},o(r){n=n.filter(Boolean);for(let u=0;u<n.length;u+=1)Q(n[u]);t=!1},d(r){r&&f(e),ge(n,r)}}}function jt(s){let e,t,l,n,i,r,u,a;return t=new ql({props:{endpoint_parameters:s[8].named_endpoints["/"+s[29].api_name].parameters,dependency:s[29],current_language:s[6],root:s[0],space_id:s[3],username:s[4],api_prefix:s[2].api_prefix,api_description:s[8].named_endpoints["/"+s[29].api_name].description}}),n=new cl({props:{endpoint_returns:s[8].named_endpoints["/"+s[29].api_name].parameters,js_returns:s[9].named_endpoints["/"+s[29].api_name].parameters,is_running:Lt,current_language:s[6]}}),r=new Xl({props:{endpoint_returns:s[8].named_endpoints["/"+s[29].api_name].returns,js_returns:s[9].named_endpoints["/"+s[29].api_name].returns,is_running:Lt,current_language:s[6]}}),{c(){e=C("div"),ne(t.$$.fragment),l=J(),ne(n.$$.fragment),i=J(),ne(r.$$.fragment),u=J(),this.h()},l(c){e=w(c,"DIV",{class:!0});var o=O(e);se(t.$$.fragment,o),l=Y(o),se(n.$$.fragment,o),i=Y(o),se(r.$$.fragment,o),u=Y(o),o.forEach(f),this.h()},h(){D(e,"class","endpoint-container svelte-mko7us")},m(c,o){v(c,e,o),ie(t,e,null),p(e,l),ie(n,e,null),p(e,i),ie(r,e,null),p(e,u),a=!0},p(c,o){const _={};o[0]&258&&(_.endpoint_parameters=c[8].named_endpoints["/"+c[29].api_name].parameters),o[0]&2&&(_.dependency=c[29]),o[0]&64&&(_.current_language=c[6]),o[0]&1&&(_.root=c[0]),o[0]&8&&(_.space_id=c[3]),o[0]&16&&(_.username=c[4]),o[0]&4&&(_.api_prefix=c[2].api_prefix),o[0]&258&&(_.api_description=c[8].named_endpoints["/"+c[29].api_name].description),t.$set(_);const h={};o[0]&258&&(h.endpoint_returns=c[8].named_endpoints["/"+c[29].api_name].parameters),o[0]&514&&(h.js_returns=c[9].named_endpoints["/"+c[29].api_name].parameters),o[0]&64&&(h.current_language=c[6]),n.$set(h);const k={};o[0]&258&&(k.endpoint_returns=c[8].named_endpoints["/"+c[29].api_name].returns),o[0]&514&&(k.js_returns=c[9].named_endpoints["/"+c[29].api_name].returns),o[0]&64&&(k.current_language=c[6]),r.$set(k)},i(c){a||(W(t.$$.fragment,c),W(n.$$.fragment,c),W(r.$$.fragment,c),a=!0)},o(c){Q(t.$$.fragment,c),Q(n.$$.fragment,c),Q(r.$$.fragment,c),a=!1},d(c){c&&f(e),re(t),re(n),re(r)}}}function Mt(s){let e,t,l=s[29].show_api&&s[8].named_endpoints["/"+s[29].api_name]&&jt(s);return{c(){l&&l.c(),e=fe()},l(n){l&&l.l(n),e=fe()},m(n,i){l&&l.m(n,i),v(n,e,i),t=!0},p(n,i){n[29].show_api&&n[8].named_endpoints["/"+n[29].api_name]?l?(l.p(n,i),i[0]&258&&W(l,1)):(l=jt(n),l.c(),W(l,1),l.m(e.parentNode,e)):l&&(me(),Q(l,1,1,()=>{l=null}),ve())},i(n){t||(W(l),t=!0)},o(n){Q(l),t=!1},d(n){n&&f(e),l&&l.d(n)}}}function dn(s){let e,t,l=s[8]&&Pt(s);return{c(){l&&l.c(),e=fe()},l(n){l&&l.l(n),e=fe()},m(n,i){l&&l.m(n,i),v(n,e,i),t=!0},p(n,i){n[8]?l?(l.p(n,i),i[0]&256&&W(l,1)):(l=Pt(n),l.c(),W(l,1),l.m(e.parentNode,e)):l&&(me(),Q(l,1,1,()=>{l=null}),ve())},i(n){t||(W(l),t=!0)},o(n){Q(l),t=!1},d(n){n&&f(e),l&&l.d(n)}}}const Se="https://www.gradio.app/guides/getting-started-with-the-js-client",Te="https://www.gradio.app/guides/getting-started-with-the-python-client",ze="https://www.gradio.app/guides/querying-gradio-apps-with-curl",Ie="#connecting-to-a-hugging-face-space",mn="https://www.gradio.app/guides/building-mcp-server-with-gradio";let Lt=!1;function vn(s,e){const t=new URL(window.location.href);t.searchParams.set(s,e),history.replaceState(null,"",t.toString())}function gn(s){return new URL(window.location.href).searchParams.get(s)}function je(s){return["python","javascript","bash","mcp"].includes(s??"")}function bn(s,e,t){let{dependencies:l}=e,{root:n}=e,{app:i}=e,{space_id:r}=e,{root_node:u}=e,{username:a}=e,c=l.filter(B=>B.show_api).length;n===""&&(n=location.protocol+"//"+location.host+location.pathname),n.endsWith("/")||(n+="/");let{api_calls:o=[]}=e,_="python";const h=[["python","Python",Ul],["javascript","JavaScript",Fl],["bash","cURL",Bl],["mcp","MCP",Kl]];let k=!1;async function b(){return await(await fetch(n.replace(/\/$/,"")+i.api_prefix+"/info")).json()}async function T(){return await i.view_api()}let E,A;b().then(B=>{t(8,E=B)}),T().then(B=>{t(9,A=B)});const g=He(),P=`${n}gradio_api/mcp/sse`;let $=[],N=[],y,I,S=!1;const R={command:"uvx",args:["--from","gradio[mcp]","gradio","upload-mcp",n,"<UPLOAD_DIRECTORY>"]};async function U(){try{const L=await(await fetch(`${n}gradio_api/mcp/schema`)).json();t(13,S=L.map(q=>{var z;return(z=q.meta)==null?void 0:z.file_data_present}).some(q=>q)),t(10,$=L.map(q=>{var z;return{name:q.name,description:q.description||"",parameters:((z=q.inputSchema)==null?void 0:z.properties)||{},expanded:!1}})),N=L.map(q=>{var z;return((z=q.meta)==null?void 0:z.headers)||[]}).flat(),N.length>0?(t(11,y={mcpServers:{gradio:{url:P,headers:N.reduce((q,z)=>(q[z]="<YOUR_HEADER_VALUE>",q),{})}}}),t(12,I={mcpServers:{gradio:{command:"npx",args:["mcp-remote",P,"--transport","sse-only",...N.map(q=>["--header",`${q}: <YOUR_HEADER_VALUE>`]).flat()]}}})):(t(11,y={mcpServers:{gradio:{url:P}}}),t(12,I={mcpServers:{gradio:{command:"npx",args:["mcp-remote",P,"--transport","sse-only"]}}}),S&&(t(11,y.mcpServers.upload_files_to_gradio=R,y),t(12,I.mcpServers.upload_files_to_gradio=R,I)))}catch(B){console.error("Failed to fetch MCP tools:",B),t(10,$=[])}}Ht(()=>{var L;document.body.style.overflow="hidden","parentIFrame"in window&&((L=window.parentIFrame)==null||L.scrollTo(0,0));const B=gn("lang");return je(B)&&t(6,_=B),fetch(P).then(q=>{t(7,k=q.ok),k?(U(),je(B)||t(6,_="mcp")):je(B)||t(6,_="python")}).catch(()=>{t(7,k=!1)}),()=>{document.body.style.overflow="auto"}});function G(B){Be.call(this,s,B)}const H=B=>{t(6,_=B),vn("lang",B)},V=(B,L,q)=>t(10,L[q].expanded=!B.expanded,$),j=()=>g("close",{api_recorder_visible:!0});function F(B){Be.call(this,s,B)}return s.$$set=B=>{"dependencies"in B&&t(1,l=B.dependencies),"root"in B&&t(0,n=B.root),"app"in B&&t(2,i=B.app),"space_id"in B&&t(3,r=B.space_id),"root_node"in B&&t(18,u=B.root_node),"username"in B&&t(4,a=B.username),"api_calls"in B&&t(5,o=B.api_calls)},[n,l,i,r,a,o,_,k,E,A,$,y,I,S,c,h,g,P,u,G,H,V,j,F]}class En extends Ce{constructor(e){super(),we(this,e,bn,dn,Ee,{dependencies:1,root:0,app:2,space_id:3,root_node:18,username:4,api_calls:5},null,[-1,-1])}}export{En as default};
//# sourceMappingURL=ApiDocs.BIM9Rgz9.js.map
