{"version": 3, "file": "rgbdEncode.fragment.B5LpBaEr.js", "sources": ["../../../../../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/ShadersWGSL/rgbdEncode.fragment.js"], "sourcesContent": ["// Do not edit.\nimport { ShaderStore } from \"../Engines/shaderStore.js\";\nimport \"./ShadersInclude/helperFunctions.js\";\nconst name = \"rgbdEncodePixelShader\";\nconst shader = `varying vUV: vec2f;var textureSamplerSampler: sampler;var textureSampler: texture_2d<f32>;\n#include<helperFunctions>\n#define CUSTOM_FRAGMENT_DEFINITIONS\n@fragment\nfn main(input: FragmentInputs)->FragmentOutputs {fragmentOutputs.color=toRGBD(textureSample(textureSampler,textureSamplerSampler,input.vUV).rgb);}`;\n// Sideeffect\nif (!ShaderStore.ShadersStoreWGSL[name]) {\n    ShaderStore.ShadersStoreWGSL[name] = shader;\n}\n/** @internal */\nexport const rgbdEncodePixelShaderWGSL = { name, shader };\n//# sourceMappingURL=rgbdEncode.fragment.js.map"], "names": ["name", "shader", "ShaderStore", "rgbdEncodePixelShaderWGSL"], "mappings": "8EAGA,MAAMA,EAAO,wBACPC,EAAS;AAAA;AAAA;AAAA;AAAA,oJAMVC,EAAY,iBAAiBF,CAAI,IAClCE,EAAY,iBAAiBF,CAAI,EAAIC,GAG7B,MAACE,EAA4B,CAAE,KAAAH,EAAM,OAAAC,CAAM", "x_google_ignoreList": [0]}