{"version": 3, "file": "Dropdown.BMX2NvmS.js", "sources": ["../../../../../../../dropdown/shared/DropdownOptions.svelte", "../../../../../../../dropdown/shared/utils.ts", "../../../../../../../dropdown/shared/Dropdown.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport { fly } from \"svelte/transition\";\n\timport { createEventDispatcher } from \"svelte\";\n\texport let choices: [string, string | number][];\n\texport let filtered_indices: number[];\n\texport let show_options = false;\n\texport let disabled = false;\n\texport let selected_indices: (string | number)[] = [];\n\texport let active_index: number | null = null;\n\texport let remember_scroll = false;\n\n\tlet distance_from_top: number;\n\tlet distance_from_bottom: number;\n\tlet input_height: number;\n\tlet input_width: number;\n\tlet refElement: HTMLDivElement;\n\tlet listElement: HTMLUListElement;\n\tlet top: string | null, bottom: string | null, max_height: number;\n\tlet innerHeight: number;\n\tlet list_scroll_y = 0;\n\n\tfunction calculate_window_distance(): void {\n\t\tconst { top: ref_top, bottom: ref_bottom } =\n\t\t\trefElement.getBoundingClientRect();\n\t\tdistance_from_top = ref_top;\n\t\tdistance_from_bottom = innerHeight - ref_bottom;\n\t}\n\n\tlet scroll_timeout: NodeJS.Timeout | null = null;\n\tfunction scroll_listener(): void {\n\t\tif (!show_options) return;\n\t\tif (scroll_timeout !== null) {\n\t\t\tclearTimeout(scroll_timeout);\n\t\t}\n\n\t\tscroll_timeout = setTimeout(() => {\n\t\t\tcalculate_window_distance();\n\t\t\tscroll_timeout = null;\n\t\t}, 10);\n\t}\n\n\tfunction restore_last_scroll(): void {\n\t\tlistElement?.scrollTo?.(0, list_scroll_y);\n\t}\n\n\t$: {\n\t\tif (show_options && refElement) {\n\t\t\tif (remember_scroll) {\n\t\t\t\trestore_last_scroll();\n\t\t\t} else {\n\t\t\t\tif (listElement && selected_indices.length > 0) {\n\t\t\t\t\tlet elements = listElement.querySelectorAll(\"li\");\n\t\t\t\t\tfor (const element of Array.from(elements)) {\n\t\t\t\t\t\tif (\n\t\t\t\t\t\t\telement.getAttribute(\"data-index\") ===\n\t\t\t\t\t\t\tselected_indices[0].toString()\n\t\t\t\t\t\t) {\n\t\t\t\t\t\t\tlistElement?.scrollTo?.(0, (element as HTMLLIElement).offsetTop);\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\tcalculate_window_distance();\n\t\t\tconst rect = refElement.parentElement?.getBoundingClientRect();\n\t\t\tinput_height = rect?.height || 0;\n\t\t\tinput_width = rect?.width || 0;\n\t\t}\n\t\tif (distance_from_bottom > distance_from_top) {\n\t\t\ttop = `${distance_from_top}px`;\n\t\t\tmax_height = distance_from_bottom;\n\t\t\tbottom = null;\n\t\t} else {\n\t\t\tbottom = `${distance_from_bottom + input_height}px`;\n\t\t\tmax_height = distance_from_top - input_height;\n\t\t\ttop = null;\n\t\t}\n\t}\n\n\tconst dispatch = createEventDispatcher();\n</script>\n\n<svelte:window on:scroll={scroll_listener} bind:innerHeight />\n\n<div class=\"reference\" bind:this={refElement} />\n{#if show_options && !disabled}\n\t<ul\n\t\tclass=\"options\"\n\t\ttransition:fly={{ duration: 200, y: 5 }}\n\t\ton:mousedown|preventDefault={(e) => dispatch(\"change\", e)}\n\t\ton:scroll={(e) => (list_scroll_y = e.currentTarget.scrollTop)}\n\t\tstyle:top\n\t\tstyle:bottom\n\t\tstyle:max-height={`calc(${max_height}px - var(--window-padding))`}\n\t\tstyle:width={input_width + \"px\"}\n\t\tbind:this={listElement}\n\t\trole=\"listbox\"\n\t>\n\t\t{#each filtered_indices as index}\n\t\t\t<li\n\t\t\t\tclass=\"item\"\n\t\t\t\tclass:selected={selected_indices.includes(index)}\n\t\t\t\tclass:active={index === active_index}\n\t\t\t\tclass:bg-gray-100={index === active_index}\n\t\t\t\tclass:dark:bg-gray-600={index === active_index}\n\t\t\t\tstyle:width={input_width + \"px\"}\n\t\t\t\tdata-index={index}\n\t\t\t\taria-label={choices[index][0]}\n\t\t\t\tdata-testid=\"dropdown-option\"\n\t\t\t\trole=\"option\"\n\t\t\t\taria-selected={selected_indices.includes(index)}\n\t\t\t>\n\t\t\t\t<span class:hide={!selected_indices.includes(index)} class=\"inner-item\">\n\t\t\t\t\t✓\n\t\t\t\t</span>\n\t\t\t\t{choices[index][0]}\n\t\t\t</li>\n\t\t{/each}\n\t</ul>\n{/if}\n\n<style>\n\t.options {\n\t\t--window-padding: var(--size-8);\n\t\tposition: fixed;\n\t\tz-index: var(--layer-top);\n\t\tmargin-left: 0;\n\t\tbox-shadow: var(--shadow-drop-lg);\n\t\tborder-radius: var(--container-radius);\n\t\tbackground: var(--background-fill-primary);\n\t\tmin-width: fit-content;\n\t\tmax-width: inherit;\n\t\toverflow: auto;\n\t\tcolor: var(--body-text-color);\n\t\tlist-style: none;\n\t}\n\n\t.item {\n\t\tdisplay: flex;\n\t\tcursor: pointer;\n\t\tpadding: var(--size-2);\n\t\tword-break: break-word;\n\t}\n\n\t.item:hover,\n\t.active {\n\t\tbackground: var(--background-fill-secondary);\n\t}\n\n\t.inner-item {\n\t\tpadding-right: var(--size-1);\n\t}\n\n\t.hide {\n\t\tvisibility: hidden;\n\t}\n</style>\n", "function positive_mod(n: number, m: number): number {\n\treturn ((n % m) + m) % m;\n}\n\nexport function handle_filter(\n\tchoices: [string, string | number][],\n\tinput_text: string\n): number[] {\n\treturn choices.reduce((filtered_indices, o, index) => {\n\t\tif (\n\t\t\tinput_text ? o[0].toLowerCase().includes(input_text.toLowerCase()) : true\n\t\t) {\n\t\t\tfiltered_indices.push(index);\n\t\t}\n\t\treturn filtered_indices;\n\t}, [] as number[]);\n}\n\nexport function handle_change(\n\tdispatch: any,\n\tvalue: string | number | (string | number)[] | undefined,\n\tvalue_is_output: boolean\n): void {\n\tdispatch(\"change\", value);\n\tif (!value_is_output) {\n\t\tdispatch(\"input\");\n\t}\n}\n\nexport function handle_shared_keys(\n\te: KeyboardEvent,\n\tactive_index: number | null,\n\tfiltered_indices: number[]\n): [boolean, number | null] {\n\tif (e.key === \"Escape\") {\n\t\treturn [false, active_index];\n\t}\n\tif (e.key === \"ArrowDown\" || e.key === \"ArrowUp\") {\n\t\tif (filtered_indices.length > 0) {\n\t\t\tif (active_index === null) {\n\t\t\t\tactive_index =\n\t\t\t\t\te.key === \"ArrowDown\"\n\t\t\t\t\t\t? filtered_indices[0]\n\t\t\t\t\t\t: filtered_indices[filtered_indices.length - 1];\n\t\t\t} else {\n\t\t\t\tconst index_in_filtered = filtered_indices.indexOf(active_index);\n\t\t\t\tconst increment = e.key === \"ArrowUp\" ? -1 : 1;\n\t\t\t\tactive_index =\n\t\t\t\t\tfiltered_indices[\n\t\t\t\t\t\tpositive_mod(index_in_filtered + increment, filtered_indices.length)\n\t\t\t\t\t];\n\t\t\t}\n\t\t}\n\t}\n\treturn [true, active_index];\n}\n", "<script lang=\"ts\">\n\timport DropdownOptions from \"./DropdownOptions.svelte\";\n\timport { createEventDispatcher, afterUpdate } from \"svelte\";\n\timport { BlockTitle } from \"@gradio/atoms\";\n\timport { DropdownArrow } from \"@gradio/icons\";\n\timport type { SelectData, KeyUpData } from \"@gradio/utils\";\n\timport { handle_filter, handle_change, handle_shared_keys } from \"./utils\";\n\n\ttype Item = string | number;\n\n\texport let label: string;\n\texport let info: string | undefined = undefined;\n\texport let value: Item | Item[] | undefined = undefined;\n\tlet old_value: typeof value = undefined;\n\texport let value_is_output = false;\n\texport let choices: [string, Item][];\n\tlet old_choices: typeof choices;\n\texport let disabled = false;\n\texport let show_label: boolean;\n\texport let container = true;\n\texport let allow_custom_value = false;\n\texport let filterable = true;\n\n\tlet filter_input: HTMLElement;\n\n\tlet show_options = false;\n\tlet choices_names: string[];\n\tlet choices_values: (string | number)[];\n\tlet input_text = \"\";\n\tlet old_input_text = \"\";\n\tlet initialized = false;\n\n\t// All of these are indices with respect to the choices array\n\tlet filtered_indices: number[] = [];\n\tlet active_index: number | null = null;\n\t// selected_index is null if allow_custom_value is true and the input_text is not in choices_names\n\tlet selected_index: number | null = null;\n\tlet old_selected_index: number | null;\n\n\tconst dispatch = createEventDispatcher<{\n\t\tchange: string | undefined;\n\t\tinput: undefined;\n\t\tselect: SelectData;\n\t\tblur: undefined;\n\t\tfocus: undefined;\n\t\tkey_up: KeyUpData;\n\t}>();\n\n\t// Setting the initial value of the dropdown\n\tif (value) {\n\t\told_selected_index = choices.map((c) => c[1]).indexOf(value as string);\n\t\tselected_index = old_selected_index;\n\t\tif (selected_index === -1) {\n\t\t\told_value = value;\n\t\t\tselected_index = null;\n\t\t} else {\n\t\t\t[input_text, old_value] = choices[selected_index];\n\t\t\told_input_text = input_text;\n\t\t}\n\t\tset_input_text();\n\t}\n\n\t$: {\n\t\tif (\n\t\t\tselected_index !== old_selected_index &&\n\t\t\tselected_index !== null &&\n\t\t\tinitialized\n\t\t) {\n\t\t\t[input_text, value] = choices[selected_index];\n\t\t\told_selected_index = selected_index;\n\t\t\tdispatch(\"select\", {\n\t\t\t\tindex: selected_index,\n\t\t\t\tvalue: choices_values[selected_index],\n\t\t\t\tselected: true\n\t\t\t});\n\t\t}\n\t}\n\n\t$: if (JSON.stringify(old_value) !== JSON.stringify(value)) {\n\t\tset_input_text();\n\t\thandle_change(dispatch, value, value_is_output);\n\t\told_value = value;\n\t}\n\n\tfunction set_choice_names_values(): void {\n\t\tchoices_names = choices.map((c) => c[0]);\n\t\tchoices_values = choices.map((c) => c[1]);\n\t}\n\n\t$: choices, set_choice_names_values();\n\n\tconst is_browser = typeof window !== \"undefined\";\n\n\t$: {\n\t\tif (choices !== old_choices) {\n\t\t\tif (!allow_custom_value) {\n\t\t\t\tset_input_text();\n\t\t\t}\n\t\t\told_choices = choices;\n\t\t\tfiltered_indices = handle_filter(choices, input_text);\n\t\t\tif (!allow_custom_value && filtered_indices.length > 0) {\n\t\t\t\tactive_index = filtered_indices[0];\n\t\t\t}\n\t\t\tif (is_browser && filter_input === document.activeElement) {\n\t\t\t\tshow_options = true;\n\t\t\t}\n\t\t}\n\t}\n\n\t$: {\n\t\tif (input_text !== old_input_text) {\n\t\t\tfiltered_indices = handle_filter(choices, input_text);\n\t\t\told_input_text = input_text;\n\t\t\tif (!allow_custom_value && filtered_indices.length > 0) {\n\t\t\t\tactive_index = filtered_indices[0];\n\t\t\t}\n\t\t}\n\t}\n\n\tfunction set_input_text(): void {\n\t\tset_choice_names_values();\n\t\tif (value === undefined || (Array.isArray(value) && value.length === 0)) {\n\t\t\tinput_text = \"\";\n\t\t\tselected_index = null;\n\t\t} else if (choices_values.includes(value as string)) {\n\t\t\tinput_text = choices_names[choices_values.indexOf(value as string)];\n\t\t\tselected_index = choices_values.indexOf(value as string);\n\t\t} else if (allow_custom_value) {\n\t\t\tinput_text = value as string;\n\t\t\tselected_index = null;\n\t\t} else {\n\t\t\tinput_text = \"\";\n\t\t\tselected_index = null;\n\t\t}\n\t\told_selected_index = selected_index;\n\t}\n\n\tfunction handle_option_selected(e: any): void {\n\t\tselected_index = parseInt(e.detail.target.dataset.index);\n\t\tif (isNaN(selected_index)) {\n\t\t\t// This is the case when the user clicks on the scrollbar\n\t\t\tselected_index = null;\n\t\t\treturn;\n\t\t}\n\t\tshow_options = false;\n\t\tactive_index = null;\n\t\tfilter_input.blur();\n\t}\n\n\tfunction handle_focus(e: FocusEvent): void {\n\t\tfiltered_indices = choices.map((_, i) => i);\n\t\tshow_options = true;\n\t\tdispatch(\"focus\");\n\t}\n\n\tfunction handle_blur(): void {\n\t\tif (!allow_custom_value) {\n\t\t\tinput_text = choices_names[choices_values.indexOf(value as string)];\n\t\t} else {\n\t\t\tvalue = input_text;\n\t\t}\n\t\tshow_options = false;\n\t\tactive_index = null;\n\t\tdispatch(\"blur\");\n\t}\n\n\tfunction handle_key_down(e: KeyboardEvent): void {\n\t\t[show_options, active_index] = handle_shared_keys(\n\t\t\te,\n\t\t\tactive_index,\n\t\t\tfiltered_indices\n\t\t);\n\t\tif (e.key === \"Enter\") {\n\t\t\tif (active_index !== null) {\n\t\t\t\tselected_index = active_index;\n\t\t\t\tshow_options = false;\n\t\t\t\tfilter_input.blur();\n\t\t\t\tactive_index = null;\n\t\t\t} else if (choices_names.includes(input_text)) {\n\t\t\t\tselected_index = choices_names.indexOf(input_text);\n\t\t\t\tshow_options = false;\n\t\t\t\tactive_index = null;\n\t\t\t\tfilter_input.blur();\n\t\t\t} else if (allow_custom_value) {\n\t\t\t\tvalue = input_text;\n\t\t\t\tselected_index = null;\n\t\t\t\tshow_options = false;\n\t\t\t\tactive_index = null;\n\t\t\t\tfilter_input.blur();\n\t\t\t}\n\t\t}\n\t}\n\n\tafterUpdate(() => {\n\t\tvalue_is_output = false;\n\t\tinitialized = true;\n\t});\n</script>\n\n<div class:container>\n\t<BlockTitle {show_label} {info}>{label}</BlockTitle>\n\n\t<div class=\"wrap\">\n\t\t<div class=\"wrap-inner\" class:show_options>\n\t\t\t<div class=\"secondary-wrap\">\n\t\t\t\t<input\n\t\t\t\t\trole=\"listbox\"\n\t\t\t\t\taria-controls=\"dropdown-options\"\n\t\t\t\t\taria-expanded={show_options}\n\t\t\t\t\taria-label={label}\n\t\t\t\t\tclass=\"border-none\"\n\t\t\t\t\tclass:subdued={!choices_names.includes(input_text) &&\n\t\t\t\t\t\t!allow_custom_value}\n\t\t\t\t\t{disabled}\n\t\t\t\t\tautocomplete=\"off\"\n\t\t\t\t\tbind:value={input_text}\n\t\t\t\t\tbind:this={filter_input}\n\t\t\t\t\ton:keydown={handle_key_down}\n\t\t\t\t\ton:keyup={(e) =>\n\t\t\t\t\t\tdispatch(\"key_up\", {\n\t\t\t\t\t\t\tkey: e.key,\n\t\t\t\t\t\t\tinput_value: input_text\n\t\t\t\t\t\t})}\n\t\t\t\t\ton:blur={handle_blur}\n\t\t\t\t\ton:focus={handle_focus}\n\t\t\t\t\treadonly={!filterable}\n\t\t\t\t/>\n\t\t\t\t{#if !disabled}\n\t\t\t\t\t<div class=\"icon-wrap\">\n\t\t\t\t\t\t<DropdownArrow />\n\t\t\t\t\t</div>\n\t\t\t\t{/if}\n\t\t\t</div>\n\t\t</div>\n\t\t<DropdownOptions\n\t\t\t{show_options}\n\t\t\t{choices}\n\t\t\t{filtered_indices}\n\t\t\t{disabled}\n\t\t\tselected_indices={selected_index === null ? [] : [selected_index]}\n\t\t\t{active_index}\n\t\t\ton:change={handle_option_selected}\n\t\t/>\n\t</div>\n</div>\n\n<style>\n\t.icon-wrap {\n\t\tposition: absolute;\n\t\ttop: 50%;\n\t\ttransform: translateY(-50%);\n\t\tright: var(--size-5);\n\t\tcolor: var(--body-text-color);\n\t\twidth: var(--size-5);\n\t\tpointer-events: none;\n\t}\n\t.container {\n\t\theight: 100%;\n\t}\n\t.container .wrap {\n\t\tbox-shadow: var(--input-shadow);\n\t\tborder: var(--input-border-width) solid var(--border-color-primary);\n\t}\n\n\t.wrap {\n\t\tposition: relative;\n\t\tborder-radius: var(--input-radius);\n\t\tbackground: var(--input-background-fill);\n\t}\n\n\t.wrap:focus-within {\n\t\tbox-shadow: var(--input-shadow-focus);\n\t\tborder-color: var(--input-border-color-focus);\n\t\tbackground: var(--input-background-fill-focus);\n\t}\n\n\t.wrap-inner {\n\t\tdisplay: flex;\n\t\tposition: relative;\n\t\tflex-wrap: wrap;\n\t\talign-items: center;\n\t\tgap: var(--checkbox-label-gap);\n\t\tpadding: var(--checkbox-label-padding);\n\t\theight: 100%;\n\t}\n\t.secondary-wrap {\n\t\tdisplay: flex;\n\t\tflex: 1 1 0%;\n\t\talign-items: center;\n\t\tborder: none;\n\t\tmin-width: min-content;\n\t\theight: 100%;\n\t}\n\n\tinput {\n\t\tmargin: var(--spacing-sm);\n\t\toutline: none;\n\t\tborder: none;\n\t\tbackground: inherit;\n\t\twidth: var(--size-full);\n\t\tcolor: var(--body-text-color);\n\t\tfont-size: var(--input-text-size);\n\t\theight: 100%;\n\t}\n\n\tinput:disabled {\n\t\t-webkit-text-fill-color: var(--body-text-color);\n\t\t-webkit-opacity: 1;\n\t\topacity: 1;\n\t\tcursor: not-allowed;\n\t}\n\n\t.subdued {\n\t\tcolor: var(--body-text-color-subdued);\n\t}\n\n\tinput[readonly] {\n\t\tcursor: pointer;\n\t}\n</style>\n"], "names": ["ctx", "i", "set_style", "ul", "insert_hydration", "target", "anchor", "ul_transition", "create_bidirectional_transition", "fly", "toggle_class", "li", "append_hydration", "span", "set_data", "t2", "t2_value", "if_block", "create_if_block", "div", "choices", "$$props", "filtered_indices", "show_options", "disabled", "selected_indices", "active_index", "remember_scroll", "distance_from_top", "distance_from_bottom", "input_height", "input_width", "refElement", "listElement", "top", "bottom", "max_height", "innerHeight", "list_scroll_y", "calculate_window_distance", "ref_top", "ref_bottom", "$$invalidate", "scroll_timeout", "scroll_listener", "restore_last_scroll", "_a", "dispatch", "createEventDispatcher", "$$value", "mousedown_handler", "e", "scroll_handler", "elements", "element", "rect", "_b", "positive_mod", "m", "handle_filter", "input_text", "o", "index", "handle_change", "value", "value_is_output", "handle_shared_keys", "index_in_filtered", "increment", "input", "div3", "div2", "div1", "div0", "dirty", "dropdownoptions_changes", "label", "info", "old_value", "old_choices", "show_label", "container", "allow_custom_value", "filterable", "filter_input", "choices_names", "choices_values", "old_input_text", "initialized", "selected_index", "old_selected_index", "c", "set_input_text", "set_choice_names_values", "is_browser", "handle_option_selected", "handle_focus", "_", "handle_blur", "handle_key_down", "afterUpdate", "keyup_handler"], "mappings": "m+BAkGSA,EAAgB,CAAA,CAAA,uBAArB,OAAIC,GAAA,iUALoBD,EAAU,EAAA,CAAA,6BAAA,EACvBE,EAAAC,EAAA,QAAAH,KAAc,IAAI,UARhCI,EAgCIC,EAAAF,EAAAG,CAAA,qJApBIN,EAAgB,CAAA,CAAA,oBAArB,OAAIC,GAAA,EAAA,mHAAJ,2FALwBD,EAAU,EAAA,CAAA,6BAAA,SACvBE,EAAAC,EAAA,QAAAH,KAAc,IAAI,2BANbO,IAAAA,EAAAC,GAAAL,EAAAM,GAAA,CAAA,SAAU,IAAK,EAAG,CAAC,EAAA,EAAA,+BAAnBF,IAAAA,EAAAC,GAAAL,EAAAM,GAAA,CAAA,SAAU,IAAK,EAAG,CAAC,EAAA,EAAA,gHA2BlCT,EAAO,CAAA,EAACA,EAAK,EAAA,CAAA,EAAE,CAAC,EAAA,8YAHEA,EAAgB,CAAA,EAAC,SAASA,EAAK,EAAA,CAAA,CAAA,uDANtCA,EAAK,EAAA,CAAA,qBACLA,EAAO,CAAA,EAACA,EAAK,EAAA,CAAA,EAAE,CAAC,CAAA,kFAGbA,EAAgB,CAAA,EAAC,SAASA,EAAK,EAAA,CAAA,CAAA,iBAT9BA,EAAgB,CAAA,EAAC,SAASA,EAAK,EAAA,CAAA,CAAA,EACjCU,EAAAC,EAAA,SAAAX,QAAUA,EAAY,CAAA,CAAA,EACjBU,EAAAC,EAAA,cAAAX,QAAUA,EAAY,CAAA,CAAA,EACjBU,EAAAC,EAAA,mBAAAX,QAAUA,EAAY,CAAA,CAAA,EACjCE,EAAAS,EAAA,QAAAX,KAAc,IAAI,UANhCI,EAiBIC,EAAAM,EAAAL,CAAA,EAJHM,EAEMD,EAAAE,CAAA,iDAFab,EAAgB,CAAA,EAAC,SAASA,EAAK,EAAA,CAAA,CAAA,cAGjDA,EAAO,CAAA,EAACA,EAAK,EAAA,CAAA,EAAE,CAAC,EAAA,KAAAc,GAAAC,EAAAC,CAAA,cATLhB,EAAK,EAAA,oCACLA,EAAO,CAAA,EAACA,EAAK,EAAA,CAAA,EAAE,CAAC,qCAGbA,EAAgB,CAAA,EAAC,SAASA,EAAK,EAAA,CAAA,gDAT9BA,EAAgB,CAAA,EAAC,SAASA,EAAK,EAAA,CAAA,CAAA,QACjCU,EAAAC,EAAA,SAAAX,QAAUA,EAAY,CAAA,CAAA,QACjBU,EAAAC,EAAA,cAAAX,QAAUA,EAAY,CAAA,CAAA,QACjBU,EAAAC,EAAA,mBAAAX,QAAUA,EAAY,CAAA,CAAA,SACjCE,EAAAS,EAAA,QAAAX,KAAc,IAAI,yDApB9B,IAAAiB,EAAAjB,OAAiBA,EAAQ,CAAA,GAAAkB,GAAAlB,CAAA,4KAD9BI,EAA+CC,EAAAc,EAAAb,CAAA,kEAFrBN,EAAe,EAAA,CAAA,4CAGpCA,OAAiBA,EAAQ,CAAA,kNAlFlB,GAAA,CAAA,QAAAoB,CAAA,EAAAC,EACA,CAAA,iBAAAC,CAAA,EAAAD,GACA,aAAAE,EAAe,EAAA,EAAAF,GACf,SAAAG,EAAW,EAAA,EAAAH,EACX,CAAA,iBAAAI,EAAA,EAAA,EAAAJ,GACA,aAAAK,EAA8B,IAAA,EAAAL,GAC9B,gBAAAM,EAAkB,EAAA,EAAAN,EAEzBO,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EAAoBC,EAAuBC,EAC3CC,EACAC,EAAgB,EAEX,SAAAC,GAAA,OACA,IAAKC,EAAS,OAAQC,GAC7BT,EAAW,6BACZJ,EAAoBY,CAAA,EACpBE,EAAA,GAAAb,EAAuBQ,EAAcI,CAAA,MAGlCE,EAAwC,KACnC,SAAAC,GAAA,CACHrB,IACDoB,IAAmB,MACtB,aAAaA,CAAc,EAG5BA,EAAiB,gBAChBJ,IACAI,EAAiB,MACf,KAGK,SAAAE,GAAA,QACRC,EAAAb,GAAA,YAAAA,EAAa,WAAb,MAAAa,EAAA,KAAAb,EAAwB,EAAGK,SAqCtBS,EAAWC,sFAKgBhB,EAAUiB,WAKZ,MAAAC,EAAAC,GAAMJ,EAAS,SAAUI,CAAC,EAC5CC,EAAAD,QAAOb,EAAgBa,EAAE,cAAc,SAAS,2CAKjDlB,EAAWgB,wXAlDvB,IACK1B,GAAgBS,EAAA,CACf,GAAAL,EACHkB,YAEIZ,GAAeR,EAAiB,OAAS,EAAA,KACxC4B,EAAWpB,EAAY,iBAAiB,IAAI,YACrCqB,KAAW,MAAM,KAAKD,CAAQ,EAEvC,GAAAC,EAAQ,aAAa,YAAY,IACjC7B,EAAiB,CAAC,EAAE,WAAA,EAEpBqB,EAAAb,GAAA,YAAAA,EAAa,WAAb,MAAAa,EAAA,KAAAb,EAAwB,EAAIqB,EAA0B,kBAM1Df,IACM,MAAAgB,GAAOC,EAAAxB,EAAW,gBAAX,YAAAwB,EAA0B,wBACvCd,EAAA,GAAAZ,GAAeyB,GAAA,YAAAA,EAAM,SAAU,CAAA,EAC/Bb,EAAA,EAAAX,GAAcwB,GAAA,YAAAA,EAAM,QAAS,CAAA,EAE1B1B,EAAuBD,GAC1Bc,EAAA,EAAAR,EAAA,GAASN,CAAiB,IAAA,OAC1BQ,EAAaP,CAAA,OACbM,EAAS,IAAA,IAETO,EAAA,GAAAP,EAAA,GAAYN,EAAuBC,CAAY,IAAA,EAC/CY,EAAA,GAAAN,EAAaR,EAAoBE,CAAA,MACjCI,EAAM,IAAA,uOC3ET,SAASuB,GAAa,EAAWC,EAAmB,CAC1C,OAAA,EAAIA,EAAKA,GAAKA,CACxB,CAEgB,SAAAC,GACfvC,EACAwC,EACW,CACX,OAAOxC,EAAQ,OAAO,CAACE,EAAkBuC,EAAGC,MAE1C,CAAAF,GAAaC,EAAE,CAAC,EAAE,YAAA,EAAc,SAASD,EAAW,aAAa,IAEjEtC,EAAiB,KAAKwC,CAAK,EAErBxC,GACL,CAAc,CAAA,CAClB,CAEgB,SAAAyC,GACfhB,EACAiB,EACAC,EACO,CACPlB,EAAS,SAAUiB,CAAK,EACnBC,GACJlB,EAAS,OAAO,CAElB,CAEgB,SAAAmB,GACff,EACAzB,EACAJ,EAC2B,CACvB,GAAA6B,EAAE,MAAQ,SACN,MAAA,CAAC,GAAOzB,CAAY,EAE5B,IAAIyB,EAAE,MAAQ,aAAeA,EAAE,MAAQ,YAClC7B,EAAiB,OAAS,EAC7B,GAAII,IAAiB,KAEnBA,EAAAyB,EAAE,MAAQ,YACP7B,EAAiB,CAAC,EAClBA,EAAiBA,EAAiB,OAAS,CAAC,MAC1C,CACA,MAAA6C,EAAoB7C,EAAiB,QAAQI,CAAY,EACzD0C,EAAYjB,EAAE,MAAQ,UAAY,GAAK,EAC7CzB,EACCJ,EACCmC,GAAaU,EAAoBC,EAAW9C,EAAiB,MAAM,CACpE,CACF,CAGK,MAAA,CAAC,GAAMI,CAAY,CAC3B,sCCiJkC1B,EAAK,CAAA,CAAA,eAALA,EAAK,CAAA,CAAA,wCAALA,EAAK,CAAA,CAAA,gPA4BlCI,EAEKC,EAAAc,EAAAb,CAAA,sPAHAN,EAAQ,CAAA,GAAAkB,GAAA,+FAYG,iBAAAlB,QAAmB,SAAaA,EAAc,EAAA,CAAA,uCAErDA,EAAsB,EAAA,CAAA,ylBAjChBA,EAAY,EAAA,CAAA,mBACfA,EAAK,CAAA,CAAA,oGAgBNA,EAAU,CAAA,EAdLU,EAAA2D,EAAA,UAAA,CAAArE,EAAc,EAAA,EAAA,SAASA,QACrCA,EAAkB,CAAA,CAAA,iNAbzBI,EA6CKC,EAAAiE,EAAAhE,CAAA,sBA1CJM,EAyCK0D,EAAAC,CAAA,EAxCJ3D,EA8BK2D,EAAAC,CAAA,EA7BJ5D,EA4BK4D,EAAAC,CAAA,EA3BJ7D,EAqBC6D,EAAAJ,CAAA,OAXYrE,EAAU,CAAA,CAAA,kGAEVA,EAAe,EAAA,CAAA,gCAMlBA,EAAW,EAAA,CAAA,cACVA,EAAY,EAAA,CAAA,yKAhBPA,EAAY,EAAA,CAAA,iCACfA,EAAK,CAAA,CAAA,yDAgBNA,EAAU,CAAA,wCAVTA,EAAU,CAAA,QAAVA,EAAU,CAAA,CAAA,mBAJNU,EAAA2D,EAAA,UAAA,CAAArE,EAAc,EAAA,EAAA,SAASA,QACrCA,EAAkB,CAAA,CAAA,EAefA,EAAQ,CAAA,2QAYG0E,EAAA,CAAA,EAAA,OAAAC,EAAA,iBAAA3E,QAAmB,SAAaA,EAAc,EAAA,CAAA,qRArOvD,GAAA,CAAA,MAAA4E,CAAA,EAAAvD,GACA,KAAAwD,EAA2B,MAAA,EAAAxD,GAC3B,MAAA2C,EAAmC,MAAA,EAAA3C,EAC1CyD,GACO,gBAAAb,EAAkB,EAAA,EAAA5C,EAClB,CAAA,QAAAD,CAAA,EAAAC,EACP0D,GACO,SAAAvD,EAAW,EAAA,EAAAH,EACX,CAAA,WAAA2D,CAAA,EAAA3D,GACA,UAAA4D,EAAY,EAAA,EAAA5D,GACZ,mBAAA6D,EAAqB,EAAA,EAAA7D,GACrB,WAAA8D,EAAa,EAAA,EAAA9D,EAEpB+D,EAEA7D,EAAe,GACf8D,EACAC,EACA1B,EAAa,GACb2B,EAAiB,GACjBC,EAAc,GAGdlE,EAAA,CAAA,EACAI,EAA8B,KAE9B+D,EAAgC,KAChCC,QAEE3C,EAAWC,KAUbgB,IACH0B,EAAqBtE,EAAQ,IAAKuE,GAAMA,EAAE,CAAC,CAAA,EAAG,QAAQ3B,CAAe,EACrEyB,EAAiBC,EACbD,IAAmB,IACtBX,EAAYd,EACZyB,EAAiB,QAEhB7B,EAAYkB,CAAS,EAAI1D,EAAQqE,CAAc,EAChDF,EAAiB3B,GAElBgC,KAyBQ,SAAAC,GAAA,CACRnD,EAAA,GAAA2C,EAAgBjE,EAAQ,IAAKuE,GAAMA,EAAE,CAAC,CAAA,CAAA,EACtCjD,EAAA,GAAA4C,EAAiBlE,EAAQ,IAAKuE,GAAMA,EAAE,CAAC,CAAA,CAAA,EAKlC,MAAAG,EAAA,OAAoB,OAAW,IA4B5B,SAAAF,GAAA,CACRC,IACI7B,IAAA,QAAwB,MAAM,QAAQA,CAAK,GAAKA,EAAM,SAAW,OACpEJ,EAAa,EAAA,OACb6B,EAAiB,IAAA,GACPH,EAAe,SAAStB,CAAe,OACjDJ,EAAayB,EAAcC,EAAe,QAAQtB,CAAe,CAAA,CAAA,OACjEyB,EAAiBH,EAAe,QAAQtB,CAAe,CAAA,GAC7CkB,OACVtB,EAAaI,CAAA,OACbyB,EAAiB,IAAA,QAEjB7B,EAAa,EAAA,OACb6B,EAAiB,IAAA,QAElBC,EAAqBD,CAAA,WAGbM,EAAuB5C,EAAA,CAE3B,GADJT,EAAA,GAAA+C,EAAiB,SAAStC,EAAE,OAAO,OAAO,QAAQ,KAAK,CAAA,EACnD,MAAMsC,CAAc,EAAA,MAEvBA,EAAiB,IAAA,cAGlBlE,EAAe,EAAA,OACfG,EAAe,IAAA,EACf0D,EAAa,KAAA,WAGLY,EAAa7C,EAAA,CACrBT,EAAA,GAAApB,EAAmBF,EAAQ,IAAA,CAAK6E,GAAGhG,KAAMA,EAAC,CAAA,OAC1CsB,EAAe,EAAA,EACfwB,EAAS,OAAO,EAGR,SAAAmD,GAAA,CACHhB,OAGJlB,EAAQJ,CAAA,MAFRA,EAAayB,EAAcC,EAAe,QAAQtB,CAAe,CAAA,CAAA,OAIlEzC,EAAe,EAAA,OACfG,EAAe,IAAA,EACfqB,EAAS,MAAM,WAGPoD,EAAgBhD,EAAA,CACvBT,EAAA,GAAA,CAAAnB,EAAcG,CAAY,EAAIwC,GAC9Bf,EACAzB,EACAJ,CAAA,EAAAC,GAAAmB,EAAA,GAAAhB,CAAA,EAAAgB,EAAA,EAAAtB,CAAA,EAAAsB,EAAA,GAAAqC,CAAA,EAAArC,EAAA,EAAAwC,CAAA,EAAAxC,EAAA,EAAAkB,CAAA,EAAAlB,EAAA,GAAApB,CAAA,EAAAoB,EAAA,EAAA0C,CAAA,EAAA1C,EAAA,GAAA6C,CAAA,EAAA7C,EAAA,GAAA+C,CAAA,EAAA/C,EAAA,GAAAgD,CAAA,EAAAhD,EAAA,GAAA8C,CAAA,EAAA9C,EAAA,GAAA4C,CAAA,EAAA,EAEGnC,EAAE,MAAQ,UACTzB,IAAiB,WACpB+D,EAAiB/D,CAAA,OACjBH,EAAe,EAAA,EACf6D,EAAa,KAAA,OACb1D,EAAe,IAAA,GACL2D,EAAc,SAASzB,CAAU,QAC3C6B,EAAiBJ,EAAc,QAAQzB,CAAU,CAAA,OACjDrC,EAAe,EAAA,OACfG,EAAe,IAAA,EACf0D,EAAa,KAAA,GACHF,SACVlB,EAAQJ,CAAA,OACR6B,EAAiB,IAAA,OACjBlE,EAAe,EAAA,OACfG,EAAe,IAAA,EACf0D,EAAa,KAAA,IAKhBgB,GAAA,IAAA,MACCnC,EAAkB,EAAA,OAClBuB,EAAc,EAAA,iBAoBC5B,EAAU,KAAA,6FACXwB,EAAYnC,WAEZ,MAAAoD,GAAAlD,GACVJ,EAAS,SACR,CAAA,IAAKI,EAAE,IACP,YAAaS,CAAA,CAAA,4aA7JjB6B,IAAmBC,GACnBD,IAAmB,MACnBD,SAEC5B,EAAYI,CAAK,EAAI5C,EAAQqE,CAAc,EAAA7B,GAAAlB,EAAA,GAAAsB,CAAA,EAAAtB,EAAA,GAAA+C,CAAA,EAAA/C,EAAA,GAAAgD,CAAA,EAAAhD,EAAA,GAAA8C,CAAA,EAAA9C,EAAA,EAAAtB,CAAA,EAAAsB,EAAA,GAAA4C,CAAA,SAC5CI,EAAqBD,CAAA,EACrB1C,EAAS,SAAA,CACR,MAAO0C,EACP,MAAOH,EAAeG,CAAc,EACpC,SAAU,6BAKN,KAAK,UAAUX,CAAS,IAAM,KAAK,UAAUd,CAAK,IACxD4B,IACA7B,GAAchB,EAAUiB,EAAOC,CAAe,OAC9Ca,EAAYd,CAAA,oBAQD6B,EAAA,yBAKPzE,IAAY2D,IACVG,GACJU,SAEDb,EAAc3D,CAAA,OACdE,EAAmBqC,GAAcvC,EAASwC,CAAU,CAAA,EAC/C,CAAAsB,GAAsB5D,EAAiB,OAAS,GACpDoB,EAAA,GAAAhB,EAAeJ,EAAiB,CAAC,CAAA,EAE9BwE,GAAcV,IAAiB,SAAS,oBAC3C7D,EAAe,EAAA,2BAMbqC,IAAe2B,SAClBjE,EAAmBqC,GAAcvC,EAASwC,CAAU,CAAA,OACpD2B,EAAiB3B,CAAA,EACZ,CAAAsB,GAAsB5D,EAAiB,OAAS,GACpDoB,EAAA,GAAAhB,EAAeJ,EAAiB,CAAC,CAAA"}