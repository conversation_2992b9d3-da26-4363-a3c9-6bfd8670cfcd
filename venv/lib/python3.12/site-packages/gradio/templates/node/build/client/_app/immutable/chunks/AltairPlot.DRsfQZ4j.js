import{SvelteComponent as Op,init as Ap,safe_not_equal as Rp,element as Xs,space as Ip,claim_element as Ys,children as K<PERSON>,detach as Oi,claim_space as Lp,attr as Js,insert_hydration as Ql,append_hydration as Qs,noop as uc,onMount as Pp,onD<PERSON><PERSON> as zp,text as Dp,claim_text as jp,set_data as Mp,binding_callbacks as fc}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";import{V as ws,f as Co}from"./2.CXBv8kT_.js";import{g as Up}from"./color.C0OE1KLS.js";import{h as Bn,i as ue,a as z,w as No,s as ui,$ as B,b as Y,c as fi,d as O,e as ce,l as Wp,W as Bp,f as Gp,m as Rr,g as di,j as qp,p as Hp,v as Vp,k as Zl,n as Xp,o as Yp,H as Kp,q as Cs}from"./vega-tooltip.module.DWgbLLbs.js";function Jp(e,t,n,i){let r=t.getPropertyValue("--color-accent"),s=t.getPropertyValue("--body-text-color"),o=t.getPropertyValue("--border-color-primary"),a=t.fontFamily,c=t.getPropertyValue("--block-title-text-weight");const l=h=>h.endsWith("px")?parseFloat(h.slice(0,-2)):12;let u=l(t.getPropertyValue("--text-md")),f=l(t.getPropertyValue("--text-sm")),d={autosize:{type:"fit",contains:"padding"},axis:{labelFont:a,labelColor:s,titleFont:a,titleColor:s,tickColor:o,labelFontSize:f,gridColor:o,titleFontWeight:"normal",titleFontSize:f,labelFontWeight:"normal",domain:!1,labelAngle:0},legend:{labelColor:s,labelFont:a,titleColor:s,titleFont:a,titleFontWeight:"normal",titleFontSize:f,labelFontWeight:"normal",offset:2},title:{color:s,font:a,fontSize:u,fontWeight:c,anchor:"middle"},view:{stroke:o}};e.config=d;let p=e.encoding,g=e.layer;switch(n){case"scatter":e.config.mark={stroke:r},p.color&&p.color.type=="nominal"?p.color.scale.range=p.color.scale.range.map((h,m)=>Ns(i,m)):p.color&&p.color.type=="quantitative"&&(p.color.scale.range=["#eff6ff","#1e3a8a"],p.color.scale.range.interpolate="hsl");break;case"line":e.config.mark={stroke:r,cursor:"crosshair"},g.forEach(h=>{h.encoding.color&&(h.encoding.color.scale.range=h.encoding.color.scale.range.map((m,y)=>Ns(i,y)))});break;case"bar":e.config.mark={opacity:.8,fill:r},p.color&&(p.color.scale.range=p.color.scale.range.map((h,m)=>Ns(i,m)));break}return e}function Ns(e,t){var i;let n=e[t%e.length];return n&&n in ws?(i=ws[n])==null?void 0:i.primary:n||ws[Up(t)].primary}const Qp="vega-lite",Zp='Dominik Moritz, Kanit "Ham" Wongsuphasawat, Arvind Satyanarayan, Jeffrey Heer',eg="5.12.0",tg=["Kanit Wongsuphasawat (http://kanitw.yellowpigz.com)","Dominik Moritz (https://www.domoritz.de)","Arvind Satyanarayan (https://arvindsatya.com)","Jeffrey Heer (https://jheer.org)"],ng="https://vega.github.io/vega-lite/",ig="Vega-Lite is a concise high-level language for interactive visualization.",rg=["vega","chart","visualization"],sg="build/vega-lite.js",og="build/vega-lite.min.js",ag="build/vega-lite.min.js",cg="build/src/index",lg="build/src/index.d.ts",ug={vl2pdf:"./bin/vl2pdf",vl2png:"./bin/vl2png",vl2svg:"./bin/vl2svg",vl2vg:"./bin/vl2vg"},fg=["bin","build","src","vega-lite*","tsconfig.json"],dg={changelog:"conventional-changelog -p angular -r 2",prebuild:"yarn clean:build",build:"yarn build:only","build:only":"tsc -p tsconfig.build.json && rollup -c","prebuild:examples":"yarn build:only","build:examples":"yarn data && TZ=America/Los_Angeles scripts/build-examples.sh","prebuild:examples-full":"yarn build:only","build:examples-full":"TZ=America/Los_Angeles scripts/build-examples.sh 1","build:example":"TZ=America/Los_Angeles scripts/build-example.sh","build:toc":"yarn build:jekyll && scripts/generate-toc","build:site":"rollup -c site/rollup.config.mjs","build:jekyll":"pushd site && bundle exec jekyll build -q && popd","build:versions":"scripts/update-version.sh",clean:"yarn clean:build && del-cli 'site/data/*' 'examples/compiled/*.png' && find site/examples ! -name 'index.md' ! -name 'data' -type f -delete","clean:build":"del-cli 'build/*' !build/vega-lite-schema.json",data:"rsync -r node_modules/vega-datasets/data/* site/data",schema:"mkdir -p build && ts-json-schema-generator -f tsconfig.json -p src/index.ts -t TopLevelSpec --no-type-check --no-ref-encode > build/vega-lite-schema.json && yarn renameschema && cp build/vega-lite-schema.json site/_data/",renameschema:"scripts/rename-schema.sh",presite:"yarn data && yarn schema && yarn build:site && yarn build:versions && scripts/create-example-pages.sh",site:"yarn site:only","site:only":"pushd site && bundle exec jekyll serve -I -l && popd",prettierbase:"prettier '**/*.{md,css,yml}'",format:"eslint . --fix && yarn prettierbase --write",lint:"eslint . && yarn prettierbase --check",jest:"NODE_OPTIONS=--experimental-vm-modules npx jest",test:"yarn jest test/ && yarn lint && yarn schema && yarn jest examples/ && yarn test:runtime","test:cover":"yarn jest --collectCoverage test/","test:inspect":"node --inspect-brk --experimental-vm-modules ./node_modules/.bin/jest --runInBand test","test:runtime":"NODE_OPTIONS=--experimental-vm-modules TZ=America/Los_Angeles npx jest test-runtime/ --config test-runtime/jest-config.json","test:runtime:generate":"yarn build:only && del-cli test-runtime/resources && VL_GENERATE_TESTS=true yarn test:runtime",watch:"tsc -p tsconfig.build.json -w","watch:site":"yarn build:site -w","watch:test":"yarn jest --watch test/","watch:test:runtime":"NODE_OPTIONS=--experimental-vm-modules TZ=America/Los_Angeles npx jest --watch test-runtime/ --config test-runtime/jest-config.json",release:"release-it"},pg={type:"git",url:"https://github.com/vega/vega-lite.git"},gg="BSD-3-Clause",hg={url:"https://github.com/vega/vega-lite/issues"},mg={"@babel/core":"^7.21.8","@babel/plugin-proposal-class-properties":"^7.18.6","@babel/preset-env":"^7.21.5","@babel/preset-typescript":"^7.21.5","@release-it/conventional-changelog":"^5.1.1","@rollup/plugin-alias":"^5.0.0","@rollup/plugin-babel":"^6.0.3","@rollup/plugin-commonjs":"^25.0.0","@rollup/plugin-json":"^6.0.0","@rollup/plugin-node-resolve":"^15.0.2","@rollup/plugin-terser":"^0.4.1","@types/chai":"^4.3.5","@types/d3":"^7.4.0","@types/jest":"^27.4.1","@types/pako":"^2.0.0","@typescript-eslint/eslint-plugin":"^5.59.5","@typescript-eslint/parser":"^5.59.5",ajv:"^8.12.0","ajv-formats":"^2.1.1",chai:"^4.3.7",cheerio:"^1.0.0-rc.12","conventional-changelog-cli":"^3.0.0",d3:"^7.8.4","del-cli":"^5.0.0",eslint:"^8.40.0","eslint-config-prettier":"^8.8.0","eslint-plugin-jest":"^27.2.1","eslint-plugin-prettier":"^4.2.1","highlight.js":"^11.8.0",jest:"^27.5.1","jest-dev-server":"^6.1.1",mkdirp:"^3.0.1",pako:"^2.1.0",prettier:"^2.8.8",puppeteer:"^15.0.0","release-it":"^15.10.3",rollup:"^3.21.6","rollup-plugin-bundle-size":"^1.0.3","rollup-plugin-sourcemaps":"^0.6.3",serve:"^14.2.0",terser:"^5.17.3","ts-jest":"^29.1.0","ts-json-schema-generator":"^1.2.0",typescript:"~4.9.5","vega-cli":"^5.25.0","vega-datasets":"^2.7.0","vega-embed":"^6.22.1","vega-tooltip":"^0.32.0","yaml-front-matter":"^4.1.1"},yg={"@types/clone":"~2.1.1",clone:"~2.1.2","fast-deep-equal":"~3.1.3","fast-json-stable-stringify":"~2.1.0","json-stringify-pretty-compact":"~3.0.0",tslib:"~2.5.0","vega-event-selector":"~3.0.1","vega-expression":"~5.1.0","vega-util":"~1.17.2",yargs:"~17.7.2"},bg={vega:"^5.24.0"},xg={node:">=16"},vg={name:Qp,author:Zp,version:eg,collaborators:tg,homepage:ng,description:ig,keywords:rg,main:sg,unpkg:og,jsdelivr:ag,module:cg,types:lg,bin:ug,files:fg,scripts:dg,repository:pg,license:gg,bugs:hg,devDependencies:mg,dependencies:yg,peerDependencies:bg,engines:xg};var eu={exports:{}};(function(e){var t=function(){function n(d,p){return p!=null&&d instanceof p}var i;try{i=Map}catch{i=function(){}}var r;try{r=Set}catch{r=function(){}}var s;try{s=Promise}catch{s=function(){}}function o(d,p,g,h,m){typeof p=="object"&&(g=p.depth,h=p.prototype,m=p.includeNonEnumerable,p=p.circular);var y=[],b=[],N=typeof Buffer<"u";typeof p>"u"&&(p=!0),typeof g>"u"&&(g=1/0);function P(x,_){if(x===null)return null;if(_===0)return x;var w,T;if(typeof x!="object")return x;if(n(x,i))w=new i;else if(n(x,r))w=new r;else if(n(x,s))w=new s(function(L,k){x.then(function(D){L(P(D,_-1))},function(D){k(P(D,_-1))})});else if(o.__isArray(x))w=[];else if(o.__isRegExp(x))w=new RegExp(x.source,f(x)),x.lastIndex&&(w.lastIndex=x.lastIndex);else if(o.__isDate(x))w=new Date(x.getTime());else{if(N&&Buffer.isBuffer(x))return Buffer.allocUnsafe?w=Buffer.allocUnsafe(x.length):w=new Buffer(x.length),x.copy(w),w;n(x,Error)?w=Object.create(x):typeof h>"u"?(T=Object.getPrototypeOf(x),w=Object.create(T)):(w=Object.create(h),T=h)}if(p){var W=y.indexOf(x);if(W!=-1)return b[W];y.push(x),b.push(w)}n(x,i)&&x.forEach(function(L,k){var D=P(k,_-1),H=P(L,_-1);w.set(D,H)}),n(x,r)&&x.forEach(function(L){var k=P(L,_-1);w.add(k)});for(var X in x){var le;T&&(le=Object.getOwnPropertyDescriptor(T,X)),!(le&&le.set==null)&&(w[X]=P(x[X],_-1))}if(Object.getOwnPropertySymbols)for(var pe=Object.getOwnPropertySymbols(x),X=0;X<pe.length;X++){var F=pe[X],$=Object.getOwnPropertyDescriptor(x,F);$&&!$.enumerable&&!m||(w[F]=P(x[F],_-1),$.enumerable||Object.defineProperty(w,F,{enumerable:!1}))}if(m)for(var I=Object.getOwnPropertyNames(x),X=0;X<I.length;X++){var A=I[X],$=Object.getOwnPropertyDescriptor(x,A);$&&$.enumerable||(w[A]=P(x[A],_-1),Object.defineProperty(w,A,{enumerable:!1}))}return w}return P(d,g)}o.clonePrototype=function(p){if(p===null)return null;var g=function(){};return g.prototype=p,new g};function a(d){return Object.prototype.toString.call(d)}o.__objToStr=a;function c(d){return typeof d=="object"&&a(d)==="[object Date]"}o.__isDate=c;function l(d){return typeof d=="object"&&a(d)==="[object Array]"}o.__isArray=l;function u(d){return typeof d=="object"&&a(d)==="[object RegExp]"}o.__isRegExp=u;function f(d){var p="";return d.global&&(p+="g"),d.ignoreCase&&(p+="i"),d.multiline&&(p+="m"),p}return o.__getRegExpFlags=f,o}();e.exports&&(e.exports=t)})(eu);var Sg=eu.exports;const Eg=Co(Sg);var $g=function e(t,n){if(t===n)return!0;if(t&&n&&typeof t=="object"&&typeof n=="object"){if(t.constructor!==n.constructor)return!1;var i,r,s;if(Array.isArray(t)){if(i=t.length,i!=n.length)return!1;for(r=i;r--!==0;)if(!e(t[r],n[r]))return!1;return!0}if(t.constructor===RegExp)return t.source===n.source&&t.flags===n.flags;if(t.valueOf!==Object.prototype.valueOf)return t.valueOf()===n.valueOf();if(t.toString!==Object.prototype.toString)return t.toString()===n.toString();if(s=Object.keys(t),i=s.length,i!==Object.keys(n).length)return!1;for(r=i;r--!==0;)if(!Object.prototype.hasOwnProperty.call(n,s[r]))return!1;for(r=i;r--!==0;){var o=s[r];if(!e(t[o],n[o]))return!1}return!0}return t!==t&&n!==n};const wg=Co($g);var Cg=function(e,t){t||(t={}),typeof t=="function"&&(t={cmp:t});var n=typeof t.cycles=="boolean"?t.cycles:!1,i=t.cmp&&function(s){return function(o){return function(a,c){var l={key:a,value:o[a]},u={key:c,value:o[c]};return s(l,u)}}}(t.cmp),r=[];return function s(o){if(o&&o.toJSON&&typeof o.toJSON=="function"&&(o=o.toJSON()),o!==void 0){if(typeof o=="number")return isFinite(o)?""+o:"null";if(typeof o!="object")return JSON.stringify(o);var a,c;if(Array.isArray(o)){for(c="[",a=0;a<o.length;a++)a&&(c+=","),c+=s(o[a])||"null";return c+"]"}if(o===null)return"null";if(r.indexOf(o)!==-1){if(n)return JSON.stringify("__cycle__");throw new TypeError("Converting circular structure to JSON")}var l=r.push(o)-1,u=Object.keys(o).sort(i&&i(o));for(c="",a=0;a<u.length;a++){var f=u[a],d=s(o[f]);d&&(c&&(c+=","),c+=JSON.stringify(f)+":"+d)}return r.splice(l,1),"{"+c+"}"}}(e)};const Fo=Co(Cg);function _o(e){return!!e.or}function To(e){return!!e.and}function ko(e){return!!e.not}function gr(e,t){if(ko(e))gr(e.not,t);else if(To(e))for(const n of e.and)gr(n,t);else if(_o(e))for(const n of e.or)gr(n,t);else t(e)}function Gn(e,t){return ko(e)?{not:Gn(e.not,t)}:To(e)?{and:e.and.map(n=>Gn(n,t))}:_o(e)?{or:e.or.map(n=>Gn(n,t))}:t(e)}const lt=wg,j=Eg;function tu(e){throw new Error(e)}function Kn(e,t){const n={};for(const i of t)Bn(e,i)&&(n[i]=e[i]);return n}function Te(e,t){const n={...e};for(const i of t)delete n[i];return n}Set.prototype.toJSON=function(){return`Set(${[...this].map(e=>Fo(e)).join(",")})`};const te=Fo;function G(e){if(ue(e))return e;const t=z(e)?e:Fo(e);if(t.length<250)return t;let n=0;for(let i=0;i<t.length;i++){const r=t.charCodeAt(i);n=(n<<5)-n+r,n=n&n}return n}function Zs(e){return e===!1||e===null}function q(e,t){return e.includes(t)}function xn(e,t){let n=0;for(const[i,r]of e.entries())if(t(r,i,n++))return!0;return!1}function Oo(e,t){let n=0;for(const[i,r]of e.entries())if(!t(r,i,n++))return!1;return!0}function nu(e,...t){for(const n of t)Ng(e,n??{});return e}function Ng(e,t){for(const n of v(t))No(e,n,t[n],!0)}function ut(e,t){const n=[],i={};let r;for(const s of e)r=t(s),!(r in i)&&(i[r]=1,n.push(s));return n}function Fg(e,t){const n=v(e),i=v(t);if(n.length!==i.length)return!1;for(const r of n)if(e[r]!==t[r])return!1;return!0}function iu(e,t){if(e.size!==t.size)return!1;for(const n of e)if(!t.has(n))return!1;return!0}function Ao(e,t){for(const n of e)if(t.has(n))return!0;return!1}function eo(e){const t=new Set;for(const n of e){const r=ui(n).map((o,a)=>a===0?o:`[${o}]`),s=r.map((o,a)=>r.slice(0,a+1).join(""));for(const o of s)t.add(o)}return t}function Ro(e,t){return e===void 0||t===void 0?!0:Ao(eo(e),eo(t))}function Q(e){return v(e).length===0}const v=Object.keys,Se=Object.values,Gt=Object.entries;function Pi(e){return e===!0||e===!1}function se(e){const t=e.replace(/\W/g,"_");return(e.match(/^\d+/)?"_":"")+t}function Ai(e,t){return ko(e)?`!(${Ai(e.not,t)})`:To(e)?`(${e.and.map(n=>Ai(n,t)).join(") && (")})`:_o(e)?`(${e.or.map(n=>Ai(n,t)).join(") || (")})`:t(e)}function hr(e,t){if(t.length===0)return!0;const n=t.shift();return n in e&&hr(e[n],t)&&delete e[n],Q(e)}function Bi(e){return e.charAt(0).toUpperCase()+e.substr(1)}function Io(e,t="datum"){const n=ui(e),i=[];for(let r=1;r<=n.length;r++){const s=`[${n.slice(0,r).map(B).join("][")}]`;i.push(`${t}${s}`)}return i.join(" && ")}function ru(e,t="datum"){return`${t}[${B(ui(e).join("."))}]`}function _g(e){return e.replace(/(\[|\]|\.|'|")/g,"\\$1")}function Me(e){return`${ui(e).map(_g).join("\\.")}`}function vn(e,t,n){return e.replace(new RegExp(t.replace(/[-/\\^$*+?.()|[\]{}]/g,"\\$&"),"g"),n)}function Lo(e){return`${ui(e).join(".")}`}function Jn(e){return e?ui(e).length:0}function fe(...e){for(const t of e)if(t!==void 0)return t}let su=42;function ou(e){const t=++su;return e?String(e)+t:t}function Tg(){su=42}function au(e){return cu(e)?e:`__${e}`}function cu(e){return e.startsWith("__")}function zi(e){if(e!==void 0)return(e%360+360)%360}function Ir(e){return ue(e)?!0:!isNaN(e)&&!isNaN(parseFloat(e))}const Ft="row",_t="column",Lr="facet",oe="x",ye="y",et="x2",yt="y2",Kt="xOffset",pi="yOffset",tt="radius",Rt="radius2",We="theta",It="theta2",nt="latitude",it="longitude",rt="latitude2",Ue="longitude2",Ae="color",bt="fill",xt="stroke",Re="shape",Lt="size",kn="angle",Pt="opacity",Jt="fillOpacity",Qt="strokeOpacity",Zt="strokeWidth",en="strokeDash",Gi="text",Qn="order",qi="detail",Pr="key",Sn="tooltip",zr="href",Dr="url",jr="description",kg={x:1,y:1,x2:1,y2:1},lu={theta:1,theta2:1,radius:1,radius2:1};function uu(e){return e in lu}const Po={longitude:1,longitude2:1,latitude:1,latitude2:1};function fu(e){switch(e){case nt:return"y";case rt:return"y2";case it:return"x";case Ue:return"x2"}}function du(e){return e in Po}const Og=v(Po),zo={...kg,...lu,...Po,xOffset:1,yOffset:1,color:1,fill:1,stroke:1,opacity:1,fillOpacity:1,strokeOpacity:1,strokeWidth:1,strokeDash:1,size:1,angle:1,shape:1,order:1,text:1,detail:1,key:1,tooltip:1,href:1,url:1,description:1};function qn(e){return e===Ae||e===bt||e===xt}const pu={row:1,column:1,facet:1},je=v(pu),Do={...zo,...pu},Ag=v(Do),{order:Sw,detail:Ew,tooltip:$w,...Rg}=Do,{row:ww,column:Cw,facet:Nw,...Ig}=Rg;function Lg(e){return!!Ig[e]}function gu(e){return!!Do[e]}const Pg=[et,yt,rt,Ue,It,Rt];function hu(e){return On(e)!==e}function On(e){switch(e){case et:return oe;case yt:return ye;case rt:return nt;case Ue:return it;case It:return We;case Rt:return tt}return e}function qt(e){if(uu(e))switch(e){case We:return"startAngle";case It:return"endAngle";case tt:return"outerRadius";case Rt:return"innerRadius"}return e}function vt(e){switch(e){case oe:return et;case ye:return yt;case nt:return rt;case it:return Ue;case We:return It;case tt:return Rt}}function Ie(e){switch(e){case oe:case et:return"width";case ye:case yt:return"height"}}function mu(e){switch(e){case oe:return"xOffset";case ye:return"yOffset";case et:return"x2Offset";case yt:return"y2Offset";case We:return"thetaOffset";case tt:return"radiusOffset";case It:return"theta2Offset";case Rt:return"radius2Offset"}}function jo(e){switch(e){case oe:return"xOffset";case ye:return"yOffset"}}function yu(e){switch(e){case"xOffset":return"x";case"yOffset":return"y"}}const zg=v(zo),{x:Fw,y:_w,x2:Tw,y2:kw,xOffset:Ow,yOffset:Aw,latitude:Rw,longitude:Iw,latitude2:Lw,longitude2:Pw,theta:zw,theta2:Dw,radius:jw,radius2:Mw,...Mo}=zo,Dg=v(Mo),Uo={x:1,y:1},St=v(Uo);function be(e){return e in Uo}const Wo={theta:1,radius:1},jg=v(Wo);function Mr(e){return e==="width"?oe:ye}const bu={xOffset:1,yOffset:1};function gi(e){return e in bu}const{text:Uw,tooltip:Ww,href:Bw,url:Gw,description:qw,detail:Hw,key:Vw,order:Xw,...xu}=Mo,Mg=v(xu);function Ug(e){return!!Mo[e]}function Wg(e){switch(e){case Ae:case bt:case xt:case Lt:case Re:case Pt:case Zt:case en:return!0;case Jt:case Qt:case kn:return!1}}const vu={...Uo,...Wo,...bu,...xu},Ur=v(vu);function zt(e){return!!vu[e]}function Bg(e,t){return qg(e)[t]}const Su={arc:"always",area:"always",bar:"always",circle:"always",geoshape:"always",image:"always",line:"always",rule:"always",point:"always",rect:"always",square:"always",trail:"always",text:"always",tick:"always"},{geoshape:Yw,...Gg}=Su;function qg(e){switch(e){case Ae:case bt:case xt:case jr:case qi:case Pr:case Sn:case zr:case Qn:case Pt:case Jt:case Qt:case Zt:case Lr:case Ft:case _t:return Su;case oe:case ye:case Kt:case pi:case nt:case it:return Gg;case et:case yt:case rt:case Ue:return{area:"always",bar:"always",image:"always",rect:"always",rule:"always",circle:"binned",point:"binned",square:"binned",tick:"binned",line:"binned",trail:"binned"};case Lt:return{point:"always",tick:"always",rule:"always",circle:"always",square:"always",bar:"always",text:"always",line:"always",trail:"always"};case en:return{line:"always",point:"always",tick:"always",rule:"always",circle:"always",square:"always",bar:"always",geoshape:"always"};case Re:return{point:"always",geoshape:"always"};case Gi:return{text:"always"};case kn:return{point:"always",square:"always",text:"always"};case Dr:return{image:"always"};case We:return{text:"always",arc:"always"};case tt:return{text:"always",arc:"always"};case It:case Rt:return{arc:"always"}}}function Fs(e){switch(e){case oe:case ye:case We:case tt:case Kt:case pi:case Lt:case kn:case Zt:case Pt:case Jt:case Qt:case et:case yt:case It:case Rt:return;case Lr:case Ft:case _t:case Re:case en:case Gi:case Sn:case zr:case Dr:case jr:return"discrete";case Ae:case bt:case xt:return"flexible";case nt:case it:case rt:case Ue:case qi:case Pr:case Qn:return}}const Hg={argmax:1,argmin:1,average:1,count:1,distinct:1,product:1,max:1,mean:1,median:1,min:1,missing:1,q1:1,q3:1,ci0:1,ci1:1,stderr:1,stdev:1,stdevp:1,sum:1,valid:1,values:1,variance:1,variancep:1},Vg={count:1,min:1,max:1};function Ot(e){return!!e&&!!e.argmin}function tn(e){return!!e&&!!e.argmax}function Bo(e){return z(e)&&!!Hg[e]}const Xg=new Set(["count","valid","missing","distinct"]);function Eu(e){return z(e)&&Xg.has(e)}function Yg(e){return z(e)&&q(["min","max"],e)}const Kg=new Set(["count","sum","distinct","valid","missing"]),Jg=new Set(["mean","average","median","q1","q3","min","max"]);function $u(e){return fi(e)&&(e=Zr(e,void 0)),"bin"+v(e).map(t=>Wr(e[t])?se(`_${t}_${Gt(e[t])}`):se(`_${t}_${e[t]}`)).join("")}function ne(e){return e===!0||An(e)&&!e.binned}function ve(e){return e==="binned"||An(e)&&e.binned===!0}function An(e){return Y(e)}function Wr(e){return e==null?void 0:e.param}function dc(e){switch(e){case Ft:case _t:case Lt:case Ae:case bt:case xt:case Zt:case Pt:case Jt:case Qt:case Re:return 6;case en:return 4;default:return 10}}function Hi(e){return!!(e!=null&&e.expr)}function _e(e){const t=v(e||{}),n={};for(const i of t)n[i]=Pe(e[i]);return n}function wu(e){const{anchor:t,frame:n,offset:i,orient:r,angle:s,limit:o,color:a,subtitleColor:c,subtitleFont:l,subtitleFontSize:u,subtitleFontStyle:f,subtitleFontWeight:d,subtitleLineHeight:p,subtitlePadding:g,...h}=e,m={...h,...a?{fill:a}:{}},y={...t?{anchor:t}:{},...n?{frame:n}:{},...i?{offset:i}:{},...r?{orient:r}:{},...s!==void 0?{angle:s}:{},...o!==void 0?{limit:o}:{}},b={...c?{subtitleColor:c}:{},...l?{subtitleFont:l}:{},...u?{subtitleFontSize:u}:{},...f?{subtitleFontStyle:f}:{},...d?{subtitleFontWeight:d}:{},...p?{subtitleLineHeight:p}:{},...g?{subtitlePadding:g}:{}},N=Kn(e,["align","baseline","dx","dy","limit"]);return{titleMarkConfig:m,subtitleMarkConfig:N,nonMarkTitleProperties:y,subtitle:b}}function Ut(e){return z(e)||O(e)&&z(e[0])}function R(e){return!!(e!=null&&e.signal)}function nn(e){return!!e.step}function Qg(e){return O(e)?!1:"fields"in e&&!("data"in e)}function Zg(e){return O(e)?!1:"fields"in e&&"data"in e}function Nt(e){return O(e)?!1:"field"in e&&"data"in e}const eh={aria:1,description:1,ariaRole:1,ariaRoleDescription:1,blend:1,opacity:1,fill:1,fillOpacity:1,stroke:1,strokeCap:1,strokeWidth:1,strokeOpacity:1,strokeDash:1,strokeDashOffset:1,strokeJoin:1,strokeOffset:1,strokeMiterLimit:1,startAngle:1,endAngle:1,padAngle:1,innerRadius:1,outerRadius:1,size:1,shape:1,interpolate:1,tension:1,orient:1,align:1,baseline:1,text:1,dir:1,dx:1,dy:1,ellipsis:1,limit:1,radius:1,theta:1,angle:1,font:1,fontSize:1,fontWeight:1,fontStyle:1,lineBreak:1,lineHeight:1,cursor:1,href:1,tooltip:1,cornerRadius:1,cornerRadiusTopLeft:1,cornerRadiusTopRight:1,cornerRadiusBottomLeft:1,cornerRadiusBottomRight:1,aspect:1,width:1,height:1,url:1,smooth:1},th=v(eh),nh={arc:1,area:1,group:1,image:1,line:1,path:1,rect:1,rule:1,shape:1,symbol:1,text:1,trail:1},to=["cornerRadius","cornerRadiusTopLeft","cornerRadiusTopRight","cornerRadiusBottomLeft","cornerRadiusBottomRight"];function Cu(e){const t=O(e.condition)?e.condition.map(pc):pc(e.condition);return{...Pe(e),condition:t}}function Pe(e){if(Hi(e)){const{expr:t,...n}=e;return{signal:t,...n}}return e}function pc(e){if(Hi(e)){const{expr:t,...n}=e;return{signal:t,...n}}return e}function re(e){if(Hi(e)){const{expr:t,...n}=e;return{signal:t,...n}}return R(e)?e:e!==void 0?{value:e}:void 0}function ih(e){return R(e)?e.signal:B(e)}function gc(e){return R(e)?e.signal:B(e.value)}function Ve(e){return R(e)?e.signal:e==null?null:B(e)}function rh(e,t,n){for(const i of n){const r=At(i,t.markDef,t.config);r!==void 0&&(e[i]=re(r))}return e}function Nu(e){return[].concat(e.type,e.style??[])}function K(e,t,n,i={}){const{vgChannel:r,ignoreVgConfig:s}=i;return r&&t[r]!==void 0?t[r]:t[e]!==void 0?t[e]:s&&(!r||r===e)?void 0:At(e,t,n,i)}function At(e,t,n,{vgChannel:i}={}){return fe(i?mr(e,t,n.style):void 0,mr(e,t,n.style),i?n[t.type][i]:void 0,n[t.type][e],i?n.mark[i]:n.mark[e])}function mr(e,t,n){return Fu(e,Nu(t),n)}function Fu(e,t,n){t=ce(t);let i;for(const r of t){const s=n[r];s&&s[e]!==void 0&&(i=s[e])}return i}function _u(e,t){return ce(e).reduce((n,i)=>(n.field.push(C(i,t)),n.order.push(i.sort??"ascending"),n),{field:[],order:[]})}function Tu(e,t){const n=[...e];return t.forEach(i=>{for(const r of n)if(lt(r,i))return;n.push(i)}),n}function ku(e,t){return lt(e,t)||!t?e:e?[...ce(e),...ce(t)].join(", "):t}function Ou(e,t){const n=e.value,i=t.value;if(n==null||i===null)return{explicit:e.explicit,value:null};if((Ut(n)||R(n))&&(Ut(i)||R(i)))return{explicit:e.explicit,value:ku(n,i)};if(Ut(n)||R(n))return{explicit:e.explicit,value:n};if(Ut(i)||R(i))return{explicit:e.explicit,value:i};if(!Ut(n)&&!R(n)&&!Ut(i)&&!R(i))return{explicit:e.explicit,value:Tu(n,i)};throw new Error("It should never reach here")}function Go(e){return`Invalid specification ${te(e)}. Make sure the specification includes at least one of the following properties: "mark", "layer", "facet", "hconcat", "vconcat", "concat", or "repeat".`}const sh='Autosize "fit" only works for single views and layered views.';function hc(e){return`${e=="width"?"Width":"Height"} "container" only works for single views and layered views.`}function mc(e){const t=e=="width"?"Width":"Height",n=e=="width"?"x":"y";return`${t} "container" only works well with autosize "fit" or "fit-${n}".`}function yc(e){return e?`Dropping "fit-${e}" because spec has discrete ${Ie(e)}.`:'Dropping "fit" because spec has discrete size.'}function qo(e){return`Unknown field for ${e}. Cannot calculate view size.`}function bc(e){return`Cannot project a selection on encoding channel "${e}", which has no field.`}function oh(e,t){return`Cannot project a selection on encoding channel "${e}" as it uses an aggregate function ("${t}").`}function ah(e){return`The "nearest" transform is not supported for ${e} marks.`}function Au(e){return`Selection not supported for ${e} yet.`}function ch(e){return`Cannot find a selection named "${e}".`}const lh="Scale bindings are currently only supported for scales with unbinned, continuous domains.",uh="Legend bindings are only supported for selections over an individual field or encoding channel.";function fh(e){return`Lookups can only be performed on selection parameters. "${e}" is a variable parameter.`}function dh(e){return`Cannot define and lookup the "${e}" selection in the same view. Try moving the lookup into a second, layered view?`}const ph="The same selection must be used to override scale domains in a layered view.",gh='Interval selections should be initialized using "x", "y", "longitude", or "latitude" keys.';function hh(e){return`Unknown repeated value "${e}".`}function xc(e){return`The "columns" property cannot be used when "${e}" has nested row/column.`}const mh="Axes cannot be shared in concatenated or repeated views yet (https://github.com/vega/vega-lite/issues/2415).";function yh(e){return`Unrecognized parse "${e}".`}function vc(e,t,n){return`An ancestor parsed field "${e}" as ${n} but a child wants to parse the field as ${t}.`}const bh="Attempt to add the same child twice.";function xh(e){return`Ignoring an invalid transform: ${te(e)}.`}const vh='If "from.fields" is not specified, "as" has to be a string that specifies the key to be used for the data from the secondary source.';function Sc(e){return`Config.customFormatTypes is not true, thus custom format type and format for channel ${e} are dropped.`}function Sh(e){const{parentProjection:t,projection:n}=e;return`Layer's shared projection ${te(t)} is overridden by a child projection ${te(n)}.`}const Eh="Arc marks uses theta channel rather than angle, replacing angle with theta.";function $h(e){return`${e}Offset dropped because ${e} is continuous`}function wh(e){return`There is no ${e} encoding. Replacing ${e}Offset encoding as ${e}.`}function Ch(e,t,n){return`Channel ${e} is a ${t}. Converted to {value: ${te(n)}}.`}function Ru(e){return`Invalid field type "${e}".`}function Nh(e,t){return`Invalid field type "${e}" for aggregate: "${t}", using "quantitative" instead.`}function Fh(e){return`Invalid aggregation operator "${e}".`}function Iu(e,t){const{fill:n,stroke:i}=t;return`Dropping color ${e} as the plot also has ${n&&i?"fill and stroke":n?"fill":"stroke"}.`}function _h(e){return`Position range does not support relative band size for ${e}.`}function no(e,t){return`Dropping ${te(e)} from channel "${t}" since it does not contain any data field, datum, value, or signal.`}const Th="Line marks cannot encode size with a non-groupby field. You may want to use trail marks instead.";function Br(e,t,n){return`${e} dropped as it is incompatible with "${t}".`}function kh(e){return`${e} encoding has no scale, so specified scale is ignored.`}function Oh(e){return`${e}-encoding is dropped as ${e} is not a valid encoding channel.`}function Ah(e){return`${e} encoding should be discrete (ordinal / nominal / binned).`}function Rh(e){return`${e} encoding should be discrete (ordinal / nominal / binned) or use a discretizing scale (e.g. threshold).`}function Ih(e){return`Facet encoding dropped as ${e.join(" and ")} ${e.length>1?"are":"is"} also specified.`}function _s(e,t){return`Using discrete channel "${e}" to encode "${t}" field can be misleading as it does not encode ${t==="ordinal"?"order":"magnitude"}.`}function Lh(e){return`The ${e} for range marks cannot be an expression`}function Ph(e,t){return`Line mark is for continuous lines and thus cannot be used with ${e&&t?"x2 and y2":e?"x2":"y2"}. We will use the rule mark (line segments) instead.`}function zh(e,t){return`Specified orient "${e}" overridden with "${t}".`}function Dh(e){return`Cannot use the scale property "${e}" with non-color channel.`}function jh(e){return`Cannot use the relative band size with ${e} scale.`}function Mh(e){return`Using unaggregated domain with raw field has no effect (${te(e)}).`}function Uh(e){return`Unaggregated domain not applicable for "${e}" since it produces values outside the origin domain of the source data.`}function Wh(e){return`Unaggregated domain is currently unsupported for log scale (${te(e)}).`}function Bh(e){return`Cannot apply size to non-oriented mark "${e}".`}function Gh(e,t,n){return`Channel "${e}" does not work with "${t}" scale. We are using "${n}" scale instead.`}function qh(e,t){return`FieldDef does not work with "${e}" scale. We are using "${t}" scale instead.`}function Lu(e,t,n){return`${n}-scale's "${t}" is dropped as it does not work with ${e} scale.`}function Pu(e){return`The step for "${e}" is dropped because the ${e==="width"?"x":"y"} is continuous.`}function Hh(e,t,n,i){return`Conflicting ${t.toString()} property "${e.toString()}" (${te(n)} and ${te(i)}). Using ${te(n)}.`}function Vh(e,t,n,i){return`Conflicting ${t.toString()} property "${e.toString()}" (${te(n)} and ${te(i)}). Using the union of the two domains.`}function Xh(e){return`Setting the scale to be independent for "${e}" means we also have to set the guide (axis or legend) to be independent.`}function Yh(e){return`Dropping sort property ${te(e)} as unioned domains only support boolean or op "count", "min", and "max".`}const Ec="Domains that should be unioned has conflicting sort properties. Sort will be set to true.",Kh="Detected faceted independent scales that union domain of multiple fields from different data sources. We will use the first field. The result view size may be incorrect.",Jh="Detected faceted independent scales that union domain of the same fields from different source. We will assume that this is the same field from a different fork of the same data source. However, if this is not the case, the result view size may be incorrect.",Qh="Detected faceted independent scales that union domain of multiple fields from the same data source. We will use the first field. The result view size may be incorrect.";function Zh(e){return`Cannot stack "${e}" if there is already "${e}2".`}function em(e){return`Cannot stack non-linear scale (${e}).`}function tm(e){return`Stacking is applied even though the aggregate function is non-summative ("${e}").`}function yr(e,t){return`Invalid ${e}: ${te(t)}.`}function nm(e){return`Dropping day from datetime ${te(e)} as day cannot be combined with other units.`}function im(e,t){return`${t?"extent ":""}${t&&e?"and ":""}${e?"center ":""}${t&&e?"are ":"is "}not needed when data are aggregated.`}function rm(e,t,n){return`${e} is not usually used with ${t} for ${n}.`}function sm(e,t){return`Continuous axis should not have customized aggregation function ${e}; ${t} already agregates the axis.`}function $c(e){return`1D error band does not support ${e}.`}function zu(e){return`Channel ${e} is required for "binned" bin.`}function om(e){return`Channel ${e} should not be used with "binned" bin.`}function am(e){return`Domain for ${e} is required for threshold scale.`}const Du=Wp(Bp);let Zn=Du;function cm(e){return Zn=e,Zn}function lm(){return Zn=Du,Zn}function S(...e){Zn.warn(...e)}function um(...e){Zn.debug(...e)}function Rn(e){if(e&&Y(e)){for(const t of Vo)if(t in e)return!0}return!1}const ju=["january","february","march","april","may","june","july","august","september","october","november","december"],fm=ju.map(e=>e.substr(0,3)),Mu=["sunday","monday","tuesday","wednesday","thursday","friday","saturday"],dm=Mu.map(e=>e.substr(0,3));function pm(e){if(Ir(e)&&(e=+e),ue(e))return e>4&&S(yr("quarter",e)),e-1;throw new Error(yr("quarter",e))}function gm(e){if(Ir(e)&&(e=+e),ue(e))return e-1;{const t=e.toLowerCase(),n=ju.indexOf(t);if(n!==-1)return n;const i=t.substr(0,3),r=fm.indexOf(i);if(r!==-1)return r;throw new Error(yr("month",e))}}function hm(e){if(Ir(e)&&(e=+e),ue(e))return e%7;{const t=e.toLowerCase(),n=Mu.indexOf(t);if(n!==-1)return n;const i=t.substr(0,3),r=dm.indexOf(i);if(r!==-1)return r;throw new Error(yr("day",e))}}function Ho(e,t){const n=[];if(t&&e.day!==void 0&&v(e).length>1&&(S(nm(e)),e=j(e),delete e.day),e.year!==void 0?n.push(e.year):n.push(2012),e.month!==void 0){const i=t?gm(e.month):e.month;n.push(i)}else if(e.quarter!==void 0){const i=t?pm(e.quarter):e.quarter;n.push(ue(i)?i*3:`${i}*3`)}else n.push(0);if(e.date!==void 0)n.push(e.date);else if(e.day!==void 0){const i=t?hm(e.day):e.day;n.push(ue(i)?i+1:`${i}+1`)}else n.push(1);for(const i of["hours","minutes","seconds","milliseconds"]){const r=e[i];n.push(typeof r>"u"?0:r)}return n}function En(e){const n=Ho(e,!0).join(", ");return e.utc?`utc(${n})`:`datetime(${n})`}function mm(e){const n=Ho(e,!1).join(", ");return e.utc?`utc(${n})`:`datetime(${n})`}function ym(e){const t=Ho(e,!0);return e.utc?+new Date(Date.UTC(...t)):+new Date(...t)}const Uu={year:1,quarter:1,month:1,week:1,day:1,dayofyear:1,date:1,hours:1,minutes:1,seconds:1,milliseconds:1},Vo=v(Uu);function bm(e){return!!Uu[e]}function In(e){return Y(e)?e.binned:Wu(e)}function Wu(e){return e&&e.startsWith("binned")}function Xo(e){return e.startsWith("utc")}function xm(e){return e.substring(3)}const vm={"year-month":"%b %Y ","year-month-date":"%b %d, %Y "};function Gr(e){return Vo.filter(t=>Gu(e,t))}function Bu(e){const t=Gr(e);return t[t.length-1]}function Gu(e,t){const n=e.indexOf(t);return!(n<0||n>0&&t==="seconds"&&e.charAt(n-1)==="i"||e.length>n+3&&t==="day"&&e.charAt(n+3)==="o"||n>0&&t==="year"&&e.charAt(n-1)==="f")}function Sm(e,t,{end:n}={end:!1}){const i=Io(t),r=Xo(e)?"utc":"";function s(c){return c==="quarter"?`(${r}quarter(${i})-1)`:`${r}${c}(${i})`}let o;const a={};for(const c of Vo)Gu(e,c)&&(a[c]=s(c),o=c);return n&&(a[o]+="+1"),mm(a)}function qu(e){if(!e)return;const t=Gr(e);return`timeUnitSpecifier(${te(t)}, ${te(vm)})`}function Em(e,t,n){if(!e)return;const i=qu(e);return`${n||Xo(e)?"utc":"time"}Format(${t}, ${i})`}function me(e){if(!e)return;let t;return z(e)?Wu(e)?t={unit:e.substring(6),binned:!0}:t={unit:e}:Y(e)&&(t={...e,...e.unit?{unit:e.unit}:{}}),Xo(t.unit)&&(t.utc=!0,t.unit=xm(t.unit)),t}function $m(e){const{utc:t,...n}=me(e);return n.unit?(t?"utc":"")+v(n).map(i=>se(`${i==="unit"?"":`_${i}_`}${n[i]}`)).join(""):(t?"utc":"")+"timeunit"+v(n).map(i=>se(`_${i}_${n[i]}`)).join("")}function Hu(e,t=n=>n){const n=me(e),i=Bu(n.unit);if(i&&i!=="day"){const r={year:2001,month:1,date:1,hours:0,minutes:0,seconds:0,milliseconds:0},{step:s,part:o}=Vu(i,n.step),a={...r,[o]:+r[o]+s};return`${t(En(a))} - ${t(En(r))}`}}const wm={year:1,month:1,date:1,hours:1,minutes:1,seconds:1,milliseconds:1};function Cm(e){return!!wm[e]}function Vu(e,t=1){if(Cm(e))return{part:e,step:t};switch(e){case"day":case"dayofyear":return{part:"date",step:t};case"quarter":return{part:"month",step:t*3};case"week":return{part:"date",step:t*7}}}function Nm(e){return e==null?void 0:e.param}function Yo(e){return!!(e!=null&&e.field)&&e.equal!==void 0}function Ko(e){return!!(e!=null&&e.field)&&e.lt!==void 0}function Jo(e){return!!(e!=null&&e.field)&&e.lte!==void 0}function Qo(e){return!!(e!=null&&e.field)&&e.gt!==void 0}function Zo(e){return!!(e!=null&&e.field)&&e.gte!==void 0}function ea(e){if(e!=null&&e.field){if(O(e.range)&&e.range.length===2)return!0;if(R(e.range))return!0}return!1}function ta(e){return!!(e!=null&&e.field)&&(O(e.oneOf)||O(e.in))}function Fm(e){return!!(e!=null&&e.field)&&e.valid!==void 0}function Xu(e){return ta(e)||Yo(e)||ea(e)||Ko(e)||Qo(e)||Jo(e)||Zo(e)}function st(e,t){return es(e,{timeUnit:t,wrapTime:!0})}function _m(e,t){return e.map(n=>st(n,t))}function Yu(e,t=!0){const{field:n}=e,i=me(e.timeUnit),{unit:r,binned:s}=i||{},o=C(e,{expr:"datum"}),a=r?`time(${s?o:Sm(r,n)})`:o;if(Yo(e))return`${a}===${st(e.equal,r)}`;if(Ko(e)){const c=e.lt;return`${a}<${st(c,r)}`}else if(Qo(e)){const c=e.gt;return`${a}>${st(c,r)}`}else if(Jo(e)){const c=e.lte;return`${a}<=${st(c,r)}`}else if(Zo(e)){const c=e.gte;return`${a}>=${st(c,r)}`}else{if(ta(e))return`indexof([${_m(e.oneOf,r).join(",")}], ${a}) !== -1`;if(Fm(e))return na(a,e.valid);if(ea(e)){const{range:c}=e,l=R(c)?{signal:`${c.signal}[0]`}:c[0],u=R(c)?{signal:`${c.signal}[1]`}:c[1];if(l!==null&&u!==null&&t)return"inrange("+a+", ["+st(l,r)+", "+st(u,r)+"])";const f=[];return l!==null&&f.push(`${a} >= ${st(l,r)}`),u!==null&&f.push(`${a} <= ${st(u,r)}`),f.length>0?f.join(" && "):"true"}}throw new Error(`Invalid field predicate: ${te(e)}`)}function na(e,t=!0){return t?`isValid(${e}) && isFinite(+${e})`:`!isValid(${e}) || !isFinite(+${e})`}function Tm(e){return Xu(e)&&e.timeUnit?{...e,timeUnit:me(e.timeUnit)}:e}const Vi={quantitative:"quantitative",ordinal:"ordinal",temporal:"temporal",nominal:"nominal",geojson:"geojson"};function km(e){return e==="quantitative"||e==="temporal"}function Ku(e){return e==="ordinal"||e==="nominal"}const $n=Vi.quantitative,ia=Vi.ordinal,ei=Vi.temporal,ra=Vi.nominal,hi=Vi.geojson;function Om(e){if(e)switch(e=e.toLowerCase(),e){case"q":case $n:return"quantitative";case"t":case ei:return"temporal";case"o":case ia:return"ordinal";case"n":case ra:return"nominal";case hi:return"geojson"}}const ke={LINEAR:"linear",LOG:"log",POW:"pow",SQRT:"sqrt",SYMLOG:"symlog",IDENTITY:"identity",SEQUENTIAL:"sequential",TIME:"time",UTC:"utc",QUANTILE:"quantile",QUANTIZE:"quantize",THRESHOLD:"threshold",BIN_ORDINAL:"bin-ordinal",ORDINAL:"ordinal",POINT:"point",BAND:"band"},io={linear:"numeric",log:"numeric",pow:"numeric",sqrt:"numeric",symlog:"numeric",identity:"numeric",sequential:"numeric",time:"time",utc:"time",ordinal:"ordinal","bin-ordinal":"bin-ordinal",point:"ordinal-position",band:"ordinal-position",quantile:"discretizing",quantize:"discretizing",threshold:"discretizing"};function Am(e,t){const n=io[e],i=io[t];return n===i||n==="ordinal-position"&&i==="time"||i==="ordinal-position"&&n==="time"}const Rm={linear:0,log:1,pow:1,sqrt:1,symlog:1,identity:1,sequential:1,time:0,utc:0,point:10,band:11,ordinal:0,"bin-ordinal":0,quantile:0,quantize:0,threshold:0};function wc(e){return Rm[e]}const Ju=new Set(["linear","log","pow","sqrt","symlog"]),Qu=new Set([...Ju,"time","utc"]);function Zu(e){return Ju.has(e)}const ef=new Set(["quantile","quantize","threshold"]),Im=new Set([...Qu,...ef,"sequential","identity"]),Lm=new Set(["ordinal","bin-ordinal","point","band"]);function xe(e){return Lm.has(e)}function ze(e){return Im.has(e)}function Xe(e){return Qu.has(e)}function ti(e){return ef.has(e)}const Pm={pointPadding:.5,barBandPaddingInner:.1,rectBandPaddingInner:0,bandWithNestedOffsetPaddingInner:.2,bandWithNestedOffsetPaddingOuter:.2,minBandSize:2,minFontSize:8,maxFontSize:40,minOpacity:.3,maxOpacity:.8,minSize:9,minStrokeWidth:1,maxStrokeWidth:4,quantileCount:4,quantizeCount:4,zero:!0};function zm(e){return!z(e)&&!!e.name}function tf(e){return e==null?void 0:e.param}function Dm(e){return e==null?void 0:e.unionWith}function jm(e){return Y(e)&&"field"in e}const Mm={type:1,domain:1,domainMax:1,domainMin:1,domainMid:1,align:1,range:1,rangeMax:1,rangeMin:1,scheme:1,bins:1,reverse:1,round:1,clamp:1,nice:1,base:1,exponent:1,constant:1,interpolate:1,zero:1,padding:1,paddingInner:1,paddingOuter:1},{type:Kw,domain:Jw,range:Qw,rangeMax:Zw,rangeMin:e0,scheme:t0,...Um}=Mm,Wm=v(Um);function ro(e,t){switch(t){case"type":case"domain":case"reverse":case"range":return!0;case"scheme":case"interpolate":return!["point","band","identity"].includes(e);case"bins":return!["point","band","identity","ordinal"].includes(e);case"round":return Xe(e)||e==="band"||e==="point";case"padding":case"rangeMin":case"rangeMax":return Xe(e)||["point","band"].includes(e);case"paddingOuter":case"align":return["point","band"].includes(e);case"paddingInner":return e==="band";case"domainMax":case"domainMid":case"domainMin":case"clamp":return Xe(e);case"nice":return Xe(e)||e==="quantize"||e==="threshold";case"exponent":return e==="pow";case"base":return e==="log";case"constant":return e==="symlog";case"zero":return ze(e)&&!q(["log","time","utc","threshold","quantile"],e)}}function nf(e,t){switch(t){case"interpolate":case"scheme":case"domainMid":return qn(e)?void 0:Dh(t);case"align":case"type":case"bins":case"domain":case"domainMax":case"domainMin":case"range":case"base":case"exponent":case"constant":case"nice":case"padding":case"paddingInner":case"paddingOuter":case"rangeMax":case"rangeMin":case"reverse":case"round":case"clamp":case"zero":return}}function Bm(e,t){return q([ia,ra],t)?e===void 0||xe(e):t===ei?q([ke.TIME,ke.UTC,void 0],e):t===$n?Zu(e)||ti(e)||e===void 0:!0}function Gm(e,t,n=!1){if(!zt(e))return!1;switch(e){case oe:case ye:case Kt:case pi:case We:case tt:return Xe(t)||t==="band"?!0:t==="point"?!n:!1;case Lt:case Zt:case Pt:case Jt:case Qt:case kn:return Xe(t)||ti(t)||q(["band","point","ordinal"],t);case Ae:case bt:case xt:return t!=="band";case en:case Re:return t==="ordinal"||ti(t)}}const Fe={arc:"arc",area:"area",bar:"bar",image:"image",line:"line",point:"point",rect:"rect",rule:"rule",text:"text",tick:"tick",trail:"trail",circle:"circle",square:"square",geoshape:"geoshape"},rf=Fe.arc,qr=Fe.area,Hr=Fe.bar,qm=Fe.image,Vr=Fe.line,Xr=Fe.point,Hm=Fe.rect,br=Fe.rule,sf=Fe.text,sa=Fe.tick,Vm=Fe.trail,oa=Fe.circle,aa=Fe.square,of=Fe.geoshape;function rn(e){return["line","area","trail"].includes(e)}function ca(e){return["rect","bar","image","arc"].includes(e)}const Xm=new Set(v(Fe));function gt(e){return e.type}const Ym=["stroke","strokeWidth","strokeDash","strokeDashOffset","strokeOpacity","strokeJoin","strokeMiterLimit"],Km=["fill","fillOpacity"],Jm=[...Ym,...Km],Qm={color:1,filled:1,invalid:1,order:1,radius2:1,theta2:1,timeUnitBandSize:1,timeUnitBandPosition:1},Cc=v(Qm),Zm={area:["line","point"],bar:["binSpacing","continuousBandSize","discreteBandSize","minBandSize"],rect:["binSpacing","continuousBandSize","discreteBandSize","minBandSize"],line:["point"],tick:["bandSize","thickness"]},ey={color:"#4c78a8",invalid:"filter",timeUnitBandSize:1},ty={mark:1,arc:1,area:1,bar:1,circle:1,image:1,line:1,point:1,rect:1,rule:1,square:1,text:1,tick:1,trail:1,geoshape:1},af=v(ty);function wn(e){return e&&e.band!=null}const ny={horizontal:["cornerRadiusTopRight","cornerRadiusBottomRight"],vertical:["cornerRadiusTopLeft","cornerRadiusTopRight"]},cf=5,iy={binSpacing:1,continuousBandSize:cf,minBandSize:.25,timeUnitBandPosition:.5},ry={binSpacing:0,continuousBandSize:cf,minBandSize:.25,timeUnitBandPosition:.5},sy={thickness:1};function oy(e){return gt(e)?e.type:e}function la(e){const{channel:t,channelDef:n,markDef:i,scale:r,config:s}=e,o=fa(e);return E(n)&&!Eu(n.aggregate)&&r&&Xe(r.get("type"))?ay({fieldDef:n,channel:t,markDef:i,ref:o,config:s}):o}function ay({fieldDef:e,channel:t,markDef:n,ref:i,config:r}){return rn(n.type)?i:K("invalid",n,r)===null?[cy(e,t),i]:i}function cy(e,t){const n=ua(e,!0),r=On(t)==="y"?{field:{group:"height"}}:{value:0};return{test:n,...r}}function ua(e,t=!0){return na(z(e)?e:C(e,{expr:"datum"}),!t)}function ly(e){const{datum:t}=e;return Rn(t)?En(t):`${te(t)}`}function hn(e,t,n,i){const r={};if(t&&(r.scale=t),Et(e)){const{datum:s}=e;Rn(s)?r.signal=En(s):R(s)?r.signal=s.signal:Hi(s)?r.signal=s.expr:r.value=s}else r.field=C(e,n);if(i){const{offset:s,band:o}=i;s&&(r.offset=s),o&&(r.band=o)}return r}function xr({scaleName:e,fieldOrDatumDef:t,fieldOrDatumDef2:n,offset:i,startSuffix:r,bandPosition:s=.5}){const o=0<s&&s<1?"datum":void 0,a=C(t,{expr:o,suffix:r}),c=n!==void 0?C(n,{expr:o}):C(t,{suffix:"end",expr:o}),l={};if(s===0||s===1){l.scale=e;const u=s===0?a:c;l.field=u}else{const u=R(s)?`${s.signal} * ${a} + (1-${s.signal}) * ${c}`:`${s} * ${a} + ${1-s} * ${c}`;l.signal=`scale("${e}", ${u})`}return i&&(l.offset=i),l}function uy({scaleName:e,fieldDef:t}){const n=C(t,{expr:"datum"}),i=C(t,{expr:"datum",suffix:"end"});return`abs(scale("${e}", ${i}) - scale("${e}", ${n}))`}function fa({channel:e,channelDef:t,channel2Def:n,markDef:i,config:r,scaleName:s,scale:o,stack:a,offset:c,defaultRef:l,bandPosition:u}){if(t){if(M(t)){const f=o==null?void 0:o.get("type");if(Ne(t)){u??(u=mf({fieldDef:t,fieldDef2:n,markDef:i,config:r}));const{bin:d,timeUnit:p,type:g}=t;if(ne(d)||u&&p&&g===ei)return a!=null&&a.impute?hn(t,s,{binSuffix:"mid"},{offset:c}):u&&!xe(f)?xr({scaleName:s,fieldOrDatumDef:t,bandPosition:u,offset:c}):hn(t,s,Ji(t,e)?{binSuffix:"range"}:{},{offset:c});if(ve(d)){if(E(n))return xr({scaleName:s,fieldOrDatumDef:t,fieldOrDatumDef2:n,bandPosition:u,offset:c});S(zu(e===oe?et:yt))}}return hn(t,s,xe(f)?{binSuffix:"range"}:{},{offset:c,band:f==="band"?u??t.bandPosition??.5:void 0})}else if(Qe(t)){const f=t.value,d=c?{offset:c}:{};return{...Ri(e,f),...d}}}return Gp(l)&&(l=l()),l&&{...l,...c?{offset:c}:{}}}function Ri(e,t){return q(["x","x2"],e)&&t==="width"?{field:{group:"width"}}:q(["y","y2"],e)&&t==="height"?{field:{group:"height"}}:re(t)}function Cn(e){return e&&e!=="number"&&e!=="time"}function lf(e,t,n){return`${e}(${t}${n?`, ${te(n)}`:""})`}const fy=" – ";function da({fieldOrDatumDef:e,format:t,formatType:n,expr:i,normalizeStack:r,config:s}){var c,l;if(Cn(n))return Ye({fieldOrDatumDef:e,format:t,formatType:n,expr:i,config:s});const o=uf(e,i,r),a=ni(e);if(t===void 0&&n===void 0&&s.customFormatTypes){if(a==="quantitative"){if(r&&s.normalizedNumberFormatType)return Ye({fieldOrDatumDef:e,format:s.normalizedNumberFormat,formatType:s.normalizedNumberFormatType,expr:i,config:s});if(s.numberFormatType)return Ye({fieldOrDatumDef:e,format:s.numberFormat,formatType:s.numberFormatType,expr:i,config:s})}if(a==="temporal"&&s.timeFormatType&&E(e)&&e.timeUnit===void 0)return Ye({fieldOrDatumDef:e,format:s.timeFormat,formatType:s.timeFormatType,expr:i,config:s})}if(ri(e)){const u=py({field:o,timeUnit:E(e)?(c=me(e.timeUnit))==null?void 0:c.unit:void 0,format:t,formatType:s.timeFormatType,rawTimeFormat:s.timeFormat,isUTCScale:Ln(e)&&((l=e.scale)==null?void 0:l.type)===ke.UTC});return u?{signal:u}:void 0}if(t=so({type:a,specifiedFormat:t,config:s,normalizeStack:r}),E(e)&&ne(e.bin)){const u=C(e,{expr:i,binSuffix:"end"});return{signal:Xi(o,u,t,n,s)}}else return t||ni(e)==="quantitative"?{signal:`${pf(o,t)}`}:{signal:`isValid(${o}) ? ${o} : ""+${o}`}}function uf(e,t,n){return E(e)?n?`${C(e,{expr:t,suffix:"end"})}-${C(e,{expr:t,suffix:"start"})}`:C(e,{expr:t}):ly(e)}function Ye({fieldOrDatumDef:e,format:t,formatType:n,expr:i,normalizeStack:r,config:s,field:o}){if(o??(o=uf(e,i,r)),o!=="datum.value"&&E(e)&&ne(e.bin)){const a=C(e,{expr:i,binSuffix:"end"});return{signal:Xi(o,a,t,n,s)}}return{signal:lf(n,o,t)}}function ff(e,t,n,i,r,s){var o;if(!(z(i)&&Cn(i))&&!(n===void 0&&i===void 0&&r.customFormatTypes&&ni(e)==="quantitative"&&(r.normalizedNumberFormatType&&ii(e)&&e.stack==="normalize"||r.numberFormatType))){if(ii(e)&&e.stack==="normalize"&&r.normalizedNumberFormat)return so({type:"quantitative",config:r,normalizeStack:!0});if(ri(e)){const a=E(e)?(o=me(e.timeUnit))==null?void 0:o.unit:void 0;return a===void 0&&r.customFormatTypes&&r.timeFormatType?void 0:dy({specifiedFormat:n,timeUnit:a,config:r,omitTimeFormatConfig:s})}return so({type:t,specifiedFormat:n,config:r})}}function df(e,t,n){var i;if(e&&(R(e)||e==="number"||e==="time"))return e;if(ri(t)&&n!=="time"&&n!=="utc")return E(t)&&((i=me(t==null?void 0:t.timeUnit))!=null&&i.utc)?"utc":"time"}function so({type:e,specifiedFormat:t,config:n,normalizeStack:i}){if(z(t))return t;if(e===$n)return i?n.normalizedNumberFormat:n.numberFormat}function dy({specifiedFormat:e,timeUnit:t,config:n,omitTimeFormatConfig:i}){return e||(t?{signal:qu(t)}:i?void 0:n.timeFormat)}function pf(e,t){return`format(${e}, "${t||""}")`}function Nc(e,t,n,i){return Cn(n)?lf(n,e,t):pf(e,(z(t)?t:void 0)??i.numberFormat)}function Xi(e,t,n,i,r){if(n===void 0&&i===void 0&&r.customFormatTypes&&r.numberFormatType)return Xi(e,t,r.numberFormat,r.numberFormatType,r);const s=Nc(e,n,i,r),o=Nc(t,n,i,r);return`${na(e,!1)} ? "null" : ${s} + "${fy}" + ${o}`}function py({field:e,timeUnit:t,format:n,formatType:i,rawTimeFormat:r,isUTCScale:s}){return!t||n?!t&&i?`${i}(${e}, '${n}')`:(n=z(n)?n:r,`${s?"utc":"time"}Format(${e}, '${n}')`):Em(t,e,s)}const Yr="min",gy={x:1,y:1,color:1,fill:1,stroke:1,strokeWidth:1,size:1,shape:1,fillOpacity:1,strokeOpacity:1,opacity:1,text:1};function Fc(e){return e in gy}function gf(e){return!!(e!=null&&e.encoding)}function ft(e){return e&&(e.op==="count"||!!e.field)}function hf(e){return e&&O(e)}function Yi(e){return"row"in e||"column"in e}function pa(e){return!!e&&"header"in e}function Kr(e){return"facet"in e}function hy(e){return e.param}function my(e){return e&&!z(e)&&"repeat"in e}function _c(e){const{field:t,timeUnit:n,bin:i,aggregate:r}=e;return{...n?{timeUnit:n}:{},...i?{bin:i}:{},...r?{aggregate:r}:{},field:t}}function ga(e){return"sort"in e}function mf({fieldDef:e,fieldDef2:t,markDef:n,config:i}){if(M(e)&&e.bandPosition!==void 0)return e.bandPosition;if(E(e)){const{timeUnit:r,bin:s}=e;if(r&&!t)return ca(n.type)?0:At("timeUnitBandPosition",n,i);if(ne(s))return .5}}function yf({channel:e,fieldDef:t,fieldDef2:n,markDef:i,config:r,scaleType:s,useVlSizeChannel:o}){var l,u,f;const a=Ie(e),c=K(o?"size":a,i,r,{vgChannel:a});if(c!==void 0)return c;if(E(t)){const{timeUnit:d,bin:p}=t;if(d&&!n)return{band:At("timeUnitBandSize",i,r)};if(ne(p)&&!xe(s))return{band:1}}if(ca(i.type))return s?xe(s)?((l=r[i.type])==null?void 0:l.discreteBandSize)||{band:1}:(u=r[i.type])==null?void 0:u.continuousBandSize:(f=r[i.type])==null?void 0:f.discreteBandSize}function bf(e,t,n,i){return ne(e.bin)||e.timeUnit&&Ne(e)&&e.type==="temporal"?mf({fieldDef:e,fieldDef2:t,markDef:n,config:i})!==void 0:!1}function xf(e){return e&&!!e.sort&&!e.field}function Jr(e){return e&&"condition"in e}function Qr(e){const t=e==null?void 0:e.condition;return!!t&&!O(t)&&E(t)}function Ki(e){const t=e==null?void 0:e.condition;return!!t&&!O(t)&&M(t)}function yy(e){const t=e==null?void 0:e.condition;return!!t&&(O(t)||Qe(t))}function E(e){return e&&(!!e.field||e.aggregate==="count")}function ni(e){return e==null?void 0:e.type}function Et(e){return e&&"datum"in e}function Wt(e){return Ne(e)&&!Sr(e)||vr(e)}function Tc(e){return Ne(e)&&e.type==="quantitative"&&!e.bin||vr(e)}function vr(e){return Et(e)&&ue(e.datum)}function M(e){return E(e)||Et(e)}function Ne(e){return e&&("field"in e||e.aggregate==="count")&&"type"in e}function Qe(e){return e&&"value"in e&&"value"in e}function Ln(e){return e&&("scale"in e||"sort"in e)}function ii(e){return e&&("axis"in e||"stack"in e||"impute"in e)}function vf(e){return e&&"legend"in e}function Sf(e){return e&&("format"in e||"formatType"in e)}function by(e){return Te(e,["legend","axis","header","scale"])}function xy(e){return"op"in e}function C(e,t={}){let n=e.field;const i=t.prefix;let r=t.suffix,s="";if(Sy(e))n=au("count");else{let o;if(!t.nofn)if(xy(e))o=e.op;else{const{bin:a,aggregate:c,timeUnit:l}=e;ne(a)?(o=$u(a),r=(t.binSuffix??"")+(t.suffix??"")):c?tn(c)?(s=`["${n}"]`,n=`argmax_${c.argmax}`):Ot(c)?(s=`["${n}"]`,n=`argmin_${c.argmin}`):o=String(c):l&&!In(l)&&(o=$m(l),r=(!["range","mid"].includes(t.binSuffix)&&t.binSuffix||"")+(t.suffix??""))}o&&(n=n?`${o}_${n}`:o)}return r&&(n=`${n}_${r}`),i&&(n=`${i}_${n}`),t.forAs?Lo(n):t.expr?ru(n,t.expr)+s:Me(n)+s}function Sr(e){switch(e.type){case"nominal":case"ordinal":case"geojson":return!0;case"quantitative":return E(e)&&!!e.bin;case"temporal":return!1}throw new Error(Ru(e.type))}function vy(e){var t;return Ln(e)&&ti((t=e.scale)==null?void 0:t.type)}function Sy(e){return e.aggregate==="count"}function Ey(e,t){var o;const{field:n,bin:i,timeUnit:r,aggregate:s}=e;if(s==="count")return t.countTitle;if(ne(i))return`${n} (binned)`;if(r&&!In(r)){const a=(o=me(r))==null?void 0:o.unit;if(a)return`${n} (${Gr(a).join("-")})`}else if(s)return tn(s)?`${n} for max ${s.argmax}`:Ot(s)?`${n} for min ${s.argmin}`:`${Bi(s)} of ${n}`;return n}function $y(e){const{aggregate:t,bin:n,timeUnit:i,field:r}=e;if(tn(t))return`${r} for argmax(${t.argmax})`;if(Ot(t))return`${r} for argmin(${t.argmin})`;const s=i&&!In(i)?me(i):void 0,o=t||(s==null?void 0:s.unit)||(s==null?void 0:s.maxbins)&&"timeunit"||ne(n)&&"bin";return o?`${o.toUpperCase()}(${r})`:r}const Ef=(e,t)=>{switch(t.fieldTitle){case"plain":return e.field;case"functional":return $y(e);default:return Ey(e,t)}};let $f=Ef;function wf(e){$f=e}function wy(){wf(Ef)}function Hn(e,t,{allowDisabling:n,includeDefault:i=!0}){var a;const r=(a=ha(e))==null?void 0:a.title;if(!E(e))return r??e.title;const s=e,o=i?ma(s,t):void 0;return n?fe(r,s.title,o):r??s.title??o}function ha(e){if(ii(e)&&e.axis)return e.axis;if(vf(e)&&e.legend)return e.legend;if(pa(e)&&e.header)return e.header}function ma(e,t){return $f(e,t)}function Er(e){if(Sf(e)){const{format:t,formatType:n}=e;return{format:t,formatType:n}}else{const t=ha(e)??{},{format:n,formatType:i}=t;return{format:n,formatType:i}}}function Cy(e,t){var s;switch(t){case"latitude":case"longitude":return"quantitative";case"row":case"column":case"facet":case"shape":case"strokeDash":return"nominal";case"order":return"ordinal"}if(ga(e)&&O(e.sort))return"ordinal";const{aggregate:n,bin:i,timeUnit:r}=e;if(r)return"temporal";if(i||n&&!tn(n)&&!Ot(n))return"quantitative";if(Ln(e)&&((s=e.scale)!=null&&s.type))switch(io[e.scale.type]){case"numeric":case"discretizing":return"quantitative";case"time":return"temporal"}return"nominal"}function ht(e){if(E(e))return e;if(Qr(e))return e.condition}function de(e){if(M(e))return e;if(Ki(e))return e.condition}function Cf(e,t,n,i={}){if(z(e)||ue(e)||fi(e)){const r=z(e)?"string":ue(e)?"number":"boolean";return S(Ch(t,r,e)),{value:e}}return M(e)?$r(e,t,n,i):Ki(e)?{...e,condition:$r(e.condition,t,n,i)}:e}function $r(e,t,n,i){if(Sf(e)){const{format:r,formatType:s,...o}=e;if(Cn(s)&&!n.customFormatTypes)return S(Sc(t)),$r(o,t,n,i)}else{const r=ii(e)?"axis":vf(e)?"legend":pa(e)?"header":null;if(r&&e[r]){const{format:s,formatType:o,...a}=e[r];if(Cn(o)&&!n.customFormatTypes)return S(Sc(t)),$r({...e,[r]:a},t,n,i)}}return E(e)?ya(e,t,i):Ny(e)}function Ny(e){let t=e.type;if(t)return e;const{datum:n}=e;return t=ue(n)?"quantitative":z(n)?"nominal":Rn(n)?"temporal":void 0,{...e,type:t}}function ya(e,t,{compositeMark:n=!1}={}){const{aggregate:i,timeUnit:r,bin:s,field:o}=e,a={...e};if(!n&&i&&!Bo(i)&&!tn(i)&&!Ot(i)&&(S(Fh(i)),delete a.aggregate),r&&(a.timeUnit=me(r)),o&&(a.field=`${o}`),ne(s)&&(a.bin=Zr(s,t)),ve(s)&&!be(t)&&S(om(t)),Ne(a)){const{type:c}=a,l=Om(c);c!==l&&(a.type=l),c!=="quantitative"&&Eu(i)&&(S(Nh(c,i)),a.type="quantitative")}else if(!hu(t)){const c=Cy(a,t);a.type=c}if(Ne(a)){const{compatible:c,warning:l}=Fy(a,t)||{};c===!1&&S(l)}if(ga(a)&&z(a.sort)){const{sort:c}=a;if(Fc(c))return{...a,sort:{encoding:c}};const l=c.substr(1);if(c.charAt(0)==="-"&&Fc(l))return{...a,sort:{encoding:l,order:"descending"}}}if(pa(a)){const{header:c}=a;if(c){const{orient:l,...u}=c;if(l)return{...a,header:{...u,labelOrient:c.labelOrient||l,titleOrient:c.titleOrient||l}}}}return a}function Zr(e,t){return fi(e)?{maxbins:dc(t)}:e==="binned"?{binned:!0}:!e.maxbins&&!e.step?{...e,maxbins:dc(t)}:e}const Dn={compatible:!0};function Fy(e,t){const n=e.type;if(n==="geojson"&&t!=="shape")return{compatible:!1,warning:`Channel ${t} should not be used with a geojson data.`};switch(t){case Ft:case _t:case Lr:return Sr(e)?Dn:{compatible:!1,warning:Ah(t)};case oe:case ye:case Kt:case pi:case Ae:case bt:case xt:case Gi:case qi:case Pr:case Sn:case zr:case Dr:case kn:case We:case tt:case jr:return Dn;case it:case Ue:case nt:case rt:return n!==$n?{compatible:!1,warning:`Channel ${t} should be used with a quantitative field only, not ${e.type} field.`}:Dn;case Pt:case Jt:case Qt:case Zt:case Lt:case It:case Rt:case et:case yt:return n==="nominal"&&!e.sort?{compatible:!1,warning:`Channel ${t} should not be used with an unsorted discrete field.`}:Dn;case Re:case en:return!Sr(e)&&!vy(e)?{compatible:!1,warning:Rh(t)}:Dn;case Qn:return e.type==="nominal"&&!("sort"in e)?{compatible:!1,warning:"Channel order is inappropriate for nominal field, which has no inherent order."}:Dn}}function ri(e){const{formatType:t}=Er(e);return t==="time"||!t&&_y(e)}function _y(e){return e&&(e.type==="temporal"||E(e)&&!!e.timeUnit)}function es(e,{timeUnit:t,type:n,wrapTime:i,undefinedIfExprNotRequired:r}){var c;const s=t&&((c=me(t))==null?void 0:c.unit);let o=s||n==="temporal",a;return Hi(e)?a=e.expr:R(e)?a=e.signal:Rn(e)?(o=!0,a=En(e)):(z(e)||ue(e))&&o&&(a=`datetime(${te(e)})`,bm(s)&&(ue(e)&&e<1e4||z(e)&&isNaN(Date.parse(e)))&&(a=En({[s]:e}))),a?i&&o?`time(${a})`:a:r?void 0:te(e)}function Nf(e,t){const{type:n}=e;return t.map(i=>{const r=E(e)&&!In(e.timeUnit)?e.timeUnit:void 0,s=es(i,{timeUnit:r,type:n,undefinedIfExprNotRequired:!0});return s!==void 0?{signal:s}:i})}function Ji(e,t){return ne(e.bin)?zt(t)&&["ordinal","nominal"].includes(e.type):(console.warn("Only call this method for binned field defs."),!1)}const kc={labelAlign:{part:"labels",vgProp:"align"},labelBaseline:{part:"labels",vgProp:"baseline"},labelColor:{part:"labels",vgProp:"fill"},labelFont:{part:"labels",vgProp:"font"},labelFontSize:{part:"labels",vgProp:"fontSize"},labelFontStyle:{part:"labels",vgProp:"fontStyle"},labelFontWeight:{part:"labels",vgProp:"fontWeight"},labelOpacity:{part:"labels",vgProp:"opacity"},labelOffset:null,labelPadding:null,gridColor:{part:"grid",vgProp:"stroke"},gridDash:{part:"grid",vgProp:"strokeDash"},gridDashOffset:{part:"grid",vgProp:"strokeDashOffset"},gridOpacity:{part:"grid",vgProp:"opacity"},gridWidth:{part:"grid",vgProp:"strokeWidth"},tickColor:{part:"ticks",vgProp:"stroke"},tickDash:{part:"ticks",vgProp:"strokeDash"},tickDashOffset:{part:"ticks",vgProp:"strokeDashOffset"},tickOpacity:{part:"ticks",vgProp:"opacity"},tickSize:null,tickWidth:{part:"ticks",vgProp:"strokeWidth"}};function Qi(e){return e==null?void 0:e.condition}const Ff=["domain","grid","labels","ticks","title"],Ty={grid:"grid",gridCap:"grid",gridColor:"grid",gridDash:"grid",gridDashOffset:"grid",gridOpacity:"grid",gridScale:"grid",gridWidth:"grid",orient:"main",bandPosition:"both",aria:"main",description:"main",domain:"main",domainCap:"main",domainColor:"main",domainDash:"main",domainDashOffset:"main",domainOpacity:"main",domainWidth:"main",format:"main",formatType:"main",labelAlign:"main",labelAngle:"main",labelBaseline:"main",labelBound:"main",labelColor:"main",labelFlush:"main",labelFlushOffset:"main",labelFont:"main",labelFontSize:"main",labelFontStyle:"main",labelFontWeight:"main",labelLimit:"main",labelLineHeight:"main",labelOffset:"main",labelOpacity:"main",labelOverlap:"main",labelPadding:"main",labels:"main",labelSeparation:"main",maxExtent:"main",minExtent:"main",offset:"both",position:"main",tickCap:"main",tickColor:"main",tickDash:"main",tickDashOffset:"main",tickMinStep:"both",tickOffset:"both",tickOpacity:"main",tickRound:"both",ticks:"main",tickSize:"main",tickWidth:"both",title:"main",titleAlign:"main",titleAnchor:"main",titleAngle:"main",titleBaseline:"main",titleColor:"main",titleFont:"main",titleFontSize:"main",titleFontStyle:"main",titleFontWeight:"main",titleLimit:"main",titleLineHeight:"main",titleOpacity:"main",titlePadding:"main",titleX:"main",titleY:"main",encode:"both",scale:"both",tickBand:"both",tickCount:"both",tickExtra:"both",translate:"both",values:"both",zindex:"both"},_f={orient:1,aria:1,bandPosition:1,description:1,domain:1,domainCap:1,domainColor:1,domainDash:1,domainDashOffset:1,domainOpacity:1,domainWidth:1,format:1,formatType:1,grid:1,gridCap:1,gridColor:1,gridDash:1,gridDashOffset:1,gridOpacity:1,gridWidth:1,labelAlign:1,labelAngle:1,labelBaseline:1,labelBound:1,labelColor:1,labelFlush:1,labelFlushOffset:1,labelFont:1,labelFontSize:1,labelFontStyle:1,labelFontWeight:1,labelLimit:1,labelLineHeight:1,labelOffset:1,labelOpacity:1,labelOverlap:1,labelPadding:1,labels:1,labelSeparation:1,maxExtent:1,minExtent:1,offset:1,position:1,tickBand:1,tickCap:1,tickColor:1,tickCount:1,tickDash:1,tickDashOffset:1,tickExtra:1,tickMinStep:1,tickOffset:1,tickOpacity:1,tickRound:1,ticks:1,tickSize:1,tickWidth:1,title:1,titleAlign:1,titleAnchor:1,titleAngle:1,titleBaseline:1,titleColor:1,titleFont:1,titleFontSize:1,titleFontStyle:1,titleFontWeight:1,titleLimit:1,titleLineHeight:1,titleOpacity:1,titlePadding:1,titleX:1,titleY:1,translate:1,values:1,zindex:1},ky={..._f,style:1,labelExpr:1,encoding:1};function Oc(e){return!!ky[e]}const Oy={axis:1,axisBand:1,axisBottom:1,axisDiscrete:1,axisLeft:1,axisPoint:1,axisQuantitative:1,axisRight:1,axisTemporal:1,axisTop:1,axisX:1,axisXBand:1,axisXDiscrete:1,axisXPoint:1,axisXQuantitative:1,axisXTemporal:1,axisY:1,axisYBand:1,axisYDiscrete:1,axisYPoint:1,axisYQuantitative:1,axisYTemporal:1},Tf=v(Oy);function Dt(e){return"mark"in e}class ts{constructor(t,n){this.name=t,this.run=n}hasMatchingType(t){return Dt(t)?oy(t.mark)===this.name:!1}}function mn(e,t){const n=e&&e[t];return n?O(n)?xn(n,i=>!!i.field):E(n)||Qr(n):!1}function kf(e,t){const n=e&&e[t];return n?O(n)?xn(n,i=>!!i.field):E(n)||Et(n)||Ki(n):!1}function oo(e,t){if(be(t)){const n=e[t];if((E(n)||Et(n))&&(Ku(n.type)||E(n)&&n.timeUnit)){const i=jo(t);return kf(e,i)}}return!1}function ba(e){return xn(Ag,t=>{if(mn(e,t)){const n=e[t];if(O(n))return xn(n,i=>!!i.aggregate);{const i=ht(n);return i&&!!i.aggregate}}return!1})}function Of(e,t){const n=[],i=[],r=[],s=[],o={};return xa(e,(a,c)=>{if(E(a)){const{field:l,aggregate:u,bin:f,timeUnit:d,...p}=a;if(u||d||f){const g=ha(a),h=g==null?void 0:g.title;let m=C(a,{forAs:!0});const y={...h?[]:{title:Hn(a,t,{allowDisabling:!0})},...p,field:m};if(u){let b;if(tn(u)?(b="argmax",m=C({op:"argmax",field:u.argmax},{forAs:!0}),y.field=`${m}.${l}`):Ot(u)?(b="argmin",m=C({op:"argmin",field:u.argmin},{forAs:!0}),y.field=`${m}.${l}`):u!=="boxplot"&&u!=="errorbar"&&u!=="errorband"&&(b=u),b){const N={op:b,as:m};l&&(N.field=l),s.push(N)}}else if(n.push(m),Ne(a)&&ne(f)){if(i.push({bin:f,field:l,as:m}),n.push(C(a,{binSuffix:"end"})),Ji(a,c)&&n.push(C(a,{binSuffix:"range"})),be(c)){const b={field:`${m}_end`};o[`${c}2`]=b}y.bin="binned",hu(c)||(y.type=$n)}else if(d&&!In(d)){r.push({timeUnit:d,field:l,as:m});const b=Ne(a)&&a.type!==ei&&"time";b&&(c===Gi||c===Sn?y.formatType=b:Ug(c)?y.legend={formatType:b,...y.legend}:be(c)&&(y.axis={formatType:b,...y.axis}))}o[c]=y}else n.push(l),o[c]=e[c]}else o[c]=e[c]}),{bins:i,timeUnits:r,aggregate:s,groupby:n,encoding:o}}function Ay(e,t,n){const i=Bg(t,n);if(i){if(i==="binned"){const r=e[t===et?oe:ye];return!!(E(r)&&E(e[t])&&ve(r.bin))}}else return!1;return!0}function Ry(e,t,n,i){const r={};for(const s of v(e))gu(s)||S(Oh(s));for(let s of zg){if(!e[s])continue;const o=e[s];if(gi(s)){const a=yu(s),c=r[a];if(E(c)){if(km(c.type)&&E(o)&&!c.timeUnit){S($h(a));continue}}else s=a,S(wh(a))}if(s==="angle"&&t==="arc"&&!e.theta&&(S(Eh),s=We),!Ay(e,s,t)){S(Br(s,t));continue}if(s===Lt&&t==="line"){const a=ht(e[s]);if(a!=null&&a.aggregate){S(Th);continue}}if(s===Ae&&(n?"fill"in e:"stroke"in e)){S(Iu("encoding",{fill:"fill"in e,stroke:"stroke"in e}));continue}if(s===qi||s===Qn&&!O(o)&&!Qe(o)||s===Sn&&O(o)){if(o){if(s===Qn){const a=e[s];if(xf(a)){r[s]=a;continue}}r[s]=ce(o).reduce((a,c)=>(E(c)?a.push(ya(c,s)):S(no(c,s)),a),[])}}else{if(s===Sn&&o===null)r[s]=null;else if(!E(o)&&!Et(o)&&!Qe(o)&&!Jr(o)&&!R(o)){S(no(o,s));continue}r[s]=Cf(o,s,i)}}return r}function ns(e,t){const n={};for(const i of v(e)){const r=Cf(e[i],i,t,{compositeMark:!0});n[i]=r}return n}function Iy(e){const t=[];for(const n of v(e))if(mn(e,n)){const i=e[n],r=ce(i);for(const s of r)E(s)?t.push(s):Qr(s)&&t.push(s.condition)}return t}function xa(e,t,n){if(e)for(const i of v(e)){const r=e[i];if(O(r))for(const s of r)t.call(n,s,i);else t.call(n,r,i)}}function Ly(e,t,n,i){return e?v(e).reduce((r,s)=>{const o=e[s];return O(o)?o.reduce((a,c)=>t.call(i,a,c,s),r):t.call(i,r,o,s)},n):n}function Af(e,t){return v(t).reduce((n,i)=>{switch(i){case oe:case ye:case zr:case jr:case Dr:case et:case yt:case Kt:case pi:case We:case It:case tt:case Rt:case nt:case it:case rt:case Ue:case Gi:case Re:case kn:case Sn:return n;case Qn:if(e==="line"||e==="trail")return n;case qi:case Pr:{const r=t[i];if(O(r)||E(r))for(const s of ce(r))s.aggregate||n.push(C(s,{}));return n}case Lt:if(e==="trail")return n;case Ae:case bt:case xt:case Pt:case Jt:case Qt:case en:case Zt:{const r=ht(t[i]);return r&&!r.aggregate&&n.push(C(r,{})),n}}},[])}function Py(e){const{tooltip:t,...n}=e;if(!t)return{filteredEncoding:n};let i,r;if(O(t)){for(const s of t)s.aggregate?(i||(i=[]),i.push(s)):(r||(r=[]),r.push(s));i&&(n.tooltip=i)}else t.aggregate?n.tooltip=t:r=t;return O(r)&&r.length===1&&(r=r[0]),{customTooltipWithoutAggregatedField:r,filteredEncoding:n}}function ao(e,t,n,i=!0){if("tooltip"in n)return{tooltip:n.tooltip};const r=e.map(({fieldPrefix:o,titlePrefix:a})=>{const c=i?` of ${va(t)}`:"";return{field:o+t.field,type:t.type,title:R(a)?{signal:`${a}"${escape(c)}"`}:a+c}}),s=Iy(n).map(by);return{tooltip:[...r,...ut(s,G)]}}function va(e){const{title:t,field:n}=e;return fe(t,n)}function Sa(e,t,n,i,r){const{scale:s,axis:o}=n;return({partName:a,mark:c,positionPrefix:l,endPositionPrefix:u=void 0,extraEncoding:f={}})=>{const d=va(n);return Rf(e,a,r,{mark:c,encoding:{[t]:{field:`${l}_${n.field}`,type:n.type,...d!==void 0?{title:d}:{},...s!==void 0?{scale:s}:{},...o!==void 0?{axis:o}:{}},...z(u)?{[`${t}2`]:{field:`${u}_${n.field}`}}:{},...i,...f}})}}function Rf(e,t,n,i){const{clip:r,color:s,opacity:o}=e,a=e.type;return e[t]||e[t]===void 0&&n[t]?[{...i,mark:{...n[t],...r?{clip:r}:{},...s?{color:s}:{},...o?{opacity:o}:{},...gt(i.mark)?i.mark:{type:i.mark},style:`${a}-${String(t)}`,...fi(e[t])?{}:e[t]}}]:[]}function If(e,t,n){const{encoding:i}=e,r=t==="vertical"?"y":"x",s=i[r],o=i[`${r}2`],a=i[`${r}Error`],c=i[`${r}Error2`];return{continuousAxisChannelDef:ir(s,n),continuousAxisChannelDef2:ir(o,n),continuousAxisChannelDefError:ir(a,n),continuousAxisChannelDefError2:ir(c,n),continuousAxis:r}}function ir(e,t){if(e!=null&&e.aggregate){const{aggregate:n,...i}=e;return n!==t&&S(sm(n,t)),i}else return e}function Lf(e,t){const{mark:n,encoding:i}=e,{x:r,y:s}=i;if(gt(n)&&n.orient)return n.orient;if(Wt(r)){if(Wt(s)){const o=E(r)&&r.aggregate,a=E(s)&&s.aggregate;if(!o&&a===t)return"vertical";if(!a&&o===t)return"horizontal";if(o===t&&a===t)throw new Error("Both x and y cannot have aggregate");return ri(s)&&!ri(r)?"horizontal":"vertical"}return"horizontal"}else{if(Wt(s))return"vertical";throw new Error(`Need a valid continuous axis for ${t}s`)}}const wr="boxplot",zy=["box","median","outliers","rule","ticks"],Dy=new ts(wr,zf);function Pf(e){return ue(e)?"tukey":e}function zf(e,{config:t}){e={...e,encoding:ns(e.encoding,t)};const{mark:n,encoding:i,params:r,projection:s,...o}=e,a=gt(n)?n:{type:n};r&&S(Au("boxplot"));const c=a.extent??t.boxplot.extent,l=K("size",a,t),u=a.invalid,f=Pf(c),{bins:d,timeUnits:p,transform:g,continuousAxisChannelDef:h,continuousAxis:m,groupby:y,aggregate:b,encodingWithoutContinuousAxis:N,ticksOrient:P,boxOrient:x,customTooltipWithoutAggregatedField:_}=jy(e,c,t),{color:w,size:T,...W}=N,X=kp=>Sa(a,m,h,kp,t.boxplot),le=X(W),pe=X(N),F=X({...W,...T?{size:T}:{}}),$=ao([{fieldPrefix:f==="min-max"?"upper_whisker_":"max_",titlePrefix:"Max"},{fieldPrefix:"upper_box_",titlePrefix:"Q3"},{fieldPrefix:"mid_box_",titlePrefix:"Median"},{fieldPrefix:"lower_box_",titlePrefix:"Q1"},{fieldPrefix:f==="min-max"?"lower_whisker_":"min_",titlePrefix:"Min"}],h,N),I={type:"tick",color:"black",opacity:1,orient:P,invalid:u,aria:!1},A=f==="min-max"?$:ao([{fieldPrefix:"upper_whisker_",titlePrefix:"Upper Whisker"},{fieldPrefix:"lower_whisker_",titlePrefix:"Lower Whisker"}],h,N),L=[...le({partName:"rule",mark:{type:"rule",invalid:u,aria:!1},positionPrefix:"lower_whisker",endPositionPrefix:"lower_box",extraEncoding:A}),...le({partName:"rule",mark:{type:"rule",invalid:u,aria:!1},positionPrefix:"upper_box",endPositionPrefix:"upper_whisker",extraEncoding:A}),...le({partName:"ticks",mark:I,positionPrefix:"lower_whisker",extraEncoding:A}),...le({partName:"ticks",mark:I,positionPrefix:"upper_whisker",extraEncoding:A})],k=[...f!=="tukey"?L:[],...pe({partName:"box",mark:{type:"bar",...l?{size:l}:{},orient:x,invalid:u,ariaRoleDescription:"box"},positionPrefix:"lower_box",endPositionPrefix:"upper_box",extraEncoding:$}),...F({partName:"median",mark:{type:"tick",invalid:u,...Y(t.boxplot.median)&&t.boxplot.median.color?{color:t.boxplot.median.color}:{},...l?{size:l}:{},orient:P,aria:!1},positionPrefix:"mid_box",extraEncoding:$})];if(f==="min-max")return{...o,transform:(o.transform??[]).concat(g),layer:k};const D=`datum["lower_box_${h.field}"]`,H=`datum["upper_box_${h.field}"]`,U=`(${H} - ${D})`,Z=`${D} - ${c} * ${U}`,Ee=`${H} + ${c} * ${U}`,ie=`datum["${h.field}"]`,sn={joinaggregate:Df(h.field),groupby:y},$s={transform:[{filter:`(${Z} <= ${ie}) && (${ie} <= ${Ee})`},{aggregate:[{op:"min",field:h.field,as:`lower_whisker_${h.field}`},{op:"max",field:h.field,as:`upper_whisker_${h.field}`},{op:"min",field:`lower_box_${h.field}`,as:`lower_box_${h.field}`},{op:"max",field:`upper_box_${h.field}`,as:`upper_box_${h.field}`},...b],groupby:y}],layer:L},{tooltip:hw,..._p}=W,{scale:sc,axis:Tp}=h,oc=va(h),ac=Te(Tp,["title"]),cc=Rf(a,"outliers",t.boxplot,{transform:[{filter:`(${ie} < ${Z}) || (${ie} > ${Ee})`}],mark:"point",encoding:{[m]:{field:h.field,type:h.type,...oc!==void 0?{title:oc}:{},...sc!==void 0?{scale:sc}:{},...Q(ac)?{}:{axis:ac}},..._p,...w?{color:w}:{},..._?{tooltip:_}:{}}})[0];let nr;const lc=[...d,...p,sn];return cc?nr={transform:lc,layer:[cc,$s]}:(nr=$s,nr.transform.unshift(...lc)),{...o,layer:[nr,{transform:g,layer:k}]}}function Df(e){return[{op:"q1",field:e,as:`lower_box_${e}`},{op:"q3",field:e,as:`upper_box_${e}`}]}function jy(e,t,n){const i=Lf(e,wr),{continuousAxisChannelDef:r,continuousAxis:s}=If(e,i,wr),o=r.field,a=Pf(t),c=[...Df(o),{op:"median",field:o,as:`mid_box_${o}`},{op:"min",field:o,as:(a==="min-max"?"lower_whisker_":"min_")+o},{op:"max",field:o,as:(a==="min-max"?"upper_whisker_":"max_")+o}],l=a==="min-max"||a==="tukey"?[]:[{calculate:`datum["upper_box_${o}"] - datum["lower_box_${o}"]`,as:`iqr_${o}`},{calculate:`min(datum["upper_box_${o}"] + datum["iqr_${o}"] * ${t}, datum["max_${o}"])`,as:`upper_whisker_${o}`},{calculate:`max(datum["lower_box_${o}"] - datum["iqr_${o}"] * ${t}, datum["min_${o}"])`,as:`lower_whisker_${o}`}],{[s]:u,...f}=e.encoding,{customTooltipWithoutAggregatedField:d,filteredEncoding:p}=Py(f),{bins:g,timeUnits:h,aggregate:m,groupby:y,encoding:b}=Of(p,n),N=i==="vertical"?"horizontal":"vertical",P=i,x=[...g,...h,{aggregate:[...m,...c],groupby:y},...l];return{bins:g,timeUnits:h,transform:x,groupby:y,aggregate:m,continuousAxisChannelDef:r,continuousAxis:s,encodingWithoutContinuousAxis:b,ticksOrient:N,boxOrient:P,customTooltipWithoutAggregatedField:d}}const Ea="errorbar",My=["ticks","rule"],Uy=new ts(Ea,jf);function jf(e,{config:t}){e={...e,encoding:ns(e.encoding,t)};const{transform:n,continuousAxisChannelDef:i,continuousAxis:r,encodingWithoutContinuousAxis:s,ticksOrient:o,markDef:a,outerSpec:c,tooltipEncoding:l}=Mf(e,Ea,t);delete s.size;const u=Sa(a,r,i,s,t.errorbar),f=a.thickness,d=a.size,p={type:"tick",orient:o,aria:!1,...f!==void 0?{thickness:f}:{},...d!==void 0?{size:d}:{}},g=[...u({partName:"ticks",mark:p,positionPrefix:"lower",extraEncoding:l}),...u({partName:"ticks",mark:p,positionPrefix:"upper",extraEncoding:l}),...u({partName:"rule",mark:{type:"rule",ariaRoleDescription:"errorbar",...f!==void 0?{size:f}:{}},positionPrefix:"lower",endPositionPrefix:"upper",extraEncoding:l})];return{...c,transform:n,...g.length>1?{layer:g}:{...g[0]}}}function Wy(e,t){const{encoding:n}=e;if(By(n))return{orient:Lf(e,t),inputType:"raw"};const i=Gy(n),r=qy(n),s=n.x,o=n.y;if(i){if(r)throw new Error(`${t} cannot be both type aggregated-upper-lower and aggregated-error`);const a=n.x2,c=n.y2;if(M(a)&&M(c))throw new Error(`${t} cannot have both x2 and y2`);if(M(a)){if(Wt(s))return{orient:"horizontal",inputType:"aggregated-upper-lower"};throw new Error(`Both x and x2 have to be quantitative in ${t}`)}else if(M(c)){if(Wt(o))return{orient:"vertical",inputType:"aggregated-upper-lower"};throw new Error(`Both y and y2 have to be quantitative in ${t}`)}throw new Error("No ranged axis")}else{const a=n.xError,c=n.xError2,l=n.yError,u=n.yError2;if(M(c)&&!M(a))throw new Error(`${t} cannot have xError2 without xError`);if(M(u)&&!M(l))throw new Error(`${t} cannot have yError2 without yError`);if(M(a)&&M(l))throw new Error(`${t} cannot have both xError and yError with both are quantiative`);if(M(a)){if(Wt(s))return{orient:"horizontal",inputType:"aggregated-error"};throw new Error("All x, xError, and xError2 (if exist) have to be quantitative")}else if(M(l)){if(Wt(o))return{orient:"vertical",inputType:"aggregated-error"};throw new Error("All y, yError, and yError2 (if exist) have to be quantitative")}throw new Error("No ranged axis")}}function By(e){return(M(e.x)||M(e.y))&&!M(e.x2)&&!M(e.y2)&&!M(e.xError)&&!M(e.xError2)&&!M(e.yError)&&!M(e.yError2)}function Gy(e){return M(e.x2)||M(e.y2)}function qy(e){return M(e.xError)||M(e.xError2)||M(e.yError)||M(e.yError2)}function Mf(e,t,n){const{mark:i,encoding:r,params:s,projection:o,...a}=e,c=gt(i)?i:{type:i};s&&S(Au(t));const{orient:l,inputType:u}=Wy(e,t),{continuousAxisChannelDef:f,continuousAxisChannelDef2:d,continuousAxisChannelDefError:p,continuousAxisChannelDefError2:g,continuousAxis:h}=If(e,l,t),{errorBarSpecificAggregate:m,postAggregateCalculates:y,tooltipSummary:b,tooltipTitleWithFieldName:N}=Hy(c,f,d,p,g,u,t,n),{[h]:P,[h==="x"?"x2":"y2"]:x,[h==="x"?"xError":"yError"]:_,[h==="x"?"xError2":"yError2"]:w,...T}=r,{bins:W,timeUnits:X,aggregate:le,groupby:pe,encoding:F}=Of(T,n),$=[...le,...m],I=u!=="raw"?[]:pe,A=ao(b,f,F,N);return{transform:[...a.transform??[],...W,...X,...$.length===0?[]:[{aggregate:$,groupby:I}],...y],groupby:I,continuousAxisChannelDef:f,continuousAxis:h,encodingWithoutContinuousAxis:F,ticksOrient:l==="vertical"?"horizontal":"vertical",markDef:c,outerSpec:a,tooltipEncoding:A}}function Hy(e,t,n,i,r,s,o,a){let c=[],l=[];const u=t.field;let f,d=!1;if(s==="raw"){const p=e.center?e.center:e.extent?e.extent==="iqr"?"median":"mean":a.errorbar.center,g=e.extent?e.extent:p==="mean"?"stderr":"iqr";if(p==="median"!=(g==="iqr")&&S(rm(p,g,o)),g==="stderr"||g==="stdev")c=[{op:g,field:u,as:`extent_${u}`},{op:p,field:u,as:`center_${u}`}],l=[{calculate:`datum["center_${u}"] + datum["extent_${u}"]`,as:`upper_${u}`},{calculate:`datum["center_${u}"] - datum["extent_${u}"]`,as:`lower_${u}`}],f=[{fieldPrefix:"center_",titlePrefix:Bi(p)},{fieldPrefix:"upper_",titlePrefix:Ac(p,g,"+")},{fieldPrefix:"lower_",titlePrefix:Ac(p,g,"-")}],d=!0;else{let h,m,y;g==="ci"?(h="mean",m="ci0",y="ci1"):(h="median",m="q1",y="q3"),c=[{op:m,field:u,as:`lower_${u}`},{op:y,field:u,as:`upper_${u}`},{op:h,field:u,as:`center_${u}`}],f=[{fieldPrefix:"upper_",titlePrefix:Hn({field:u,aggregate:y,type:"quantitative"},a,{allowDisabling:!1})},{fieldPrefix:"lower_",titlePrefix:Hn({field:u,aggregate:m,type:"quantitative"},a,{allowDisabling:!1})},{fieldPrefix:"center_",titlePrefix:Hn({field:u,aggregate:h,type:"quantitative"},a,{allowDisabling:!1})}]}}else{(e.center||e.extent)&&S(im(e.center,e.extent)),s==="aggregated-upper-lower"?(f=[],l=[{calculate:`datum["${n.field}"]`,as:`upper_${u}`},{calculate:`datum["${u}"]`,as:`lower_${u}`}]):s==="aggregated-error"&&(f=[{fieldPrefix:"",titlePrefix:u}],l=[{calculate:`datum["${u}"] + datum["${i.field}"]`,as:`upper_${u}`}],r?l.push({calculate:`datum["${u}"] + datum["${r.field}"]`,as:`lower_${u}`}):l.push({calculate:`datum["${u}"] - datum["${i.field}"]`,as:`lower_${u}`}));for(const p of l)f.push({fieldPrefix:p.as.substring(0,6),titlePrefix:vn(vn(p.calculate,'datum["',""),'"]',"")})}return{postAggregateCalculates:l,errorBarSpecificAggregate:c,tooltipSummary:f,tooltipTitleWithFieldName:d}}function Ac(e,t,n){return`${Bi(e)} ${n} ${t}`}const $a="errorband",Vy=["band","borders"],Xy=new ts($a,Uf);function Uf(e,{config:t}){e={...e,encoding:ns(e.encoding,t)};const{transform:n,continuousAxisChannelDef:i,continuousAxis:r,encodingWithoutContinuousAxis:s,markDef:o,outerSpec:a,tooltipEncoding:c}=Mf(e,$a,t),l=o,u=Sa(l,r,i,s,t.errorband),f=e.encoding.x!==void 0&&e.encoding.y!==void 0;let d={type:f?"area":"rect"},p={type:f?"line":"rule"};const g={...l.interpolate?{interpolate:l.interpolate}:{},...l.tension&&l.interpolate?{tension:l.tension}:{}};return f?(d={...d,...g,ariaRoleDescription:"errorband"},p={...p,...g,aria:!1}):l.interpolate?S($c("interpolate")):l.tension&&S($c("tension")),{...a,transform:n,layer:[...u({partName:"band",mark:d,positionPrefix:"lower",endPositionPrefix:"upper",extraEncoding:c}),...u({partName:"borders",mark:p,positionPrefix:"lower",extraEncoding:c}),...u({partName:"borders",mark:p,positionPrefix:"upper",extraEncoding:c})]}}const Wf={};function wa(e,t,n){const i=new ts(e,t);Wf[e]={normalizer:i,parts:n}}function Yy(){return v(Wf)}wa(wr,zf,zy);wa(Ea,jf,My);wa($a,Uf,Vy);const Ky=["gradientHorizontalMaxLength","gradientHorizontalMinLength","gradientVerticalMaxLength","gradientVerticalMinLength","unselectedOpacity"],Bf={titleAlign:"align",titleAnchor:"anchor",titleAngle:"angle",titleBaseline:"baseline",titleColor:"color",titleFont:"font",titleFontSize:"fontSize",titleFontStyle:"fontStyle",titleFontWeight:"fontWeight",titleLimit:"limit",titleLineHeight:"lineHeight",titleOrient:"orient",titlePadding:"offset"},Gf={labelAlign:"align",labelAnchor:"anchor",labelAngle:"angle",labelBaseline:"baseline",labelColor:"color",labelFont:"font",labelFontSize:"fontSize",labelFontStyle:"fontStyle",labelFontWeight:"fontWeight",labelLimit:"limit",labelLineHeight:"lineHeight",labelOrient:"orient",labelPadding:"offset"},Jy=v(Bf),Qy=v(Gf),Zy={header:1,headerRow:1,headerColumn:1,headerFacet:1},qf=v(Zy),Hf=["size","shape","fill","stroke","strokeDash","strokeWidth","opacity"],eb={gradientHorizontalMaxLength:200,gradientHorizontalMinLength:100,gradientVerticalMaxLength:200,gradientVerticalMinLength:64,unselectedOpacity:.35},tb={aria:1,clipHeight:1,columnPadding:1,columns:1,cornerRadius:1,description:1,direction:1,fillColor:1,format:1,formatType:1,gradientLength:1,gradientOpacity:1,gradientStrokeColor:1,gradientStrokeWidth:1,gradientThickness:1,gridAlign:1,labelAlign:1,labelBaseline:1,labelColor:1,labelFont:1,labelFontSize:1,labelFontStyle:1,labelFontWeight:1,labelLimit:1,labelOffset:1,labelOpacity:1,labelOverlap:1,labelPadding:1,labelSeparation:1,legendX:1,legendY:1,offset:1,orient:1,padding:1,rowPadding:1,strokeColor:1,symbolDash:1,symbolDashOffset:1,symbolFillColor:1,symbolLimit:1,symbolOffset:1,symbolOpacity:1,symbolSize:1,symbolStrokeColor:1,symbolStrokeWidth:1,symbolType:1,tickCount:1,tickMinStep:1,title:1,titleAlign:1,titleAnchor:1,titleBaseline:1,titleColor:1,titleFont:1,titleFontSize:1,titleFontStyle:1,titleFontWeight:1,titleLimit:1,titleLineHeight:1,titleOpacity:1,titleOrient:1,titlePadding:1,type:1,values:1,zindex:1},Ze="_vgsid_",nb={point:{on:"click",fields:[Ze],toggle:"event.shiftKey",resolve:"global",clear:"dblclick"},interval:{on:"[mousedown, window:mouseup] > window:mousemove!",encodings:["x","y"],translate:"[mousedown, window:mouseup] > window:mousemove!",zoom:"wheel!",mark:{fill:"#333",fillOpacity:.125,stroke:"white"},resolve:"global",clear:"dblclick"}};function Ca(e){return e==="legend"||!!(e!=null&&e.legend)}function Ts(e){return Ca(e)&&Y(e)}function Na(e){return!!(e!=null&&e.select)}function Vf(e){const t=[];for(const n of e||[]){if(Na(n))continue;const{expr:i,bind:r,...s}=n;if(r&&i){const o={...s,bind:r,init:i};t.push(o)}else{const o={...s,...i?{update:i}:{},...r?{bind:r}:{}};t.push(o)}}return t}function ib(e){return is(e)||_a(e)||Fa(e)}function Fa(e){return"concat"in e}function is(e){return"vconcat"in e}function _a(e){return"hconcat"in e}function Xf({step:e,offsetIsDiscrete:t}){return t?e.for??"offset":"position"}function mt(e){return Y(e)&&e.step!==void 0}function Rc(e){return e.view||e.width||e.height}const Ic=20,rb={align:1,bounds:1,center:1,columns:1,spacing:1},sb=v(rb);function ob(e,t,n){const i=n[t],r={},{spacing:s,columns:o}=i;s!==void 0&&(r.spacing=s),o!==void 0&&(Kr(e)&&!Yi(e.facet)||Fa(e))&&(r.columns=o),is(e)&&(r.columns=1);for(const a of sb)if(e[a]!==void 0)if(a==="spacing"){const c=e[a];r[a]=ue(c)?c:{row:c.row??s,column:c.column??s}}else r[a]=e[a];return r}function co(e,t){return e[t]??e[t==="width"?"continuousWidth":"continuousHeight"]}function Cr(e,t){const n=Nr(e,t);return mt(n)?n.step:Yf}function Nr(e,t){const n=e[t]??e[t==="width"?"discreteWidth":"discreteHeight"];return fe(n,{step:e.step})}const Yf=20,ab={continuousWidth:200,continuousHeight:200,step:Yf},cb={background:"white",padding:5,timeFormat:"%b %d, %Y",countTitle:"Count of Records",view:ab,mark:ey,arc:{},area:{},bar:iy,circle:{},geoshape:{},image:{},line:{},point:{},rect:ry,rule:{color:"black"},square:{},text:{color:"black"},tick:sy,trail:{},boxplot:{size:14,extent:1.5,box:{},median:{color:"white"},outliers:{},rule:{},ticks:null},errorbar:{center:"mean",rule:!0,ticks:!1},errorband:{band:{opacity:.3},borders:!1},scale:Pm,projection:{},legend:eb,header:{titlePadding:10,labelPadding:10},headerColumn:{},headerRow:{},headerFacet:{},selection:nb,style:{},title:{},facet:{spacing:Ic},concat:{spacing:Ic},normalizedNumberFormat:".0%"},$t=["#4c78a8","#f58518","#e45756","#72b7b2","#54a24b","#eeca3b","#b279a2","#ff9da6","#9d755d","#bab0ac"],Lc={text:11,guideLabel:10,guideTitle:11,groupTitle:13,groupSubtitle:12},Pc={blue:$t[0],orange:$t[1],red:$t[2],teal:$t[3],green:$t[4],yellow:$t[5],purple:$t[6],pink:$t[7],brown:$t[8],gray0:"#000",gray1:"#111",gray2:"#222",gray3:"#333",gray4:"#444",gray5:"#555",gray6:"#666",gray7:"#777",gray8:"#888",gray9:"#999",gray10:"#aaa",gray11:"#bbb",gray12:"#ccc",gray13:"#ddd",gray14:"#eee",gray15:"#fff"};function lb(e={}){return{signals:[{name:"color",value:Y(e)?{...Pc,...e}:Pc}],mark:{color:{signal:"color.blue"}},rule:{color:{signal:"color.gray0"}},text:{color:{signal:"color.gray0"}},style:{"guide-label":{fill:{signal:"color.gray0"}},"guide-title":{fill:{signal:"color.gray0"}},"group-title":{fill:{signal:"color.gray0"}},"group-subtitle":{fill:{signal:"color.gray0"}},cell:{stroke:{signal:"color.gray8"}}},axis:{domainColor:{signal:"color.gray13"},gridColor:{signal:"color.gray8"},tickColor:{signal:"color.gray13"}},range:{category:[{signal:"color.blue"},{signal:"color.orange"},{signal:"color.red"},{signal:"color.teal"},{signal:"color.green"},{signal:"color.yellow"},{signal:"color.purple"},{signal:"color.pink"},{signal:"color.brown"},{signal:"color.grey8"}]}}}function ub(e){return{signals:[{name:"fontSize",value:Y(e)?{...Lc,...e}:Lc}],text:{fontSize:{signal:"fontSize.text"}},style:{"guide-label":{fontSize:{signal:"fontSize.guideLabel"}},"guide-title":{fontSize:{signal:"fontSize.guideTitle"}},"group-title":{fontSize:{signal:"fontSize.groupTitle"}},"group-subtitle":{fontSize:{signal:"fontSize.groupSubtitle"}}}}}function fb(e){return{text:{font:e},style:{"guide-label":{font:e},"guide-title":{font:e},"group-title":{font:e},"group-subtitle":{font:e}}}}function Kf(e){const t=v(e||{}),n={};for(const i of t){const r=e[i];n[i]=Qi(r)?Cu(r):Pe(r)}return n}function db(e){const t=v(e),n={};for(const i of t)n[i]=Kf(e[i]);return n}const pb=[...af,...Tf,...qf,"background","padding","legend","lineBreak","scale","style","title","view"];function Jf(e={}){const{color:t,font:n,fontSize:i,selection:r,...s}=e,o=Rr({},j(cb),n?fb(n):{},t?lb(t):{},i?ub(i):{},s||{});r&&No(o,"selection",r,!0);const a=Te(o,pb);for(const c of["background","lineBreak","padding"])o[c]&&(a[c]=Pe(o[c]));for(const c of af)o[c]&&(a[c]=_e(o[c]));for(const c of Tf)o[c]&&(a[c]=Kf(o[c]));for(const c of qf)o[c]&&(a[c]=_e(o[c]));return o.legend&&(a.legend=_e(o.legend)),o.scale&&(a.scale=_e(o.scale)),o.style&&(a.style=db(o.style)),o.title&&(a.title=_e(o.title)),o.view&&(a.view=_e(o.view)),a}const gb=new Set(["view",...Xm]),hb=["color","fontSize","background","padding","facet","concat","numberFormat","numberFormatType","normalizedNumberFormat","normalizedNumberFormatType","timeFormat","countTitle","header","axisQuantitative","axisTemporal","axisDiscrete","axisPoint","axisXBand","axisXPoint","axisXDiscrete","axisXQuantitative","axisXTemporal","axisYBand","axisYPoint","axisYDiscrete","axisYQuantitative","axisYTemporal","scale","selection","overlay"],mb={view:["continuousWidth","continuousHeight","discreteWidth","discreteHeight","step"],...Zm};function yb(e){e=j(e);for(const t of hb)delete e[t];if(e.axis)for(const t in e.axis)Qi(e.axis[t])&&delete e.axis[t];if(e.legend)for(const t of Ky)delete e.legend[t];if(e.mark){for(const t of Cc)delete e.mark[t];e.mark.tooltip&&Y(e.mark.tooltip)&&delete e.mark.tooltip}e.params&&(e.signals=(e.signals||[]).concat(Vf(e.params)),delete e.params);for(const t of gb){for(const i of Cc)delete e[t][i];const n=mb[t];if(n)for(const i of n)delete e[t][i];xb(e,t)}for(const t of Yy())delete e[t];bb(e);for(const t in e)Y(e[t])&&Q(e[t])&&delete e[t];return Q(e)?void 0:e}function bb(e){const{titleMarkConfig:t,subtitleMarkConfig:n,subtitle:i}=wu(e.title);Q(t)||(e.style["group-title"]={...e.style["group-title"],...t}),Q(n)||(e.style["group-subtitle"]={...e.style["group-subtitle"],...n}),Q(i)?delete e.title:e.title=i}function xb(e,t,n,i){const r=e[t];t==="view"&&(n="cell");const s={...r,...e.style[n??t]};Q(s)||(e.style[n??t]=s),delete e[t]}function rs(e){return"layer"in e}function vb(e){return"repeat"in e}function Sb(e){return!O(e.repeat)&&e.repeat.layer}class Ta{map(t,n){return Kr(t)?this.mapFacet(t,n):vb(t)?this.mapRepeat(t,n):_a(t)?this.mapHConcat(t,n):is(t)?this.mapVConcat(t,n):Fa(t)?this.mapConcat(t,n):this.mapLayerOrUnit(t,n)}mapLayerOrUnit(t,n){if(rs(t))return this.mapLayer(t,n);if(Dt(t))return this.mapUnit(t,n);throw new Error(Go(t))}mapLayer(t,n){return{...t,layer:t.layer.map(i=>this.mapLayerOrUnit(i,n))}}mapHConcat(t,n){return{...t,hconcat:t.hconcat.map(i=>this.map(i,n))}}mapVConcat(t,n){return{...t,vconcat:t.vconcat.map(i=>this.map(i,n))}}mapConcat(t,n){const{concat:i,...r}=t;return{...r,concat:i.map(s=>this.map(s,n))}}mapFacet(t,n){return{...t,spec:this.map(t.spec,n)}}mapRepeat(t,n){return{...t,spec:this.map(t.spec,n)}}}const Eb={zero:1,center:1,normalize:1};function $b(e){return e in Eb}const wb=new Set([rf,Hr,qr,br,Xr,oa,aa,Vr,sf,sa]),Cb=new Set([Hr,qr,rf]);function jn(e){return E(e)&&ni(e)==="quantitative"&&!e.bin}function zc(e,t,{orient:n,type:i}){const r=t==="x"?"y":"radius",s=t==="x",o=e[t],a=e[r];if(E(o)&&E(a))if(jn(o)&&jn(a)){if(o.stack)return t;if(a.stack)return r;const c=E(o)&&!!o.aggregate,l=E(a)&&!!a.aggregate;if(c!==l)return c?t:r;if(s&&i==="bar"){if(n==="vertical")return r;if(n==="horizontal")return t}}else{if(jn(o))return t;if(jn(a))return r}else{if(jn(o))return t;if(jn(a))return r}}function Nb(e){switch(e){case"x":return"y";case"y":return"x";case"theta":return"radius";case"radius":return"theta"}}function Qf(e,t){var d,p;const n=gt(e)?e:{type:e},i=n.type;if(!wb.has(i))return null;const r=zc(t,"x",n)||zc(t,"theta",n);if(!r)return null;const s=t[r],o=E(s)?C(s,{}):void 0,a=Nb(r),c=[],l=new Set;if(t[a]){const g=t[a],h=E(g)?C(g,{}):void 0;h&&h!==o&&(c.push(a),l.add(h));const m=a==="x"?"xOffset":"yOffset",y=t[m],b=E(y)?C(y,{}):void 0;b&&b!==o&&(c.push(m),l.add(b))}const u=Dg.reduce((g,h)=>{if(h!=="tooltip"&&mn(t,h)){const m=t[h];for(const y of ce(m)){const b=ht(y);if(b.aggregate)continue;const N=C(b,{});(!N||!l.has(N))&&g.push({channel:h,fieldDef:b})}}return g},[]);let f;return s.stack!==void 0?fi(s.stack)?f=s.stack?"zero":null:f=s.stack:Cb.has(i)&&(f="zero"),!f||!$b(f)||ba(t)&&u.length===0?null:(d=s==null?void 0:s.scale)!=null&&d.type&&((p=s==null?void 0:s.scale)==null?void 0:p.type)!==ke.LINEAR?(s!=null&&s.stack&&S(em(s.scale.type)),null):M(t[vt(r)])?(s.stack!==void 0&&S(Zh(r)),null):(E(s)&&s.aggregate&&!Kg.has(s.aggregate)&&S(tm(s.aggregate)),{groupbyChannels:c,groupbyFields:l,fieldChannel:r,impute:s.impute===null?!1:rn(i),stackBy:u,offset:f})}function Fb(e){const{point:t,line:n,...i}=e;return v(i).length>1?i:i.type}function _b(e){for(const t of["line","area","rule","trail"])e[t]&&(e={...e,[t]:Te(e[t],["point","line"])});return e}function ks(e,t={},n){return e.point==="transparent"?{opacity:0}:e.point?Y(e.point)?e.point:{}:e.point!==void 0?null:t.point||n.shape?Y(t.point)?t.point:{}:void 0}function Dc(e,t={}){return e.line?e.line===!0?{}:e.line:e.line!==void 0?null:t.line?t.line===!0?{}:t.line:void 0}class Tb{constructor(){this.name="path-overlay"}hasMatchingType(t,n){if(Dt(t)){const{mark:i,encoding:r}=t,s=gt(i)?i:{type:i};switch(s.type){case"line":case"rule":case"trail":return!!ks(s,n[s.type],r);case"area":return!!ks(s,n[s.type],r)||!!Dc(s,n[s.type])}}return!1}run(t,n,i){const{config:r}=n,{params:s,projection:o,mark:a,name:c,encoding:l,...u}=t,f=ns(l,r),d=gt(a)?a:{type:a},p=ks(d,r[d.type],f),g=d.type==="area"&&Dc(d,r[d.type]),h=[{name:c,...s?{params:s}:{},mark:Fb({...d.type==="area"&&d.opacity===void 0&&d.fillOpacity===void 0?{opacity:.7}:{},...d}),encoding:Te(f,["shape"])}],m=Qf(d,f);let y=f;if(m){const{fieldChannel:b,offset:N}=m;y={...f,[b]:{...f[b],...N?{stack:N}:{}}}}return y=Te(y,["y2","x2"]),g&&h.push({...o?{projection:o}:{},mark:{type:"line",...Kn(d,["clip","interpolate","tension","tooltip"]),...g},encoding:y}),p&&h.push({...o?{projection:o}:{},mark:{type:"point",opacity:1,filled:!0,...Kn(d,["clip","tooltip"]),...p},encoding:y}),i({...u,layer:h},{...n,config:_b(r)})}}function kb(e,t){return t?Yi(e)?ed(e,t):Zf(e,t):e}function Os(e,t){return t?ed(e,t):e}function lo(e,t,n){const i=t[e];if(my(i)){if(i.repeat in n)return{...t,[e]:n[i.repeat]};S(hh(i.repeat));return}return t}function Zf(e,t){if(e=lo("field",e,t),e!==void 0){if(e===null)return null;if(ga(e)&&ft(e.sort)){const n=lo("field",e.sort,t);e={...e,...n?{sort:n}:{}}}return e}}function jc(e,t){if(E(e))return Zf(e,t);{const n=lo("datum",e,t);return n!==e&&!n.type&&(n.type="nominal"),n}}function Mc(e,t){if(M(e)){const n=jc(e,t);if(n)return n;if(Jr(e))return{condition:e.condition}}else{if(Ki(e)){const n=jc(e.condition,t);if(n)return{...e,condition:n};{const{condition:i,...r}=e;return r}}return e}}function ed(e,t){const n={};for(const i in e)if(Bn(e,i)){const r=e[i];if(O(r))n[i]=r.map(s=>Mc(s,t)).filter(s=>s);else{const s=Mc(r,t);s!==void 0&&(n[i]=s)}}return n}class Ob{constructor(){this.name="RuleForRangedLine"}hasMatchingType(t){if(Dt(t)){const{encoding:n,mark:i}=t;if(i==="line"||gt(i)&&i.type==="line")for(const r of Pg){const s=On(r),o=n[s];if(n[r]&&(E(o)&&!ve(o.bin)||Et(o)))return!0}}return!1}run(t,n,i){const{encoding:r,mark:s}=t;return S(Ph(!!r.x2,!!r.y2)),i({...t,mark:Y(s)?{...s,type:"rule"}:"rule"},n)}}class Ab extends Ta{constructor(){super(...arguments),this.nonFacetUnitNormalizers=[Dy,Uy,Xy,new Tb,new Ob]}map(t,n){if(Dt(t)){const i=mn(t.encoding,Ft),r=mn(t.encoding,_t),s=mn(t.encoding,Lr);if(i||r||s)return this.mapFacetedUnit(t,n)}return super.map(t,n)}mapUnit(t,n){const{parentEncoding:i,parentProjection:r}=n,s=Os(t.encoding,n.repeater),o={...t,...t.name?{name:[n.repeaterPrefix,t.name].filter(c=>c).join("_")}:{},...s?{encoding:s}:{}};if(i||r)return this.mapUnitWithParentEncodingOrProjection(o,n);const a=this.mapLayerOrUnit.bind(this);for(const c of this.nonFacetUnitNormalizers)if(c.hasMatchingType(o,n.config))return c.run(o,n,a);return o}mapRepeat(t,n){return Sb(t)?this.mapLayerRepeat(t,n):this.mapNonLayerRepeat(t,n)}mapLayerRepeat(t,n){const{repeat:i,spec:r,...s}=t,{row:o,column:a,layer:c}=i,{repeater:l={},repeaterPrefix:u=""}=n;return o||a?this.mapRepeat({...t,repeat:{...o?{row:o}:{},...a?{column:a}:{}},spec:{repeat:{layer:c},spec:r}},n):{...s,layer:c.map(f=>{const d={...l,layer:f},p=`${(r.name?`${r.name}_`:"")+u}child__layer_${se(f)}`,g=this.mapLayerOrUnit(r,{...n,repeater:d,repeaterPrefix:p});return g.name=p,g})}}mapNonLayerRepeat(t,n){const{repeat:i,spec:r,data:s,...o}=t;!O(i)&&t.columns&&(t=Te(t,["columns"]),S(xc("repeat")));const a=[],{repeater:c={},repeaterPrefix:l=""}=n,u=!O(i)&&i.row||[c?c.row:null],f=!O(i)&&i.column||[c?c.column:null],d=O(i)&&i||[c?c.repeat:null];for(const g of d)for(const h of u)for(const m of f){const y={repeat:g,row:h,column:m,layer:c.layer},b=(r.name?`${r.name}_`:"")+l+"child__"+(O(i)?`${se(g)}`:(i.row?`row_${se(h)}`:"")+(i.column?`column_${se(m)}`:"")),N=this.map(r,{...n,repeater:y,repeaterPrefix:b});N.name=b,a.push(Te(N,["data"]))}const p=O(i)?t.columns:i.column?i.column.length:1;return{data:r.data??s,align:"all",...o,columns:p,concat:a}}mapFacet(t,n){const{facet:i}=t;return Yi(i)&&t.columns&&(t=Te(t,["columns"]),S(xc("facet"))),super.mapFacet(t,n)}mapUnitWithParentEncodingOrProjection(t,n){const{encoding:i,projection:r}=t,{parentEncoding:s,parentProjection:o,config:a}=n,c=Wc({parentProjection:o,projection:r}),l=Uc({parentEncoding:s,encoding:Os(i,n.repeater)});return this.mapUnit({...t,...c?{projection:c}:{},...l?{encoding:l}:{}},{config:a})}mapFacetedUnit(t,n){const{row:i,column:r,facet:s,...o}=t.encoding,{mark:a,width:c,projection:l,height:u,view:f,params:d,encoding:p,...g}=t,{facetMapping:h,layout:m}=this.getFacetMappingAndLayout({row:i,column:r,facet:s},n),y=Os(o,n.repeater);return this.mapFacet({...g,...m,facet:h,spec:{...c?{width:c}:{},...u?{height:u}:{},...f?{view:f}:{},...l?{projection:l}:{},mark:a,encoding:y,...d?{params:d}:{}}},n)}getFacetMappingAndLayout(t,n){const{row:i,column:r,facet:s}=t;if(i||r){s&&S(Ih([...i?[Ft]:[],...r?[_t]:[]]));const o={},a={};for(const c of[Ft,_t]){const l=t[c];if(l){const{align:u,center:f,spacing:d,columns:p,...g}=l;o[c]=g;for(const h of["align","center","spacing"])l[h]!==void 0&&(a[h]??(a[h]={}),a[h][c]=l[h])}}return{facetMapping:o,layout:a}}else{const{align:o,center:a,spacing:c,columns:l,...u}=s;return{facetMapping:kb(u,n.repeater),layout:{...o?{align:o}:{},...a?{center:a}:{},...c?{spacing:c}:{},...l?{columns:l}:{}}}}}mapLayer(t,{parentEncoding:n,parentProjection:i,...r}){const{encoding:s,projection:o,...a}=t,c={...r,parentEncoding:Uc({parentEncoding:n,encoding:s,layer:!0}),parentProjection:Wc({parentProjection:i,projection:o})};return super.mapLayer({...a,...t.name?{name:[c.repeaterPrefix,t.name].filter(l=>l).join("_")}:{}},c)}}function Uc({parentEncoding:e,encoding:t={},layer:n}){let i={};if(e){const r=new Set([...v(e),...v(t)]);for(const s of r){const o=t[s],a=e[s];if(M(o)){const c={...a,...o};i[s]=c}else Ki(o)?i[s]={...o,condition:{...a,...o.condition}}:o||o===null?i[s]=o:(n||Qe(a)||R(a)||M(a)||O(a))&&(i[s]=a)}}else i=t;return!i||Q(i)?void 0:i}function Wc(e){const{parentProjection:t,projection:n}=e;return t&&n&&S(Sh({parentProjection:t,projection:n})),n??t}function ka(e){return"filter"in e}function Rb(e){return(e==null?void 0:e.stop)!==void 0}function td(e){return"lookup"in e}function Ib(e){return"data"in e}function Lb(e){return"param"in e}function Pb(e){return"pivot"in e}function zb(e){return"density"in e}function Db(e){return"quantile"in e}function jb(e){return"regression"in e}function Mb(e){return"loess"in e}function Ub(e){return"sample"in e}function Wb(e){return"window"in e}function Bb(e){return"joinaggregate"in e}function Gb(e){return"flatten"in e}function qb(e){return"calculate"in e}function nd(e){return"bin"in e}function Hb(e){return"impute"in e}function Vb(e){return"timeUnit"in e}function Xb(e){return"aggregate"in e}function Yb(e){return"stack"in e}function Kb(e){return"fold"in e}function Jb(e){return"extent"in e&&!("density"in e)}function Qb(e){return e.map(t=>ka(t)?{filter:Gn(t.filter,Tm)}:t)}class Zb extends Ta{map(t,n){return n.emptySelections??(n.emptySelections={}),n.selectionPredicates??(n.selectionPredicates={}),t=Bc(t,n),super.map(t,n)}mapLayerOrUnit(t,n){if(t=Bc(t,n),t.encoding){const i={};for(const[r,s]of Gt(t.encoding))i[r]=id(s,n);t={...t,encoding:i}}return super.mapLayerOrUnit(t,n)}mapUnit(t,n){const{selection:i,...r}=t;return i?{...r,params:Gt(i).map(([s,o])=>{const{init:a,bind:c,empty:l,...u}=o;u.type==="single"?(u.type="point",u.toggle=!1):u.type==="multi"&&(u.type="point"),n.emptySelections[s]=l!=="none";for(const f of Se(n.selectionPredicates[s]??{}))f.empty=l!=="none";return{name:s,value:a,select:u,bind:c}})}:t}}function Bc(e,t){const{transform:n,...i}=e;if(n){const r=n.map(s=>{if(ka(s))return{filter:uo(s,t)};if(nd(s)&&An(s.bin))return{...s,bin:rd(s.bin)};if(td(s)){const{selection:o,...a}=s.from;return o?{...s,from:{param:o,...a}}:s}return s});return{...i,transform:r}}return e}function id(e,t){var i,r;const n=j(e);if(E(n)&&An(n.bin)&&(n.bin=rd(n.bin)),Ln(n)&&((r=(i=n.scale)==null?void 0:i.domain)!=null&&r.selection)){const{selection:s,...o}=n.scale.domain;n.scale.domain={...o,...s?{param:s}:{}}}if(Jr(n))if(O(n.condition))n.condition=n.condition.map(s=>{const{selection:o,param:a,test:c,...l}=s;return a?s:{...l,test:uo(s,t)}});else{const{selection:s,param:o,test:a,...c}=id(n.condition,t);n.condition=o?n.condition:{...c,test:uo(n.condition,t)}}return n}function rd(e){const t=e.extent;if(t!=null&&t.selection){const{selection:n,...i}=t;return{...e,extent:{...i,param:n}}}return e}function uo(e,t){const n=i=>Gn(i,r=>{var s;const o=t.emptySelections[r]??!0,a={param:r,empty:o};return(s=t.selectionPredicates)[r]??(s[r]=[]),t.selectionPredicates[r].push(a),a});return e.selection?n(e.selection):Gn(e.test||e.filter,i=>i.selection?n(i.selection):i)}class fo extends Ta{map(t,n){const i=n.selections??[];if(t.params&&!Dt(t)){const r=[];for(const s of t.params)Na(s)?i.push(s):r.push(s);t.params=r}return n.selections=i,super.map(t,n)}mapUnit(t,n){const i=n.selections;if(!i||!i.length)return t;const r=(n.path??[]).concat(t.name),s=[];for(const o of i)if(!o.views||!o.views.length)s.push(o);else for(const a of o.views)(z(a)&&(a===t.name||r.includes(a))||O(a)&&a.map(c=>r.indexOf(c)).every((c,l,u)=>c!==-1&&(l===0||c>u[l-1])))&&s.push(o);return s.length&&(t.params=s),t}}for(const e of["mapFacet","mapRepeat","mapHConcat","mapVConcat","mapLayer"]){const t=fo.prototype[e];fo.prototype[e]=function(n,i){return t.call(this,n,ex(n,i))}}function ex(e,t){return e.name?{...t,path:(t.path??[]).concat(e.name)}:t}function sd(e,t){t===void 0&&(t=Jf(e.config));const n=rx(e,t),{width:i,height:r}=e,s=sx(n,{width:i,height:r,autosize:e.autosize},t);return{...n,...s?{autosize:s}:{}}}const tx=new Ab,nx=new Zb,ix=new fo;function rx(e,t={}){const n={config:t};return ix.map(tx.map(nx.map(e,n),n),n)}function Gc(e){return z(e)?{type:e}:e??{}}function sx(e,t,n){let{width:i,height:r}=t;const s=Dt(e)||rs(e),o={};s?i=="container"&&r=="container"?(o.type="fit",o.contains="padding"):i=="container"?(o.type="fit-x",o.contains="padding"):r=="container"&&(o.type="fit-y",o.contains="padding"):(i=="container"&&(S(hc("width")),i=void 0),r=="container"&&(S(hc("height")),r=void 0));const a={type:"pad",...o,...n?Gc(n.autosize):{},...Gc(e.autosize)};if(a.type==="fit"&&!s&&(S(sh),a.type="pad"),i=="container"&&!(a.type=="fit"||a.type=="fit-x")&&S(mc("width")),r=="container"&&!(a.type=="fit"||a.type=="fit-y")&&S(mc("height")),!lt(a,{type:"pad"}))return a}function ox(e){return e==="fit"||e==="fit-x"||e==="fit-y"}function ax(e){return e?`fit-${Mr(e)}`:"fit"}const cx=["background","padding"];function qc(e,t){const n={};for(const i of cx)e&&e[i]!==void 0&&(n[i]=Pe(e[i]));return t&&(n.params=e.params),n}class jt{constructor(t={},n={}){this.explicit=t,this.implicit=n}clone(){return new jt(j(this.explicit),j(this.implicit))}combine(){return{...this.explicit,...this.implicit}}get(t){return fe(this.explicit[t],this.implicit[t])}getWithExplicit(t){return this.explicit[t]!==void 0?{explicit:!0,value:this.explicit[t]}:this.implicit[t]!==void 0?{explicit:!1,value:this.implicit[t]}:{explicit:!1,value:void 0}}setWithExplicit(t,{value:n,explicit:i}){n!==void 0&&this.set(t,n,i)}set(t,n,i){return delete this[i?"implicit":"explicit"][t],this[i?"explicit":"implicit"][t]=n,this}copyKeyFromSplit(t,{explicit:n,implicit:i}){n[t]!==void 0?this.set(t,n[t],!0):i[t]!==void 0&&this.set(t,i[t],!1)}copyKeyFromObject(t,n){n[t]!==void 0&&this.set(t,n[t],!0)}copyAll(t){for(const n of v(t.combine())){const i=t.getWithExplicit(n);this.setWithExplicit(n,i)}}}function ct(e){return{explicit:!0,value:e}}function Le(e){return{explicit:!1,value:e}}function od(e){return(t,n,i,r)=>{const s=e(t.value,n.value);return s>0?t:s<0?n:ss(t,n,i,r)}}function ss(e,t,n,i){return e.explicit&&t.explicit&&S(Hh(n,i,e.value,t.value)),e}function Ht(e,t,n,i,r=ss){return e===void 0||e.value===void 0?t:e.explicit&&!t.explicit?e:t.explicit&&!e.explicit?t:lt(e.value,t.value)?e:r(e,t,n,i)}class lx extends jt{constructor(t={},n={},i=!1){super(t,n),this.explicit=t,this.implicit=n,this.parseNothing=i}clone(){const t=super.clone();return t.parseNothing=this.parseNothing,t}}function si(e){return"url"in e}function Di(e){return"values"in e}function ad(e){return"name"in e&&!si(e)&&!Di(e)&&!Bt(e)}function Bt(e){return e&&(cd(e)||ld(e)||Oa(e))}function cd(e){return"sequence"in e}function ld(e){return"sphere"in e}function Oa(e){return"graticule"in e}var ee;(function(e){e[e.Raw=0]="Raw",e[e.Main=1]="Main",e[e.Row=2]="Row",e[e.Column=3]="Column",e[e.Lookup=4]="Lookup"})(ee||(ee={}));function ud(e){const{signals:t,hasLegend:n,index:i,...r}=e;return r.field=Me(r.field),r}function Nn(e,t=!0,n=qp){if(O(e)){const i=e.map(r=>Nn(r,t,n));return t?`[${i.join(", ")}]`:i}else if(Rn(e))return n(t?En(e):ym(e));return t?n(te(e)):e}function ux(e,t){for(const n of Se(e.component.selection??{})){const i=n.name;let r=`${i}${Xt}, ${n.resolve==="global"?"true":`{unit: ${yn(e)}}`}`;for(const s of as)s.defined(n)&&(s.signals&&(t=s.signals(e,n,t)),s.modifyExpr&&(r=s.modifyExpr(e,n,r)));t.push({name:i+Wx,on:[{events:{signal:n.name+Xt},update:`modify(${B(n.name+Fn)}, ${r})`}]})}return Aa(t)}function fx(e,t){if(e.component.selection&&v(e.component.selection).length){const n=B(e.getName("cell"));t.unshift({name:"facet",value:{},on:[{events:di("mousemove","scope"),update:`isTuple(facet) ? facet : group(${n}).datum`}]})}return Aa(t)}function dx(e,t){let n=!1;for(const i of Se(e.component.selection??{})){const r=i.name,s=B(r+Fn);if(t.filter(a=>a.name===r).length===0){const a=i.resolve==="global"?"union":i.resolve,c=i.type==="point"?", true, true)":")";t.push({name:i.name,update:`${Fd}(${s}, ${B(a)}${c}`})}n=!0;for(const a of as)a.defined(i)&&a.topLevelSignals&&(t=a.topLevelSignals(e,i,t))}return n&&t.filter(r=>r.name==="unit").length===0&&t.unshift({name:"unit",value:{},on:[{events:"mousemove",update:"isTuple(group()) ? group() : unit"}]}),Aa(t)}function px(e,t){const n=[...t],i=yn(e,{escape:!1});for(const r of Se(e.component.selection??{})){const s={name:r.name+Fn};if(r.project.hasSelectionId&&(s.transform=[{type:"collect",sort:{field:Ze}}]),r.init){const a=r.project.items.map(ud);s.values=r.project.hasSelectionId?r.init.map(c=>({unit:i,[Ze]:Nn(c,!1)[0]})):r.init.map(c=>({unit:i,fields:a,values:Nn(c,!1)}))}n.filter(a=>a.name===r.name+Fn).length||n.push(s)}return n}function fd(e,t){for(const n of Se(e.component.selection??{}))for(const i of as)i.defined(n)&&i.marks&&(t=i.marks(e,n,t));return t}function gx(e,t){for(const n of e.children)ae(n)&&(t=fd(n,t));return t}function hx(e,t,n,i){const r=Ad(e,t.param,t);return{signal:ze(n.get("type"))&&O(i)&&i[0]>i[1]?`isValid(${r}) && reverse(${r})`:r}}function Aa(e){return e.map(t=>(t.on&&!t.on.length&&delete t.on,t))}class J{constructor(t,n){this.debugName=n,this._children=[],this._parent=null,t&&(this.parent=t)}clone(){throw new Error("Cannot clone node")}get parent(){return this._parent}set parent(t){this._parent=t,t&&t.addChild(this)}get children(){return this._children}numChildren(){return this._children.length}addChild(t,n){if(this._children.includes(t)){S(bh);return}n!==void 0?this._children.splice(n,0,t):this._children.push(t)}removeChild(t){const n=this._children.indexOf(t);return this._children.splice(n,1),n}remove(){let t=this._parent.removeChild(this);for(const n of this._children)n._parent=this._parent,this._parent.addChild(n,t++)}insertAsParentOf(t){const n=t.parent;n.removeChild(this),this.parent=n,t.parent=this}swapWithParent(){const t=this._parent,n=t.parent;for(const r of this._children)r.parent=t;this._children=[],t.removeChild(this);const i=t.parent.removeChild(t);this._parent=n,n.addChild(this,i),t.parent=this}}class Ce extends J{clone(){const t=new this.constructor;return t.debugName=`clone_${this.debugName}`,t._source=this._source,t._name=`clone_${this._name}`,t.type=this.type,t.refCounts=this.refCounts,t.refCounts[t._name]=0,t}constructor(t,n,i,r){super(t,n),this.type=i,this.refCounts=r,this._source=this._name=n,this.refCounts&&!(this._name in this.refCounts)&&(this.refCounts[this._name]=0)}dependentFields(){return new Set}producedFields(){return new Set}hash(){return this._hash===void 0&&(this._hash=`Output ${ou()}`),this._hash}getSource(){return this.refCounts[this._name]++,this._source}isRequired(){return!!this.refCounts[this._name]}setSource(t){this._source=t}}function As(e){return e.as!==void 0}function Hc(e){return`${e}_end`}class dt extends J{clone(){return new dt(null,j(this.formula))}constructor(t,n){super(t),this.formula=n}static makeFromEncoding(t,n){const i=n.reduceFieldDef((r,s)=>{const{field:o,timeUnit:a}=s;if(a){let c;if(In(a)){if(ae(n)){const{mark:l}=n;(ca(l)||s.bandPosition)&&(c={timeUnit:me(a),field:o})}}else c={as:C(s,{forAs:!0}),field:o,timeUnit:a};c&&(r[G(c)]=c)}return r},{});return Q(i)?null:new dt(t,i)}static makeFromTransform(t,n){const{timeUnit:i,...r}={...n},s=me(i),o={...r,timeUnit:s};return new dt(t,{[G(o)]:o})}merge(t){this.formula={...this.formula};for(const n in t.formula)this.formula[n]||(this.formula[n]=t.formula[n]);for(const n of t.children)t.removeChild(n),n.parent=this;t.remove()}removeFormulas(t){const n={};for(const[i,r]of Gt(this.formula)){const s=As(r)?r.as:`${r.field}_end`;t.has(s)||(n[i]=r)}this.formula=n}producedFields(){return new Set(Se(this.formula).map(t=>As(t)?t.as:Hc(t.field)))}dependentFields(){return new Set(Se(this.formula).map(t=>t.field))}hash(){return`TimeUnit ${G(this.formula)}`}assemble(){const t=[];for(const n of Se(this.formula))if(As(n)){const{field:i,as:r,timeUnit:s}=n,{unit:o,utc:a,...c}=me(s);t.push({field:Me(i),type:"timeunit",...o?{units:Gr(o)}:{},...a?{timezone:"utc"}:{},...c,as:[r,`${r}_end`]})}else if(n){const{field:i,timeUnit:r}=n,s=Bu(r==null?void 0:r.unit),{part:o,step:a}=Vu(s,r.step);t.push({type:"formula",expr:`timeOffset('${o}', datum['${i}'], ${a})`,as:Hc(i)})}return t}}const Zi="_tuple_fields";class mx{constructor(...t){this.items=t,this.hasChannel={},this.hasField={},this.hasSelectionId=!1}}const yx={defined:()=>!0,parse:(e,t,n)=>{const i=t.name,r=t.project??(t.project=new mx),s={},o={},a=new Set,c=(g,h)=>{const m=h==="visual"?g.channel:g.field;let y=se(`${i}_${m}`);for(let b=1;a.has(y);b++)y=se(`${i}_${m}_${b}`);return a.add(y),{[h]:y}},l=t.type,u=e.config.selection[l],f=n.value!==void 0?ce(n.value):null;let{fields:d,encodings:p}=Y(n.select)?n.select:{};if(!d&&!p&&f){for(const g of f)if(Y(g))for(const h of v(g))Lg(h)?(p||(p=[])).push(h):l==="interval"?(S(gh),p=u.encodings):(d??(d=[])).push(h)}!d&&!p&&(p=u.encodings,"fields"in u&&(d=u.fields));for(const g of p??[]){const h=e.fieldDef(g);if(h){let m=h.field;if(h.aggregate){S(oh(g,h.aggregate));continue}else if(!m){S(bc(g));continue}if(h.timeUnit&&!In(h.timeUnit)){m=e.vgField(g);const y={timeUnit:h.timeUnit,as:m,field:h.field};o[G(y)]=y}if(!s[m]){const y=l==="interval"&&zt(g)&&ze(e.getScaleComponent(g).get("type"))?"R":h.bin?"R-RE":"E",b={field:m,channel:g,type:y,index:r.items.length};b.signals={...c(b,"data"),...c(b,"visual")},r.items.push(s[m]=b),r.hasField[m]=s[m],r.hasSelectionId=r.hasSelectionId||m===Ze,du(g)?(b.geoChannel=g,b.channel=fu(g),r.hasChannel[b.channel]=s[m]):r.hasChannel[g]=s[m]}}else S(bc(g))}for(const g of d??[]){if(r.hasField[g])continue;const h={type:"E",field:g,index:r.items.length};h.signals={...c(h,"data")},r.items.push(h),r.hasField[g]=h,r.hasSelectionId=r.hasSelectionId||g===Ze}f&&(t.init=f.map(g=>r.items.map(h=>Y(g)?g[h.geoChannel||h.channel]!==void 0?g[h.geoChannel||h.channel]:g[h.field]:g))),Q(o)||(r.timeUnit=new dt(null,o))},signals:(e,t,n)=>{const i=t.name+Zi;return n.filter(s=>s.name===i).length>0||t.project.hasSelectionId?n:n.concat({name:i,value:t.project.items.map(ud)})}},Tt={defined:e=>e.type==="interval"&&e.resolve==="global"&&e.bind&&e.bind==="scales",parse:(e,t)=>{const n=t.scales=[];for(const i of t.project.items){const r=i.channel;if(!zt(r))continue;const s=e.getScaleComponent(r),o=s?s.get("type"):void 0;if(!s||!ze(o)){S(lh);continue}s.set("selectionExtent",{param:t.name,field:i.field},!0),n.push(i)}},topLevelSignals:(e,t,n)=>{const i=t.scales.filter(o=>n.filter(a=>a.name===o.signals.data).length===0);if(!e.parent||Vc(e)||i.length===0)return n;const r=n.filter(o=>o.name===t.name)[0];let s=r.update;if(s.indexOf(Fd)>=0)r.update=`{${i.map(o=>`${B(Me(o.field))}: ${o.signals.data}`).join(", ")}}`;else{for(const o of i){const a=`${B(Me(o.field))}: ${o.signals.data}`;s.includes(a)||(s=`${s.substring(0,s.length-1)}, ${a}}`)}r.update=s}return n.concat(i.map(o=>({name:o.signals.data})))},signals:(e,t,n)=>{if(e.parent&&!Vc(e))for(const i of t.scales){const r=n.filter(s=>s.name===i.signals.data)[0];r.push="outer",delete r.value,delete r.update}return n}};function po(e,t){return`domain(${B(e.scaleName(t))})`}function Vc(e){return e.parent&&vi(e.parent)&&!e.parent.parent}const Vn="_brush",dd="_scale_trigger",Ei="geo_interval_init_tick",pd="_init",bx="_center",xx={defined:e=>e.type==="interval",parse:(e,t,n)=>{var i;if(e.hasProjection){const r={...Y(n.select)?n.select:{}};r.fields=[Ze],r.encodings||(r.encodings=n.value?v(n.value):[it,nt]),n.select={type:"interval",...r}}if(t.translate&&!Tt.defined(t)){const r=`!event.item || event.item.mark.name !== ${B(t.name+Vn)}`;for(const s of t.events){if(!s.between){S(`${s} is not an ordered event stream for interval selections.`);continue}const o=ce((i=s.between[0]).filter??(i.filter=[]));o.indexOf(r)<0&&o.push(r)}}},signals:(e,t,n)=>{const i=t.name,r=i+Xt,s=Se(t.project.hasChannel).filter(a=>a.channel===oe||a.channel===ye),o=t.init?t.init[0]:null;if(n.push(...s.reduce((a,c)=>a.concat(vx(e,t,c,o&&o[c.index])),[])),e.hasProjection){const a=B(e.projectionName()),c=e.projectionName()+bx,{x:l,y:u}=t.project.hasChannel,f=l&&l.signals.visual,d=u&&u.signals.visual,p=l?o&&o[l.index]:`${c}[0]`,g=u?o&&o[u.index]:`${c}[1]`,h=x=>e.getSizeSignalRef(x).signal,m=`[[${f?f+"[0]":"0"}, ${d?d+"[0]":"0"}],[${f?f+"[1]":h("width")}, ${d?d+"[1]":h("height")}]]`;o&&(n.unshift({name:i+pd,init:`[scale(${a}, [${l?p[0]:p}, ${u?g[0]:g}]), scale(${a}, [${l?p[1]:p}, ${u?g[1]:g}])]`}),(!l||!u)&&(n.find(_=>_.name===c)||n.unshift({name:c,update:`invert(${a}, [${h("width")}/2, ${h("height")}/2])`})));const y=`intersect(${m}, {markname: ${B(e.getName("marks"))}}, unit.mark)`,b=`{unit: ${yn(e)}}`,N=`vlSelectionTuples(${y}, ${b})`,P=s.map(x=>x.signals.visual);return n.concat({name:r,on:[{events:[...P.length?[{signal:P.join(" || ")}]:[],...o?[{signal:Ei}]:[]],update:N}]})}else{if(!Tt.defined(t)){const l=i+dd,u=s.map(f=>{const d=f.channel,{data:p,visual:g}=f.signals,h=B(e.scaleName(d)),m=e.getScaleComponent(d).get("type"),y=ze(m)?"+":"";return`(!isArray(${p}) || (${y}invert(${h}, ${g})[0] === ${y}${p}[0] && ${y}invert(${h}, ${g})[1] === ${y}${p}[1]))`});u.length&&n.push({name:l,value:{},on:[{events:s.map(f=>({scale:e.scaleName(f.channel)})),update:u.join(" && ")+` ? ${l} : {}`}]})}const a=s.map(l=>l.signals.data),c=`unit: ${yn(e)}, fields: ${i+Zi}, values`;return n.concat({name:r,...o?{init:`{${c}: ${Nn(o)}}`}:{},...a.length?{on:[{events:[{signal:a.join(" || ")}],update:`${a.join(" && ")} ? {${c}: [${a}]} : null`}]}:{}})}},topLevelSignals:(e,t,n)=>(ae(e)&&e.hasProjection&&t.init&&(n.filter(r=>r.name===Ei).length||n.unshift({name:Ei,value:null,on:[{events:"timer{1}",update:`${Ei} === null ? {} : ${Ei}`}]})),n),marks:(e,t,n)=>{const i=t.name,{x:r,y:s}=t.project.hasChannel,o=r==null?void 0:r.signals.visual,a=s==null?void 0:s.signals.visual,c=`data(${B(t.name+Fn)})`;if(Tt.defined(t)||!r&&!s)return n;const l={x:r!==void 0?{signal:`${o}[0]`}:{value:0},y:s!==void 0?{signal:`${a}[0]`}:{value:0},x2:r!==void 0?{signal:`${o}[1]`}:{field:{group:"width"}},y2:s!==void 0?{signal:`${a}[1]`}:{field:{group:"height"}}};if(t.resolve==="global")for(const h of v(l))l[h]=[{test:`${c}.length && ${c}[0].unit === ${yn(e)}`,...l[h]},{value:0}];const{fill:u,fillOpacity:f,cursor:d,...p}=t.mark,g=v(p).reduce((h,m)=>(h[m]=[{test:[r!==void 0&&`${o}[0] !== ${o}[1]`,s!==void 0&&`${a}[0] !== ${a}[1]`].filter(y=>y).join(" && "),value:p[m]},{value:null}],h),{});return[{name:`${i+Vn}_bg`,type:"rect",clip:!0,encode:{enter:{fill:{value:u},fillOpacity:{value:f}},update:l}},...n,{name:i+Vn,type:"rect",clip:!0,encode:{enter:{...d?{cursor:{value:d}}:{},fill:{value:"transparent"}},update:{...l,...g}}}]}};function vx(e,t,n,i){const r=!e.hasProjection,s=n.channel,o=n.signals.visual,a=B(r?e.scaleName(s):e.projectionName()),c=d=>`scale(${a}, ${d})`,l=e.getSizeSignalRef(s===oe?"width":"height").signal,u=`${s}(unit)`,f=t.events.reduce((d,p)=>[...d,{events:p.between[0],update:`[${u}, ${u}]`},{events:p,update:`[${o}[0], clamp(${u}, 0, ${l})]`}],[]);if(r){const d=n.signals.data,p=Tt.defined(t),g=e.getScaleComponent(s),h=g?g.get("type"):void 0,m=i?{init:Nn(i,!0,c)}:{value:[]};return f.push({events:{signal:t.name+dd},update:ze(h)?`[${c(`${d}[0]`)}, ${c(`${d}[1]`)}]`:"[0, 0]"}),p?[{name:d,on:[]}]:[{name:o,...m,on:f},{name:d,...i?{init:Nn(i)}:{},on:[{events:{signal:o},update:`${o}[0] === ${o}[1] ? null : invert(${a}, ${o})`}]}]}else{const d=s===oe?0:1,p=t.name+pd,g=i?{init:`[${p}[0][${d}], ${p}[1][${d}]]`}:{value:[]};return[{name:o,...g,on:f}]}}const Sx={defined:e=>e.type==="point",signals:(e,t,n)=>{const i=t.name,r=i+Zi,s=t.project,o="(item().isVoronoi ? datum.datum : datum)",a=Se(e.component.selection??{}).reduce((f,d)=>d.type==="interval"?f.concat(d.name+Vn):f,[]).map(f=>`indexof(item().mark.name, '${f}') < 0`).join(" && "),c=`datum && item().mark.marktype !== 'group' && indexof(item().mark.role, 'legend') < 0${a?` && ${a}`:""}`;let l=`unit: ${yn(e)}, `;if(t.project.hasSelectionId)l+=`${Ze}: ${o}[${B(Ze)}]`;else{const f=s.items.map(d=>{const p=e.fieldDef(d.channel);return p!=null&&p.bin?`[${o}[${B(e.vgField(d.channel,{}))}], ${o}[${B(e.vgField(d.channel,{binSuffix:"end"}))}]]`:`${o}[${B(d.field)}]`}).join(", ");l+=`fields: ${r}, values: [${f}]`}const u=t.events;return n.concat([{name:i+Xt,on:u?[{events:u,update:`${c} ? {${l}} : null`,force:!0}]:[]}])}};function mi(e,t,n,i){const r=Jr(t)&&t.condition,s=i(t);if(r){const a=ce(r).map(c=>{const l=i(c);if(hy(c)){const{param:u,empty:f}=c;return{test:Od(e,{param:u,empty:f}),...l}}else return{test:Tr(e,c.test),...l}});return{[n]:[...a,...s!==void 0?[s]:[]]}}else return s!==void 0?{[n]:s}:{}}function Ra(e,t="text"){const n=e.encoding[t];return mi(e,n,t,i=>os(i,e.config))}function os(e,t,n="datum"){if(e){if(Qe(e))return re(e.value);if(M(e)){const{format:i,formatType:r}=Er(e);return da({fieldOrDatumDef:e,format:i,formatType:r,expr:n,config:t})}}}function gd(e,t={}){const{encoding:n,markDef:i,config:r,stack:s}=e,o=n.tooltip;if(O(o))return{tooltip:Xc({tooltip:o},s,r,t)};{const a=t.reactiveGeom?"datum.datum":"datum";return mi(e,o,"tooltip",c=>{const l=os(c,r,a);if(l)return l;if(c===null)return;let u=K("tooltip",i,r);if(u===!0&&(u={content:"encoding"}),z(u))return{value:u};if(Y(u))return R(u)?u:u.content==="encoding"?Xc(n,s,r,t):{signal:a}})}}function hd(e,t,n,{reactiveGeom:i}={}){const r={...n,...n.tooltipFormat},s={},o=i?"datum.datum":"datum",a=[];function c(u,f){const d=On(f),p=Ne(u)?u:{...u,type:e[d].type},g=p.title||ma(p,r),h=ce(g).join(", ");let m;if(be(f)){const y=f==="x"?"x2":"y2",b=ht(e[y]);if(ve(p.bin)&&b){const N=C(p,{expr:o}),P=C(b,{expr:o}),{format:x,formatType:_}=Er(p);m=Xi(N,P,x,_,r),s[y]=!0}}if((be(f)||f===We||f===tt)&&t&&t.fieldChannel===f&&t.offset==="normalize"){const{format:y,formatType:b}=Er(p);m=da({fieldOrDatumDef:p,format:y,formatType:b,expr:o,config:r,normalizeStack:!0}).signal}m??(m=os(p,r,o).signal),a.push({channel:f,key:h,value:m})}xa(e,(u,f)=>{E(u)?c(u,f):Qr(u)&&c(u.condition,f)});const l={};for(const{channel:u,key:f,value:d}of a)!s[u]&&!l[f]&&(l[f]=d);return l}function Xc(e,t,n,{reactiveGeom:i}={}){const r=hd(e,t,n,{reactiveGeom:i}),s=Gt(r).map(([o,a])=>`"${o}": ${a}`);return s.length>0?{signal:`{${s.join(", ")}}`}:void 0}function Ex(e){const{markDef:t,config:n}=e,i=K("aria",t,n);return i===!1?{}:{...i?{aria:i}:{},...$x(e),...wx(e)}}function $x(e){const{mark:t,markDef:n,config:i}=e;if(i.aria===!1)return{};const r=K("ariaRoleDescription",n,i);return r!=null?{ariaRoleDescription:{value:r}}:t in nh?{}:{ariaRoleDescription:{value:t}}}function wx(e){const{encoding:t,markDef:n,config:i,stack:r}=e,s=t.description;if(s)return mi(e,s,"description",c=>os(c,e.config));const o=K("description",n,i);if(o!=null)return{description:re(o)};if(i.aria===!1)return{};const a=hd(t,r,i);if(!Q(a))return{description:{signal:Gt(a).map(([c,l],u)=>`"${u>0?"; ":""}${c}: " + (${l})`).join(" + ")}}}function he(e,t,n={}){const{markDef:i,encoding:r,config:s}=t,{vgChannel:o}=n;let{defaultRef:a,defaultValue:c}=n;a===void 0&&(c??(c=K(e,i,s,{vgChannel:o,ignoreVgConfig:!0})),c!==void 0&&(a=re(c)));const l=r[e];return mi(t,l,o??e,u=>fa({channel:e,channelDef:u,markDef:i,config:s,scaleName:t.scaleName(e),scale:t.getScaleComponent(e),stack:null,defaultRef:a}))}function md(e,t={filled:void 0}){const{markDef:n,encoding:i,config:r}=e,{type:s}=n,o=t.filled??K("filled",n,r),a=q(["bar","point","circle","square","geoshape"],s)?"transparent":void 0,c=K(o===!0?"color":void 0,n,r,{vgChannel:"fill"})??r.mark[o===!0&&"color"]??a,l=K(o===!1?"color":void 0,n,r,{vgChannel:"stroke"})??r.mark[o===!1&&"color"],u=o?"fill":"stroke",f={...c?{fill:re(c)}:{},...l?{stroke:re(l)}:{}};return n.color&&(o?n.fill:n.stroke)&&S(Iu("property",{fill:"fill"in n,stroke:"stroke"in n})),{...f,...he("color",e,{vgChannel:u,defaultValue:o?c:l}),...he("fill",e,{defaultValue:i.fill?c:void 0}),...he("stroke",e,{defaultValue:i.stroke?l:void 0})}}function Cx(e){const{encoding:t,mark:n}=e,i=t.order;return!rn(n)&&Qe(i)?mi(e,i,"zindex",r=>re(r.value)):{}}function oi({channel:e,markDef:t,encoding:n={},model:i,bandPosition:r}){const s=`${e}Offset`,o=t[s],a=n[s];if((s==="xOffset"||s==="yOffset")&&a)return{offsetType:"encoding",offset:fa({channel:s,channelDef:a,markDef:t,config:i==null?void 0:i.config,scaleName:i.scaleName(s),scale:i.getScaleComponent(s),stack:null,defaultRef:re(o),bandPosition:r})};const c=t[s];return c?{offsetType:"visual",offset:c}:{}}function $e(e,t,{defaultPos:n,vgChannel:i}){const{encoding:r,markDef:s,config:o,stack:a}=t,c=r[e],l=r[vt(e)],u=t.scaleName(e),f=t.getScaleComponent(e),{offset:d,offsetType:p}=oi({channel:e,markDef:s,encoding:r,model:t,bandPosition:.5}),g=Ia({model:t,defaultPos:n,channel:e,scaleName:u,scale:f}),h=!c&&be(e)&&(r.latitude||r.longitude)?{field:t.getName(e)}:Nx({channel:e,channelDef:c,channel2Def:l,markDef:s,config:o,scaleName:u,scale:f,stack:a,offset:d,defaultRef:g,bandPosition:p==="encoding"?0:void 0});return h?{[i||e]:h}:void 0}function Nx(e){const{channel:t,channelDef:n,scaleName:i,stack:r,offset:s,markDef:o}=e;if(M(n)&&r&&t===r.fieldChannel){if(E(n)){let a=n.bandPosition;if(a===void 0&&o.type==="text"&&(t==="radius"||t==="theta")&&(a=.5),a!==void 0)return xr({scaleName:i,fieldOrDatumDef:n,startSuffix:"start",bandPosition:a,offset:s})}return hn(n,i,{suffix:"end"},{offset:s})}return la(e)}function Ia({model:e,defaultPos:t,channel:n,scaleName:i,scale:r}){const{markDef:s,config:o}=e;return()=>{const a=On(n),c=qt(n),l=K(n,s,o,{vgChannel:c});if(l!==void 0)return Ri(n,l);switch(t){case"zeroOrMin":case"zeroOrMax":if(i){const u=r.get("type");if(!q([ke.LOG,ke.TIME,ke.UTC],u)){if(r.domainDefinitelyIncludesZero())return{scale:i,value:0}}}if(t==="zeroOrMin")return a==="y"?{field:{group:"height"}}:{value:0};switch(a){case"radius":return{signal:`min(${e.width.signal},${e.height.signal})/2`};case"theta":return{signal:"2*PI"};case"x":return{field:{group:"width"}};case"y":return{value:0}}break;case"mid":return{...e[Ie(n)],mult:.5}}}}const Fx={left:"x",center:"xc",right:"x2"},_x={top:"y",middle:"yc",bottom:"y2"};function yd(e,t,n,i="middle"){if(e==="radius"||e==="theta")return qt(e);const r=e==="x"?"align":"baseline",s=K(r,t,n);let o;return R(s)?(S(Lh(r)),o=void 0):o=s,e==="x"?Fx[o||(i==="top"?"left":"center")]:_x[o||i]}function Fr(e,t,{defaultPos:n,defaultPos2:i,range:r}){return r?bd(e,t,{defaultPos:n,defaultPos2:i}):$e(e,t,{defaultPos:n})}function bd(e,t,{defaultPos:n,defaultPos2:i}){const{markDef:r,config:s}=t,o=vt(e),a=Ie(e),c=Tx(t,i,o),l=c[a]?yd(e,r,s):qt(e);return{...$e(e,t,{defaultPos:n,vgChannel:l}),...c}}function Tx(e,t,n){const{encoding:i,mark:r,markDef:s,stack:o,config:a}=e,c=On(n),l=Ie(n),u=qt(n),f=i[c],d=e.scaleName(c),p=e.getScaleComponent(c),{offset:g}=n in i||n in s?oi({channel:n,markDef:s,encoding:i,model:e}):oi({channel:c,markDef:s,encoding:i,model:e});if(!f&&(n==="x2"||n==="y2")&&(i.latitude||i.longitude)){const m=Ie(n),y=e.markDef[m];return y!=null?{[m]:{value:y}}:{[u]:{field:e.getName(n)}}}const h=kx({channel:n,channelDef:f,channel2Def:i[n],markDef:s,config:a,scaleName:d,scale:p,stack:o,offset:g,defaultRef:void 0});return h!==void 0?{[u]:h}:rr(n,s)||rr(n,{[n]:mr(n,s,a.style),[l]:mr(l,s,a.style)})||rr(n,a[r])||rr(n,a.mark)||{[u]:Ia({model:e,defaultPos:t,channel:n,scaleName:d,scale:p})()}}function kx({channel:e,channelDef:t,channel2Def:n,markDef:i,config:r,scaleName:s,scale:o,stack:a,offset:c,defaultRef:l}){return M(t)&&a&&e.charAt(0)===a.fieldChannel.charAt(0)?hn(t,s,{suffix:"start"},{offset:c}):la({channel:e,channelDef:n,scaleName:s,scale:o,stack:a,markDef:i,config:r,offset:c,defaultRef:l})}function rr(e,t){const n=Ie(e),i=qt(e);if(t[i]!==void 0)return{[i]:Ri(e,t[i])};if(t[e]!==void 0)return{[i]:Ri(e,t[e])};if(t[n]){const r=t[n];if(wn(r))S(_h(n));else return{[n]:Ri(e,r)}}}function Vt(e,t){const{config:n,encoding:i,markDef:r}=e,s=r.type,o=vt(t),a=Ie(t),c=i[t],l=i[o],u=e.getScaleComponent(t),f=u?u.get("type"):void 0,d=r.orient,p=i[a]??i.size??K("size",r,n,{vgChannel:a}),g=mu(t),h=s==="bar"&&(t==="x"?d==="vertical":d==="horizontal");return E(c)&&(ne(c.bin)||ve(c.bin)||c.timeUnit&&!l)&&!(p&&!wn(p))&&!i[g]&&!xe(f)?Rx({fieldDef:c,fieldDef2:l,channel:t,model:e}):(M(c)&&xe(f)||h)&&!l?Ax(c,t,e):bd(t,e,{defaultPos:"zeroOrMax",defaultPos2:"zeroOrMin"})}function Ox(e,t,n,i,r,s,o){if(wn(r))if(n){const c=n.get("type");if(c==="band"){let l=`bandwidth('${t}')`;r.band!==1&&(l=`${r.band} * ${l}`);const u=At("minBandSize",{type:o},i);return{signal:u?`max(${Ve(u)}, ${l})`:l}}else r.band!==1&&(S(jh(c)),r=void 0)}else return{mult:r.band,field:{group:e}};else{if(R(r))return r;if(r)return{value:r}}if(n){const c=n.get("range");if(nn(c)&&ue(c.step))return{value:c.step-2}}if(!s){const{bandPaddingInner:c,barBandPaddingInner:l,rectBandPaddingInner:u}=i.scale,f=fe(c,o==="bar"?l:u);if(R(f))return{signal:`(1 - (${f.signal})) * ${e}`};if(ue(f))return{signal:`${1-f} * ${e}`}}return{value:Cr(i.view,e)-2}}function Ax(e,t,n){const{markDef:i,encoding:r,config:s,stack:o}=n,a=i.orient,c=n.scaleName(t),l=n.getScaleComponent(t),u=Ie(t),f=vt(t),d=mu(t),p=n.scaleName(d),g=n.getScaleComponent(jo(t)),h=a==="horizontal"&&t==="y"||a==="vertical"&&t==="x";let m;(r.size||i.size)&&(h?m=he("size",n,{vgChannel:u,defaultRef:re(i.size)}):S(Bh(i.type)));const y=!!m,b=yf({channel:t,fieldDef:e,markDef:i,config:s,scaleType:l==null?void 0:l.get("type"),useVlSizeChannel:h});m=m||{[u]:Ox(u,p||c,g||l,s,b,!!e,i.type)};const N=(l==null?void 0:l.get("type"))==="band"&&wn(b)&&!y?"top":"middle",P=yd(t,i,s,N),x=P==="xc"||P==="yc",{offset:_,offsetType:w}=oi({channel:t,markDef:i,encoding:r,model:n,bandPosition:x?.5:0}),T=la({channel:t,channelDef:e,markDef:i,config:s,scaleName:c,scale:l,stack:o,offset:_,defaultRef:Ia({model:n,defaultPos:"mid",channel:t,scaleName:c,scale:l}),bandPosition:x?w==="encoding"?0:.5:R(b)?{signal:`(1-${b})/2`}:wn(b)?(1-b.band)/2:0});if(u)return{[P]:T,...m};{const W=qt(f),X=m[u],le=_?{...X,offset:_}:X;return{[P]:T,[W]:O(T)?[T[0],{...T[1],offset:le}]:{...T,offset:le}}}}function Yc(e,t,n,i,r,s,o){if(uu(e))return 0;const a=e==="x"||e==="y2",c=a?-t/2:t/2;if(R(n)||R(r)||R(i)||s){const l=Ve(n),u=Ve(r),f=Ve(i),d=Ve(s),g=s?`(${o} < ${d} ? ${a?"":"-"}0.5 * (${d} - (${o})) : ${c})`:c,h=f?`${f} + `:"",m=l?`(${l} ? -1 : 1) * `:"",y=u?`(${u} + ${g})`:g;return{signal:h+m+y}}else return r=r||0,i+(n?-r-c:+r+c)}function Rx({fieldDef:e,fieldDef2:t,channel:n,model:i}){var W;const{config:r,markDef:s,encoding:o}=i,a=i.getScaleComponent(n),c=i.scaleName(n),l=a?a.get("type"):void 0,u=a.get("reverse"),f=yf({channel:n,fieldDef:e,markDef:s,config:r,scaleType:l}),d=(W=i.component.axes[n])==null?void 0:W[0],p=(d==null?void 0:d.get("translate"))??.5,g=be(n)?K("binSpacing",s,r)??0:0,h=vt(n),m=qt(n),y=qt(h),b=At("minBandSize",s,r),{offset:N}=oi({channel:n,markDef:s,encoding:o,model:i,bandPosition:0}),{offset:P}=oi({channel:h,markDef:s,encoding:o,model:i,bandPosition:0}),x=uy({fieldDef:e,scaleName:c}),_=Yc(n,g,u,p,N,b,x),w=Yc(h,g,u,p,P??N,b,x),T=R(f)?{signal:`(1-${f.signal})/2`}:wn(f)?(1-f.band)/2:.5;if(ne(e.bin)||e.timeUnit)return{[y]:Kc({fieldDef:e,scaleName:c,bandPosition:T,offset:w}),[m]:Kc({fieldDef:e,scaleName:c,bandPosition:R(T)?{signal:`1-${T.signal}`}:1-T,offset:_})};if(ve(e.bin)){const X=hn(e,c,{},{offset:w});if(E(t))return{[y]:X,[m]:hn(t,c,{},{offset:_})};if(An(e.bin)&&e.bin.step)return{[y]:X,[m]:{signal:`scale("${c}", ${C(e,{expr:"datum"})} + ${e.bin.step})`,offset:_}}}S(zu(h))}function Kc({fieldDef:e,scaleName:t,bandPosition:n,offset:i}){return xr({scaleName:t,fieldOrDatumDef:e,bandPosition:n,offset:i})}const Ix=new Set(["aria","width","height"]);function Be(e,t){const{fill:n=void 0,stroke:i=void 0}=t.color==="include"?md(e):{};return{...Lx(e.markDef,t),...Jc(e,"fill",n),...Jc(e,"stroke",i),...he("opacity",e),...he("fillOpacity",e),...he("strokeOpacity",e),...he("strokeWidth",e),...he("strokeDash",e),...Cx(e),...gd(e),...Ra(e,"href"),...Ex(e)}}function Jc(e,t,n){const{config:i,mark:r,markDef:s}=e;if(K("invalid",s,i)==="hide"&&n&&!rn(r)){const a=Px(e,{invalid:!0,channels:Ur});if(a)return{[t]:[{test:a,value:null},...ce(n)]}}return n?{[t]:n}:{}}function Lx(e,t){return th.reduce((n,i)=>(!Ix.has(i)&&e[i]!==void 0&&t[i]!=="ignore"&&(n[i]=re(e[i])),n),{})}function Px(e,{invalid:t=!1,channels:n}){const i=n.reduce((s,o)=>{const a=e.getScaleComponent(o);if(a){const c=a.get("type"),l=e.vgField(o,{expr:"datum"});l&&ze(c)&&(s[l]=!0)}return s},{}),r=v(i);if(r.length>0){const s=t?"||":"&&";return r.map(o=>ua(o,t)).join(` ${s} `)}}function La(e){const{config:t,markDef:n}=e;if(K("invalid",n,t)){const r=zx(e,{channels:St});if(r)return{defined:{signal:r}}}return{}}function zx(e,{invalid:t=!1,channels:n}){const i=n.reduce((s,o)=>{var c;const a=e.getScaleComponent(o);if(a){const l=a.get("type"),u=e.vgField(o,{expr:"datum",binSuffix:(c=e.stack)!=null&&c.impute?"mid":void 0});u&&ze(l)&&(s[u]=!0)}return s},{}),r=v(i);if(r.length>0){const s=t?"||":"&&";return r.map(o=>ua(o,t)).join(` ${s} `)}}function Qc(e,t){if(t!==void 0)return{[e]:re(t)}}const Rs="voronoi",xd={defined:e=>e.type==="point"&&e.nearest,parse:(e,t)=>{if(t.events)for(const n of t.events)n.markname=e.getName(Rs)},marks:(e,t,n)=>{const{x:i,y:r}=t.project.hasChannel,s=e.mark;if(rn(s))return S(ah(s)),n;const o={name:e.getName(Rs),type:"path",interactive:!0,from:{data:e.getName("marks")},encode:{update:{fill:{value:"transparent"},strokeWidth:{value:.35},stroke:{value:"transparent"},isVoronoi:{value:!0},...gd(e,{reactiveGeom:!0})}},transform:[{type:"voronoi",x:{expr:i||!r?"datum.datum.x || 0":"0"},y:{expr:r||!i?"datum.datum.y || 0":"0"},size:[e.getSizeSignalRef("width"),e.getSizeSignalRef("height")]}]};let a=0,c=!1;return n.forEach((l,u)=>{const f=l.name??"";f===e.component.mark[0].name?a=u:f.indexOf(Rs)>=0&&(c=!0)}),c||n.splice(a+1,0,o),n}},vd={defined:e=>e.type==="point"&&e.resolve==="global"&&e.bind&&e.bind!=="scales"&&!Ca(e.bind),parse:(e,t,n)=>_d(t,n),topLevelSignals:(e,t,n)=>{const i=t.name,r=t.project,s=t.bind,o=t.init&&t.init[0],a=xd.defined(t)?"(item().isVoronoi ? datum.datum : datum)":"datum";return r.items.forEach((c,l)=>{const u=se(`${i}_${c.field}`);n.filter(d=>d.name===u).length||n.unshift({name:u,...o?{init:Nn(o[l])}:{value:null},on:t.events?[{events:t.events,update:`datum && item().mark.marktype !== 'group' ? ${a}[${B(c.field)}] : null`}]:[],bind:s[c.field]??s[c.channel]??s})}),n},signals:(e,t,n)=>{const i=t.name,r=t.project,s=n.filter(l=>l.name===i+Xt)[0],o=i+Zi,a=r.items.map(l=>se(`${i}_${l.field}`)),c=a.map(l=>`${l} !== null`).join(" && ");return a.length&&(s.update=`${c} ? {fields: ${o}, values: [${a.join(", ")}]} : null`),delete s.value,delete s.on,n}},_r="_toggle",Sd={defined:e=>e.type==="point"&&!!e.toggle,signals:(e,t,n)=>n.concat({name:t.name+_r,value:!1,on:[{events:t.events,update:t.toggle}]}),modifyExpr:(e,t)=>{const n=t.name+Xt,i=t.name+_r;return`${i} ? null : ${n}, `+(t.resolve==="global"?`${i} ? null : true, `:`${i} ? null : {unit: ${yn(e)}}, `)+`${i} ? ${n} : null`}},Dx={defined:e=>e.clear!==void 0&&e.clear!==!1,parse:(e,t)=>{t.clear&&(t.clear=z(t.clear)?di(t.clear,"view"):t.clear)},topLevelSignals:(e,t,n)=>{if(vd.defined(t))for(const i of t.project.items){const r=n.findIndex(s=>s.name===se(`${t.name}_${i.field}`));r!==-1&&n[r].on.push({events:t.clear,update:"null"})}return n},signals:(e,t,n)=>{function i(r,s){r!==-1&&n[r].on&&n[r].on.push({events:t.clear,update:s})}if(t.type==="interval")for(const r of t.project.items){const s=n.findIndex(o=>o.name===r.signals.visual);if(i(s,"[0, 0]"),s===-1){const o=n.findIndex(a=>a.name===r.signals.data);i(o,"null")}}else{let r=n.findIndex(s=>s.name===t.name+Xt);i(r,"null"),Sd.defined(t)&&(r=n.findIndex(s=>s.name===t.name+_r),i(r,"false"))}return n}},Ed={defined:e=>{const t=e.resolve==="global"&&e.bind&&Ca(e.bind),n=e.project.items.length===1&&e.project.items[0].field!==Ze;return t&&!n&&S(uh),t&&n},parse:(e,t,n)=>{const i=j(n);if(i.select=z(i.select)?{type:i.select,toggle:t.toggle}:{...i.select,toggle:t.toggle},_d(t,i),Y(n.select)&&(n.select.on||n.select.clear)){const o='event.item && indexof(event.item.mark.role, "legend") < 0';for(const a of t.events)a.filter=ce(a.filter??[]),a.filter.includes(o)||a.filter.push(o)}const r=Ts(t.bind)?t.bind.legend:"click",s=z(r)?di(r,"view"):ce(r);t.bind={legend:{merge:s}}},topLevelSignals:(e,t,n)=>{const i=t.name,r=Ts(t.bind)&&t.bind.legend,s=o=>a=>{const c=j(a);return c.markname=o,c};for(const o of t.project.items){if(!o.hasLegend)continue;const a=`${se(o.field)}_legend`,c=`${i}_${a}`;if(n.filter(u=>u.name===c).length===0){const u=r.merge.map(s(`${a}_symbols`)).concat(r.merge.map(s(`${a}_labels`))).concat(r.merge.map(s(`${a}_entries`)));n.unshift({name:c,...t.init?{}:{value:null},on:[{events:u,update:"isDefined(datum.value) ? datum.value : item().items[0].items[0].datum.value",force:!0},{events:r.merge,update:`!event.item || !datum ? null : ${c}`,force:!0}]})}}return n},signals:(e,t,n)=>{const i=t.name,r=t.project,s=n.find(d=>d.name===i+Xt),o=i+Zi,a=r.items.filter(d=>d.hasLegend).map(d=>se(`${i}_${se(d.field)}_legend`)),l=`${a.map(d=>`${d} !== null`).join(" && ")} ? {fields: ${o}, values: [${a.join(", ")}]} : null`;t.events&&a.length>0?s.on.push({events:a.map(d=>({signal:d})),update:l}):a.length>0&&(s.update=l,delete s.value,delete s.on);const u=n.find(d=>d.name===i+_r),f=Ts(t.bind)&&t.bind.legend;return u&&(t.events?u.on.push({...u.on[0],events:f}):u.on[0].events=f),n}};function jx(e,t,n){var r;const i=(r=e.fieldDef(t))==null?void 0:r.field;for(const s of Se(e.component.selection??{})){const o=s.project.hasField[i]??s.project.hasChannel[t];if(o&&Ed.defined(s)){const a=n.get("selections")??[];a.push(s.name),n.set("selections",a,!1),o.hasLegend=!0}}}const $d="_translate_anchor",wd="_translate_delta",Mx={defined:e=>e.type==="interval"&&e.translate,signals:(e,t,n)=>{const i=t.name,r=Tt.defined(t),s=i+$d,{x:o,y:a}=t.project.hasChannel;let c=di(t.translate,"scope");return r||(c=c.map(l=>(l.between[0].markname=i+Vn,l))),n.push({name:s,value:{},on:[{events:c.map(l=>l.between[0]),update:"{x: x(unit), y: y(unit)"+(o!==void 0?`, extent_x: ${r?po(e,oe):`slice(${o.signals.visual})`}`:"")+(a!==void 0?`, extent_y: ${r?po(e,ye):`slice(${a.signals.visual})`}`:"")+"}"}]},{name:i+wd,value:{},on:[{events:c,update:`{x: ${s}.x - x(unit), y: ${s}.y - y(unit)}`}]}),o!==void 0&&Zc(e,t,o,"width",n),a!==void 0&&Zc(e,t,a,"height",n),n}};function Zc(e,t,n,i,r){const s=t.name,o=s+$d,a=s+wd,c=n.channel,l=Tt.defined(t),u=r.filter(x=>x.name===n.signals[l?"data":"visual"])[0],f=e.getSizeSignalRef(i).signal,d=e.getScaleComponent(c),p=d&&d.get("type"),g=d&&d.get("reverse"),h=l?c===oe?g?"":"-":g?"-":"":"",m=`${o}.extent_${c}`,y=`${h}${a}.${c} / ${l?`${f}`:`span(${m})`}`,b=!l||!d?"panLinear":p==="log"?"panLog":p==="symlog"?"panSymlog":p==="pow"?"panPow":"panLinear",N=l?p==="pow"?`, ${d.get("exponent")??1}`:p==="symlog"?`, ${d.get("constant")??1}`:"":"",P=`${b}(${m}, ${y}${N})`;u.on.push({events:{signal:a},update:l?P:`clampRange(${P}, 0, ${f})`})}const Cd="_zoom_anchor",Nd="_zoom_delta",Ux={defined:e=>e.type==="interval"&&e.zoom,signals:(e,t,n)=>{const i=t.name,r=Tt.defined(t),s=i+Nd,{x:o,y:a}=t.project.hasChannel,c=B(e.scaleName(oe)),l=B(e.scaleName(ye));let u=di(t.zoom,"scope");return r||(u=u.map(f=>(f.markname=i+Vn,f))),n.push({name:i+Cd,on:[{events:u,update:r?"{"+[c?`x: invert(${c}, x(unit))`:"",l?`y: invert(${l}, y(unit))`:""].filter(f=>f).join(", ")+"}":"{x: x(unit), y: y(unit)}"}]},{name:s,on:[{events:u,force:!0,update:"pow(1.001, event.deltaY * pow(16, event.deltaMode))"}]}),o!==void 0&&el(e,t,o,"width",n),a!==void 0&&el(e,t,a,"height",n),n}};function el(e,t,n,i,r){const s=t.name,o=n.channel,a=Tt.defined(t),c=r.filter(b=>b.name===n.signals[a?"data":"visual"])[0],l=e.getSizeSignalRef(i).signal,u=e.getScaleComponent(o),f=u&&u.get("type"),d=a?po(e,o):c.name,p=s+Nd,g=`${s}${Cd}.${o}`,h=!a||!u?"zoomLinear":f==="log"?"zoomLog":f==="symlog"?"zoomSymlog":f==="pow"?"zoomPow":"zoomLinear",m=a?f==="pow"?`, ${u.get("exponent")??1}`:f==="symlog"?`, ${u.get("constant")??1}`:"":"",y=`${h}(${d}, ${g}, ${p}${m})`;c.on.push({events:{signal:p},update:a?y:`clampRange(${y}, 0, ${l})`})}const Fn="_store",Xt="_tuple",Wx="_modify",Fd="vlSelectionResolve",as=[Sx,xx,yx,Sd,vd,Tt,Ed,Dx,Mx,Ux,xd];function Bx(e){let t=e.parent;for(;t&&!Je(t);)t=t.parent;return t}function yn(e,{escape:t}={escape:!0}){let n=t?B(e.name):e.name;const i=Bx(e);if(i){const{facet:r}=i;for(const s of je)r[s]&&(n+=` + '__facet_${s}_' + (facet[${B(i.vgField(s))}])`)}return n}function Pa(e){return Se(e.component.selection??{}).reduce((t,n)=>t||n.project.hasSelectionId,!1)}function _d(e,t){(z(t.select)||!t.select.on)&&delete e.events,(z(t.select)||!t.select.clear)&&delete e.clear,(z(t.select)||!t.select.toggle)&&delete e.toggle}function go(e){const t=[];return e.type==="Identifier"?[e.name]:e.type==="Literal"?[e.value]:(e.type==="MemberExpression"&&(t.push(...go(e.object)),t.push(...go(e.property))),t)}function Td(e){return e.object.type==="MemberExpression"?Td(e.object):e.object.name==="datum"}function kd(e){const t=Hp(e),n=new Set;return t.visit(i=>{i.type==="MemberExpression"&&Td(i)&&n.add(go(i).slice(1).join("."))}),n}class yi extends J{clone(){return new yi(null,this.model,j(this.filter))}constructor(t,n,i){super(t),this.model=n,this.filter=i,this.expr=Tr(this.model,this.filter,this),this._dependentFields=kd(this.expr)}dependentFields(){return this._dependentFields}producedFields(){return new Set}assemble(){return{type:"filter",expr:this.expr}}hash(){return`Filter ${this.expr}`}}function Gx(e,t){const n={},i=e.config.selection;if(!t||!t.length)return n;for(const r of t){const s=se(r.name),o=r.select,a=z(o)?o:o.type,c=Y(o)?j(o):{type:a},l=i[a];for(const d in l)d==="fields"||d==="encodings"||(d==="mark"&&(c[d]={...l[d],...c[d]}),(c[d]===void 0||c[d]===!0)&&(c[d]=j(l[d]??c[d])));const u=n[s]={...c,name:s,type:a,init:r.value,bind:r.bind,events:z(c.on)?di(c.on,"scope"):ce(j(c.on))},f=j(r);for(const d of as)d.defined(u)&&d.parse&&d.parse(e,u,f)}return n}function Od(e,t,n,i="datum"){const r=z(t)?t:t.param,s=se(r),o=B(s+Fn);let a;try{a=e.getSelectionComponent(s,r)}catch{return`!!${s}`}if(a.project.timeUnit){const d=n??e.component.data.raw,p=a.project.timeUnit.clone();d.parent?p.insertAsParentOf(d):d.parent=p}const c=a.project.hasSelectionId?"vlSelectionIdTest(":"vlSelectionTest(",l=a.resolve==="global"?")":`, ${B(a.resolve)})`,u=`${c}${o}, ${i}${l}`,f=`length(data(${o}))`;return t.empty===!1?`${f} && ${u}`:`!${f} || ${u}`}function Ad(e,t,n){const i=se(t),r=n.encoding;let s=n.field,o;try{o=e.getSelectionComponent(i,t)}catch{return i}if(!r&&!s)s=o.project.items[0].field,o.project.items.length>1&&S(`A "field" or "encoding" must be specified when using a selection as a scale domain. Using "field": ${B(s)}.`);else if(r&&!s){const a=o.project.items.filter(c=>c.channel===r);!a.length||a.length>1?(s=o.project.items[0].field,S((a.length?"Multiple ":"No ")+`matching ${B(r)} encoding found for selection ${B(n.param)}. Using "field": ${B(s)}.`)):s=a[0].field}return`${o.name}[${B(Me(s))}]`}function qx(e,t){for(const[n,i]of Gt(e.component.selection??{})){const r=e.getName(`lookup_${n}`);e.component.data.outputNodes[r]=i.materialized=new Ce(new yi(t,e,{param:n}),r,ee.Lookup,e.component.data.outputNodeRefCounts)}}function Tr(e,t,n){return Ai(t,i=>z(i)?i:Nm(i)?Od(e,i,n):Yu(i))}function Hx(e,t){if(e)return O(e)&&!Ut(e)?e.map(n=>ma(n,t)).join(", "):e}function Is(e,t,n,i){var r,s;e.encode??(e.encode={}),(r=e.encode)[t]??(r[t]={}),(s=e.encode[t]).update??(s.update={}),e.encode[t].update[n]=i}function _i(e,t,n,i={header:!1}){var f,d;const{disable:r,orient:s,scale:o,labelExpr:a,title:c,zindex:l,...u}=e.combine();if(!r){for(const p in u){const g=Ty[p],h=u[p];if(g&&g!==t&&g!=="both")delete u[p];else if(Qi(h)){const{condition:m,...y}=h,b=ce(m),N=kc[p];if(N){const{vgProp:P,part:x}=N,_=[...b.map(w=>{const{test:T,...W}=w;return{test:Tr(null,T),...W}}),y];Is(u,x,P,_),delete u[p]}else if(N===null){const P={signal:b.map(x=>{const{test:_,...w}=x;return`${Tr(null,_)} ? ${gc(w)} : `}).join("")+gc(y)};u[p]=P}}else if(R(h)){const m=kc[p];if(m){const{vgProp:y,part:b}=m;Is(u,b,y,h),delete u[p]}}q(["labelAlign","labelBaseline"],p)&&u[p]===null&&delete u[p]}if(t==="grid"){if(!u.grid)return;if(u.encode){const{grid:p}=u.encode;u.encode={...p?{grid:p}:{}},Q(u.encode)&&delete u.encode}return{scale:o,orient:s,...u,domain:!1,labels:!1,aria:!1,maxExtent:0,minExtent:0,ticks:!1,zindex:fe(l,0)}}else{if(!i.header&&e.mainExtracted)return;if(a!==void 0){let g=a;(d=(f=u.encode)==null?void 0:f.labels)!=null&&d.update&&R(u.encode.labels.update.text)&&(g=vn(a,"datum.label",u.encode.labels.update.text.signal)),Is(u,"labels","text",{signal:g})}if(u.labelAlign===null&&delete u.labelAlign,u.encode){for(const g of Ff)e.hasAxisPart(g)||delete u.encode[g];Q(u.encode)&&delete u.encode}const p=Hx(c,n);return{scale:o,orient:s,grid:!1,...p?{title:p}:{},...u,...n.aria===!1?{aria:!1}:{},zindex:fe(l,0)}}}}function Rd(e){const{axes:t}=e.component,n=[];for(const i of St)if(t[i]){for(const r of t[i])if(!r.get("disable")&&!r.get("gridScale")){const s=i==="x"?"height":"width",o=e.getSizeSignalRef(s).signal;s!==o&&n.push({name:s,update:o})}}return n}function Vx(e,t){const{x:n=[],y:i=[]}=e;return[...n.map(r=>_i(r,"grid",t)),...i.map(r=>_i(r,"grid",t)),...n.map(r=>_i(r,"main",t)),...i.map(r=>_i(r,"main",t))].filter(r=>r)}function tl(e,t,n,i){return Object.assign.apply(null,[{},...e.map(r=>{if(r==="axisOrient"){const s=n==="x"?"bottom":"left",o=t[n==="x"?"axisBottom":"axisLeft"]||{},a=t[n==="x"?"axisTop":"axisRight"]||{},c=new Set([...v(o),...v(a)]),l={};for(const u of c.values())l[u]={signal:`${i.signal} === "${s}" ? ${Ve(o[u])} : ${Ve(a[u])}`};return l}return t[r]})])}function Xx(e,t,n,i){const r=t==="band"?["axisDiscrete","axisBand"]:t==="point"?["axisDiscrete","axisPoint"]:Zu(t)?["axisQuantitative"]:t==="time"||t==="utc"?["axisTemporal"]:[],s=e==="x"?"axisX":"axisY",o=R(n)?"axisOrient":`axis${Bi(n)}`,a=[...r,...r.map(l=>s+l.substr(4))],c=["axis",o,s];return{vlOnlyAxisConfig:tl(a,i,e,n),vgAxisConfig:tl(c,i,e,n),axisConfigStyle:Yx([...c,...a],i)}}function Yx(e,t){var i;const n=[{}];for(const r of e){let s=(i=t[r])==null?void 0:i.style;if(s){s=ce(s);for(const o of s)n.push(t.style[o])}}return Object.assign.apply(null,n)}function ho(e,t,n,i={}){var s;const r=Fu(e,n,t);if(r!==void 0)return{configFrom:"style",configValue:r};for(const o of["vlOnlyAxisConfig","vgAxisConfig","axisConfigStyle"])if(((s=i[o])==null?void 0:s[e])!==void 0)return{configFrom:o,configValue:i[o][e]};return{}}const nl={scale:({model:e,channel:t})=>e.scaleName(t),format:({format:e})=>e,formatType:({formatType:e})=>e,grid:({fieldOrDatumDef:e,axis:t,scaleType:n})=>t.grid??Kx(n,e),gridScale:({model:e,channel:t})=>Jx(e,t),labelAlign:({axis:e,labelAngle:t,orient:n,channel:i})=>e.labelAlign||Ld(t,n,i),labelAngle:({labelAngle:e})=>e,labelBaseline:({axis:e,labelAngle:t,orient:n,channel:i})=>e.labelBaseline||Id(t,n,i),labelFlush:({axis:e,fieldOrDatumDef:t,channel:n})=>e.labelFlush??Zx(t.type,n),labelOverlap:({axis:e,fieldOrDatumDef:t,scaleType:n})=>e.labelOverlap??ev(t.type,n,E(t)&&!!t.timeUnit,E(t)?t.sort:void 0),orient:({orient:e})=>e,tickCount:({channel:e,model:t,axis:n,fieldOrDatumDef:i,scaleType:r})=>{const s=e==="x"?"width":e==="y"?"height":void 0,o=s?t.getSizeSignalRef(s):void 0;return n.tickCount??nv({fieldOrDatumDef:i,scaleType:r,size:o,values:n.values})},tickMinStep:iv,title:({axis:e,model:t,channel:n})=>{if(e.title!==void 0)return e.title;const i=Pd(t,n);if(i!==void 0)return i;const r=t.typedFieldDef(n),s=n==="x"?"x2":"y2",o=t.fieldDef(s);return Tu(r?[_c(r)]:[],E(o)?[_c(o)]:[])},values:({axis:e,fieldOrDatumDef:t})=>rv(e,t),zindex:({axis:e,fieldOrDatumDef:t,mark:n})=>e.zindex??sv(n,t)};function Kx(e,t){return!xe(e)&&E(t)&&!ne(t==null?void 0:t.bin)&&!ve(t==null?void 0:t.bin)}function Jx(e,t){const n=t==="x"?"y":"x";if(e.getScaleComponent(n))return e.scaleName(n)}function Qx(e,t,n,i,r){const s=t==null?void 0:t.labelAngle;if(s!==void 0)return R(s)?s:zi(s);{const{configValue:o}=ho("labelAngle",i,t==null?void 0:t.style,r);return o!==void 0?zi(o):n===oe&&q([ra,ia],e.type)&&!(E(e)&&e.timeUnit)?270:void 0}}function mo(e){return`(((${e.signal} % 360) + 360) % 360)`}function Id(e,t,n,i){if(e!==void 0)if(n==="x"){if(R(e)){const r=mo(e),s=R(t)?`(${t.signal} === "top")`:t==="top";return{signal:`(45 < ${r} && ${r} < 135) || (225 < ${r} && ${r} < 315) ? "middle" :(${r} <= 45 || 315 <= ${r}) === ${s} ? "bottom" : "top"`}}if(45<e&&e<135||225<e&&e<315)return"middle";if(R(t)){const r=e<=45||315<=e?"===":"!==";return{signal:`${t.signal} ${r} "top" ? "bottom" : "top"`}}return(e<=45||315<=e)==(t==="top")?"bottom":"top"}else{if(R(e)){const r=mo(e),s=R(t)?`(${t.signal} === "left")`:t==="left";return{signal:`${r} <= 45 || 315 <= ${r} || (135 <= ${r} && ${r} <= 225) ? ${i?'"middle"':"null"} : (45 <= ${r} && ${r} <= 135) === ${s} ? "top" : "bottom"`}}if(e<=45||315<=e||135<=e&&e<=225)return i?"middle":null;if(R(t)){const r=45<=e&&e<=135?"===":"!==";return{signal:`${t.signal} ${r} "left" ? "top" : "bottom"`}}return(45<=e&&e<=135)==(t==="left")?"top":"bottom"}}function Ld(e,t,n){if(e===void 0)return;const i=n==="x",r=i?0:90,s=i?"bottom":"left";if(R(e)){const o=mo(e),a=R(t)?`(${t.signal} === "${s}")`:t===s;return{signal:`(${r?`(${o} + 90)`:o} % 180 === 0) ? ${i?null:'"center"'} :(${r} < ${o} && ${o} < ${180+r}) === ${a} ? "left" : "right"`}}if((e+r)%180===0)return i?null:"center";if(R(t)){const o=r<e&&e<180+r?"===":"!==";return{signal:`${`${t.signal} ${o} "${s}"`} ? "left" : "right"`}}return(r<e&&e<180+r)==(t===s)?"left":"right"}function Zx(e,t){if(t==="x"&&q(["quantitative","temporal"],e))return!0}function ev(e,t,n,i){if(n&&!Y(i)||e!=="nominal"&&e!=="ordinal")return t==="log"||t==="symlog"?"greedy":!0}function tv(e){return e==="x"?"bottom":"left"}function nv({fieldOrDatumDef:e,scaleType:t,size:n,values:i}){var r;if(!i&&!xe(t)&&t!=="log"){if(E(e)){if(ne(e.bin))return{signal:`ceil(${n.signal}/10)`};if(e.timeUnit&&q(["month","hours","day","quarter"],(r=me(e.timeUnit))==null?void 0:r.unit))return}return{signal:`ceil(${n.signal}/40)`}}}function iv({format:e,fieldOrDatumDef:t}){if(e==="d")return 1;if(E(t)){const{timeUnit:n}=t;if(n){const i=Hu(n);if(i)return{signal:i}}}}function Pd(e,t){const n=t==="x"?"x2":"y2",i=e.fieldDef(t),r=e.fieldDef(n),s=i?i.title:void 0,o=r?r.title:void 0;if(s&&o)return ku(s,o);if(s)return s;if(o)return o;if(s!==void 0)return s;if(o!==void 0)return o}function rv(e,t){const n=e.values;if(O(n))return Nf(t,n);if(R(n))return n}function sv(e,t){return e==="rect"&&Sr(t)?1:0}class ai extends J{clone(){return new ai(null,j(this.transform))}constructor(t,n){super(t),this.transform=n,this._dependentFields=kd(this.transform.calculate)}static parseAllForSortIndex(t,n){return n.forEachFieldDef((i,r)=>{if(Ln(i)&&hf(i.sort)){const{field:s,timeUnit:o}=i,a=i.sort,c=a.map((l,u)=>`${Yu({field:s,timeUnit:o,equal:l})} ? ${u} : `).join("")+a.length;t=new ai(t,{calculate:c,as:ci(i,r,{forAs:!0})})}}),t}producedFields(){return new Set([this.transform.as])}dependentFields(){return this._dependentFields}assemble(){return{type:"formula",expr:this.transform.calculate,as:this.transform.as}}hash(){return`Calculate ${G(this.transform)}`}}function ci(e,t,n){return C(e,{prefix:t,suffix:"sort_index",...n??{}})}function cs(e,t){return q(["top","bottom"],t)?"column":q(["left","right"],t)||e==="row"?"row":"column"}function li(e,t,n,i){const r=i==="row"?n.headerRow:i==="column"?n.headerColumn:n.headerFacet;return fe((t||{})[e],r[e],n.header[e])}function ls(e,t,n,i){const r={};for(const s of e){const o=li(s,t||{},n,i);o!==void 0&&(r[s]=o)}return r}const za=["row","column"],Da=["header","footer"];function ov(e,t){const n=e.component.layoutHeaders[t].title,i=e.config?e.config:void 0,r=e.component.layoutHeaders[t].facetFieldDef?e.component.layoutHeaders[t].facetFieldDef:void 0,{titleAnchor:s,titleAngle:o,titleOrient:a}=ls(["titleAnchor","titleAngle","titleOrient"],r.header,i,t),c=cs(t,a),l=zi(o);return{name:`${t}-title`,type:"group",role:`${c}-title`,title:{text:n,...t==="row"?{orient:"left"}:{},style:"guide-title",...Dd(l,c),...zd(c,l,s),...jd(i,r,t,Jy,Bf)}}}function zd(e,t,n="middle"){switch(n){case"start":return{align:"left"};case"end":return{align:"right"}}const i=Ld(t,e==="row"?"left":"top",e==="row"?"y":"x");return i?{align:i}:{}}function Dd(e,t){const n=Id(e,t==="row"?"left":"top",t==="row"?"y":"x",!0);return n?{baseline:n}:{}}function av(e,t){const n=e.component.layoutHeaders[t],i=[];for(const r of Da)if(n[r])for(const s of n[r]){const o=lv(e,t,r,n,s);o!=null&&i.push(o)}return i}function cv(e,t){const{sort:n}=e;return ft(n)?{field:C(n,{expr:"datum"}),order:n.order??"ascending"}:O(n)?{field:ci(e,t,{expr:"datum"}),order:"ascending"}:{field:C(e,{expr:"datum"}),order:n??"ascending"}}function yo(e,t,n){const{format:i,formatType:r,labelAngle:s,labelAnchor:o,labelOrient:a,labelExpr:c}=ls(["format","formatType","labelAngle","labelAnchor","labelOrient","labelExpr"],e.header,n,t),l=da({fieldOrDatumDef:e,format:i,formatType:r,expr:"parent",config:n}).signal,u=cs(t,a);return{text:{signal:c?vn(vn(c,"datum.label",l),"datum.value",C(e,{expr:"parent"})):l},...t==="row"?{orient:"left"}:{},style:"guide-label",frame:"group",...Dd(s,u),...zd(u,s,o),...jd(n,e,t,Qy,Gf)}}function lv(e,t,n,i,r){if(r){let s=null;const{facetFieldDef:o}=i,a=e.config?e.config:void 0;if(o&&r.labels){const{labelOrient:f}=ls(["labelOrient"],o.header,a,t);(t==="row"&&!q(["top","bottom"],f)||t==="column"&&!q(["left","right"],f))&&(s=yo(o,t,a))}const c=Je(e)&&!Yi(e.facet),l=r.axes,u=(l==null?void 0:l.length)>0;if(s||u){const f=t==="row"?"height":"width";return{name:e.getName(`${t}_${n}`),type:"group",role:`${t}-${n}`,...i.facetFieldDef?{from:{data:e.getName(`${t}_domain`)},sort:cv(o,t)}:{},...u&&c?{from:{data:e.getName(`facet_domain_${t}`)}}:{},...s?{title:s}:{},...r.sizeSignal?{encode:{update:{[f]:r.sizeSignal}}}:{},...u?{axes:l}:{}}}}return null}const uv={column:{start:0,end:1},row:{start:1,end:0}};function fv(e,t){return uv[t][e]}function dv(e,t){const n={};for(const i of je){const r=e[i];if(r!=null&&r.facetFieldDef){const{titleAnchor:s,titleOrient:o}=ls(["titleAnchor","titleOrient"],r.facetFieldDef.header,t,i),a=cs(i,o),c=fv(s,a);c!==void 0&&(n[a]=c)}}return Q(n)?void 0:n}function jd(e,t,n,i,r){const s={};for(const o of i){if(!r[o])continue;const a=li(o,t==null?void 0:t.header,e,n);a!==void 0&&(s[r[o]]=a)}return s}function ja(e){return[...sr(e,"width"),...sr(e,"height"),...sr(e,"childWidth"),...sr(e,"childHeight")]}function sr(e,t){const n=t==="width"?"x":"y",i=e.component.layoutSize.get(t);if(!i||i==="merged")return[];const r=e.getSizeSignalRef(t).signal;if(i==="step"){const s=e.getScaleComponent(n);if(s){const o=s.get("type"),a=s.get("range");if(xe(o)&&nn(a)){const c=e.scaleName(n);return Je(e.parent)&&e.parent.component.resolve.scale[n]==="independent"?[il(c,a)]:[il(c,a),{name:r,update:Md(c,s,`domain('${c}').length`)}]}}throw new Error("layout size is step although width/height is not step.")}else if(i=="container"){const s=r.endsWith("width"),o=s?"containerSize()[0]":"containerSize()[1]",a=co(e.config.view,s?"width":"height"),c=`isFinite(${o}) ? ${o} : ${a}`;return[{name:r,init:c,on:[{update:c,events:"window:resize"}]}]}else return[{name:r,value:i}]}function il(e,t){const n=`${e}_step`;return R(t.step)?{name:n,update:t.step.signal}:{name:n,value:t.step}}function Md(e,t,n){const i=t.get("type"),r=t.get("padding"),s=fe(t.get("paddingOuter"),r);let o=t.get("paddingInner");return o=i==="band"?o!==void 0?o:r:1,`bandspace(${n}, ${Ve(o)}, ${Ve(s)}) * ${e}_step`}function Ud(e){return e==="childWidth"?"width":e==="childHeight"?"height":e}function Wd(e,t){return v(e).reduce((n,i)=>{const r=e[i];return{...n,...mi(t,r,i,s=>re(s.value))}},{})}function Bd(e,t){if(Je(t))return e==="theta"?"independent":"shared";if(vi(t))return"shared";if(Ha(t))return be(e)||e==="theta"||e==="radius"?"independent":"shared";throw new Error("invalid model type for resolve")}function Ma(e,t){const n=e.scale[t],i=be(t)?"axis":"legend";return n==="independent"?(e[i][t]==="shared"&&S(Xh(t)),"independent"):e[i][t]||"shared"}const pv={...tb,disable:1,labelExpr:1,selections:1,opacity:1,shape:1,stroke:1,fill:1,size:1,strokeWidth:1,strokeDash:1,encode:1},Gd=v(pv);class gv extends jt{}const rl={symbols:hv,gradient:mv,labels:yv,entries:bv};function hv(e,{fieldOrDatumDef:t,model:n,channel:i,legendCmpt:r,legendType:s}){if(s!=="symbol")return;const{markDef:o,encoding:a,config:c,mark:l}=n,u=o.filled&&l!=="trail";let f={...rh({},n,Jm),...md(n,{filled:u})};const d=r.get("symbolOpacity")??c.legend.symbolOpacity,p=r.get("symbolFillColor")??c.legend.symbolFillColor,g=r.get("symbolStrokeColor")??c.legend.symbolStrokeColor,h=d===void 0?qd(a.opacity)??o.opacity:void 0;if(f.fill){if(i==="fill"||u&&i===Ae)delete f.fill;else if(f.fill.field)p?delete f.fill:(f.fill=re(c.legend.symbolBaseFillColor??"black"),f.fillOpacity=re(h??1));else if(O(f.fill)){const m=bo(a.fill??a.color)??o.fill??(u&&o.color);m&&(f.fill=re(m))}}if(f.stroke){if(i==="stroke"||!u&&i===Ae)delete f.stroke;else if(f.stroke.field||g)delete f.stroke;else if(O(f.stroke)){const m=fe(bo(a.stroke||a.color),o.stroke,u?o.color:void 0);m&&(f.stroke={value:m})}}if(i!==Pt){const m=E(t)&&Vd(n,r,t);m?f.opacity=[{test:m,...re(h??1)},re(c.legend.unselectedOpacity)]:h&&(f.opacity=re(h))}return f={...f,...e},Q(f)?void 0:f}function mv(e,{model:t,legendType:n,legendCmpt:i}){if(n!=="gradient")return;const{config:r,markDef:s,encoding:o}=t;let a={};const l=(i.get("gradientOpacity")??r.legend.gradientOpacity)===void 0?qd(o.opacity)||s.opacity:void 0;return l&&(a.opacity=re(l)),a={...a,...e},Q(a)?void 0:a}function yv(e,{fieldOrDatumDef:t,model:n,channel:i,legendCmpt:r}){const s=n.legend(i)||{},o=n.config,a=E(t)?Vd(n,r,t):void 0,c=a?[{test:a,value:1},{value:o.legend.unselectedOpacity}]:void 0,{format:l,formatType:u}=s;let f;Cn(u)?f=Ye({fieldOrDatumDef:t,field:"datum.value",format:l,formatType:u,config:o}):l===void 0&&u===void 0&&o.customFormatTypes&&(t.type==="quantitative"&&o.numberFormatType?f=Ye({fieldOrDatumDef:t,field:"datum.value",format:o.numberFormat,formatType:o.numberFormatType,config:o}):t.type==="temporal"&&o.timeFormatType&&E(t)&&t.timeUnit===void 0&&(f=Ye({fieldOrDatumDef:t,field:"datum.value",format:o.timeFormat,formatType:o.timeFormatType,config:o})));const d={...c?{opacity:c}:{},...f?{text:f}:{},...e};return Q(d)?void 0:d}function bv(e,{legendCmpt:t}){const n=t.get("selections");return n!=null&&n.length?{...e,fill:{value:"transparent"}}:e}function qd(e){return Hd(e,(t,n)=>Math.max(t,n.value))}function bo(e){return Hd(e,(t,n)=>fe(t,n.value))}function Hd(e,t){if(yy(e))return ce(e.condition).reduce(t,e.value);if(Qe(e))return e.value}function Vd(e,t,n){const i=t.get("selections");if(!(i!=null&&i.length))return;const r=B(n.field);return i.map(s=>`(!length(data(${B(se(s)+Fn)})) || (${s}[${r}] && indexof(${s}[${r}], datum.value) >= 0))`).join(" || ")}const sl={direction:({direction:e})=>e,format:({fieldOrDatumDef:e,legend:t,config:n})=>{const{format:i,formatType:r}=t;return ff(e,e.type,i,r,n,!1)},formatType:({legend:e,fieldOrDatumDef:t,scaleType:n})=>{const{formatType:i}=e;return df(i,t,n)},gradientLength:e=>{const{legend:t,legendConfig:n}=e;return t.gradientLength??n.gradientLength??Cv(e)},labelOverlap:({legend:e,legendConfig:t,scaleType:n})=>e.labelOverlap??t.labelOverlap??Nv(n),symbolType:({legend:e,markDef:t,channel:n,encoding:i})=>e.symbolType??vv(t.type,n,i.shape,t.shape),title:({fieldOrDatumDef:e,config:t})=>Hn(e,t,{allowDisabling:!0}),type:({legendType:e,scaleType:t,channel:n})=>{if(qn(n)&&Xe(t)){if(e==="gradient")return}else if(e==="symbol")return;return e},values:({fieldOrDatumDef:e,legend:t})=>xv(t,e)};function xv(e,t){const n=e.values;if(O(n))return Nf(t,n);if(R(n))return n}function vv(e,t,n,i){if(t!=="shape"){const r=bo(n)??i;if(r)return r}switch(e){case"bar":case"rect":case"image":case"square":return"square";case"line":case"trail":case"rule":return"stroke";case"arc":case"point":case"circle":case"tick":case"geoshape":case"area":case"text":return"circle"}}function Sv(e){const{legend:t}=e;return fe(t.type,Ev(e))}function Ev({channel:e,timeUnit:t,scaleType:n}){if(qn(e)){if(q(["quarter","month","day"],t))return"symbol";if(Xe(n))return"gradient"}return"symbol"}function $v({legendConfig:e,legendType:t,orient:n,legend:i}){return i.direction??e[t?"gradientDirection":"symbolDirection"]??wv(n,t)}function wv(e,t){switch(e){case"top":case"bottom":return"horizontal";case"left":case"right":case"none":case void 0:return;default:return t==="gradient"?"horizontal":void 0}}function Cv({legendConfig:e,model:t,direction:n,orient:i,scaleType:r}){const{gradientHorizontalMaxLength:s,gradientHorizontalMinLength:o,gradientVerticalMaxLength:a,gradientVerticalMinLength:c}=e;if(Xe(r))return n==="horizontal"?i==="top"||i==="bottom"?ol(t,"width",o,s):o:ol(t,"height",c,a)}function ol(e,t,n,i){return{signal:`clamp(${e.getSizeSignalRef(t).signal}, ${n}, ${i})`}}function Nv(e){if(q(["quantile","threshold","log","symlog"],e))return"greedy"}function Xd(e){const t=ae(e)?Fv(e):Ov(e);return e.component.legends=t,t}function Fv(e){const{encoding:t}=e,n={};for(const i of[Ae,...Hf]){const r=de(t[i]);!r||!e.getScaleComponent(i)||i===Re&&E(r)&&r.type===hi||(n[i]=kv(e,i))}return n}function _v(e,t){const n=e.scaleName(t);if(e.mark==="trail"){if(t==="color")return{stroke:n};if(t==="size")return{strokeWidth:n}}return t==="color"?e.markDef.filled?{fill:n}:{stroke:n}:{[t]:n}}function Tv(e,t,n,i){switch(t){case"disable":return n!==void 0;case"values":return!!(n!=null&&n.values);case"title":if(t==="title"&&e===(i==null?void 0:i.title))return!0}return e===(n||{})[t]}function kv(e,t){var P;let n=e.legend(t);const{markDef:i,encoding:r,config:s}=e,o=s.legend,a=new gv({},_v(e,t));jx(e,t,a);const c=n!==void 0?!n:o.disable;if(a.set("disable",c,n!==void 0),c)return a;n=n||{};const l=e.getScaleComponent(t).get("type"),u=de(r[t]),f=E(u)?(P=me(u.timeUnit))==null?void 0:P.unit:void 0,d=n.orient||s.legend.orient||"right",p=Sv({legend:n,channel:t,timeUnit:f,scaleType:l}),g=$v({legend:n,legendType:p,orient:d,legendConfig:o}),h={legend:n,channel:t,model:e,markDef:i,encoding:r,fieldOrDatumDef:u,legendConfig:o,config:s,scaleType:l,orient:d,legendType:p,direction:g};for(const x of Gd){if(p==="gradient"&&x.startsWith("symbol")||p==="symbol"&&x.startsWith("gradient"))continue;const _=x in sl?sl[x](h):n[x];if(_!==void 0){const w=Tv(_,x,n,e.fieldDef(t));(w||s.legend[x]===void 0)&&a.set(x,_,w)}}const m=(n==null?void 0:n.encoding)??{},y=a.get("selections"),b={},N={fieldOrDatumDef:u,model:e,channel:t,legendCmpt:a,legendType:p};for(const x of["labels","legend","title","symbols","gradient","entries"]){const _=Wd(m[x]??{},e),w=x in rl?rl[x](_,N):_;w!==void 0&&!Q(w)&&(b[x]={...y!=null&&y.length&&E(u)?{name:`${se(u.field)}_legend_${x}`}:{},...y!=null&&y.length?{interactive:!!y}:{},update:w})}return Q(b)||a.set("encode",b,!!(n!=null&&n.encoding)),a}function Ov(e){const{legends:t,resolve:n}=e.component;for(const i of e.children){Xd(i);for(const r of v(i.component.legends))n.legend[r]=Ma(e.component.resolve,r),n.legend[r]==="shared"&&(t[r]=Yd(t[r],i.component.legends[r]),t[r]||(n.legend[r]="independent",delete t[r]))}for(const i of v(t))for(const r of e.children)r.component.legends[i]&&n.legend[i]==="shared"&&delete r.component.legends[i];return t}function Yd(e,t){var s,o,a,c;if(!e)return t.clone();const n=e.getWithExplicit("orient"),i=t.getWithExplicit("orient");if(n.explicit&&i.explicit&&n.value!==i.value)return;let r=!1;for(const l of Gd){const u=Ht(e.getWithExplicit(l),t.getWithExplicit(l),l,"legend",(f,d)=>{switch(l){case"symbolType":return Av(f,d);case"title":return Ou(f,d);case"type":return r=!0,Le("symbol")}return ss(f,d,l,"legend")});e.setWithExplicit(l,u)}return r&&((o=(s=e.implicit)==null?void 0:s.encode)!=null&&o.gradient&&hr(e.implicit,["encode","gradient"]),(c=(a=e.explicit)==null?void 0:a.encode)!=null&&c.gradient&&hr(e.explicit,["encode","gradient"])),e}function Av(e,t){return t.value==="circle"?t:e}function Rv(e,t,n,i){var r,s;e.encode??(e.encode={}),(r=e.encode)[t]??(r[t]={}),(s=e.encode[t]).update??(s.update={}),e.encode[t].update[n]=i}function Kd(e){const t=e.component.legends,n={};for(const r of v(t)){const s=e.getScaleComponent(r),o=te(s.get("domains"));if(n[o])for(const a of n[o])Yd(a,t[r])||n[o].push(t[r]);else n[o]=[t[r].clone()]}return Se(n).flat().map(r=>Iv(r,e.config)).filter(r=>r!==void 0)}function Iv(e,t){var o,a,c;const{disable:n,labelExpr:i,selections:r,...s}=e.combine();if(!n){if(t.aria===!1&&s.aria==null&&(s.aria=!1),(o=s.encode)!=null&&o.symbols){const l=s.encode.symbols.update;l.fill&&l.fill.value!=="transparent"&&!l.stroke&&!s.stroke&&(l.stroke={value:"transparent"});for(const u of Hf)s[u]&&delete l[u]}if(s.title||delete s.title,i!==void 0){let l=i;(c=(a=s.encode)==null?void 0:a.labels)!=null&&c.update&&R(s.encode.labels.update.text)&&(l=vn(i,"datum.label",s.encode.labels.update.text.signal)),Rv(s,"labels","text",{signal:l})}return s}}function Lv(e){return vi(e)||Ha(e)?Pv(e):Jd(e)}function Pv(e){return e.children.reduce((t,n)=>t.concat(n.assembleProjections()),Jd(e))}function Jd(e){const t=e.component.projection;if(!t||t.merged)return[];const n=t.combine(),{name:i}=n;if(t.data){const r={signal:`[${t.size.map(o=>o.signal).join(", ")}]`},s=t.data.reduce((o,a)=>{const c=R(a)?a.signal:`data('${e.lookupDataSource(a)}')`;return q(o,c)||o.push(c),o},[]);if(s.length<=0)throw new Error("Projection's fit didn't find any data sources");return[{name:i,size:r,fit:{signal:s.length>1?`[${s.join(", ")}]`:s[0]},...n}]}else return[{name:i,translate:{signal:"[width / 2, height / 2]"},...n}]}const zv=["type","clipAngle","clipExtent","center","rotate","precision","reflectX","reflectY","coefficient","distance","fraction","lobes","parallel","radius","ratio","spacing","tilt"];class Qd extends jt{constructor(t,n,i,r){super({...n},{name:t}),this.specifiedProjection=n,this.size=i,this.data=r,this.merged=!1}get isFit(){return!!this.data}}function Zd(e){e.component.projection=ae(e)?Dv(e):Uv(e)}function Dv(e){if(e.hasProjection){const t=_e(e.specifiedProjection),n=!(t&&(t.scale!=null||t.translate!=null)),i=n?[e.getSizeSignalRef("width"),e.getSizeSignalRef("height")]:void 0,r=n?jv(e):void 0,s=new Qd(e.projectionName(!0),{..._e(e.config.projection)??{},...t??{}},i,r);return s.get("type")||s.set("type","equalEarth",!1),s}}function jv(e){const t=[],{encoding:n}=e;for(const i of[[it,nt],[Ue,rt]])(de(n[i[0]])||de(n[i[1]]))&&t.push({signal:e.getName(`geojson_${t.length}`)});return e.channelHasField(Re)&&e.typedFieldDef(Re).type===hi&&t.push({signal:e.getName(`geojson_${t.length}`)}),t.length===0&&t.push(e.requestDataName(ee.Main)),t}function Mv(e,t){const n=Oo(zv,r=>!!(!Bn(e.explicit,r)&&!Bn(t.explicit,r)||Bn(e.explicit,r)&&Bn(t.explicit,r)&&lt(e.get(r),t.get(r))));if(lt(e.size,t.size)){if(n)return e;if(lt(e.explicit,{}))return t;if(lt(t.explicit,{}))return e}return null}function Uv(e){if(e.children.length===0)return;let t;for(const i of e.children)Zd(i);const n=Oo(e.children,i=>{const r=i.component.projection;if(r)if(t){const s=Mv(t,r);return s&&(t=s),!!s}else return t=r,!0;else return!0});if(t&&n){const i=e.projectionName(!0),r=new Qd(i,t.specifiedProjection,t.size,j(t.data));for(const s of e.children){const o=s.component.projection;o&&(o.isFit&&r.data.push(...s.component.projection.data),s.renameProjection(o.get("name"),i),o.merged=!0)}return r}}function Wv(e,t,n,i){if(Ji(t,n)){const r=ae(e)?e.axis(n)??e.legend(n)??{}:{},s=C(t,{expr:"datum"}),o=C(t,{expr:"datum",binSuffix:"end"});return{formulaAs:C(t,{binSuffix:"range",forAs:!0}),formula:Xi(s,o,r.format,r.formatType,i)}}return{}}function ep(e,t){return`${$u(e)}_${t}`}function Bv(e,t){return{signal:e.getName(`${t}_bins`),extentSignal:e.getName(`${t}_extent`)}}function Ua(e,t,n){const i=Zr(n,void 0)??{},r=ep(i,t);return e.getName(`${r}_bins`)}function Gv(e){return"as"in e}function al(e,t,n){let i,r;Gv(e)?i=z(e.as)?[e.as,`${e.as}_end`]:[e.as[0],e.as[1]]:i=[C(e,{forAs:!0}),C(e,{binSuffix:"end",forAs:!0})];const s={...Zr(t,void 0)},o=ep(s,e.field),{signal:a,extentSignal:c}=Bv(n,o);if(Wr(s.extent)){const u=s.extent;r=Ad(n,u.param,u),delete s.extent}const l={bin:s,field:e.field,as:[i],...a?{signal:a}:{},...c?{extentSignal:c}:{},...r?{span:r}:{}};return{key:o,binComponent:l}}class pt extends J{clone(){return new pt(null,j(this.bins))}constructor(t,n){super(t),this.bins=n}static makeFromEncoding(t,n){const i=n.reduceFieldDef((r,s,o)=>{if(Ne(s)&&ne(s.bin)){const{key:a,binComponent:c}=al(s,s.bin,n);r[a]={...c,...r[a],...Wv(n,s,o,n.config)}}return r},{});return Q(i)?null:new pt(t,i)}static makeFromTransform(t,n,i){const{key:r,binComponent:s}=al(n,n.bin,i);return new pt(t,{[r]:s})}merge(t,n){for(const i of v(t.bins))i in this.bins?(n(t.bins[i].signal,this.bins[i].signal),this.bins[i].as=ut([...this.bins[i].as,...t.bins[i].as],G)):this.bins[i]=t.bins[i];for(const i of t.children)t.removeChild(i),i.parent=this;t.remove()}producedFields(){return new Set(Se(this.bins).map(t=>t.as).flat(2))}dependentFields(){return new Set(Se(this.bins).map(t=>t.field))}hash(){return`Bin ${G(this.bins)}`}assemble(){return Se(this.bins).flatMap(t=>{const n=[],[i,...r]=t.as,{extent:s,...o}=t.bin,a={type:"bin",field:Me(t.field),as:i,signal:t.signal,...Wr(s)?{extent:null}:{extent:s},...t.span?{span:{signal:`span(${t.span})`}}:{},...o};!s&&t.extentSignal&&(n.push({type:"extent",field:Me(t.field),signal:t.extentSignal}),a.extent={signal:t.extentSignal}),n.push(a);for(const c of r)for(let l=0;l<2;l++)n.push({type:"formula",expr:C({field:i[l]},{expr:"datum"}),as:c[l]});return t.formula&&n.push({type:"formula",expr:t.formula,as:t.formulaAs}),n})}}function qv(e,t,n,i){var s;const r=ae(i)?i.encoding[vt(t)]:void 0;if(Ne(n)&&ae(i)&&bf(n,r,i.markDef,i.config))e.add(C(n,{})),e.add(C(n,{suffix:"end"})),n.bin&&Ji(n,t)&&e.add(C(n,{binSuffix:"range"}));else if(du(t)){const o=fu(t);e.add(i.getName(o))}else e.add(C(n));return Ln(n)&&jm((s=n.scale)==null?void 0:s.range)&&e.add(n.scale.range.field),e}function Hv(e,t){for(const n of v(t)){const i=t[n];for(const r of v(i))n in e?e[n][r]=new Set([...e[n][r]??[],...i[r]]):e[n]={[r]:i[r]}}}class Ke extends J{clone(){return new Ke(null,new Set(this.dimensions),j(this.measures))}constructor(t,n,i){super(t),this.dimensions=n,this.measures=i}get groupBy(){return this.dimensions}static makeFromEncoding(t,n){let i=!1;n.forEachFieldDef(o=>{o.aggregate&&(i=!0)});const r={},s=new Set;return!i||(n.forEachFieldDef((o,a)=>{const{aggregate:c,field:l}=o;if(c)if(c==="count")r["*"]??(r["*"]={}),r["*"].count=new Set([C(o,{forAs:!0})]);else{if(Ot(c)||tn(c)){const u=Ot(c)?"argmin":"argmax",f=c[u];r[f]??(r[f]={}),r[f][u]=new Set([C({op:u,field:f},{forAs:!0})])}else r[l]??(r[l]={}),r[l][c]=new Set([C(o,{forAs:!0})]);zt(a)&&n.scaleDomain(a)==="unaggregated"&&(r[l]??(r[l]={}),r[l].min=new Set([C({field:l,aggregate:"min"},{forAs:!0})]),r[l].max=new Set([C({field:l,aggregate:"max"},{forAs:!0})]))}else qv(s,a,o,n)}),s.size+v(r).length===0)?null:new Ke(t,s,r)}static makeFromTransform(t,n){const i=new Set,r={};for(const s of n.aggregate){const{op:o,field:a,as:c}=s;o&&(o==="count"?(r["*"]??(r["*"]={}),r["*"].count=new Set([c||C(s,{forAs:!0})])):(r[a]??(r[a]={}),r[a][o]=new Set([c||C(s,{forAs:!0})])))}for(const s of n.groupby??[])i.add(s);return i.size+v(r).length===0?null:new Ke(t,i,r)}merge(t){return iu(this.dimensions,t.dimensions)?(Hv(this.measures,t.measures),!0):(um("different dimensions, cannot merge"),!1)}addDimensions(t){t.forEach(this.dimensions.add,this.dimensions)}dependentFields(){return new Set([...this.dimensions,...v(this.measures)])}producedFields(){const t=new Set;for(const n of v(this.measures))for(const i of v(this.measures[n])){const r=this.measures[n][i];r.size===0?t.add(`${i}_${n}`):r.forEach(t.add,t)}return t}hash(){return`Aggregate ${G({dimensions:this.dimensions,measures:this.measures})}`}assemble(){const t=[],n=[],i=[];for(const s of v(this.measures))for(const o of v(this.measures[s]))for(const a of this.measures[s][o])i.push(a),t.push(o),n.push(s==="*"?null:Me(s));return{type:"aggregate",groupby:[...this.dimensions].map(Me),ops:t,fields:n,as:i}}}class bi extends J{constructor(t,n,i,r){super(t),this.model=n,this.name=i,this.data=r;for(const s of je){const o=n.facet[s];if(o){const{bin:a,sort:c}=o;this[s]={name:n.getName(`${s}_domain`),fields:[C(o),...ne(a)?[C(o,{binSuffix:"end"})]:[]],...ft(c)?{sortField:c}:O(c)?{sortIndexField:ci(o,s)}:{}}}}this.childModel=n.child}hash(){let t="Facet";for(const n of je)this[n]&&(t+=` ${n.charAt(0)}:${G(this[n])}`);return t}get fields(){var n;const t=[];for(const i of je)(n=this[i])!=null&&n.fields&&t.push(...this[i].fields);return t}dependentFields(){const t=new Set(this.fields);for(const n of je)this[n]&&(this[n].sortField&&t.add(this[n].sortField.field),this[n].sortIndexField&&t.add(this[n].sortIndexField));return t}producedFields(){return new Set}getSource(){return this.name}getChildIndependentFieldsWithStep(){const t={};for(const n of St){const i=this.childModel.component.scales[n];if(i&&!i.merged){const r=i.get("type"),s=i.get("range");if(xe(r)&&nn(s)){const o=us(this.childModel,n),a=qa(o);a?t[n]=a:S(qo(n))}}}return t}assembleRowColumnHeaderData(t,n,i){const r={row:"y",column:"x",facet:void 0}[t],s=[],o=[],a=[];r&&i&&i[r]&&(n?(s.push(`distinct_${i[r]}`),o.push("max")):(s.push(i[r]),o.push("distinct")),a.push(`distinct_${i[r]}`));const{sortField:c,sortIndexField:l}=this[t];if(c){const{op:u=Yr,field:f}=c;s.push(f),o.push(u),a.push(C(c,{forAs:!0}))}else l&&(s.push(l),o.push("max"),a.push(l));return{name:this[t].name,source:n??this.data,transform:[{type:"aggregate",groupby:this[t].fields,...s.length?{fields:s,ops:o,as:a}:{}}]}}assembleFacetHeaderData(t){var c;const{columns:n}=this.model.layout,{layoutHeaders:i}=this.model.component,r=[],s={};for(const l of za){for(const u of Da){const f=(i[l]&&i[l][u])??[];for(const d of f)if(((c=d.axes)==null?void 0:c.length)>0){s[l]=!0;break}}if(s[l]){const u=`length(data("${this.facet.name}"))`,f=l==="row"?n?{signal:`ceil(${u} / ${n})`}:1:n?{signal:`min(${u}, ${n})`}:{signal:u};r.push({name:`${this.facet.name}_${l}`,transform:[{type:"sequence",start:0,stop:f}]})}}const{row:o,column:a}=s;return(o||a)&&r.unshift(this.assembleRowColumnHeaderData("facet",null,t)),r}assemble(){const t=[];let n=null;const i=this.getChildIndependentFieldsWithStep(),{column:r,row:s,facet:o}=this;if(r&&s&&(i.x||i.y)){n=`cross_${this.column.name}_${this.row.name}`;const a=[].concat(i.x??[],i.y??[]),c=a.map(()=>"distinct");t.push({name:n,source:this.data,transform:[{type:"aggregate",groupby:this.fields,fields:a,ops:c}]})}for(const a of[_t,Ft])this[a]&&t.push(this.assembleRowColumnHeaderData(a,n,i));if(o){const a=this.assembleFacetHeaderData(i);a&&t.push(...a)}return t}}function cl(e){return e.startsWith("'")&&e.endsWith("'")||e.startsWith('"')&&e.endsWith('"')?e.slice(1,-1):e}function Vv(e,t){const n=Io(e);if(t==="number")return`toNumber(${n})`;if(t==="boolean")return`toBoolean(${n})`;if(t==="string")return`toString(${n})`;if(t==="date")return`toDate(${n})`;if(t==="flatten")return n;if(t.startsWith("date:")){const i=cl(t.slice(5,t.length));return`timeParse(${n},'${i}')`}else if(t.startsWith("utc:")){const i=cl(t.slice(4,t.length));return`utcParse(${n},'${i}')`}else return S(yh(t)),null}function Xv(e){const t={};return gr(e.filter,n=>{if(Xu(n)){let i=null;Yo(n)?i=Pe(n.equal):Jo(n)?i=Pe(n.lte):Ko(n)?i=Pe(n.lt):Qo(n)?i=Pe(n.gt):Zo(n)?i=Pe(n.gte):ea(n)?i=n.range[0]:ta(n)&&(i=(n.oneOf??n.in)[0]),i&&(Rn(i)?t[n.field]="date":ue(i)?t[n.field]="number":z(i)&&(t[n.field]="string")),n.timeUnit&&(t[n.field]="date")}}),t}function Yv(e){const t={};function n(i){ri(i)?t[i.field]="date":i.type==="quantitative"&&Yg(i.aggregate)?t[i.field]="number":Jn(i.field)>1?i.field in t||(t[i.field]="flatten"):Ln(i)&&ft(i.sort)&&Jn(i.sort.field)>1&&(i.sort.field in t||(t[i.sort.field]="flatten"))}if((ae(e)||Je(e))&&e.forEachFieldDef((i,r)=>{if(Ne(i))n(i);else{const s=On(r),o=e.fieldDef(s);n({...i,type:o.type})}}),ae(e)){const{mark:i,markDef:r,encoding:s}=e;if(rn(i)&&!e.encoding.order){const o=r.orient==="horizontal"?"y":"x",a=s[o];E(a)&&a.type==="quantitative"&&!(a.field in t)&&(t[a.field]="number")}}return t}function Kv(e){const t={};if(ae(e)&&e.component.selection)for(const n of v(e.component.selection)){const i=e.component.selection[n];for(const r of i.project.items)!r.channel&&Jn(r.field)>1&&(t[r.field]="flatten")}return t}class we extends J{clone(){return new we(null,j(this._parse))}constructor(t,n){super(t),this._parse=n}hash(){return`Parse ${G(this._parse)}`}static makeExplicit(t,n,i){var o;let r={};const s=n.data;return!Bt(s)&&((o=s==null?void 0:s.format)!=null&&o.parse)&&(r=s.format.parse),this.makeWithAncestors(t,r,{},i)}static makeWithAncestors(t,n,i,r){for(const a of v(i)){const c=r.getWithExplicit(a);c.value!==void 0&&(c.explicit||c.value===i[a]||c.value==="derived"||i[a]==="flatten"?delete i[a]:S(vc(a,i[a],c.value)))}for(const a of v(n)){const c=r.get(a);c!==void 0&&(c===n[a]?delete n[a]:S(vc(a,n[a],c)))}const s=new jt(n,i);r.copyAll(s);const o={};for(const a of v(s.combine())){const c=s.get(a);c!==null&&(o[a]=c)}return v(o).length===0||r.parseNothing?null:new we(t,o)}get parse(){return this._parse}merge(t){this._parse={...this._parse,...t.parse},t.remove()}assembleFormatParse(){const t={};for(const n of v(this._parse)){const i=this._parse[n];Jn(n)===1&&(t[n]=i)}return t}producedFields(){return new Set(v(this._parse))}dependentFields(){return new Set(v(this._parse))}assembleTransforms(t=!1){return v(this._parse).filter(n=>t?Jn(n)>1:!0).map(n=>{const i=Vv(n,this._parse[n]);return i?{type:"formula",expr:i,as:Lo(n)}:null}).filter(n=>n!==null)}}class Yt extends J{clone(){return new Yt(null)}constructor(t){super(t)}dependentFields(){return new Set}producedFields(){return new Set([Ze])}hash(){return"Identifier"}assemble(){return{type:"identifier",as:Ze}}}class er extends J{clone(){return new er(null,this.params)}constructor(t,n){super(t),this.params=n}dependentFields(){return new Set}producedFields(){}hash(){return`Graticule ${G(this.params)}`}assemble(){return{type:"graticule",...this.params===!0?{}:this.params}}}class tr extends J{clone(){return new tr(null,this.params)}constructor(t,n){super(t),this.params=n}dependentFields(){return new Set}producedFields(){return new Set([this.params.as??"data"])}hash(){return`Hash ${G(this.params)}`}assemble(){return{type:"sequence",...this.params}}}class _n extends J{constructor(t){super(null),t??(t={name:"source"});let n;if(Bt(t)||(n=t.format?{...Te(t.format,["parse"])}:{}),Di(t))this._data={values:t.values};else if(si(t)){if(this._data={url:t.url},!n.type){let i=/(?:\.([^.]+))?$/.exec(t.url)[1];q(["json","csv","tsv","dsv","topojson"],i)||(i="json"),n.type=i}}else ld(t)?this._data={values:[{type:"Sphere"}]}:(ad(t)||Bt(t))&&(this._data={});this._generator=Bt(t),t.name&&(this._name=t.name),n&&!Q(n)&&(this._data.format=n)}dependentFields(){return new Set}producedFields(){}get data(){return this._data}hasName(){return!!this._name}get isGenerator(){return this._generator}get dataName(){return this._name}set dataName(t){this._name=t}set parent(t){throw new Error("Source nodes have to be roots.")}remove(){throw new Error("Source nodes are roots and cannot be removed.")}hash(){throw new Error("Cannot hash sources")}assemble(){return{name:this._name,...this._data,transform:[]}}}var ll=function(e,t,n,i,r){if(i==="m")throw new TypeError("Private method is not writable");if(i==="a"&&!r)throw new TypeError("Private accessor was defined without a setter");if(typeof t=="function"?e!==t||!r:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return i==="a"?r.call(e,n):r?r.value=n:t.set(e,n),n},Jv=function(e,t,n,i){if(n==="a"&&!i)throw new TypeError("Private accessor was defined without a getter");if(typeof t=="function"?e!==t||!i:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return n==="m"?i:n==="a"?i.call(e):i?i.value:t.get(e)},Ti;function Wa(e){return e instanceof _n||e instanceof er||e instanceof tr}class Ba{constructor(){Ti.set(this,void 0),ll(this,Ti,!1,"f")}setModified(){ll(this,Ti,!0,"f")}get modifiedFlag(){return Jv(this,Ti,"f")}}Ti=new WeakMap;class Pn extends Ba{getNodeDepths(t,n,i){i.set(t,n);for(const r of t.children)this.getNodeDepths(r,n+1,i);return i}optimize(t){const i=[...this.getNodeDepths(t,0,new Map).entries()].sort((r,s)=>s[1]-r[1]);for(const r of i)this.run(r[0]);return this.modifiedFlag}}class Ga extends Ba{optimize(t){this.run(t);for(const n of t.children)this.optimize(n);return this.modifiedFlag}}class Qv extends Ga{mergeNodes(t,n){const i=n.shift();for(const r of n)t.removeChild(r),r.parent=i,r.remove()}run(t){const n=t.children.map(r=>r.hash()),i={};for(let r=0;r<n.length;r++)i[n[r]]===void 0?i[n[r]]=[t.children[r]]:i[n[r]].push(t.children[r]);for(const r of v(i))i[r].length>1&&(this.setModified(),this.mergeNodes(t,i[r]))}}class Zv extends Ga{constructor(t){super(),this.requiresSelectionId=t&&Pa(t)}run(t){t instanceof Yt&&(this.requiresSelectionId&&(Wa(t.parent)||t.parent instanceof Ke||t.parent instanceof we)||(this.setModified(),t.remove()))}}class eS extends Ba{optimize(t){return this.run(t,new Set),this.modifiedFlag}run(t,n){let i=new Set;t instanceof dt&&(i=t.producedFields(),Ao(i,n)&&(this.setModified(),t.removeFormulas(n),t.producedFields.length===0&&t.remove()));for(const r of t.children)this.run(r,new Set([...n,...i]))}}class tS extends Ga{constructor(){super()}run(t){t instanceof Ce&&!t.isRequired()&&(this.setModified(),t.remove())}}class nS extends Pn{run(t){if(!Wa(t)&&!(t.numChildren()>1)){for(const n of t.children)if(n instanceof we)if(t instanceof we)this.setModified(),t.merge(n);else{if(Ro(t.producedFields(),n.dependentFields()))continue;this.setModified(),n.swapWithParent()}}}}class iS extends Pn{run(t){const n=[...t.children],i=t.children.filter(r=>r instanceof we);if(t.numChildren()>1&&i.length>=1){const r={},s=new Set;for(const o of i){const a=o.parse;for(const c of v(a))c in r?r[c]!==a[c]&&s.add(c):r[c]=a[c]}for(const o of s)delete r[o];if(!Q(r)){this.setModified();const o=new we(t,r);for(const a of n){if(a instanceof we)for(const c of v(r))delete a.parse[c];t.removeChild(a),a.parent=o,a instanceof we&&v(a.parse).length===0&&a.remove()}}}}}class rS extends Pn{run(t){t instanceof Ce||t.numChildren()>0||t instanceof bi||t instanceof _n||(this.setModified(),t.remove())}}class sS extends Pn{run(t){const n=t.children.filter(r=>r instanceof dt),i=n.pop();for(const r of n)this.setModified(),i.merge(r)}}class oS extends Pn{run(t){const n=t.children.filter(r=>r instanceof Ke),i={};for(const r of n){const s=G(r.groupBy);s in i||(i[s]=[]),i[s].push(r)}for(const r of v(i)){const s=i[r];if(s.length>1){const o=s.pop();for(const a of s)o.merge(a)&&(t.removeChild(a),a.parent=o,a.remove(),this.setModified())}}}}class aS extends Pn{constructor(t){super(),this.model=t}run(t){const n=!(Wa(t)||t instanceof yi||t instanceof we||t instanceof Yt),i=[],r=[];for(const s of t.children)s instanceof pt&&(n&&!Ro(t.producedFields(),s.dependentFields())?i.push(s):r.push(s));if(i.length>0){const s=i.pop();for(const o of i)s.merge(o,this.model.renameSignal.bind(this.model));this.setModified(),t instanceof pt?t.merge(s,this.model.renameSignal.bind(this.model)):s.swapWithParent()}if(r.length>1){const s=r.pop();for(const o of r)s.merge(o,this.model.renameSignal.bind(this.model));this.setModified()}}}class cS extends Pn{run(t){const n=[...t.children];if(!xn(n,o=>o instanceof Ce)||t.numChildren()<=1)return;const r=[];let s;for(const o of n)if(o instanceof Ce){let a=o;for(;a.numChildren()===1;){const[c]=a.children;if(c instanceof Ce)a=c;else break}r.push(...a.children),s?(t.removeChild(o),o.parent=s.parent,s.parent.removeChild(s),s.parent=a,this.setModified()):s=a}else r.push(o);if(r.length){this.setModified();for(const o of r)o.parent.removeChild(o),o.parent=s}}}class zn extends J{clone(){return new zn(null,j(this.transform))}constructor(t,n){super(t),this.transform=n}addDimensions(t){this.transform.groupby=ut(this.transform.groupby.concat(t),n=>n)}dependentFields(){const t=new Set;return this.transform.groupby&&this.transform.groupby.forEach(t.add,t),this.transform.joinaggregate.map(n=>n.field).filter(n=>n!==void 0).forEach(t.add,t),t}producedFields(){return new Set(this.transform.joinaggregate.map(this.getDefaultName))}getDefaultName(t){return t.as??C(t)}hash(){return`JoinAggregateTransform ${G(this.transform)}`}assemble(){const t=[],n=[],i=[];for(const s of this.transform.joinaggregate)n.push(s.op),i.push(this.getDefaultName(s)),t.push(s.field===void 0?null:s.field);const r=this.transform.groupby;return{type:"joinaggregate",as:i,ops:n,fields:t,...r!==void 0?{groupby:r}:{}}}}function lS(e){return e.stack.stackBy.reduce((t,n)=>{const i=n.fieldDef,r=C(i);return r&&t.push(r),t},[])}function uS(e){return O(e)&&e.every(t=>z(t))&&e.length>1}class kt extends J{clone(){return new kt(null,j(this._stack))}constructor(t,n){super(t),this._stack=n}static makeFromTransform(t,n){const{stack:i,groupby:r,as:s,offset:o="zero"}=n,a=[],c=[];if(n.sort!==void 0)for(const f of n.sort)a.push(f.field),c.push(fe(f.order,"ascending"));const l={field:a,order:c};let u;return uS(s)?u=s:z(s)?u=[s,`${s}_end`]:u=[`${n.stack}_start`,`${n.stack}_end`],new kt(t,{dimensionFieldDefs:[],stackField:i,groupby:r,offset:o,sort:l,facetby:[],as:u})}static makeFromEncoding(t,n){const i=n.stack,{encoding:r}=n;if(!i)return null;const{groupbyChannels:s,fieldChannel:o,offset:a,impute:c}=i,l=s.map(p=>{const g=r[p];return ht(g)}).filter(p=>!!p),u=lS(n),f=n.encoding.order;let d;if(O(f)||E(f))d=_u(f);else{const p=xf(f)?f.sort:o==="y"?"descending":"ascending";d=u.reduce((g,h)=>(g.field.push(h),g.order.push(p),g),{field:[],order:[]})}return new kt(t,{dimensionFieldDefs:l,stackField:n.vgField(o),facetby:[],stackby:u,sort:d,offset:a,impute:c,as:[n.vgField(o,{suffix:"start",forAs:!0}),n.vgField(o,{suffix:"end",forAs:!0})]})}get stack(){return this._stack}addDimensions(t){this._stack.facetby.push(...t)}dependentFields(){const t=new Set;return t.add(this._stack.stackField),this.getGroupbyFields().forEach(t.add,t),this._stack.facetby.forEach(t.add,t),this._stack.sort.field.forEach(t.add,t),t}producedFields(){return new Set(this._stack.as)}hash(){return`Stack ${G(this._stack)}`}getGroupbyFields(){const{dimensionFieldDefs:t,impute:n,groupby:i}=this._stack;return t.length>0?t.map(r=>r.bin?n?[C(r,{binSuffix:"mid"})]:[C(r,{}),C(r,{binSuffix:"end"})]:[C(r)]).flat():i??[]}assemble(){const t=[],{facetby:n,dimensionFieldDefs:i,stackField:r,stackby:s,sort:o,offset:a,impute:c,as:l}=this._stack;if(c)for(const u of i){const{bandPosition:f=.5,bin:d}=u;if(d){const p=C(u,{expr:"datum"}),g=C(u,{expr:"datum",binSuffix:"end"});t.push({type:"formula",expr:`${f}*${p}+${1-f}*${g}`,as:C(u,{binSuffix:"mid",forAs:!0})})}t.push({type:"impute",field:r,groupby:[...s,...n],key:C(u,{binSuffix:"mid"}),method:"value",value:0})}return t.push({type:"stack",groupby:[...this.getGroupbyFields(),...n],field:r,sort:o,as:l,offset:a}),t}}class xi extends J{clone(){return new xi(null,j(this.transform))}constructor(t,n){super(t),this.transform=n}addDimensions(t){this.transform.groupby=ut(this.transform.groupby.concat(t),n=>n)}dependentFields(){const t=new Set;return(this.transform.groupby??[]).forEach(t.add,t),(this.transform.sort??[]).forEach(n=>t.add(n.field)),this.transform.window.map(n=>n.field).filter(n=>n!==void 0).forEach(t.add,t),t}producedFields(){return new Set(this.transform.window.map(this.getDefaultName))}getDefaultName(t){return t.as??C(t)}hash(){return`WindowTransform ${G(this.transform)}`}assemble(){const t=[],n=[],i=[],r=[];for(const f of this.transform.window)n.push(f.op),i.push(this.getDefaultName(f)),r.push(f.param===void 0?null:f.param),t.push(f.field===void 0?null:f.field);const s=this.transform.frame,o=this.transform.groupby;if(s&&s[0]===null&&s[1]===null&&n.every(f=>Bo(f)))return{type:"joinaggregate",as:i,ops:n,fields:t,...o!==void 0?{groupby:o}:{}};const a=[],c=[];if(this.transform.sort!==void 0)for(const f of this.transform.sort)a.push(f.field),c.push(f.order??"ascending");const l={field:a,order:c},u=this.transform.ignorePeers;return{type:"window",params:r,as:i,ops:n,fields:t,sort:l,...u!==void 0?{ignorePeers:u}:{},...o!==void 0?{groupby:o}:{},...s!==void 0?{frame:s}:{}}}}function fS(e){function t(n){if(!(n instanceof bi)){const i=n.clone();if(i instanceof Ce){const r=vo+i.getSource();i.setSource(r),e.model.component.data.outputNodes[r]=i}else(i instanceof Ke||i instanceof kt||i instanceof xi||i instanceof zn)&&i.addDimensions(e.fields);for(const r of n.children.flatMap(t))r.parent=i;return[i]}return n.children.flatMap(t)}return t}function xo(e){if(e instanceof bi)if(e.numChildren()===1&&!(e.children[0]instanceof Ce)){const t=e.children[0];(t instanceof Ke||t instanceof kt||t instanceof xi||t instanceof zn)&&t.addDimensions(e.fields),t.swapWithParent(),xo(e)}else{const t=e.model.component.data.main;tp(t);const n=fS(e),i=e.children.map(n).flat();for(const r of i)r.parent=t}else e.children.map(xo)}function tp(e){if(e instanceof Ce&&e.type===ee.Main&&e.numChildren()===1){const t=e.children[0];t instanceof bi||(t.swapWithParent(),tp(e))}}const vo="scale_",or=5;function So(e){for(const t of e){for(const n of t.children)if(n.parent!==t)return!1;if(!So(t.children))return!1}return!0}function Ge(e,t){let n=!1;for(const i of t)n=e.optimize(i)||n;return n}function ul(e,t,n){let i=e.sources,r=!1;return r=Ge(new tS,i)||r,r=Ge(new Zv(t),i)||r,i=i.filter(s=>s.numChildren()>0),r=Ge(new rS,i)||r,i=i.filter(s=>s.numChildren()>0),n||(r=Ge(new nS,i)||r,r=Ge(new aS(t),i)||r,r=Ge(new eS,i)||r,r=Ge(new iS,i)||r,r=Ge(new oS,i)||r,r=Ge(new sS,i)||r,r=Ge(new Qv,i)||r,r=Ge(new cS,i)||r),e.sources=i,r}function dS(e,t){So(e.sources);let n=0,i=0;for(let r=0;r<or&&ul(e,t,!0);r++)n++;e.sources.map(xo);for(let r=0;r<or&&ul(e,t,!1);r++)i++;So(e.sources),Math.max(n,i)===or&&S(`Maximum optimization runs(${or}) reached.`)}class Oe{constructor(t){Object.defineProperty(this,"signal",{enumerable:!0,get:t})}static fromName(t,n){return new Oe(()=>t(n))}}function np(e){ae(e)?pS(e):gS(e)}function pS(e){const t=e.component.scales;for(const n of v(t)){const i=mS(e,n);if(t[n].setWithExplicit("domains",i),bS(e,n),e.component.data.isFaceted){let s=e;for(;!Je(s)&&s.parent;)s=s.parent;if(s.component.resolve.scale[n]==="shared")for(const a of i.value)Nt(a)&&(a.data=vo+a.data.replace(vo,""))}}}function gS(e){for(const n of e.children)np(n);const t=e.component.scales;for(const n of v(t)){let i,r=null;for(const s of e.children){const o=s.component.scales[n];if(o){i===void 0?i=o.getWithExplicit("domains"):i=Ht(i,o.getWithExplicit("domains"),"domains","scale",Eo);const a=o.get("selectionExtent");r&&a&&r.param!==a.param&&S(ph),r=a}}t[n].setWithExplicit("domains",i),r&&t[n].set("selectionExtent",r,!0)}}function hS(e,t,n,i){if(e==="unaggregated"){const{valid:r,reason:s}=fl(t,n);if(!r){S(s);return}}else if(e===void 0&&i.useUnaggregatedDomain){const{valid:r}=fl(t,n);if(r)return"unaggregated"}return e}function mS(e,t){const n=e.getScaleComponent(t).get("type"),{encoding:i}=e,r=hS(e.scaleDomain(t),e.typedFieldDef(t),n,e.config.scale);return r!==e.scaleDomain(t)&&(e.specifiedScales[t]={...e.specifiedScales[t],domain:r}),t==="x"&&de(i.x2)?de(i.x)?Ht(Mt(n,r,e,"x"),Mt(n,r,e,"x2"),"domain","scale",Eo):Mt(n,r,e,"x2"):t==="y"&&de(i.y2)?de(i.y)?Ht(Mt(n,r,e,"y"),Mt(n,r,e,"y2"),"domain","scale",Eo):Mt(n,r,e,"y2"):Mt(n,r,e,t)}function yS(e,t,n){return e.map(i=>({signal:`{data: ${es(i,{timeUnit:n,type:t})}}`}))}function Ls(e,t,n){var r;const i=(r=me(n))==null?void 0:r.unit;return t==="temporal"||i?yS(e,t,i):[e]}function Mt(e,t,n,i){const{encoding:r}=n,s=de(r[i]),{type:o}=s,a=s.timeUnit;if(Dm(t)){const f=Mt(e,void 0,n,i),d=Ls(t.unionWith,o,a);return ct([...d,...f.value])}else{if(R(t))return ct([t]);if(t&&t!=="unaggregated"&&!tf(t))return ct(Ls(t,o,a))}const c=n.stack;if(c&&i===c.fieldChannel){if(c.offset==="normalize")return Le([[0,1]]);const f=n.requestDataName(ee.Main);return Le([{data:f,field:n.vgField(i,{suffix:"start"})},{data:f,field:n.vgField(i,{suffix:"end"})}])}const l=zt(i)&&E(s)?xS(n,i,e):void 0;if(Et(s)){const f=Ls([s.datum],o,a);return Le(f)}const u=s;if(t==="unaggregated"){const f=n.requestDataName(ee.Main),{field:d}=s;return Le([{data:f,field:C({field:d,aggregate:"min"})},{data:f,field:C({field:d,aggregate:"max"})}])}else if(ne(u.bin)){if(xe(e))return Le(e==="bin-ordinal"?[]:[{data:Pi(l)?n.requestDataName(ee.Main):n.requestDataName(ee.Raw),field:n.vgField(i,Ji(u,i)?{binSuffix:"range"}:{}),sort:l===!0||!Y(l)?{field:n.vgField(i,{}),op:"min"}:l}]);{const{bin:f}=u;if(ne(f)){const d=Ua(n,u.field,f);return Le([new Oe(()=>{const p=n.getSignalName(d);return`[${p}.start, ${p}.stop]`})])}else return Le([{data:n.requestDataName(ee.Main),field:n.vgField(i,{})}])}}else if(u.timeUnit&&q(["time","utc"],e)&&bf(u,ae(n)?n.encoding[vt(i)]:void 0,n.markDef,n.config)){const f=n.requestDataName(ee.Main);return Le([{data:f,field:n.vgField(i)},{data:f,field:n.vgField(i,{suffix:"end"})}])}else return Le(l?[{data:Pi(l)?n.requestDataName(ee.Main):n.requestDataName(ee.Raw),field:n.vgField(i),sort:l}]:[{data:n.requestDataName(ee.Main),field:n.vgField(i)}])}function Ps(e,t){const{op:n,field:i,order:r}=e;return{op:n??(t?"sum":Yr),...i?{field:Me(i)}:{},...r?{order:r}:{}}}function bS(e,t){var a;const n=e.component.scales[t],i=e.specifiedScales[t].domain,r=(a=e.fieldDef(t))==null?void 0:a.bin,s=tf(i)&&i,o=An(r)&&Wr(r.extent)&&r.extent;(s||o)&&n.set("selectionExtent",s??o,!0)}function xS(e,t,n){if(!xe(n))return;const i=e.fieldDef(t),r=i.sort;if(hf(r))return{op:"min",field:ci(i,t),order:"ascending"};const{stack:s}=e,o=s?new Set([...s.groupbyFields,...s.stackBy.map(a=>a.fieldDef.field)]):void 0;if(ft(r)){const a=s&&!o.has(r.field);return Ps(r,a)}else if(gf(r)){const{encoding:a,order:c}=r,l=e.fieldDef(a),{aggregate:u,field:f}=l,d=s&&!o.has(f);if(Ot(u)||tn(u))return Ps({field:C(l),order:c},d);if(Bo(u)||!u)return Ps({op:u,field:f,order:c},d)}else{if(r==="descending")return{op:"min",field:e.vgField(t),order:"descending"};if(q(["ascending",void 0],r))return!0}}function fl(e,t){const{aggregate:n,type:i}=e;return n?z(n)&&!Jg.has(n)?{valid:!1,reason:Uh(n)}:i==="quantitative"&&t==="log"?{valid:!1,reason:Wh(e)}:{valid:!0}:{valid:!1,reason:Mh(e)}}function Eo(e,t,n,i){return e.explicit&&t.explicit&&S(Vh(n,i,e.value,t.value)),{explicit:e.explicit,value:[...e.value,...t.value]}}function vS(e){const t=ut(e.map(o=>{if(Nt(o)){const{sort:a,...c}=o;return c}return o}),G),n=ut(e.map(o=>{if(Nt(o)){const a=o.sort;return a!==void 0&&!Pi(a)&&("op"in a&&a.op==="count"&&delete a.field,a.order==="ascending"&&delete a.order),a}}).filter(o=>o!==void 0),G);if(t.length===0)return;if(t.length===1){const o=e[0];if(Nt(o)&&n.length>0){let a=n[0];if(n.length>1){S(Ec);const c=n.filter(l=>Y(l)&&"op"in l&&l.op!=="min");n.every(l=>Y(l)&&"op"in l)&&c.length===1?a=c[0]:a=!0}else if(Y(a)&&"field"in a){const c=a.field;o.field===c&&(a=a.order?{order:a.order}:!0)}return{...o,sort:a}}return o}const i=ut(n.map(o=>Pi(o)||!("op"in o)||z(o.op)&&o.op in Vg?o:(S(Yh(o)),!0)),G);let r;i.length===1?r=i[0]:i.length>1&&(S(Ec),r=!0);const s=ut(e.map(o=>Nt(o)?o.data:null),o=>o);return s.length===1&&s[0]!==null?{data:s[0],fields:t.map(a=>a.field),...r?{sort:r}:{}}:{fields:t,...r?{sort:r}:{}}}function qa(e){if(Nt(e)&&z(e.field))return e.field;if(Qg(e)){let t;for(const n of e.fields)if(Nt(n)&&z(n.field)){if(!t)t=n.field;else if(t!==n.field)return S(Kh),t}return S(Jh),t}else if(Zg(e)){S(Qh);const t=e.fields[0];return z(t)?t:void 0}}function us(e,t){const i=e.component.scales[t].get("domains").map(r=>(Nt(r)&&(r.data=e.lookupDataSource(r.data)),r));return vS(i)}function ip(e){return vi(e)||Ha(e)?e.children.reduce((t,n)=>t.concat(ip(n)),dl(e)):dl(e)}function dl(e){return v(e.component.scales).reduce((t,n)=>{const i=e.component.scales[n];if(i.merged)return t;const r=i.combine(),{name:s,type:o,selectionExtent:a,domains:c,range:l,reverse:u,...f}=r,d=SS(r.range,s,n,e),p=us(e,n),g=a?hx(e,a,i,p):null;return t.push({name:s,type:o,...p?{domain:p}:{},...g?{domainRaw:g}:{},range:d,...u!==void 0?{reverse:u}:{},...f}),t},[])}function SS(e,t,n,i){if(be(n)){if(nn(e))return{step:{signal:`${t}_step`}}}else if(Y(e)&&Nt(e))return{...e,data:i.lookupDataSource(e.data)};return e}class rp extends jt{constructor(t,n){super({},{name:t}),this.merged=!1,this.setWithExplicit("type",n)}domainDefinitelyIncludesZero(){return this.get("zero")!==!1?!0:xn(this.get("domains"),t=>O(t)&&t.length===2&&t[0]<=0&&t[1]>=0)}}const ES=["range","scheme"];function $S(e){const t=e.component.scales;for(const n of Ur){const i=t[n];if(!i)continue;const r=wS(n,e);i.setWithExplicit("range",r)}}function pl(e,t){const n=e.fieldDef(t);if(n!=null&&n.bin){const{bin:i,field:r}=n,s=Ie(t),o=e.getName(s);if(Y(i)&&i.binned&&i.step!==void 0)return new Oe(()=>{const a=e.scaleName(t),c=`(domain("${a}")[1] - domain("${a}")[0]) / ${i.step}`;return`${e.getSignalName(o)} / (${c})`});if(ne(i)){const a=Ua(e,r,i);return new Oe(()=>{const c=e.getSignalName(a),l=`(${c}.stop - ${c}.start) / ${c}.step`;return`${e.getSignalName(o)} / (${l})`})}}}function wS(e,t){const n=t.specifiedScales[e],{size:i}=t,s=t.getScaleComponent(e).get("type");for(const f of ES)if(n[f]!==void 0){const d=ro(s,f),p=nf(e,f);if(!d)S(Lu(s,f,e));else if(p)S(p);else switch(f){case"range":{const g=n.range;if(O(g)){if(be(e))return ct(g.map(h=>{if(h==="width"||h==="height"){const m=t.getName(h),y=t.getSignalName.bind(t);return Oe.fromName(y,m)}return h}))}else if(Y(g))return ct({data:t.requestDataName(ee.Main),field:g.field,sort:{op:"min",field:t.vgField(e)}});return ct(g)}case"scheme":return ct(CS(n[f]))}}const o=e===oe||e==="xOffset"?"width":"height",a=i[o];if(mt(a)){if(be(e))if(xe(s)){const f=sp(a,t,e);if(f)return ct({step:f})}else S(Pu(o));else if(gi(e)){const f=e===Kt?"x":"y";if(t.getScaleComponent(f).get("type")==="band"){const g=op(a,s);if(g)return ct(g)}}}const{rangeMin:c,rangeMax:l}=n,u=NS(e,t);return(c!==void 0||l!==void 0)&&ro(s,"rangeMin")&&O(u)&&u.length===2?ct([c??u[0],l??u[1]]):Le(u)}function CS(e){return zm(e)?{scheme:e.name,...Te(e,["name"])}:{scheme:e}}function NS(e,t){const{size:n,config:i,mark:r,encoding:s}=t,o=t.getSignalName.bind(t),{type:a}=de(s[e]),l=t.getScaleComponent(e).get("type"),{domain:u,domainMid:f}=t.specifiedScales[e];switch(e){case oe:case ye:{if(q(["point","band"],l)){const g=ap(e,n,i.view);if(mt(g))return{step:sp(g,t,e)}}const d=Ie(e),p=t.getName(d);return e===ye&&ze(l)?[Oe.fromName(o,p),0]:[0,Oe.fromName(o,p)]}case Kt:case pi:return FS(e,t,l);case Lt:{const d=t.component.scales[e].get("zero"),p=cp(r,d,i),g=kS(r,n,t,i);return ti(l)?TS(p,g,_S(l,i,u,e)):[p,g]}case We:return[0,Math.PI*2];case kn:return[0,360];case tt:return[0,new Oe(()=>{const d=t.getSignalName("width"),p=t.getSignalName("height");return`min(${d},${p})/2`})];case Zt:return[i.scale.minStrokeWidth,i.scale.maxStrokeWidth];case en:return[[1,0],[4,2],[2,1],[1,1],[1,2,4,2]];case Re:return"symbol";case Ae:case bt:case xt:return l==="ordinal"?a==="nominal"?"category":"ordinal":f!==void 0?"diverging":r==="rect"||r==="geoshape"?"heatmap":"ramp";case Pt:case Jt:case Qt:return[i.scale.minOpacity,i.scale.maxOpacity]}}function sp(e,t,n){const{encoding:i}=t,r=t.getScaleComponent(n),s=jo(n),o=i[s];if(Xf({step:e,offsetIsDiscrete:M(o)&&Ku(o.type)})==="offset"&&kf(i,s)){const c=t.getScaleComponent(s);let u=`domain('${t.scaleName(s)}').length`;if(c.get("type")==="band"){const d=c.get("paddingInner")??c.get("padding")??0,p=c.get("paddingOuter")??c.get("padding")??0;u=`bandspace(${u}, ${d}, ${p})`}const f=r.get("paddingInner")??r.get("padding");return{signal:`${e.step} * ${u} / (1-${ih(f)})`}}else return e.step}function op(e,t){if(Xf({step:e,offsetIsDiscrete:xe(t)})==="offset")return{step:e.step}}function FS(e,t,n){const i=e===Kt?"x":"y",s=t.getScaleComponent(i).get("type"),o=t.scaleName(i);if(s==="band"){const a=ap(i,t.size,t.config.view);if(mt(a)){const c=op(a,n);if(c)return c}return[0,{signal:`bandwidth('${o}')`}]}else{const a=t.encoding[i];if(E(a)&&a.timeUnit){const c=Hu(a.timeUnit,u=>`scale('${o}', ${u})`),l=t.config.scale.bandWithNestedOffsetPaddingInner;if(l){const u=R(l)?`${l.signal}/2`:`${l/2}`,f=R(l)?`(1 - ${l.signal}/2)`:`${1-l/2}`;return[{signal:`${u} * (${c})`},{signal:`${f} * (${c})`}]}return[0,{signal:c}]}return tu(`Cannot use ${e} scale if ${i} scale is not discrete.`)}}function ap(e,t,n){const i=e===oe?"width":"height",r=t[i];return r||Nr(n,i)}function _S(e,t,n,i){switch(e){case"quantile":return t.scale.quantileCount;case"quantize":return t.scale.quantizeCount;case"threshold":return n!==void 0&&O(n)?n.length+1:(S(am(i)),3)}}function TS(e,t,n){const i=()=>{const r=Ve(t),s=Ve(e),o=`(${r} - ${s}) / (${n} - 1)`;return`sequence(${s}, ${r} + ${o}, ${o})`};return R(t)?new Oe(i):{signal:i()}}function cp(e,t,n){if(t)return R(t)?{signal:`${t.signal} ? 0 : ${cp(e,!1,n)}`}:0;switch(e){case"bar":case"tick":return n.scale.minBandSize;case"line":case"trail":case"rule":return n.scale.minStrokeWidth;case"text":return n.scale.minFontSize;case"point":case"square":case"circle":return n.scale.minSize}throw new Error(Br("size",e))}const gl=.95;function kS(e,t,n,i){const r={x:pl(n,"x"),y:pl(n,"y")};switch(e){case"bar":case"tick":{if(i.scale.maxBandSize!==void 0)return i.scale.maxBandSize;const s=hl(t,r,i.view);return ue(s)?s-1:new Oe(()=>`${s.signal} - 1`)}case"line":case"trail":case"rule":return i.scale.maxStrokeWidth;case"text":return i.scale.maxFontSize;case"point":case"square":case"circle":{if(i.scale.maxSize)return i.scale.maxSize;const s=hl(t,r,i.view);return ue(s)?Math.pow(gl*s,2):new Oe(()=>`pow(${gl} * ${s.signal}, 2)`)}}throw new Error(Br("size",e))}function hl(e,t,n){const i=mt(e.width)?e.width.step:Cr(n,"width"),r=mt(e.height)?e.height.step:Cr(n,"height");return t.x||t.y?new Oe(()=>`min(${[t.x?t.x.signal:i,t.y?t.y.signal:r].join(", ")})`):Math.min(i,r)}function lp(e,t){ae(e)?OS(e,t):fp(e,t)}function OS(e,t){const n=e.component.scales,{config:i,encoding:r,markDef:s,specifiedScales:o}=e;for(const a of v(n)){const c=o[a],l=n[a],u=e.getScaleComponent(a),f=de(r[a]),d=c[t],p=u.get("type"),g=u.get("padding"),h=u.get("paddingInner"),m=ro(p,t),y=nf(a,t);if(d!==void 0&&(m?y&&S(y):S(Lu(p,t,a))),m&&y===void 0)if(d!==void 0){const b=f.timeUnit,N=f.type;switch(t){case"domainMax":case"domainMin":Rn(c[t])||N==="temporal"||b?l.set(t,{signal:es(c[t],{type:N,timeUnit:b})},!0):l.set(t,c[t],!0);break;default:l.copyKeyFromObject(t,c)}}else{const b=t in ml?ml[t]({model:e,channel:a,fieldOrDatumDef:f,scaleType:p,scalePadding:g,scalePaddingInner:h,domain:c.domain,domainMin:c.domainMin,domainMax:c.domainMax,markDef:s,config:i,hasNestedOffsetScale:oo(r,a),hasSecondaryRangeChannel:!!r[vt(a)]}):i.scale[t];b!==void 0&&l.set(t,b,!1)}}}const ml={bins:({model:e,fieldOrDatumDef:t})=>E(t)?AS(e,t):void 0,interpolate:({channel:e,fieldOrDatumDef:t})=>RS(e,t.type),nice:({scaleType:e,channel:t,domain:n,domainMin:i,domainMax:r,fieldOrDatumDef:s})=>IS(e,t,n,i,r,s),padding:({channel:e,scaleType:t,fieldOrDatumDef:n,markDef:i,config:r})=>LS(e,t,r.scale,n,i,r.bar),paddingInner:({scalePadding:e,channel:t,markDef:n,scaleType:i,config:r,hasNestedOffsetScale:s})=>PS(e,t,n.type,i,r.scale,s),paddingOuter:({scalePadding:e,channel:t,scaleType:n,scalePaddingInner:i,config:r,hasNestedOffsetScale:s})=>zS(e,t,n,i,r.scale,s),reverse:({fieldOrDatumDef:e,scaleType:t,channel:n,config:i})=>{const r=E(e)?e.sort:void 0;return DS(t,r,n,i.scale)},zero:({channel:e,fieldOrDatumDef:t,domain:n,markDef:i,scaleType:r,config:s,hasSecondaryRangeChannel:o})=>jS(e,t,n,i,r,s.scale,o)};function up(e){ae(e)?$S(e):fp(e,"range")}function fp(e,t){const n=e.component.scales;for(const i of e.children)t==="range"?up(i):lp(i,t);for(const i of v(n)){let r;for(const s of e.children){const o=s.component.scales[i];if(o){const a=o.getWithExplicit(t);r=Ht(r,a,t,"scale",od((c,l)=>{switch(t){case"range":return c.step&&l.step?c.step-l.step:0}return 0}))}}n[i].setWithExplicit(t,r)}}function AS(e,t){const n=t.bin;if(ne(n)){const i=Ua(e,t.field,n);return new Oe(()=>e.getSignalName(i))}else if(ve(n)&&An(n)&&n.step!==void 0)return{step:n.step}}function RS(e,t){if(q([Ae,bt,xt],e)&&t!=="nominal")return"hcl"}function IS(e,t,n,i,r,s){var o;if(!((o=ht(s))!=null&&o.bin||O(n)||r!=null||i!=null||q([ke.TIME,ke.UTC],e)))return be(t)?!0:void 0}function LS(e,t,n,i,r,s){if(be(e)){if(Xe(t)){if(n.continuousPadding!==void 0)return n.continuousPadding;const{type:o,orient:a}=r;if(o==="bar"&&!(E(i)&&(i.bin||i.timeUnit))&&(a==="vertical"&&e==="x"||a==="horizontal"&&e==="y"))return s.continuousBandSize}if(t===ke.POINT)return n.pointPadding}}function PS(e,t,n,i,r,s=!1){if(e===void 0){if(be(t)){const{bandPaddingInner:o,barBandPaddingInner:a,rectBandPaddingInner:c,bandWithNestedOffsetPaddingInner:l}=r;return s?l:fe(o,n==="bar"?a:c)}else if(gi(t)&&i===ke.BAND)return r.offsetBandPaddingInner}}function zS(e,t,n,i,r,s=!1){if(e===void 0){if(be(t)){const{bandPaddingOuter:o,bandWithNestedOffsetPaddingOuter:a}=r;if(s)return a;if(n===ke.BAND)return fe(o,R(i)?{signal:`${i.signal}/2`}:i/2)}else if(gi(t)){if(n===ke.POINT)return .5;if(n===ke.BAND)return r.offsetBandPaddingOuter}}}function DS(e,t,n,i){if(n==="x"&&i.xReverse!==void 0)return ze(e)&&t==="descending"?R(i.xReverse)?{signal:`!${i.xReverse.signal}`}:!i.xReverse:i.xReverse;if(ze(e)&&t==="descending")return!0}function jS(e,t,n,i,r,s,o){if(!!n&&n!=="unaggregated"&&ze(r)){if(O(n)){const c=n[0],l=n[n.length-1];if(c<=0&&l>=0)return!0}return!1}if(e==="size"&&t.type==="quantitative"&&!ti(r))return!0;if(!(E(t)&&t.bin)&&q([...St,...jg],e)){const{orient:c,type:l}=i;return q(["bar","area","line","trail"],l)&&(c==="horizontal"&&e==="y"||c==="vertical"&&e==="x")?!1:q(["bar","area"],l)&&!o?!0:s==null?void 0:s.zero}return!1}function MS(e,t,n,i,r=!1){const s=US(t,n,i,r),{type:o}=e;return zt(t)?o!==void 0?Gm(t,o)?E(n)&&!Bm(o,n.type)?(S(qh(o,s)),s):o:(S(Gh(t,o,s)),s):s:null}function US(e,t,n,i){var r;switch(t.type){case"nominal":case"ordinal":{if(qn(e)||Fs(e)==="discrete")return e==="shape"&&t.type==="ordinal"&&S(_s(e,"ordinal")),"ordinal";if(be(e)||gi(e)){if(q(["rect","bar","image","rule"],n.type)||i)return"band"}else if(n.type==="arc"&&e in Wo)return"band";const s=n[Ie(e)];return wn(s)||ii(t)&&((r=t.axis)!=null&&r.tickBand)?"band":"point"}case"temporal":return qn(e)?"time":Fs(e)==="discrete"?(S(_s(e,"temporal")),"ordinal"):E(t)&&t.timeUnit&&me(t.timeUnit).utc?"utc":"time";case"quantitative":return qn(e)?E(t)&&ne(t.bin)?"bin-ordinal":"linear":Fs(e)==="discrete"?(S(_s(e,"quantitative")),"ordinal"):"linear";case"geojson":return}throw new Error(Ru(t.type))}function WS(e,{ignoreRange:t}={}){dp(e),np(e);for(const n of Wm)lp(e,n);t||up(e)}function dp(e){ae(e)?e.component.scales=BS(e):e.component.scales=qS(e)}function BS(e){const{encoding:t,mark:n,markDef:i}=e,r={};for(const s of Ur){const o=de(t[s]);if(o&&n===of&&s===Re&&o.type===hi)continue;let a=o&&o.scale;if(gi(s)){const c=yu(s);if(!oo(t,c)){a&&S(kh(s));continue}}if(o&&a!==null&&a!==!1){a??(a={});const c=oo(t,s),l=MS(a,s,o,i,c);r[s]=new rp(e.scaleName(`${s}`,!0),{value:l,explicit:a.type===l})}}return r}const GS=od((e,t)=>wc(e)-wc(t));function qS(e){var t;const n=e.component.scales={},i={},r=e.component.resolve;for(const s of e.children){dp(s);for(const o of v(s.component.scales))if((t=r.scale)[o]??(t[o]=Bd(o,e)),r.scale[o]==="shared"){const a=i[o],c=s.component.scales[o].getWithExplicit("type");a?Am(a.value,c.value)?i[o]=Ht(a,c,"type","scale",GS):(r.scale[o]="independent",delete i[o]):i[o]=c}}for(const s of v(i)){const o=e.scaleName(s,!0),a=i[s];n[s]=new rp(o,a);for(const c of e.children){const l=c.component.scales[s];l&&(c.renameScale(l.get("name"),o),l.merged=!0)}}return n}class zs{constructor(){this.nameMap={}}rename(t,n){this.nameMap[t]=n}has(t){return this.nameMap[t]!==void 0}get(t){for(;this.nameMap[t]&&t!==this.nameMap[t];)t=this.nameMap[t];return t}}function ae(e){return(e==null?void 0:e.type)==="unit"}function Je(e){return(e==null?void 0:e.type)==="facet"}function Ha(e){return(e==null?void 0:e.type)==="concat"}function vi(e){return(e==null?void 0:e.type)==="layer"}class Va{constructor(t,n,i,r,s,o,a){this.type=n,this.parent=i,this.config=s,this.correctDataNames=c=>{var l,u,f;return(l=c.from)!=null&&l.data&&(c.from.data=this.lookupDataSource(c.from.data)),(f=(u=c.from)==null?void 0:u.facet)!=null&&f.data&&(c.from.facet.data=this.lookupDataSource(c.from.facet.data)),c},this.parent=i,this.config=s,this.view=_e(a),this.name=t.name??r,this.title=Ut(t.title)?{text:t.title}:t.title?_e(t.title):void 0,this.scaleNameMap=i?i.scaleNameMap:new zs,this.projectionNameMap=i?i.projectionNameMap:new zs,this.signalNameMap=i?i.signalNameMap:new zs,this.data=t.data,this.description=t.description,this.transforms=Qb(t.transform??[]),this.layout=n==="layer"||n==="unit"?{}:ob(t,n,s),this.component={data:{sources:i?i.component.data.sources:[],outputNodes:i?i.component.data.outputNodes:{},outputNodeRefCounts:i?i.component.data.outputNodeRefCounts:{},isFaceted:Kr(t)||(i==null?void 0:i.component.data.isFaceted)&&t.data===void 0},layoutSize:new jt,layoutHeaders:{row:{},column:{},facet:{}},mark:null,resolve:{scale:{},axis:{},legend:{},...o?j(o):{}},selection:null,scales:null,projection:null,axes:{},legends:{}}}get width(){return this.getSizeSignalRef("width")}get height(){return this.getSizeSignalRef("height")}parse(){this.parseScale(),this.parseLayoutSize(),this.renameTopLevelLayoutSizeSignal(),this.parseSelections(),this.parseProjection(),this.parseData(),this.parseAxesAndHeaders(),this.parseLegends(),this.parseMarkGroup()}parseScale(){WS(this)}parseProjection(){Zd(this)}renameTopLevelLayoutSizeSignal(){this.getName("width")!=="width"&&this.renameSignal(this.getName("width"),"width"),this.getName("height")!=="height"&&this.renameSignal(this.getName("height"),"height")}parseLegends(){Xd(this)}assembleEncodeFromView(t){const{style:n,...i}=t,r={};for(const s of v(i)){const o=i[s];o!==void 0&&(r[s]=re(o))}return r}assembleGroupEncodeEntry(t){let n={};return this.view&&(n=this.assembleEncodeFromView(this.view)),!t&&(this.description&&(n.description=re(this.description)),this.type==="unit"||this.type==="layer")?{width:this.getSizeSignalRef("width"),height:this.getSizeSignalRef("height"),...n??{}}:Q(n)?void 0:n}assembleLayout(){if(!this.layout)return;const{spacing:t,...n}=this.layout,{component:i,config:r}=this,s=dv(i.layoutHeaders,r);return{padding:t,...this.assembleDefaultLayout(),...n,...s?{titleBand:s}:{}}}assembleDefaultLayout(){return{}}assembleHeaderMarks(){const{layoutHeaders:t}=this.component;let n=[];for(const i of je)t[i].title&&n.push(ov(this,i));for(const i of za)n=n.concat(av(this,i));return n}assembleAxes(){return Vx(this.component.axes,this.config)}assembleLegends(){return Kd(this)}assembleProjections(){return Lv(this)}assembleTitle(){const{encoding:t,...n}=this.title??{},i={...wu(this.config.title).nonMarkTitleProperties,...n,...t?{encode:{update:t}}:{}};if(i.text)return q(["unit","layer"],this.type)?q(["middle",void 0],i.anchor)&&(i.frame??(i.frame="group")):i.anchor??(i.anchor="start"),Q(i)?void 0:i}assembleGroup(t=[]){const n={};t=t.concat(this.assembleSignals()),t.length>0&&(n.signals=t);const i=this.assembleLayout();i&&(n.layout=i),n.marks=[].concat(this.assembleHeaderMarks(),this.assembleMarks());const r=!this.parent||Je(this.parent)?ip(this):[];r.length>0&&(n.scales=r);const s=this.assembleAxes();s.length>0&&(n.axes=s);const o=this.assembleLegends();return o.length>0&&(n.legends=o),n}getName(t){return se((this.name?`${this.name}_`:"")+t)}getDataName(t){return this.getName(ee[t].toLowerCase())}requestDataName(t){const n=this.getDataName(t),i=this.component.data.outputNodeRefCounts;return i[n]=(i[n]||0)+1,n}getSizeSignalRef(t){if(Je(this.parent)){const n=Ud(t),i=Mr(n),r=this.component.scales[i];if(r&&!r.merged){const s=r.get("type"),o=r.get("range");if(xe(s)&&nn(o)){const a=r.get("name"),c=us(this,i),l=qa(c);if(l){const u=C({aggregate:"distinct",field:l},{expr:"datum"});return{signal:Md(a,r,u)}}else return S(qo(i)),null}}}return{signal:this.signalNameMap.get(this.getName(t))}}lookupDataSource(t){const n=this.component.data.outputNodes[t];return n?n.getSource():t}getSignalName(t){return this.signalNameMap.get(t)}renameSignal(t,n){this.signalNameMap.rename(t,n)}renameScale(t,n){this.scaleNameMap.rename(t,n)}renameProjection(t,n){this.projectionNameMap.rename(t,n)}scaleName(t,n){if(n)return this.getName(t);if(gu(t)&&zt(t)&&this.component.scales[t]||this.scaleNameMap.has(this.getName(t)))return this.scaleNameMap.get(this.getName(t))}projectionName(t){if(t)return this.getName("projection");if(this.component.projection&&!this.component.projection.merged||this.projectionNameMap.has(this.getName("projection")))return this.projectionNameMap.get(this.getName("projection"))}getScaleComponent(t){if(!this.component.scales)throw new Error("getScaleComponent cannot be called before parseScale(). Make sure you have called parseScale or use parseUnitModelWithScale().");const n=this.component.scales[t];return n&&!n.merged?n:this.parent?this.parent.getScaleComponent(t):void 0}getSelectionComponent(t,n){let i=this.component.selection[t];if(!i&&this.parent&&(i=this.parent.getSelectionComponent(t,n)),!i)throw new Error(ch(n));return i}hasAxisOrientSignalRef(){var t,n;return((t=this.component.axes.x)==null?void 0:t.some(i=>i.hasOrientSignalRef()))||((n=this.component.axes.y)==null?void 0:n.some(i=>i.hasOrientSignalRef()))}}class pp extends Va{vgField(t,n={}){const i=this.fieldDef(t);if(i)return C(i,n)}reduceFieldDef(t,n){return Ly(this.getMapping(),(i,r,s)=>{const o=ht(r);return o?t(i,o,s):i},n)}forEachFieldDef(t,n){xa(this.getMapping(),(i,r)=>{const s=ht(i);s&&t(s,r)},n)}}class fs extends J{clone(){return new fs(null,j(this.transform))}constructor(t,n){super(t),this.transform=n,this.transform=j(n);const i=this.transform.as??[void 0,void 0];this.transform.as=[i[0]??"value",i[1]??"density"],n.groupby&&n.minsteps==null&&n.maxsteps==null&&n.steps==null&&(this.transform.steps=200)}dependentFields(){return new Set([this.transform.density,...this.transform.groupby??[]])}producedFields(){return new Set(this.transform.as)}hash(){return`DensityTransform ${G(this.transform)}`}assemble(){const{density:t,...n}=this.transform;return{type:"kde",field:t,...n}}}class ds extends J{clone(){return new ds(null,j(this.transform))}constructor(t,n){super(t),this.transform=n,this.transform=j(n)}dependentFields(){return new Set([this.transform.extent])}producedFields(){return new Set([])}hash(){return`ExtentTransform ${G(this.transform)}`}assemble(){const{extent:t,param:n}=this.transform;return{type:"extent",field:t,signal:n}}}class ji extends J{clone(){return new ji(null,{...this.filter})}constructor(t,n){super(t),this.filter=n}static make(t,n){const{config:i,mark:r,markDef:s}=n;if(K("invalid",s,i)!=="filter")return null;const a=n.reduceFieldDef((c,l,u)=>{const f=zt(u)&&n.getScaleComponent(u);if(f){const d=f.get("type");ze(d)&&l.aggregate!=="count"&&!rn(r)&&(c[l.field]=l)}return c},{});return v(a).length?new ji(t,a):null}dependentFields(){return new Set(v(this.filter))}producedFields(){return new Set}hash(){return`FilterInvalid ${G(this.filter)}`}assemble(){const t=v(this.filter).reduce((n,i)=>{const r=this.filter[i],s=C(r,{expr:"datum"});return r!==null&&(r.type==="temporal"?n.push(`(isDate(${s}) || (isValid(${s}) && isFinite(+${s})))`):r.type==="quantitative"&&(n.push(`isValid(${s})`),n.push(`isFinite(+${s})`))),n},[]);return t.length>0?{type:"filter",expr:t.join(" && ")}:null}}class ps extends J{clone(){return new ps(this.parent,j(this.transform))}constructor(t,n){super(t),this.transform=n,this.transform=j(n);const{flatten:i,as:r=[]}=this.transform;this.transform.as=i.map((s,o)=>r[o]??s)}dependentFields(){return new Set(this.transform.flatten)}producedFields(){return new Set(this.transform.as)}hash(){return`FlattenTransform ${G(this.transform)}`}assemble(){const{flatten:t,as:n}=this.transform;return{type:"flatten",fields:t,as:n}}}class gs extends J{clone(){return new gs(null,j(this.transform))}constructor(t,n){super(t),this.transform=n,this.transform=j(n);const i=this.transform.as??[void 0,void 0];this.transform.as=[i[0]??"key",i[1]??"value"]}dependentFields(){return new Set(this.transform.fold)}producedFields(){return new Set(this.transform.as)}hash(){return`FoldTransform ${G(this.transform)}`}assemble(){const{fold:t,as:n}=this.transform;return{type:"fold",fields:t,as:n}}}class Xn extends J{clone(){return new Xn(null,j(this.fields),this.geojson,this.signal)}static parseAll(t,n){if(n.component.projection&&!n.component.projection.isFit)return t;let i=0;for(const r of[[it,nt],[Ue,rt]]){const s=r.map(o=>{const a=de(n.encoding[o]);return E(a)?a.field:Et(a)?{expr:`${a.datum}`}:Qe(a)?{expr:`${a.value}`}:void 0});(s[0]||s[1])&&(t=new Xn(t,s,null,n.getName(`geojson_${i++}`)))}if(n.channelHasField(Re)){const r=n.typedFieldDef(Re);r.type===hi&&(t=new Xn(t,null,r.field,n.getName(`geojson_${i++}`)))}return t}constructor(t,n,i,r){super(t),this.fields=n,this.geojson=i,this.signal=r}dependentFields(){const t=(this.fields??[]).filter(z);return new Set([...this.geojson?[this.geojson]:[],...t])}producedFields(){return new Set}hash(){return`GeoJSON ${this.geojson} ${this.signal} ${G(this.fields)}`}assemble(){return[...this.geojson?[{type:"filter",expr:`isValid(datum["${this.geojson}"])`}]:[],{type:"geojson",...this.fields?{fields:this.fields}:{},...this.geojson?{geojson:this.geojson}:{},signal:this.signal}]}}class Mi extends J{clone(){return new Mi(null,this.projection,j(this.fields),j(this.as))}constructor(t,n,i,r){super(t),this.projection=n,this.fields=i,this.as=r}static parseAll(t,n){if(!n.projectionName())return t;for(const i of[[it,nt],[Ue,rt]]){const r=i.map(o=>{const a=de(n.encoding[o]);return E(a)?a.field:Et(a)?{expr:`${a.datum}`}:Qe(a)?{expr:`${a.value}`}:void 0}),s=i[0]===Ue?"2":"";(r[0]||r[1])&&(t=new Mi(t,n.projectionName(),r,[n.getName(`x${s}`),n.getName(`y${s}`)]))}return t}dependentFields(){return new Set(this.fields.filter(z))}producedFields(){return new Set(this.as)}hash(){return`Geopoint ${this.projection} ${G(this.fields)} ${G(this.as)}`}assemble(){return{type:"geopoint",projection:this.projection,fields:this.fields,as:this.as}}}class bn extends J{clone(){return new bn(null,j(this.transform))}constructor(t,n){super(t),this.transform=n}dependentFields(){return new Set([this.transform.impute,this.transform.key,...this.transform.groupby??[]])}producedFields(){return new Set([this.transform.impute])}processSequence(t){const{start:n=0,stop:i,step:r}=t;return{signal:`sequence(${[n,i,...r?[r]:[]].join(",")})`}}static makeFromTransform(t,n){return new bn(t,n)}static makeFromEncoding(t,n){const i=n.encoding,r=i.x,s=i.y;if(E(r)&&E(s)){const o=r.impute?r:s.impute?s:void 0;if(o===void 0)return;const a=r.impute?s:s.impute?r:void 0,{method:c,value:l,frame:u,keyvals:f}=o.impute,d=Af(n.mark,i);return new bn(t,{impute:o.field,key:a.field,...c?{method:c}:{},...l!==void 0?{value:l}:{},...u?{frame:u}:{},...f!==void 0?{keyvals:f}:{},...d.length?{groupby:d}:{}})}return null}hash(){return`Impute ${G(this.transform)}`}assemble(){const{impute:t,key:n,keyvals:i,method:r,groupby:s,value:o,frame:a=[null,null]}=this.transform,c={type:"impute",field:t,key:n,...i?{keyvals:Rb(i)?this.processSequence(i):i}:{},method:"value",...s?{groupby:s}:{},value:!r||r==="value"?o:null};if(r&&r!=="value"){const l={type:"window",as:[`imputed_${t}_value`],ops:[r],fields:[t],frame:a,ignorePeers:!1,...s?{groupby:s}:{}},u={type:"formula",expr:`datum.${t} === null ? datum.imputed_${t}_value : datum.${t}`,as:t};return[c,l,u]}else return[c]}}class hs extends J{clone(){return new hs(null,j(this.transform))}constructor(t,n){super(t),this.transform=n,this.transform=j(n);const i=this.transform.as??[void 0,void 0];this.transform.as=[i[0]??n.on,i[1]??n.loess]}dependentFields(){return new Set([this.transform.loess,this.transform.on,...this.transform.groupby??[]])}producedFields(){return new Set(this.transform.as)}hash(){return`LoessTransform ${G(this.transform)}`}assemble(){const{loess:t,on:n,...i}=this.transform;return{type:"loess",x:n,y:t,...i}}}class Ui extends J{clone(){return new Ui(null,j(this.transform),this.secondary)}constructor(t,n,i){super(t),this.transform=n,this.secondary=i}static make(t,n,i,r){const s=n.component.data.sources,{from:o}=i;let a=null;if(Ib(o)){let c=mp(o.data,s);c||(c=new _n(o.data),s.push(c));const l=n.getName(`lookup_${r}`);a=new Ce(c,l,ee.Lookup,n.component.data.outputNodeRefCounts),n.component.data.outputNodes[l]=a}else if(Lb(o)){const c=o.param;i={as:c,...i};let l;try{l=n.getSelectionComponent(se(c),c)}catch{throw new Error(fh(c))}if(a=l.materialized,!a)throw new Error(dh(c))}return new Ui(t,i,a.getSource())}dependentFields(){return new Set([this.transform.lookup])}producedFields(){return new Set(this.transform.as?ce(this.transform.as):this.transform.from.fields)}hash(){return`Lookup ${G({transform:this.transform,secondary:this.secondary})}`}assemble(){let t;if(this.transform.from.fields)t={values:this.transform.from.fields,...this.transform.as?{as:ce(this.transform.as)}:{}};else{let n=this.transform.as;z(n)||(S(vh),n="_lookup"),t={as:[n]}}return{type:"lookup",from:this.secondary,key:this.transform.from.key,fields:[this.transform.lookup],...t,...this.transform.default?{default:this.transform.default}:{}}}}class ms extends J{clone(){return new ms(null,j(this.transform))}constructor(t,n){super(t),this.transform=n,this.transform=j(n);const i=this.transform.as??[void 0,void 0];this.transform.as=[i[0]??"prob",i[1]??"value"]}dependentFields(){return new Set([this.transform.quantile,...this.transform.groupby??[]])}producedFields(){return new Set(this.transform.as)}hash(){return`QuantileTransform ${G(this.transform)}`}assemble(){const{quantile:t,...n}=this.transform;return{type:"quantile",field:t,...n}}}class ys extends J{clone(){return new ys(null,j(this.transform))}constructor(t,n){super(t),this.transform=n,this.transform=j(n);const i=this.transform.as??[void 0,void 0];this.transform.as=[i[0]??n.on,i[1]??n.regression]}dependentFields(){return new Set([this.transform.regression,this.transform.on,...this.transform.groupby??[]])}producedFields(){return new Set(this.transform.as)}hash(){return`RegressionTransform ${G(this.transform)}`}assemble(){const{regression:t,on:n,...i}=this.transform;return{type:"regression",x:n,y:t,...i}}}class bs extends J{clone(){return new bs(null,j(this.transform))}constructor(t,n){super(t),this.transform=n}addDimensions(t){this.transform.groupby=ut((this.transform.groupby??[]).concat(t),n=>n)}producedFields(){}dependentFields(){return new Set([this.transform.pivot,this.transform.value,...this.transform.groupby??[]])}hash(){return`PivotTransform ${G(this.transform)}`}assemble(){const{pivot:t,value:n,groupby:i,limit:r,op:s}=this.transform;return{type:"pivot",field:t,value:n,...r!==void 0?{limit:r}:{},...s!==void 0?{op:s}:{},...i!==void 0?{groupby:i}:{}}}}class xs extends J{clone(){return new xs(null,j(this.transform))}constructor(t,n){super(t),this.transform=n}dependentFields(){return new Set}producedFields(){return new Set}hash(){return`SampleTransform ${G(this.transform)}`}assemble(){return{type:"sample",size:this.transform.sample}}}function gp(e){let t=0;function n(i,r){if(i instanceof _n&&!i.isGenerator&&!si(i.data)&&(e.push(r),r={name:null,source:r.name,transform:[]}),i instanceof we&&(i.parent instanceof _n&&!r.source?(r.format={...r.format??{},parse:i.assembleFormatParse()},r.transform.push(...i.assembleTransforms(!0))):r.transform.push(...i.assembleTransforms())),i instanceof bi){r.name||(r.name=`data_${t++}`),!r.source||r.transform.length>0?(e.push(r),i.data=r.name):i.data=r.source,e.push(...i.assemble());return}switch((i instanceof er||i instanceof tr||i instanceof ji||i instanceof yi||i instanceof ai||i instanceof Mi||i instanceof Ke||i instanceof Ui||i instanceof xi||i instanceof zn||i instanceof gs||i instanceof ps||i instanceof fs||i instanceof hs||i instanceof ms||i instanceof ys||i instanceof Yt||i instanceof xs||i instanceof bs||i instanceof ds)&&r.transform.push(i.assemble()),(i instanceof pt||i instanceof dt||i instanceof bn||i instanceof kt||i instanceof Xn)&&r.transform.push(...i.assemble()),i instanceof Ce&&(r.source&&r.transform.length===0?i.setSource(r.source):i.parent instanceof Ce?i.setSource(r.name):(r.name||(r.name=`data_${t++}`),i.setSource(r.name),i.numChildren()===1&&(e.push(r),r={name:null,source:r.name,transform:[]}))),i.numChildren()){case 0:i instanceof Ce&&(!r.source||r.transform.length>0)&&e.push(r);break;case 1:n(i.children[0],r);break;default:{r.name||(r.name=`data_${t++}`);let s=r.name;!r.source||r.transform.length>0?e.push(r):s=r.source;for(const o of i.children)n(o,{name:null,source:s,transform:[]});break}}}return n}function HS(e){const t=[],n=gp(t);for(const i of e.children)n(i,{source:e.name,name:null,transform:[]});return t}function VS(e,t){const n=[],i=gp(n);let r=0;for(const o of e.sources){o.hasName()||(o.dataName=`source_${r++}`);const a=o.assemble();i(o,a)}for(const o of n)o.transform.length===0&&delete o.transform;let s=0;for(const[o,a]of n.entries())(a.transform??[]).length===0&&!a.source&&n.splice(s++,0,n.splice(o,1)[0]);for(const o of n)for(const a of o.transform??[])a.type==="lookup"&&(a.from=e.outputNodes[a.from].getSource());for(const o of n)o.name in t&&(o.values=t[o.name]);return n}function XS(e){return e==="top"||e==="left"||R(e)?"header":"footer"}function YS(e){for(const t of je)KS(e,t);yl(e,"x"),yl(e,"y")}function KS(e,t){var o;const{facet:n,config:i,child:r,component:s}=e;if(e.channelHasField(t)){const a=n[t],c=li("title",null,i,t);let l=Hn(a,i,{allowDisabling:!0,includeDefault:c===void 0||!!c});r.component.layoutHeaders[t].title&&(l=O(l)?l.join(", "):l,l+=` / ${r.component.layoutHeaders[t].title}`,r.component.layoutHeaders[t].title=null);const u=li("labelOrient",a.header,i,t),f=a.header!==null?fe((o=a.header)==null?void 0:o.labels,i.header.labels,!0):!1,d=q(["bottom","right"],u)?"footer":"header";s.layoutHeaders[t]={title:a.header!==null?l:null,facetFieldDef:a,[d]:t==="facet"?[]:[hp(e,t,f)]}}}function hp(e,t,n){const i=t==="row"?"height":"width";return{labels:n,sizeSignal:e.child.component.layoutSize.get(i)?e.child.getSizeSignalRef(i):void 0,axes:[]}}function yl(e,t){const{child:n}=e;if(n.component.axes[t]){const{layoutHeaders:i,resolve:r}=e.component;if(r.axis[t]=Ma(r,t),r.axis[t]==="shared"){const s=t==="x"?"column":"row",o=i[s];for(const a of n.component.axes[t]){const c=XS(a.get("orient"));o[c]??(o[c]=[hp(e,s,!1)]);const l=_i(a,"main",e.config,{header:!0});l&&o[c][0].axes.push(l),a.mainExtracted=!0}}}}function JS(e){Xa(e),kr(e,"width"),kr(e,"height")}function QS(e){Xa(e);const t=e.layout.columns===1?"width":"childWidth",n=e.layout.columns===void 0?"height":"childHeight";kr(e,t),kr(e,n)}function Xa(e){for(const t of e.children)t.parseLayoutSize()}function kr(e,t){const n=Ud(t),i=Mr(n),r=e.component.resolve,s=e.component.layoutSize;let o;for(const a of e.children){const c=a.component.layoutSize.getWithExplicit(n),l=r.scale[i]??Bd(i,e);if(l==="independent"&&c.value==="step"){o=void 0;break}if(o){if(l==="independent"&&o.value!==c.value){o=void 0;break}o=Ht(o,c,n,"")}else o=c}if(o){for(const a of e.children)e.renameSignal(a.getName(n),e.getName(t)),a.component.layoutSize.set(n,"merged",!1);s.setWithExplicit(t,o)}else s.setWithExplicit(t,{explicit:!1,value:void 0})}function ZS(e){const{size:t,component:n}=e;for(const i of St){const r=Ie(i);if(t[r]){const s=t[r];n.layoutSize.set(r,mt(s)?"step":s,!0)}else{const s=eE(e,r);n.layoutSize.set(r,s,!1)}}}function eE(e,t){const n=t==="width"?"x":"y",i=e.config,r=e.getScaleComponent(n);if(r){const s=r.get("type"),o=r.get("range");if(xe(s)){const a=Nr(i.view,t);return nn(o)||mt(a)?"step":a}else return co(i.view,t)}else{if(e.hasProjection||e.mark==="arc")return co(i.view,t);{const s=Nr(i.view,t);return mt(s)?s.step:s}}}function $o(e,t,n){return C(t,{suffix:`by_${C(e)}`,...n??{}})}class Ii extends pp{constructor(t,n,i,r){super(t,"facet",n,i,r,t.resolve),this.child=Za(t.spec,this,this.getName("child"),void 0,r),this.children=[this.child],this.facet=this.initFacet(t.facet)}initFacet(t){if(!Yi(t))return{facet:this.initFacetFieldDef(t,"facet")};const n=v(t),i={};for(const r of n){if(![Ft,_t].includes(r)){S(Br(r,"facet"));break}const s=t[r];if(s.field===void 0){S(no(s,r));break}i[r]=this.initFacetFieldDef(s,r)}return i}initFacetFieldDef(t,n){const i=ya(t,n);return i.header?i.header=_e(i.header):i.header===null&&(i.header=null),i}channelHasField(t){return!!this.facet[t]}fieldDef(t){return this.facet[t]}parseData(){this.component.data=vs(this),this.child.parseData()}parseLayoutSize(){Xa(this)}parseSelections(){this.child.parseSelections(),this.component.selection=this.child.component.selection}parseMarkGroup(){this.child.parseMarkGroup()}parseAxesAndHeaders(){this.child.parseAxesAndHeaders(),YS(this)}assembleSelectionTopLevelSignals(t){return this.child.assembleSelectionTopLevelSignals(t)}assembleSignals(){return this.child.assembleSignals(),[]}assembleSelectionData(t){return this.child.assembleSelectionData(t)}getHeaderLayoutMixins(){const t={};for(const n of je)for(const i of Da){const r=this.component.layoutHeaders[n],s=r[i],{facetFieldDef:o}=r;if(o){const a=li("titleOrient",o.header,this.config,n);if(["right","bottom"].includes(a)){const c=cs(n,a);t.titleAnchor??(t.titleAnchor={}),t.titleAnchor[c]="end"}}if(s!=null&&s[0]){const a=n==="row"?"height":"width",c=i==="header"?"headerBand":"footerBand";n!=="facet"&&!this.child.component.layoutSize.get(a)&&(t[c]??(t[c]={}),t[c][n]=.5),r.title&&(t.offset??(t.offset={}),t.offset[n==="row"?"rowTitle":"columnTitle"]=10)}}return t}assembleDefaultLayout(){const{column:t,row:n}=this.facet,i=t?this.columnDistinctSignal():n?1:void 0;let r="all";return(!n&&this.component.resolve.scale.x==="independent"||!t&&this.component.resolve.scale.y==="independent")&&(r="none"),{...this.getHeaderLayoutMixins(),...i?{columns:i}:{},bounds:"full",align:r}}assembleLayoutSignals(){return this.child.assembleLayoutSignals()}columnDistinctSignal(){if(!(this.parent&&this.parent instanceof Ii))return{signal:`length(data('${this.getName("column_domain")}'))`}}assembleGroupStyle(){}assembleGroup(t){return this.parent&&this.parent instanceof Ii?{...this.channelHasField("column")?{encode:{update:{columns:{field:C(this.facet.column,{prefix:"distinct"})}}}}:{},...super.assembleGroup(t)}:super.assembleGroup(t)}getCardinalityAggregateForChild(){const t=[],n=[],i=[];if(this.child instanceof Ii){if(this.child.channelHasField("column")){const r=C(this.child.facet.column);t.push(r),n.push("distinct"),i.push(`distinct_${r}`)}}else for(const r of St){const s=this.child.component.scales[r];if(s&&!s.merged){const o=s.get("type"),a=s.get("range");if(xe(o)&&nn(a)){const c=us(this.child,r),l=qa(c);l?(t.push(l),n.push("distinct"),i.push(`distinct_${l}`)):S(qo(r))}}}return{fields:t,ops:n,as:i}}assembleFacet(){const{name:t,data:n}=this.component.data.facetRoot,{row:i,column:r}=this.facet,{fields:s,ops:o,as:a}=this.getCardinalityAggregateForChild(),c=[];for(const u of je){const f=this.facet[u];if(f){c.push(C(f));const{bin:d,sort:p}=f;if(ne(d)&&c.push(C(f,{binSuffix:"end"})),ft(p)){const{field:g,op:h=Yr}=p,m=$o(f,p);i&&r?(s.push(m),o.push("max"),a.push(m)):(s.push(g),o.push(h),a.push(m))}else if(O(p)){const g=ci(f,u);s.push(g),o.push("max"),a.push(g)}}}const l=!!i&&!!r;return{name:t,data:n,groupby:c,...l||s.length>0?{aggregate:{...l?{cross:l}:{},...s.length?{fields:s,ops:o,as:a}:{}}}:{}}}facetSortFields(t){const{facet:n}=this,i=n[t];return i?ft(i.sort)?[$o(i,i.sort,{expr:"datum"})]:O(i.sort)?[ci(i,t,{expr:"datum"})]:[C(i,{expr:"datum"})]:[]}facetSortOrder(t){const{facet:n}=this,i=n[t];if(i){const{sort:r}=i;return[(ft(r)?r.order:!O(r)&&r)||"ascending"]}return[]}assembleLabelTitle(){var r;const{facet:t,config:n}=this;if(t.facet)return yo(t.facet,"facet",n);const i={row:["top","bottom"],column:["left","right"]};for(const s of za)if(t[s]){const o=li("labelOrient",(r=t[s])==null?void 0:r.header,n,s);if(i[s].includes(o))return yo(t[s],s,n)}}assembleMarks(){const{child:t}=this,n=this.component.data.facetRoot,i=HS(n),r=t.assembleGroupEncodeEntry(!1),s=this.assembleLabelTitle()||t.assembleTitle(),o=t.assembleGroupStyle();return[{name:this.getName("cell"),type:"group",...s?{title:s}:{},...o?{style:o}:{},from:{facet:this.assembleFacet()},sort:{field:je.map(c=>this.facetSortFields(c)).flat(),order:je.map(c=>this.facetSortOrder(c)).flat()},...i.length>0?{data:i}:{},...r?{encode:{update:r}}:{},...t.assembleGroup(fx(this,[]))}]}getMapping(){return this.facet}}function tE(e,t){const{row:n,column:i}=t;if(n&&i){let r=null;for(const s of[n,i])if(ft(s.sort)){const{field:o,op:a=Yr}=s.sort;e=r=new zn(e,{joinaggregate:[{op:a,field:o,as:$o(s,s.sort,{forAs:!0})}],groupby:[C(s)]})}return r}return null}function mp(e,t){var n,i,r,s;for(const o of t){const a=o.data;if(e.name&&o.hasName()&&e.name!==o.dataName)continue;const c=(n=e.format)==null?void 0:n.mesh,l=(i=a.format)==null?void 0:i.feature;if(c&&l)continue;const u=(r=e.format)==null?void 0:r.feature;if((u||l)&&u!==l)continue;const f=(s=a.format)==null?void 0:s.mesh;if(!((c||f)&&c!==f)){if(Di(e)&&Di(a)){if(lt(e.values,a.values))return o}else if(si(e)&&si(a)){if(e.url===a.url)return o}else if(ad(e)&&e.name===o.dataName)return o}}return null}function nE(e,t){if(e.data||!e.parent){if(e.data===null){const i=new _n({values:[]});return t.push(i),i}const n=mp(e.data,t);if(n)return Bt(e.data)||(n.data.format=nu({},e.data.format,n.data.format)),!n.hasName()&&e.data.name&&(n.dataName=e.data.name),n;{const i=new _n(e.data);return t.push(i),i}}else return e.parent.component.data.facetRoot?e.parent.component.data.facetRoot:e.parent.component.data.main}function iE(e,t,n){let i=0;for(const r of t.transforms){let s,o;if(qb(r))o=e=new ai(e,r),s="derived";else if(ka(r)){const a=Xv(r);o=e=we.makeWithAncestors(e,{},a,n)??e,e=new yi(e,t,r.filter)}else if(nd(r))o=e=pt.makeFromTransform(e,r,t),s="number";else if(Vb(r))s="date",n.getWithExplicit(r.field).value===void 0&&(e=new we(e,{[r.field]:s}),n.set(r.field,s,!1)),o=e=dt.makeFromTransform(e,r);else if(Xb(r))o=e=Ke.makeFromTransform(e,r),s="number",Pa(t)&&(e=new Yt(e));else if(td(r))o=e=Ui.make(e,t,r,i++),s="derived";else if(Wb(r))o=e=new xi(e,r),s="number";else if(Bb(r))o=e=new zn(e,r),s="number";else if(Yb(r))o=e=kt.makeFromTransform(e,r),s="derived";else if(Kb(r))o=e=new gs(e,r),s="derived";else if(Jb(r))o=e=new ds(e,r),s="derived";else if(Gb(r))o=e=new ps(e,r),s="derived";else if(Pb(r))o=e=new bs(e,r),s="derived";else if(Ub(r))e=new xs(e,r);else if(Hb(r))o=e=bn.makeFromTransform(e,r),s="derived";else if(zb(r))o=e=new fs(e,r),s="derived";else if(Db(r))o=e=new ms(e,r),s="derived";else if(jb(r))o=e=new ys(e,r),s="derived";else if(Mb(r))o=e=new hs(e,r),s="derived";else{S(xh(r));continue}if(o&&s!==void 0)for(const a of o.producedFields()??[])n.set(a,s,!1)}return e}function vs(e){var h;let t=nE(e,e.component.data.sources);const{outputNodes:n,outputNodeRefCounts:i}=e.component.data,r=e.data,o=!(r&&(Bt(r)||si(r)||Di(r)))&&e.parent?e.parent.component.data.ancestorParse.clone():new lx;Bt(r)?(cd(r)?t=new tr(t,r.sequence):Oa(r)&&(t=new er(t,r.graticule)),o.parseNothing=!0):((h=r==null?void 0:r.format)==null?void 0:h.parse)===null&&(o.parseNothing=!0),t=we.makeExplicit(t,e,o)??t,t=new Yt(t);const a=e.parent&&vi(e.parent);(ae(e)||Je(e))&&a&&(t=pt.makeFromEncoding(t,e)??t),e.transforms.length>0&&(t=iE(t,e,o));const c=Kv(e),l=Yv(e);t=we.makeWithAncestors(t,{},{...c,...l},o)??t,ae(e)&&(t=Xn.parseAll(t,e),t=Mi.parseAll(t,e)),(ae(e)||Je(e))&&(a||(t=pt.makeFromEncoding(t,e)??t),t=dt.makeFromEncoding(t,e)??t,t=ai.parseAllForSortIndex(t,e));const u=e.getDataName(ee.Raw),f=new Ce(t,u,ee.Raw,i);if(n[u]=f,t=f,ae(e)){const m=Ke.makeFromEncoding(t,e);m&&(t=m,Pa(e)&&(t=new Yt(t))),t=bn.makeFromEncoding(t,e)??t,t=kt.makeFromEncoding(t,e)??t}ae(e)&&(t=ji.make(t,e)??t);const d=e.getDataName(ee.Main),p=new Ce(t,d,ee.Main,i);n[d]=p,t=p,ae(e)&&qx(e,p);let g=null;if(Je(e)){const m=e.getName("facet");t=tE(t,e.facet)??t,g=new bi(t,e,m,p.getSource()),n[m]=g}return{...e.component.data,outputNodes:n,outputNodeRefCounts:i,raw:f,main:p,facetRoot:g,ancestorParse:o}}class rE extends Va{constructor(t,n,i,r){var s,o,a,c;super(t,"concat",n,i,r,t.resolve),(((o=(s=t.resolve)==null?void 0:s.axis)==null?void 0:o.x)==="shared"||((c=(a=t.resolve)==null?void 0:a.axis)==null?void 0:c.y)==="shared")&&S(mh),this.children=this.getChildren(t).map((l,u)=>Za(l,this,this.getName(`concat_${u}`),void 0,r))}parseData(){this.component.data=vs(this);for(const t of this.children)t.parseData()}parseSelections(){this.component.selection={};for(const t of this.children){t.parseSelections();for(const n of v(t.component.selection))this.component.selection[n]=t.component.selection[n]}}parseMarkGroup(){for(const t of this.children)t.parseMarkGroup()}parseAxesAndHeaders(){for(const t of this.children)t.parseAxesAndHeaders()}getChildren(t){return is(t)?t.vconcat:_a(t)?t.hconcat:t.concat}parseLayoutSize(){QS(this)}parseAxisGroup(){return null}assembleSelectionTopLevelSignals(t){return this.children.reduce((n,i)=>i.assembleSelectionTopLevelSignals(n),t)}assembleSignals(){return this.children.forEach(t=>t.assembleSignals()),[]}assembleLayoutSignals(){const t=ja(this);for(const n of this.children)t.push(...n.assembleLayoutSignals());return t}assembleSelectionData(t){return this.children.reduce((n,i)=>i.assembleSelectionData(n),t)}assembleMarks(){return this.children.map(t=>{const n=t.assembleTitle(),i=t.assembleGroupStyle(),r=t.assembleGroupEncodeEntry(!1);return{type:"group",name:t.getName("group"),...n?{title:n}:{},...i?{style:i}:{},...r?{encode:{update:r}}:{},...t.assembleGroup()}})}assembleGroupStyle(){}assembleDefaultLayout(){const t=this.layout.columns;return{...t!=null?{columns:t}:{},bounds:"full",align:"each"}}}function sE(e){return e===!1||e===null}const oE={disable:1,gridScale:1,scale:1,..._f,labelExpr:1,encode:1},yp=v(oE);class Ya extends jt{constructor(t={},n={},i=!1){super(),this.explicit=t,this.implicit=n,this.mainExtracted=i}clone(){return new Ya(j(this.explicit),j(this.implicit),this.mainExtracted)}hasAxisPart(t){return t==="axis"?!0:t==="grid"||t==="title"?!!this.get(t):!sE(this.get(t))}hasOrientSignalRef(){return R(this.explicit.orient)}}function aE(e,t,n){const{encoding:i,config:r}=e,s=de(i[t])??de(i[vt(t)]),o=e.axis(t)||{},{format:a,formatType:c}=o;if(Cn(c))return{text:Ye({fieldOrDatumDef:s,field:"datum.value",format:a,formatType:c,config:r}),...n};if(a===void 0&&c===void 0&&r.customFormatTypes){if(ni(s)==="quantitative"){if(ii(s)&&s.stack==="normalize"&&r.normalizedNumberFormatType)return{text:Ye({fieldOrDatumDef:s,field:"datum.value",format:r.normalizedNumberFormat,formatType:r.normalizedNumberFormatType,config:r}),...n};if(r.numberFormatType)return{text:Ye({fieldOrDatumDef:s,field:"datum.value",format:r.numberFormat,formatType:r.numberFormatType,config:r}),...n}}if(ni(s)==="temporal"&&r.timeFormatType&&E(s)&&!s.timeUnit)return{text:Ye({fieldOrDatumDef:s,field:"datum.value",format:r.timeFormat,formatType:r.timeFormatType,config:r}),...n}}return n}function cE(e){return St.reduce((t,n)=>(e.component.scales[n]&&(t[n]=[hE(n,e)]),t),{})}const lE={bottom:"top",top:"bottom",left:"right",right:"left"};function uE(e){const{axes:t,resolve:n}=e.component,i={top:0,bottom:0,right:0,left:0};for(const r of e.children){r.parseAxesAndHeaders();for(const s of v(r.component.axes))n.axis[s]=Ma(e.component.resolve,s),n.axis[s]==="shared"&&(t[s]=fE(t[s],r.component.axes[s]),t[s]||(n.axis[s]="independent",delete t[s]))}for(const r of St){for(const s of e.children)if(s.component.axes[r]){if(n.axis[r]==="independent"){t[r]=(t[r]??[]).concat(s.component.axes[r]);for(const o of s.component.axes[r]){const{value:a,explicit:c}=o.getWithExplicit("orient");if(!R(a)){if(i[a]>0&&!c){const l=lE[a];i[a]>i[l]&&o.set("orient",l,!1)}i[a]++}}}delete s.component.axes[r]}if(n.axis[r]==="independent"&&t[r]&&t[r].length>1)for(const[s,o]of(t[r]||[]).entries())s>0&&o.get("grid")&&!o.explicit.grid&&(o.implicit.grid=!1)}}function fE(e,t){if(e){if(e.length!==t.length)return;const n=e.length;for(let i=0;i<n;i++){const r=e[i],s=t[i];if(!!r!=!!s)return;if(r&&s){const o=r.getWithExplicit("orient"),a=s.getWithExplicit("orient");if(o.explicit&&a.explicit&&o.value!==a.value)return;e[i]=dE(r,s)}}}else return t.map(n=>n.clone());return e}function dE(e,t){for(const n of yp){const i=Ht(e.getWithExplicit(n),t.getWithExplicit(n),n,"axis",(r,s)=>{switch(n){case"title":return Ou(r,s);case"gridScale":return{explicit:r.explicit,value:fe(r.value,s.value)}}return ss(r,s,n,"axis")});e.setWithExplicit(n,i)}return e}function pE(e,t,n,i,r){if(t==="disable")return n!==void 0;switch(n=n||{},t){case"titleAngle":case"labelAngle":return e===(R(n.labelAngle)?n.labelAngle:zi(n.labelAngle));case"values":return!!n.values;case"encode":return!!n.encoding||!!n.labelAngle;case"title":if(e===Pd(i,r))return!0}return e===n[t]}const gE=new Set(["grid","translate","format","formatType","orient","labelExpr","tickCount","position","tickMinStep"]);function hE(e,t){var y,b;let n=t.axis(e);const i=new Ya,r=de(t.encoding[e]),{mark:s,config:o}=t,a=(n==null?void 0:n.orient)||((y=o[e==="x"?"axisX":"axisY"])==null?void 0:y.orient)||((b=o.axis)==null?void 0:b.orient)||tv(e),c=t.getScaleComponent(e).get("type"),l=Xx(e,c,a,t.config),u=n!==void 0?!n:ho("disable",o.style,n==null?void 0:n.style,l).configValue;if(i.set("disable",u,n!==void 0),u)return i;n=n||{};const f=Qx(r,n,e,o.style,l),d=df(n.formatType,r,c),p=ff(r,r.type,n.format,n.formatType,o,!0),g={fieldOrDatumDef:r,axis:n,channel:e,model:t,scaleType:c,orient:a,labelAngle:f,format:p,formatType:d,mark:s,config:o};for(const N of yp){const P=N in nl?nl[N](g):Oc(N)?n[N]:void 0,x=P!==void 0,_=pE(P,N,n,t,e);if(x&&_)i.set(N,P,_);else{const{configValue:w=void 0,configFrom:T=void 0}=Oc(N)&&N!=="values"?ho(N,o.style,n.style,l):{},W=w!==void 0;x&&!W?i.set(N,P,_):(T!=="vgAxisConfig"||gE.has(N)&&W||Qi(w)||R(w))&&i.set(N,w,!1)}}const h=n.encoding??{},m=Ff.reduce((N,P)=>{if(!i.hasAxisPart(P))return N;const x=Wd(h[P]??{},t),_=P==="labels"?aE(t,e,x):x;return _!==void 0&&!Q(_)&&(N[P]={update:_}),N},{});return Q(m)||i.set("encode",m,!!n.encoding||n.labelAngle!==void 0),i}function mE({encoding:e,size:t}){for(const n of St){const i=Ie(n);mt(t[i])&&Wt(e[n])&&(delete t[i],S(Pu(i)))}return t}function yE(e,t,n){const i=_e(e),r=K("orient",i,n);if(i.orient=SE(i.type,t,r),r!==void 0&&r!==i.orient&&S(zh(i.orient,r)),i.type==="bar"&&i.orient){const a=K("cornerRadiusEnd",i,n);if(a!==void 0){const c=i.orient==="horizontal"&&t.x2||i.orient==="vertical"&&t.y2?["cornerRadius"]:ny[i.orient];for(const l of c)i[l]=a;i.cornerRadiusEnd!==void 0&&delete i.cornerRadiusEnd}}return K("opacity",i,n)===void 0&&(i.opacity=xE(i.type,t)),K("cursor",i,n)===void 0&&(i.cursor=bE(i,t,n)),i}function bE(e,t,n){return t.href||e.href||K("href",e,n)?"pointer":e.cursor}function xE(e,t){if(q([Xr,sa,oa,aa],e)&&!ba(t))return .7}function vE(e,t,{graticule:n}){if(n)return!1;const i=At("filled",e,t),r=e.type;return fe(i,r!==Xr&&r!==Vr&&r!==br)}function SE(e,t,n){switch(e){case Xr:case oa:case aa:case sf:case Hm:case qm:return}const{x:i,y:r,x2:s,y2:o}=t;switch(e){case Hr:if(E(i)&&(ve(i.bin)||E(r)&&r.aggregate&&!i.aggregate))return"vertical";if(E(r)&&(ve(r.bin)||E(i)&&i.aggregate&&!r.aggregate))return"horizontal";if(o||s){if(n)return n;if(!s)return(E(i)&&i.type===$n&&!ne(i.bin)||vr(i))&&E(r)&&ve(r.bin)?"horizontal":"vertical";if(!o)return(E(r)&&r.type===$n&&!ne(r.bin)||vr(r))&&E(i)&&ve(i.bin)?"vertical":"horizontal"}case br:if(s&&!(E(i)&&ve(i.bin))&&o&&!(E(r)&&ve(r.bin)))return;case qr:if(o)return E(r)&&ve(r.bin)?"horizontal":"vertical";if(s)return E(i)&&ve(i.bin)?"vertical":"horizontal";if(e===br){if(i&&!r)return"vertical";if(r&&!i)return"horizontal"}case Vr:case sa:{const a=Tc(i),c=Tc(r);if(n)return n;if(a&&!c)return e!=="tick"?"horizontal":"vertical";if(!a&&c)return e!=="tick"?"vertical":"horizontal";if(a&&c)return"vertical";{const l=Ne(i)&&i.type===ei,u=Ne(r)&&r.type===ei;if(l&&!u)return"vertical";if(!l&&u)return"horizontal"}return}}return"vertical"}const EE={vgMark:"arc",encodeEntry:e=>({...Be(e,{align:"ignore",baseline:"ignore",color:"include",size:"ignore",orient:"ignore",theta:"ignore"}),...$e("x",e,{defaultPos:"mid"}),...$e("y",e,{defaultPos:"mid"}),...Vt(e,"radius"),...Vt(e,"theta")})},$E={vgMark:"area",encodeEntry:e=>({...Be(e,{align:"ignore",baseline:"ignore",color:"include",orient:"include",size:"ignore",theta:"ignore"}),...Fr("x",e,{defaultPos:"zeroOrMin",defaultPos2:"zeroOrMin",range:e.markDef.orient==="horizontal"}),...Fr("y",e,{defaultPos:"zeroOrMin",defaultPos2:"zeroOrMin",range:e.markDef.orient==="vertical"}),...La(e)})},wE={vgMark:"rect",encodeEntry:e=>({...Be(e,{align:"ignore",baseline:"ignore",color:"include",orient:"ignore",size:"ignore",theta:"ignore"}),...Vt(e,"x"),...Vt(e,"y")})},CE={vgMark:"shape",encodeEntry:e=>({...Be(e,{align:"ignore",baseline:"ignore",color:"include",size:"ignore",orient:"ignore",theta:"ignore"})}),postEncodingTransform:e=>{const{encoding:t}=e,n=t.shape;return[{type:"geoshape",projection:e.projectionName(),...n&&E(n)&&n.type===hi?{field:C(n,{expr:"datum"})}:{}}]}},NE={vgMark:"image",encodeEntry:e=>({...Be(e,{align:"ignore",baseline:"ignore",color:"ignore",orient:"ignore",size:"ignore",theta:"ignore"}),...Vt(e,"x"),...Vt(e,"y"),...Ra(e,"url")})},FE={vgMark:"line",encodeEntry:e=>({...Be(e,{align:"ignore",baseline:"ignore",color:"include",size:"ignore",orient:"ignore",theta:"ignore"}),...$e("x",e,{defaultPos:"mid"}),...$e("y",e,{defaultPos:"mid"}),...he("size",e,{vgChannel:"strokeWidth"}),...La(e)})},_E={vgMark:"trail",encodeEntry:e=>({...Be(e,{align:"ignore",baseline:"ignore",color:"include",size:"include",orient:"ignore",theta:"ignore"}),...$e("x",e,{defaultPos:"mid"}),...$e("y",e,{defaultPos:"mid"}),...he("size",e),...La(e)})};function Ka(e,t){const{config:n}=e;return{...Be(e,{align:"ignore",baseline:"ignore",color:"include",size:"include",orient:"ignore",theta:"ignore"}),...$e("x",e,{defaultPos:"mid"}),...$e("y",e,{defaultPos:"mid"}),...he("size",e),...he("angle",e),...TE(e,n,t)}}function TE(e,t,n){return n?{shape:{value:n}}:he("shape",e)}const kE={vgMark:"symbol",encodeEntry:e=>Ka(e)},OE={vgMark:"symbol",encodeEntry:e=>Ka(e,"circle")},AE={vgMark:"symbol",encodeEntry:e=>Ka(e,"square")},RE={vgMark:"rect",encodeEntry:e=>({...Be(e,{align:"ignore",baseline:"ignore",color:"include",orient:"ignore",size:"ignore",theta:"ignore"}),...Vt(e,"x"),...Vt(e,"y")})},IE={vgMark:"rule",encodeEntry:e=>{const{markDef:t}=e,n=t.orient;return!e.encoding.x&&!e.encoding.y&&!e.encoding.latitude&&!e.encoding.longitude?{}:{...Be(e,{align:"ignore",baseline:"ignore",color:"include",orient:"ignore",size:"ignore",theta:"ignore"}),...Fr("x",e,{defaultPos:n==="horizontal"?"zeroOrMax":"mid",defaultPos2:"zeroOrMin",range:n!=="vertical"}),...Fr("y",e,{defaultPos:n==="vertical"?"zeroOrMax":"mid",defaultPos2:"zeroOrMin",range:n!=="horizontal"}),...he("size",e,{vgChannel:"strokeWidth"})}}},LE={vgMark:"text",encodeEntry:e=>{const{config:t,encoding:n}=e;return{...Be(e,{align:"include",baseline:"include",color:"include",size:"ignore",orient:"ignore",theta:"include"}),...$e("x",e,{defaultPos:"mid"}),...$e("y",e,{defaultPos:"mid"}),...Ra(e),...he("size",e,{vgChannel:"fontSize"}),...he("angle",e),...Qc("align",PE(e.markDef,n,t)),...Qc("baseline",zE(e.markDef,n,t)),...$e("radius",e,{defaultPos:null}),...$e("theta",e,{defaultPos:null})}}};function PE(e,t,n){if(K("align",e,n)===void 0)return"center"}function zE(e,t,n){if(K("baseline",e,n)===void 0)return"middle"}const DE={vgMark:"rect",encodeEntry:e=>{const{config:t,markDef:n}=e,i=n.orient,r=i==="horizontal"?"width":"height",s=i==="horizontal"?"height":"width";return{...Be(e,{align:"ignore",baseline:"ignore",color:"include",orient:"ignore",size:"ignore",theta:"ignore"}),...$e("x",e,{defaultPos:"mid",vgChannel:"xc"}),...$e("y",e,{defaultPos:"mid",vgChannel:"yc"}),...he("size",e,{defaultValue:jE(e),vgChannel:r}),[s]:re(K("thickness",n,t))}}};function jE(e){const{config:t,markDef:n}=e,{orient:i}=n,r=i==="horizontal"?"width":"height",s=e.getScaleComponent(i==="horizontal"?"x":"y"),o=K("size",n,t,{vgChannel:r})??t.tick.bandSize;if(o!==void 0)return o;{const a=s?s.get("range"):void 0;return a&&nn(a)&&ue(a.step)?a.step*3/4:Cr(t.view,r)*3/4}}const ar={arc:EE,area:$E,bar:wE,circle:OE,geoshape:CE,image:NE,line:FE,point:kE,rect:RE,rule:IE,square:AE,text:LE,tick:DE,trail:_E};function ME(e){if(q([Vr,qr,Vm],e.mark)){const t=Af(e.mark,e.encoding);if(t.length>0)return UE(e,t)}else if(e.mark===Hr){const t=to.some(n=>K(n,e.markDef,e.config));if(e.stack&&!e.fieldDef("size")&&t)return WE(e)}return Ja(e)}const bl="faceted_path_";function UE(e,t){return[{name:e.getName("pathgroup"),type:"group",from:{facet:{name:bl+e.requestDataName(ee.Main),data:e.requestDataName(ee.Main),groupby:t}},encode:{update:{width:{field:{group:"width"}},height:{field:{group:"height"}}}},marks:Ja(e,{fromPrefix:bl})}]}const xl="stack_group_";function WE(e){var l;const[t]=Ja(e,{fromPrefix:xl}),n=e.scaleName(e.stack.fieldChannel),i=(u={})=>e.vgField(e.stack.fieldChannel,u),r=(u,f)=>{const d=[i({prefix:"min",suffix:"start",expr:f}),i({prefix:"max",suffix:"start",expr:f}),i({prefix:"min",suffix:"end",expr:f}),i({prefix:"max",suffix:"end",expr:f})];return`${u}(${d.map(p=>`scale('${n}',${p})`).join(",")})`};let s,o;e.stack.fieldChannel==="x"?(s={...Kn(t.encode.update,["y","yc","y2","height",...to]),x:{signal:r("min","datum")},x2:{signal:r("max","datum")},clip:{value:!0}},o={x:{field:{group:"x"},mult:-1},height:{field:{group:"height"}}},t.encode.update={...Te(t.encode.update,["y","yc","y2"]),height:{field:{group:"height"}}}):(s={...Kn(t.encode.update,["x","xc","x2","width"]),y:{signal:r("min","datum")},y2:{signal:r("max","datum")},clip:{value:!0}},o={y:{field:{group:"y"},mult:-1},width:{field:{group:"width"}}},t.encode.update={...Te(t.encode.update,["x","xc","x2"]),width:{field:{group:"width"}}});for(const u of to){const f=At(u,e.markDef,e.config);t.encode.update[u]?(s[u]=t.encode.update[u],delete t.encode.update[u]):f&&(s[u]=re(f)),f&&(t.encode.update[u]={value:0})}const a=[];if(((l=e.stack.groupbyChannels)==null?void 0:l.length)>0)for(const u of e.stack.groupbyChannels){const f=e.fieldDef(u),d=C(f);d&&a.push(d),(f!=null&&f.bin||f!=null&&f.timeUnit)&&a.push(C(f,{binSuffix:"end"}))}return s=["stroke","strokeWidth","strokeJoin","strokeCap","strokeDash","strokeDashOffset","strokeMiterLimit","strokeOpacity"].reduce((u,f)=>{if(t.encode.update[f])return{...u,[f]:t.encode.update[f]};{const d=At(f,e.markDef,e.config);return d!==void 0?{...u,[f]:re(d)}:u}},s),s.stroke&&(s.strokeForeground={value:!0},s.strokeOffset={value:0}),[{type:"group",from:{facet:{data:e.requestDataName(ee.Main),name:xl+e.requestDataName(ee.Main),groupby:a,aggregate:{fields:[i({suffix:"start"}),i({suffix:"start"}),i({suffix:"end"}),i({suffix:"end"})],ops:["min","max","min","max"]}}},encode:{update:s},marks:[{type:"group",encode:{update:o},marks:[t]}]}]}function BE(e){var a;const{encoding:t,stack:n,mark:i,markDef:r,config:s}=e,o=t.order;if(!(!O(o)&&Qe(o)&&Zs(o.value)||!o&&Zs(K("order",r,s)))){if((O(o)||E(o))&&!n)return _u(o,{expr:"datum"});if(rn(i)){const c=r.orient==="horizontal"?"y":"x",l=t[c];if(E(l)){const u=l.sort;if(O(u))return{field:C(l,{prefix:c,suffix:"sort_index",expr:"datum"})};if(ft(u))return{field:C({aggregate:ba(e.encoding)?u.op:void 0,field:u.field},{expr:"datum"})};if(gf(u)){const f=e.fieldDef(u.encoding);return{field:C(f,{expr:"datum"}),order:u.order}}else return u===null?void 0:{field:C(l,{binSuffix:(a=e.stack)!=null&&a.impute?"mid":void 0,expr:"datum"})}}return}}}function Ja(e,t={fromPrefix:""}){const{mark:n,markDef:i,encoding:r,config:s}=e,o=fe(i.clip,GE(e),qE(e)),a=Nu(i),c=r.key,l=BE(e),u=HE(e),f=K("aria",i,s),d=ar[n].postEncodingTransform?ar[n].postEncodingTransform(e):null;return[{name:e.getName("marks"),type:ar[n].vgMark,...o?{clip:!0}:{},...a?{style:a}:{},...c?{key:c.field}:{},...l?{sort:l}:{},...u||{},...f===!1?{aria:f}:{},from:{data:t.fromPrefix+e.requestDataName(ee.Main)},encode:{update:ar[n].encodeEntry(e)},...d?{transform:d}:{}}]}function GE(e){const t=e.getScaleComponent("x"),n=e.getScaleComponent("y");return t!=null&&t.get("selectionExtent")||n!=null&&n.get("selectionExtent")?!0:void 0}function qE(e){const t=e.component.projection;return t&&!t.isFit?!0:void 0}function HE(e){if(!e.component.selection)return null;const t=v(e.component.selection).length;let n=t,i=e.parent;for(;i&&n===0;)n=v(i.component.selection).length,i=i.parent;return n?{interactive:t>0||e.mark==="geoshape"||!!e.encoding.tooltip}:null}class bp extends pp{constructor(t,n,i,r={},s){super(t,"unit",n,i,s,void 0,Rc(t)?t.view:void 0),this.specifiedScales={},this.specifiedAxes={},this.specifiedLegends={},this.specifiedProjection={},this.selection=[],this.children=[];const o=gt(t.mark)?{...t.mark}:{type:t.mark},a=o.type;o.filled===void 0&&(o.filled=vE(o,s,{graticule:t.data&&Oa(t.data)}));const c=this.encoding=Ry(t.encoding||{},a,o.filled,s);this.markDef=yE(o,c,s),this.size=mE({encoding:c,size:Rc(t)?{...r,...t.width?{width:t.width}:{},...t.height?{height:t.height}:{}}:r}),this.stack=Qf(this.markDef,c),this.specifiedScales=this.initScales(a,c),this.specifiedAxes=this.initAxes(c),this.specifiedLegends=this.initLegends(c),this.specifiedProjection=t.projection,this.selection=(t.params??[]).filter(l=>Na(l))}get hasProjection(){const{encoding:t}=this,n=this.mark===of,i=t&&Og.some(r=>M(t[r]));return n||i}scaleDomain(t){const n=this.specifiedScales[t];return n?n.domain:void 0}axis(t){return this.specifiedAxes[t]}legend(t){return this.specifiedLegends[t]}initScales(t,n){return Ur.reduce((i,r)=>{const s=de(n[r]);return s&&(i[r]=this.initScale(s.scale??{})),i},{})}initScale(t){const{domain:n,range:i}=t,r=_e(t);return O(n)&&(r.domain=n.map(Pe)),O(i)&&(r.range=i.map(Pe)),r}initAxes(t){return St.reduce((n,i)=>{const r=t[i];if(M(r)||i===oe&&M(t.x2)||i===ye&&M(t.y2)){const s=M(r)?r.axis:void 0;n[i]=s&&this.initAxis({...s})}return n},{})}initAxis(t){const n=v(t),i={};for(const r of n){const s=t[r];i[r]=Qi(s)?Cu(s):Pe(s)}return i}initLegends(t){return Mg.reduce((n,i)=>{const r=de(t[i]);if(r&&Wg(i)){const s=r.legend;n[i]=s&&_e(s)}return n},{})}parseData(){this.component.data=vs(this)}parseLayoutSize(){ZS(this)}parseSelections(){this.component.selection=Gx(this,this.selection)}parseMarkGroup(){this.component.mark=ME(this)}parseAxesAndHeaders(){this.component.axes=cE(this)}assembleSelectionTopLevelSignals(t){return dx(this,t)}assembleSignals(){return[...Rd(this),...ux(this,[])]}assembleSelectionData(t){return px(this,t)}assembleLayout(){return null}assembleLayoutSignals(){return ja(this)}assembleMarks(){let t=this.component.mark??[];return(!this.parent||!vi(this.parent))&&(t=fd(this,t)),t.map(this.correctDataNames)}assembleGroupStyle(){const{style:t}=this.view||{};return t!==void 0?t:this.encoding.x||this.encoding.y?"cell":"view"}getMapping(){return this.encoding}get mark(){return this.markDef.type}channelHasField(t){return mn(this.encoding,t)}fieldDef(t){const n=this.encoding[t];return ht(n)}typedFieldDef(t){const n=this.fieldDef(t);return Ne(n)?n:null}}class Qa extends Va{constructor(t,n,i,r,s){super(t,"layer",n,i,s,t.resolve,t.view);const o={...r,...t.width?{width:t.width}:{},...t.height?{height:t.height}:{}};this.children=t.layer.map((a,c)=>{if(rs(a))return new Qa(a,this,this.getName(`layer_${c}`),o,s);if(Dt(a))return new bp(a,this,this.getName(`layer_${c}`),o,s);throw new Error(Go(a))})}parseData(){this.component.data=vs(this);for(const t of this.children)t.parseData()}parseLayoutSize(){JS(this)}parseSelections(){this.component.selection={};for(const t of this.children){t.parseSelections();for(const n of v(t.component.selection))this.component.selection[n]=t.component.selection[n]}}parseMarkGroup(){for(const t of this.children)t.parseMarkGroup()}parseAxesAndHeaders(){uE(this)}assembleSelectionTopLevelSignals(t){return this.children.reduce((n,i)=>i.assembleSelectionTopLevelSignals(n),t)}assembleSignals(){return this.children.reduce((t,n)=>t.concat(n.assembleSignals()),Rd(this))}assembleLayoutSignals(){return this.children.reduce((t,n)=>t.concat(n.assembleLayoutSignals()),ja(this))}assembleSelectionData(t){return this.children.reduce((n,i)=>i.assembleSelectionData(n),t)}assembleGroupStyle(){const t=new Set;for(const i of this.children)for(const r of ce(i.assembleGroupStyle()))t.add(r);const n=Array.from(t);return n.length>1?n:n.length===1?n[0]:void 0}assembleTitle(){let t=super.assembleTitle();if(t)return t;for(const n of this.children)if(t=n.assembleTitle(),t)return t}assembleLayout(){return null}assembleMarks(){return gx(this,this.children.flatMap(t=>t.assembleMarks()))}assembleLegends(){return this.children.reduce((t,n)=>t.concat(n.assembleLegends()),Kd(this))}}function Za(e,t,n,i,r){if(Kr(e))return new Ii(e,t,n,r);if(rs(e))return new Qa(e,t,n,i,r);if(Dt(e))return new bp(e,t,n,i,r);if(ib(e))return new rE(e,t,n,r);throw new Error(Go(e))}function VE(e,t={}){t.logger&&cm(t.logger),t.fieldTitle&&wf(t.fieldTitle);try{const n=Jf(Rr(t.config,e.config)),i=sd(e,n),r=Za(i,null,"",void 0,n);return r.parse(),dS(r.component.data,r),{spec:YE(r,XE(e,i.autosize,n,r),e.datasets,e.usermeta),normalized:i}}finally{t.logger&&lm(),t.fieldTitle&&wy()}}function XE(e,t,n,i){const r=i.component.layoutSize.get("width"),s=i.component.layoutSize.get("height");if(t===void 0?(t={type:"pad"},i.hasAxisOrientSignalRef()&&(t.resize=!0)):z(t)&&(t={type:t}),r&&s&&ox(t.type)){if(r==="step"&&s==="step")S(yc()),t.type="pad";else if(r==="step"||s==="step"){const o=r==="step"?"width":"height";S(yc(Mr(o)));const a=o==="width"?"height":"width";t.type=ax(a)}}return{...v(t).length===1&&t.type?t.type==="pad"?{}:{autosize:t.type}:{autosize:t},...qc(n,!1),...qc(e,!0)}}function YE(e,t,n={},i){const r=e.config?yb(e.config):void 0,s=[].concat(e.assembleSelectionData([]),VS(e.component.data,n)),o=e.assembleProjections(),a=e.assembleTitle(),c=e.assembleGroupStyle(),l=e.assembleGroupEncodeEntry(!0);let u=e.assembleLayoutSignals();u=u.filter(p=>(p.name==="width"||p.name==="height")&&p.value!==void 0?(t[p.name]=+p.value,!1):!0);const{params:f,...d}=t;return{$schema:"https://vega.github.io/schema/vega/v5.json",...e.description?{description:e.description}:{},...d,...a?{title:a}:{},...c?{style:c}:{},...l?{encode:{update:l}}:{},data:s,...o.length>0?{projections:o}:{},...e.assembleGroup([...u,...e.assembleSelectionTopLevelSignals([]),...Vf(f)]),...r?{config:r}:{},...i?{usermeta:i}:{}}}const KE=vg.version,JE=Object.freeze(Object.defineProperty({__proto__:null,accessPathDepth:Jn,accessPathWithDatum:Io,compile:VE,contains:q,deepEqual:lt,deleteNestedProperty:hr,duplicate:j,entries:Gt,every:Oo,fieldIntersection:Ro,flatAccessWithDatum:ru,getFirstDefined:fe,hasIntersection:Ao,hash:G,internalField:au,isBoolean:Pi,isEmpty:Q,isEqual:Fg,isInternalField:cu,isNullOrFalse:Zs,isNumeric:Ir,keys:v,logicalExpr:Ai,mergeDeep:nu,never:tu,normalize:sd,normalizeAngle:zi,omit:Te,pick:Kn,prefixGenerator:eo,removePathFromField:Lo,replaceAll:vn,replacePathInField:Me,resetIdCounter:Tg,setEqual:iu,some:xn,stringify:te,titleCase:Bi,unique:ut,uniqueId:ou,vals:Se,varName:se,version:KE},Symbol.toStringTag,{value:"Module"}));var QE="vega-themes",ZE="2.14.0",e1="Themes for stylized Vega and Vega-Lite visualizations.",t1=["vega","vega-lite","themes","style"],n1="BSD-3-Clause",i1={name:"UW Interactive Data Lab",url:"https://idl.cs.washington.edu"},r1=[{name:"Emily Gu",url:"https://github.com/emilygu"},{name:"Arvind Satyanarayan",url:"http://arvindsatya.com"},{name:"Jeffrey Heer",url:"https://idl.cs.washington.edu"},{name:"Dominik Moritz",url:"https://www.domoritz.de"}],s1="build/vega-themes.js",o1="build/vega-themes.module.js",a1="build/vega-themes.min.js",c1="build/vega-themes.min.js",l1="build/vega-themes.module.d.ts",u1={type:"git",url:"https://github.com/vega/vega-themes.git"},f1=["src","build"],d1={prebuild:"yarn clean",build:"rollup -c",clean:"rimraf build && rimraf examples/build","copy:data":"rsync -r node_modules/vega-datasets/data/* examples/data","copy:build":"rsync -r build/* examples/build","deploy:gh":"yarn build && mkdir -p examples/build && rsync -r build/* examples/build && gh-pages -d examples",preversion:"yarn lint",serve:"browser-sync start -s -f build examples --serveStatic examples",start:"yarn build && concurrently --kill-others -n Server,Rollup 'yarn serve' 'rollup -c -w'",format:"eslint . --fix",lint:"eslint .",release:"release-it"},p1={"@babel/core":"^7.22.9","@babel/plugin-proposal-async-generator-functions":"^7.20.7","@babel/plugin-proposal-json-strings":"^7.18.6","@babel/plugin-proposal-object-rest-spread":"^7.20.7","@babel/plugin-proposal-optional-catch-binding":"^7.18.6","@babel/plugin-transform-runtime":"^7.22.9","@babel/preset-env":"^7.22.9","@babel/preset-typescript":"^7.22.5","@release-it/conventional-changelog":"^7.0.0","@rollup/plugin-json":"^6.0.0","@rollup/plugin-node-resolve":"^15.1.0","@rollup/plugin-terser":"^0.4.3","@typescript-eslint/eslint-plugin":"^6.0.0","@typescript-eslint/parser":"^6.0.0","browser-sync":"^2.29.3",concurrently:"^8.2.0",eslint:"^8.45.0","eslint-config-prettier":"^8.8.0","eslint-plugin-prettier":"^5.0.0","gh-pages":"^5.0.0",prettier:"^3.0.0","release-it":"^16.1.0",rollup:"^3.26.2","rollup-plugin-bundle-size":"^1.0.3","rollup-plugin-ts":"^3.2.0",typescript:"^5.1.6",vega:"^5.25.0","vega-lite":"^5.9.3"},g1={vega:"*","vega-lite":"*"},h1={},m1={name:QE,version:ZE,description:e1,keywords:t1,license:n1,author:i1,contributors:r1,main:s1,module:o1,unpkg:a1,jsdelivr:c1,types:l1,repository:u1,files:f1,scripts:d1,devDependencies:p1,peerDependencies:g1,dependencies:h1};const Mn="#fff",vl="#888",y1={background:"#333",view:{stroke:vl},title:{color:Mn,subtitleColor:Mn},style:{"guide-label":{fill:Mn},"guide-title":{fill:Mn}},axis:{domainColor:Mn,gridColor:vl,tickColor:Mn}},on="#4572a7",b1={background:"#fff",arc:{fill:on},area:{fill:on},line:{stroke:on,strokeWidth:2},path:{stroke:on},rect:{fill:on},shape:{stroke:on},symbol:{fill:on,strokeWidth:1.5,size:50},axis:{bandPosition:.5,grid:!0,gridColor:"#000000",gridOpacity:1,gridWidth:.5,labelPadding:10,tickSize:5,tickWidth:.5},axisBand:{grid:!1,tickExtra:!0},legend:{labelBaseline:"middle",labelFontSize:11,symbolSize:50,symbolType:"square"},range:{category:["#4572a7","#aa4643","#8aa453","#71598e","#4598ae","#d98445","#94aace","#d09393","#b9cc98","#a99cbc"]}},an="#30a2da",Ds="#cbcbcb",x1="#999",v1="#333",Sl="#f0f0f0",El="#333",S1={arc:{fill:an},area:{fill:an},axis:{domainColor:Ds,grid:!0,gridColor:Ds,gridWidth:1,labelColor:x1,labelFontSize:10,titleColor:v1,tickColor:Ds,tickSize:10,titleFontSize:14,titlePadding:10,labelPadding:4},axisBand:{grid:!1},background:Sl,group:{fill:Sl},legend:{labelColor:El,labelFontSize:11,padding:1,symbolSize:30,symbolType:"square",titleColor:El,titleFontSize:14,titlePadding:10},line:{stroke:an,strokeWidth:2},path:{stroke:an,strokeWidth:.5},rect:{fill:an},range:{category:["#30a2da","#fc4f30","#e5ae38","#6d904f","#8b8b8b","#b96db8","#ff9e27","#56cc60","#52d2ca","#52689e","#545454","#9fe4f8"],diverging:["#cc0020","#e77866","#f6e7e1","#d6e8ed","#91bfd9","#1d78b5"],heatmap:["#d6e8ed","#cee0e5","#91bfd9","#549cc6","#1d78b5"]},point:{filled:!0,shape:"circle"},shape:{stroke:an},bar:{binSpacing:2,fill:an,stroke:null},title:{anchor:"start",fontSize:24,fontWeight:600,offset:20}},cn="#000",E1={group:{fill:"#e5e5e5"},arc:{fill:cn},area:{fill:cn},line:{stroke:cn},path:{stroke:cn},rect:{fill:cn},shape:{stroke:cn},symbol:{fill:cn,size:40},axis:{domain:!1,grid:!0,gridColor:"#FFFFFF",gridOpacity:1,labelColor:"#7F7F7F",labelPadding:4,tickColor:"#7F7F7F",tickSize:5.67,titleFontSize:16,titleFontWeight:"normal"},legend:{labelBaseline:"middle",labelFontSize:11,symbolSize:40},range:{category:["#000000","#7F7F7F","#1A1A1A","#999999","#333333","#B0B0B0","#4D4D4D","#C9C9C9","#666666","#DCDCDC"]}},$1=22,w1="normal",$l="Benton Gothic, sans-serif",wl=11.5,C1="normal",ln="#82c6df",js="Benton Gothic Bold, sans-serif",Cl="normal",Nl=13,$i={"category-6":["#ec8431","#829eb1","#c89d29","#3580b1","#adc839","#ab7fb4"],"fire-7":["#fbf2c7","#f9e39c","#f8d36e","#f4bb6a","#e68a4f","#d15a40","#ab4232"],"fireandice-6":["#e68a4f","#f4bb6a","#f9e39c","#dadfe2","#a6b7c6","#849eae"],"ice-7":["#edefee","#dadfe2","#c4ccd2","#a6b7c6","#849eae","#607785","#47525d"]},N1={background:"#ffffff",title:{anchor:"start",color:"#000000",font:js,fontSize:$1,fontWeight:w1},arc:{fill:ln},area:{fill:ln},line:{stroke:ln,strokeWidth:2},path:{stroke:ln},rect:{fill:ln},shape:{stroke:ln},symbol:{fill:ln,size:30},axis:{labelFont:$l,labelFontSize:wl,labelFontWeight:C1,titleFont:js,titleFontSize:Nl,titleFontWeight:Cl},axisX:{labelAngle:0,labelPadding:4,tickSize:3},axisY:{labelBaseline:"middle",maxExtent:45,minExtent:45,tickSize:2,titleAlign:"left",titleAngle:0,titleX:-45,titleY:-11},legend:{labelFont:$l,labelFontSize:wl,symbolType:"square",titleFont:js,titleFontSize:Nl,titleFontWeight:Cl},range:{category:$i["category-6"],diverging:$i["fireandice-6"],heatmap:$i["fire-7"],ordinal:$i["fire-7"],ramp:$i["fire-7"]}},un="#ab5787",cr="#979797",F1={background:"#f9f9f9",arc:{fill:un},area:{fill:un},line:{stroke:un},path:{stroke:un},rect:{fill:un},shape:{stroke:un},symbol:{fill:un,size:30},axis:{domainColor:cr,domainWidth:.5,gridWidth:.2,labelColor:cr,tickColor:cr,tickWidth:.2,titleColor:cr},axisBand:{grid:!1},axisX:{grid:!0,tickSize:10},axisY:{domain:!1,grid:!0,tickSize:0},legend:{labelFontSize:11,padding:1,symbolSize:30,symbolType:"square"},range:{category:["#ab5787","#51b2e5","#703c5c","#168dd9","#d190b6","#00609f","#d365ba","#154866","#666666","#c4c4c4"]}},fn="#3e5c69",_1={background:"#fff",arc:{fill:fn},area:{fill:fn},line:{stroke:fn},path:{stroke:fn},rect:{fill:fn},shape:{stroke:fn},symbol:{fill:fn},axis:{domainWidth:.5,grid:!0,labelPadding:2,tickSize:5,tickWidth:.5,titleFontWeight:"normal"},axisBand:{grid:!1},axisX:{gridWidth:.2},axisY:{gridDash:[3],gridWidth:.4},legend:{labelFontSize:11,padding:1,symbolType:"square"},range:{category:["#3e5c69","#6793a6","#182429","#0570b0","#3690c0","#74a9cf","#a6bddb","#e2ddf2"]}},De="#1696d2",Fl="#000000",T1="#FFFFFF",lr="Lato",Ms="Lato",k1="Lato",O1="#DEDDDD",A1=18,wi={"main-colors":["#1696d2","#d2d2d2","#000000","#fdbf11","#ec008b","#55b748","#5c5859","#db2b27"],"shades-blue":["#CFE8F3","#A2D4EC","#73BFE2","#46ABDB","#1696D2","#12719E","#0A4C6A","#062635"],"shades-gray":["#F5F5F5","#ECECEC","#E3E3E3","#DCDBDB","#D2D2D2","#9D9D9D","#696969","#353535"],"shades-yellow":["#FFF2CF","#FCE39E","#FDD870","#FCCB41","#FDBF11","#E88E2D","#CA5800","#843215"],"shades-magenta":["#F5CBDF","#EB99C2","#E46AA7","#E54096","#EC008B","#AF1F6B","#761548","#351123"],"shades-green":["#DCEDD9","#BCDEB4","#98CF90","#78C26D","#55B748","#408941","#2C5C2D","#1A2E19"],"shades-black":["#D5D5D4","#ADABAC","#848081","#5C5859","#332D2F","#262223","#1A1717","#0E0C0D"],"shades-red":["#F8D5D4","#F1AAA9","#E9807D","#E25552","#DB2B27","#A4201D","#6E1614","#370B0A"],"one-group":["#1696d2","#000000"],"two-groups-cat-1":["#1696d2","#000000"],"two-groups-cat-2":["#1696d2","#fdbf11"],"two-groups-cat-3":["#1696d2","#db2b27"],"two-groups-seq":["#a2d4ec","#1696d2"],"three-groups-cat":["#1696d2","#fdbf11","#000000"],"three-groups-seq":["#a2d4ec","#1696d2","#0a4c6a"],"four-groups-cat-1":["#000000","#d2d2d2","#fdbf11","#1696d2"],"four-groups-cat-2":["#1696d2","#ec0008b","#fdbf11","#5c5859"],"four-groups-seq":["#cfe8f3","#73bf42","#1696d2","#0a4c6a"],"five-groups-cat-1":["#1696d2","#fdbf11","#d2d2d2","#ec008b","#000000"],"five-groups-cat-2":["#1696d2","#0a4c6a","#d2d2d2","#fdbf11","#332d2f"],"five-groups-seq":["#cfe8f3","#73bf42","#1696d2","#0a4c6a","#000000"],"six-groups-cat-1":["#1696d2","#ec008b","#fdbf11","#000000","#d2d2d2","#55b748"],"six-groups-cat-2":["#1696d2","#d2d2d2","#ec008b","#fdbf11","#332d2f","#0a4c6a"],"six-groups-seq":["#cfe8f3","#a2d4ec","#73bfe2","#46abdb","#1696d2","#12719e"],"diverging-colors":["#ca5800","#fdbf11","#fdd870","#fff2cf","#cfe8f3","#73bfe2","#1696d2","#0a4c6a"]},R1={background:T1,title:{anchor:"start",fontSize:A1,font:lr},axisX:{domain:!0,domainColor:Fl,domainWidth:1,grid:!1,labelFontSize:12,labelFont:Ms,labelAngle:0,tickColor:Fl,tickSize:5,titleFontSize:12,titlePadding:10,titleFont:lr},axisY:{domain:!1,domainWidth:1,grid:!0,gridColor:O1,gridWidth:1,labelFontSize:12,labelFont:Ms,labelPadding:8,ticks:!1,titleFontSize:12,titlePadding:10,titleFont:lr,titleAngle:0,titleY:-10,titleX:18},legend:{labelFontSize:12,labelFont:Ms,symbolSize:100,titleFontSize:12,titlePadding:10,titleFont:lr,orient:"right",offset:10},view:{stroke:"transparent"},range:{category:wi["six-groups-cat-1"],diverging:wi["diverging-colors"],heatmap:wi["diverging-colors"],ordinal:wi["six-groups-seq"],ramp:wi["shades-blue"]},area:{fill:De},rect:{fill:De},line:{color:De,stroke:De,strokeWidth:5},trail:{color:De,stroke:De,strokeWidth:0,size:1},path:{stroke:De,strokeWidth:.5},point:{filled:!0},text:{font:k1,color:De,fontSize:11,align:"center",fontWeight:400,size:11},style:{bar:{fill:De,stroke:null}},arc:{fill:De},shape:{stroke:De},symbol:{fill:De,size:30}},dn="#3366CC",_l="#ccc",ur="Arial, sans-serif",I1={arc:{fill:dn},area:{fill:dn},path:{stroke:dn},rect:{fill:dn},shape:{stroke:dn},symbol:{stroke:dn},circle:{fill:dn},background:"#fff",padding:{top:10,right:10,bottom:10,left:10},style:{"guide-label":{font:ur,fontSize:12},"guide-title":{font:ur,fontSize:12},"group-title":{font:ur,fontSize:12}},title:{font:ur,fontSize:14,fontWeight:"bold",dy:-3,anchor:"start"},axis:{gridColor:_l,tickColor:_l,domain:!1,grid:!0},range:{category:["#4285F4","#DB4437","#F4B400","#0F9D58","#AB47BC","#00ACC1","#FF7043","#9E9D24","#5C6BC0","#F06292","#00796B","#C2185B"],heatmap:["#c6dafc","#5e97f6","#2a56c6"]}},ec=e=>e*(1/3+1),Tl=ec(9),kl=ec(10),Ol=ec(12),Ci="Segoe UI",Al="wf_standard-font, helvetica, arial, sans-serif",Rl="#252423",Ni="#605E5C",Il="transparent",L1="#C8C6C4",qe="#118DFF",P1="#12239E",z1="#E66C37",D1="#6B007B",j1="#E044A7",M1="#744EC2",U1="#D9B300",W1="#D64550",xp=qe,vp="#DEEFFF",Ll=[vp,xp],B1=[vp,"#c7e4ff","#b0d9ff","#9aceff","#83c3ff","#6cb9ff","#55aeff","#3fa3ff","#2898ff",xp],G1={view:{stroke:Il},background:Il,font:Ci,header:{titleFont:Al,titleFontSize:Ol,titleColor:Rl,labelFont:Ci,labelFontSize:kl,labelColor:Ni},axis:{ticks:!1,grid:!1,domain:!1,labelColor:Ni,labelFontSize:Tl,titleFont:Al,titleColor:Rl,titleFontSize:Ol,titleFontWeight:"normal"},axisQuantitative:{tickCount:3,grid:!0,gridColor:L1,gridDash:[1,5],labelFlush:!1},axisBand:{tickExtra:!0},axisX:{labelPadding:5},axisY:{labelPadding:10},bar:{fill:qe},line:{stroke:qe,strokeWidth:3,strokeCap:"round",strokeJoin:"round"},text:{font:Ci,fontSize:Tl,fill:Ni},arc:{fill:qe},area:{fill:qe,line:!0,opacity:.6},path:{stroke:qe},rect:{fill:qe},point:{fill:qe,filled:!0,size:75},shape:{stroke:qe},symbol:{fill:qe,strokeWidth:1.5,size:50},legend:{titleFont:Ci,titleFontWeight:"bold",titleColor:Ni,labelFont:Ci,labelFontSize:kl,labelColor:Ni,symbolType:"circle",symbolSize:75},range:{category:[qe,P1,z1,D1,j1,M1,U1,W1],diverging:Ll,heatmap:Ll,ordinal:B1}},Us='IBM Plex Sans,system-ui,-apple-system,BlinkMacSystemFont,".sfnstext-regular",sans-serif',Pl=400,q1=["#8a3ffc","#33b1ff","#007d79","#ff7eb6","#fa4d56","#fff1f1","#6fdc8c","#4589ff","#d12771","#d2a106","#08bdba","#bae6ff","#ba4e00","#d4bbff"],H1=["#6929c4","#1192e8","#005d5d","#9f1853","#fa4d56","#570408","#198038","#002d9c","#ee538b","#b28600","#009d9a","#012749","#8a3800","#a56eff"];function Ss({type:e,background:t}){const n=e==="dark"?"#161616":"#ffffff",i=e==="dark"?"#f4f4f4":"#161616",r=e==="dark"?q1:H1,s=e==="dark"?"#d4bbff":"#6929c4";return{background:t,arc:{fill:s},area:{fill:s},path:{stroke:s},rect:{fill:s},shape:{stroke:s},symbol:{stroke:s},circle:{fill:s},view:{fill:n,stroke:n},group:{fill:n},title:{color:i,anchor:"start",dy:-15,fontSize:16,font:Us,fontWeight:600},axis:{labelColor:i,labelFontSize:12,grid:!0,gridColor:"#525252",titleColor:i,labelAngle:0},style:{"guide-label":{font:Us,fill:i,fontWeight:Pl},"guide-title":{font:Us,fill:i,fontWeight:Pl}},range:{category:r,diverging:["#750e13","#a2191f","#da1e28","#fa4d56","#ff8389","#ffb3b8","#ffd7d9","#fff1f1","#e5f6ff","#bae6ff","#82cfff","#33b1ff","#1192e8","#0072c3","#00539a","#003a6d"],heatmap:["#f6f2ff","#e8daff","#d4bbff","#be95ff","#a56eff","#8a3ffc","#6929c4","#491d8b","#31135e","#1c0f30"]}}}const V1=Ss({type:"light",background:"#ffffff"}),X1=Ss({type:"light",background:"#f4f4f4"}),Y1=Ss({type:"dark",background:"#262626"}),K1=Ss({type:"dark",background:"#161616"}),J1=m1.version,Q1=Object.freeze(Object.defineProperty({__proto__:null,carbong10:X1,carbong100:K1,carbong90:Y1,carbonwhite:V1,dark:y1,excel:b1,fivethirtyeight:S1,ggplot2:E1,googlecharts:I1,latimes:N1,powerbi:G1,quartz:F1,urbaninstitute:R1,version:J1,vox:_1},Symbol.toStringTag,{value:"Module"}));var Ws={};function Z1(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Bs,zl;function e$(){return zl||(zl=1,Bs=function(e){e.prototype[Symbol.iterator]=function*(){for(let t=this.head;t;t=t.next)yield t.value}}),Bs}var t$=V;V.Node=Tn;V.create=V;function V(e){var t=this;if(t instanceof V||(t=new V),t.tail=null,t.head=null,t.length=0,e&&typeof e.forEach=="function")e.forEach(function(r){t.push(r)});else if(arguments.length>0)for(var n=0,i=arguments.length;n<i;n++)t.push(arguments[n]);return t}V.prototype.removeNode=function(e){if(e.list!==this)throw new Error("removing node which does not belong to this list");var t=e.next,n=e.prev;return t&&(t.prev=n),n&&(n.next=t),e===this.head&&(this.head=t),e===this.tail&&(this.tail=n),e.list.length--,e.next=null,e.prev=null,e.list=null,t};V.prototype.unshiftNode=function(e){if(e!==this.head){e.list&&e.list.removeNode(e);var t=this.head;e.list=this,e.next=t,t&&(t.prev=e),this.head=e,this.tail||(this.tail=e),this.length++}};V.prototype.pushNode=function(e){if(e!==this.tail){e.list&&e.list.removeNode(e);var t=this.tail;e.list=this,e.prev=t,t&&(t.next=e),this.tail=e,this.head||(this.head=e),this.length++}};V.prototype.push=function(){for(var e=0,t=arguments.length;e<t;e++)i$(this,arguments[e]);return this.length};V.prototype.unshift=function(){for(var e=0,t=arguments.length;e<t;e++)r$(this,arguments[e]);return this.length};V.prototype.pop=function(){if(this.tail){var e=this.tail.value;return this.tail=this.tail.prev,this.tail?this.tail.next=null:this.head=null,this.length--,e}};V.prototype.shift=function(){if(this.head){var e=this.head.value;return this.head=this.head.next,this.head?this.head.prev=null:this.tail=null,this.length--,e}};V.prototype.forEach=function(e,t){t=t||this;for(var n=this.head,i=0;n!==null;i++)e.call(t,n.value,i,this),n=n.next};V.prototype.forEachReverse=function(e,t){t=t||this;for(var n=this.tail,i=this.length-1;n!==null;i--)e.call(t,n.value,i,this),n=n.prev};V.prototype.get=function(e){for(var t=0,n=this.head;n!==null&&t<e;t++)n=n.next;if(t===e&&n!==null)return n.value};V.prototype.getReverse=function(e){for(var t=0,n=this.tail;n!==null&&t<e;t++)n=n.prev;if(t===e&&n!==null)return n.value};V.prototype.map=function(e,t){t=t||this;for(var n=new V,i=this.head;i!==null;)n.push(e.call(t,i.value,this)),i=i.next;return n};V.prototype.mapReverse=function(e,t){t=t||this;for(var n=new V,i=this.tail;i!==null;)n.push(e.call(t,i.value,this)),i=i.prev;return n};V.prototype.reduce=function(e,t){var n,i=this.head;if(arguments.length>1)n=t;else if(this.head)i=this.head.next,n=this.head.value;else throw new TypeError("Reduce of empty list with no initial value");for(var r=0;i!==null;r++)n=e(n,i.value,r),i=i.next;return n};V.prototype.reduceReverse=function(e,t){var n,i=this.tail;if(arguments.length>1)n=t;else if(this.tail)i=this.tail.prev,n=this.tail.value;else throw new TypeError("Reduce of empty list with no initial value");for(var r=this.length-1;i!==null;r--)n=e(n,i.value,r),i=i.prev;return n};V.prototype.toArray=function(){for(var e=new Array(this.length),t=0,n=this.head;n!==null;t++)e[t]=n.value,n=n.next;return e};V.prototype.toArrayReverse=function(){for(var e=new Array(this.length),t=0,n=this.tail;n!==null;t++)e[t]=n.value,n=n.prev;return e};V.prototype.slice=function(e,t){t=t||this.length,t<0&&(t+=this.length),e=e||0,e<0&&(e+=this.length);var n=new V;if(t<e||t<0)return n;e<0&&(e=0),t>this.length&&(t=this.length);for(var i=0,r=this.head;r!==null&&i<e;i++)r=r.next;for(;r!==null&&i<t;i++,r=r.next)n.push(r.value);return n};V.prototype.sliceReverse=function(e,t){t=t||this.length,t<0&&(t+=this.length),e=e||0,e<0&&(e+=this.length);var n=new V;if(t<e||t<0)return n;e<0&&(e=0),t>this.length&&(t=this.length);for(var i=this.length,r=this.tail;r!==null&&i>t;i--)r=r.prev;for(;r!==null&&i>e;i--,r=r.prev)n.push(r.value);return n};V.prototype.splice=function(e,t,...n){e>this.length&&(e=this.length-1),e<0&&(e=this.length+e);for(var i=0,r=this.head;r!==null&&i<e;i++)r=r.next;for(var s=[],i=0;r&&i<t;i++)s.push(r.value),r=this.removeNode(r);r===null&&(r=this.tail),r!==this.head&&r!==this.tail&&(r=r.prev);for(var i=0;i<n.length;i++)r=n$(this,r,n[i]);return s};V.prototype.reverse=function(){for(var e=this.head,t=this.tail,n=e;n!==null;n=n.prev){var i=n.prev;n.prev=n.next,n.next=i}return this.head=t,this.tail=e,this};function n$(e,t,n){var i=t===e.head?new Tn(n,null,t,e):new Tn(n,t,t.next,e);return i.next===null&&(e.tail=i),i.prev===null&&(e.head=i),e.length++,i}function i$(e,t){e.tail=new Tn(t,e.tail,null,e),e.head||(e.head=e.tail),e.length++}function r$(e,t){e.head=new Tn(t,null,e.head,e),e.tail||(e.tail=e.head),e.length++}function Tn(e,t,n,i){if(!(this instanceof Tn))return new Tn(e,t,n,i);this.list=i,this.value=e,t?(t.next=this,this.prev=t):this.prev=null,n?(n.prev=this,this.next=n):this.next=null}try{e$()(V)}catch{}const s$=t$,pn=Symbol("max"),Ct=Symbol("length"),Un=Symbol("lengthCalculator"),Li=Symbol("allowStale"),gn=Symbol("maxAge"),wt=Symbol("dispose"),Dl=Symbol("noDisposeOnSet"),ge=Symbol("lruList"),He=Symbol("cache"),Sp=Symbol("updateAgeOnGet"),Gs=()=>1;class o${constructor(t){if(typeof t=="number"&&(t={max:t}),t||(t={}),t.max&&(typeof t.max!="number"||t.max<0))throw new TypeError("max must be a non-negative number");this[pn]=t.max||1/0;const n=t.length||Gs;if(this[Un]=typeof n!="function"?Gs:n,this[Li]=t.stale||!1,t.maxAge&&typeof t.maxAge!="number")throw new TypeError("maxAge must be a number");this[gn]=t.maxAge||0,this[wt]=t.dispose,this[Dl]=t.noDisposeOnSet||!1,this[Sp]=t.updateAgeOnGet||!1,this.reset()}set max(t){if(typeof t!="number"||t<0)throw new TypeError("max must be a non-negative number");this[pn]=t||1/0,Fi(this)}get max(){return this[pn]}set allowStale(t){this[Li]=!!t}get allowStale(){return this[Li]}set maxAge(t){if(typeof t!="number")throw new TypeError("maxAge must be a non-negative number");this[gn]=t,Fi(this)}get maxAge(){return this[gn]}set lengthCalculator(t){typeof t!="function"&&(t=Gs),t!==this[Un]&&(this[Un]=t,this[Ct]=0,this[ge].forEach(n=>{n.length=this[Un](n.value,n.key),this[Ct]+=n.length})),Fi(this)}get lengthCalculator(){return this[Un]}get length(){return this[Ct]}get itemCount(){return this[ge].length}rforEach(t,n){n=n||this;for(let i=this[ge].tail;i!==null;){const r=i.prev;jl(this,t,i,n),i=r}}forEach(t,n){n=n||this;for(let i=this[ge].head;i!==null;){const r=i.next;jl(this,t,i,n),i=r}}keys(){return this[ge].toArray().map(t=>t.key)}values(){return this[ge].toArray().map(t=>t.value)}reset(){this[wt]&&this[ge]&&this[ge].length&&this[ge].forEach(t=>this[wt](t.key,t.value)),this[He]=new Map,this[ge]=new s$,this[Ct]=0}dump(){return this[ge].map(t=>Or(this,t)?!1:{k:t.key,v:t.value,e:t.now+(t.maxAge||0)}).toArray().filter(t=>t)}dumpLru(){return this[ge]}set(t,n,i){if(i=i||this[gn],i&&typeof i!="number")throw new TypeError("maxAge must be a number");const r=i?Date.now():0,s=this[Un](n,t);if(this[He].has(t)){if(s>this[pn])return Yn(this,this[He].get(t)),!1;const c=this[He].get(t).value;return this[wt]&&(this[Dl]||this[wt](t,c.value)),c.now=r,c.maxAge=i,c.value=n,this[Ct]+=s-c.length,c.length=s,this.get(t),Fi(this),!0}const o=new a$(t,n,s,r,i);return o.length>this[pn]?(this[wt]&&this[wt](t,n),!1):(this[Ct]+=o.length,this[ge].unshift(o),this[He].set(t,this[ge].head),Fi(this),!0)}has(t){if(!this[He].has(t))return!1;const n=this[He].get(t).value;return!Or(this,n)}get(t){return qs(this,t,!0)}peek(t){return qs(this,t,!1)}pop(){const t=this[ge].tail;return t?(Yn(this,t),t.value):null}del(t){Yn(this,this[He].get(t))}load(t){this.reset();const n=Date.now();for(let i=t.length-1;i>=0;i--){const r=t[i],s=r.e||0;if(s===0)this.set(r.k,r.v);else{const o=s-n;o>0&&this.set(r.k,r.v,o)}}}prune(){this[He].forEach((t,n)=>qs(this,n,!1))}}const qs=(e,t,n)=>{const i=e[He].get(t);if(i){const r=i.value;if(Or(e,r)){if(Yn(e,i),!e[Li])return}else n&&(e[Sp]&&(i.value.now=Date.now()),e[ge].unshiftNode(i));return r.value}},Or=(e,t)=>{if(!t||!t.maxAge&&!e[gn])return!1;const n=Date.now()-t.now;return t.maxAge?n>t.maxAge:e[gn]&&n>e[gn]},Fi=e=>{if(e[Ct]>e[pn])for(let t=e[ge].tail;e[Ct]>e[pn]&&t!==null;){const n=t.prev;Yn(e,t),t=n}},Yn=(e,t)=>{if(t){const n=t.value;e[wt]&&e[wt](n.key,n.value),e[Ct]-=n.length,e[He].delete(n.key),e[ge].removeNode(t)}};class a${constructor(t,n,i,r,s){this.key=t,this.value=n,this.length=i,this.now=r,this.maxAge=s||0}}const jl=(e,t,n,i)=>{let r=n.value;Or(e,r)&&(Yn(e,n),e[Li]||(r=void 0)),r&&t.call(i,r.value,r.key,e)};var c$=o$;const l$=Object.freeze({loose:!0}),u$=Object.freeze({}),f$=e=>e?typeof e!="object"?l$:e:u$;var tc=f$,wo={exports:{}};const d$="2.0.0",Ep=256,p$=Number.MAX_SAFE_INTEGER||9007199254740991,g$=16,h$=Ep-6,m$=["major","premajor","minor","preminor","patch","prepatch","prerelease"];var nc={MAX_LENGTH:Ep,MAX_SAFE_COMPONENT_LENGTH:g$,MAX_SAFE_BUILD_LENGTH:h$,MAX_SAFE_INTEGER:p$,RELEASE_TYPES:m$,SEMVER_SPEC_VERSION:d$,FLAG_INCLUDE_PRERELEASE:1,FLAG_LOOSE:2};const y$=typeof process=="object"&&Ws&&Ws.NODE_DEBUG&&/\bsemver\b/i.test(Ws.NODE_DEBUG)?(...e)=>console.error("SEMVER",...e):()=>{};var Es=y$;(function(e,t){const{MAX_SAFE_COMPONENT_LENGTH:n,MAX_SAFE_BUILD_LENGTH:i,MAX_LENGTH:r}=nc,s=Es;t=e.exports={};const o=t.re=[],a=t.safeRe=[],c=t.src=[],l=t.t={};let u=0;const f="[a-zA-Z0-9-]",d=[["\\s",1],["\\d",r],[f,i]],p=h=>{for(const[m,y]of d)h=h.split(`${m}*`).join(`${m}{0,${y}}`).split(`${m}+`).join(`${m}{1,${y}}`);return h},g=(h,m,y)=>{const b=p(m),N=u++;s(h,N,m),l[h]=N,c[N]=m,o[N]=new RegExp(m,y?"g":void 0),a[N]=new RegExp(b,y?"g":void 0)};g("NUMERICIDENTIFIER","0|[1-9]\\d*"),g("NUMERICIDENTIFIERLOOSE","\\d+"),g("NONNUMERICIDENTIFIER",`\\d*[a-zA-Z-]${f}*`),g("MAINVERSION",`(${c[l.NUMERICIDENTIFIER]})\\.(${c[l.NUMERICIDENTIFIER]})\\.(${c[l.NUMERICIDENTIFIER]})`),g("MAINVERSIONLOOSE",`(${c[l.NUMERICIDENTIFIERLOOSE]})\\.(${c[l.NUMERICIDENTIFIERLOOSE]})\\.(${c[l.NUMERICIDENTIFIERLOOSE]})`),g("PRERELEASEIDENTIFIER",`(?:${c[l.NUMERICIDENTIFIER]}|${c[l.NONNUMERICIDENTIFIER]})`),g("PRERELEASEIDENTIFIERLOOSE",`(?:${c[l.NUMERICIDENTIFIERLOOSE]}|${c[l.NONNUMERICIDENTIFIER]})`),g("PRERELEASE",`(?:-(${c[l.PRERELEASEIDENTIFIER]}(?:\\.${c[l.PRERELEASEIDENTIFIER]})*))`),g("PRERELEASELOOSE",`(?:-?(${c[l.PRERELEASEIDENTIFIERLOOSE]}(?:\\.${c[l.PRERELEASEIDENTIFIERLOOSE]})*))`),g("BUILDIDENTIFIER",`${f}+`),g("BUILD",`(?:\\+(${c[l.BUILDIDENTIFIER]}(?:\\.${c[l.BUILDIDENTIFIER]})*))`),g("FULLPLAIN",`v?${c[l.MAINVERSION]}${c[l.PRERELEASE]}?${c[l.BUILD]}?`),g("FULL",`^${c[l.FULLPLAIN]}$`),g("LOOSEPLAIN",`[v=\\s]*${c[l.MAINVERSIONLOOSE]}${c[l.PRERELEASELOOSE]}?${c[l.BUILD]}?`),g("LOOSE",`^${c[l.LOOSEPLAIN]}$`),g("GTLT","((?:<|>)?=?)"),g("XRANGEIDENTIFIERLOOSE",`${c[l.NUMERICIDENTIFIERLOOSE]}|x|X|\\*`),g("XRANGEIDENTIFIER",`${c[l.NUMERICIDENTIFIER]}|x|X|\\*`),g("XRANGEPLAIN",`[v=\\s]*(${c[l.XRANGEIDENTIFIER]})(?:\\.(${c[l.XRANGEIDENTIFIER]})(?:\\.(${c[l.XRANGEIDENTIFIER]})(?:${c[l.PRERELEASE]})?${c[l.BUILD]}?)?)?`),g("XRANGEPLAINLOOSE",`[v=\\s]*(${c[l.XRANGEIDENTIFIERLOOSE]})(?:\\.(${c[l.XRANGEIDENTIFIERLOOSE]})(?:\\.(${c[l.XRANGEIDENTIFIERLOOSE]})(?:${c[l.PRERELEASELOOSE]})?${c[l.BUILD]}?)?)?`),g("XRANGE",`^${c[l.GTLT]}\\s*${c[l.XRANGEPLAIN]}$`),g("XRANGELOOSE",`^${c[l.GTLT]}\\s*${c[l.XRANGEPLAINLOOSE]}$`),g("COERCEPLAIN",`(^|[^\\d])(\\d{1,${n}})(?:\\.(\\d{1,${n}}))?(?:\\.(\\d{1,${n}}))?`),g("COERCE",`${c[l.COERCEPLAIN]}(?:$|[^\\d])`),g("COERCEFULL",c[l.COERCEPLAIN]+`(?:${c[l.PRERELEASE]})?(?:${c[l.BUILD]})?(?:$|[^\\d])`),g("COERCERTL",c[l.COERCE],!0),g("COERCERTLFULL",c[l.COERCEFULL],!0),g("LONETILDE","(?:~>?)"),g("TILDETRIM",`(\\s*)${c[l.LONETILDE]}\\s+`,!0),t.tildeTrimReplace="$1~",g("TILDE",`^${c[l.LONETILDE]}${c[l.XRANGEPLAIN]}$`),g("TILDELOOSE",`^${c[l.LONETILDE]}${c[l.XRANGEPLAINLOOSE]}$`),g("LONECARET","(?:\\^)"),g("CARETTRIM",`(\\s*)${c[l.LONECARET]}\\s+`,!0),t.caretTrimReplace="$1^",g("CARET",`^${c[l.LONECARET]}${c[l.XRANGEPLAIN]}$`),g("CARETLOOSE",`^${c[l.LONECARET]}${c[l.XRANGEPLAINLOOSE]}$`),g("COMPARATORLOOSE",`^${c[l.GTLT]}\\s*(${c[l.LOOSEPLAIN]})$|^$`),g("COMPARATOR",`^${c[l.GTLT]}\\s*(${c[l.FULLPLAIN]})$|^$`),g("COMPARATORTRIM",`(\\s*)${c[l.GTLT]}\\s*(${c[l.LOOSEPLAIN]}|${c[l.XRANGEPLAIN]})`,!0),t.comparatorTrimReplace="$1$2$3",g("HYPHENRANGE",`^\\s*(${c[l.XRANGEPLAIN]})\\s+-\\s+(${c[l.XRANGEPLAIN]})\\s*$`),g("HYPHENRANGELOOSE",`^\\s*(${c[l.XRANGEPLAINLOOSE]})\\s+-\\s+(${c[l.XRANGEPLAINLOOSE]})\\s*$`),g("STAR","(<|>)?=?\\s*\\*"),g("GTE0","^\\s*>=\\s*0\\.0\\.0\\s*$"),g("GTE0PRE","^\\s*>=\\s*0\\.0\\.0-0\\s*$")})(wo,wo.exports);var ic=wo.exports;const Ml=/^[0-9]+$/,$p=(e,t)=>{const n=Ml.test(e),i=Ml.test(t);return n&&i&&(e=+e,t=+t),e===t?0:n&&!i?-1:i&&!n?1:e<t?-1:1},b$=(e,t)=>$p(t,e);var x$={compareIdentifiers:$p,rcompareIdentifiers:b$};const fr=Es,{MAX_LENGTH:Ul,MAX_SAFE_INTEGER:dr}=nc,{safeRe:Wl,t:Bl}=ic,v$=tc,{compareIdentifiers:Wn}=x$;let S$=class ot{constructor(t,n){if(n=v$(n),t instanceof ot){if(t.loose===!!n.loose&&t.includePrerelease===!!n.includePrerelease)return t;t=t.version}else if(typeof t!="string")throw new TypeError(`Invalid version. Must be a string. Got type "${typeof t}".`);if(t.length>Ul)throw new TypeError(`version is longer than ${Ul} characters`);fr("SemVer",t,n),this.options=n,this.loose=!!n.loose,this.includePrerelease=!!n.includePrerelease;const i=t.trim().match(n.loose?Wl[Bl.LOOSE]:Wl[Bl.FULL]);if(!i)throw new TypeError(`Invalid Version: ${t}`);if(this.raw=t,this.major=+i[1],this.minor=+i[2],this.patch=+i[3],this.major>dr||this.major<0)throw new TypeError("Invalid major version");if(this.minor>dr||this.minor<0)throw new TypeError("Invalid minor version");if(this.patch>dr||this.patch<0)throw new TypeError("Invalid patch version");i[4]?this.prerelease=i[4].split(".").map(r=>{if(/^[0-9]+$/.test(r)){const s=+r;if(s>=0&&s<dr)return s}return r}):this.prerelease=[],this.build=i[5]?i[5].split("."):[],this.format()}format(){return this.version=`${this.major}.${this.minor}.${this.patch}`,this.prerelease.length&&(this.version+=`-${this.prerelease.join(".")}`),this.version}toString(){return this.version}compare(t){if(fr("SemVer.compare",this.version,this.options,t),!(t instanceof ot)){if(typeof t=="string"&&t===this.version)return 0;t=new ot(t,this.options)}return t.version===this.version?0:this.compareMain(t)||this.comparePre(t)}compareMain(t){return t instanceof ot||(t=new ot(t,this.options)),Wn(this.major,t.major)||Wn(this.minor,t.minor)||Wn(this.patch,t.patch)}comparePre(t){if(t instanceof ot||(t=new ot(t,this.options)),this.prerelease.length&&!t.prerelease.length)return-1;if(!this.prerelease.length&&t.prerelease.length)return 1;if(!this.prerelease.length&&!t.prerelease.length)return 0;let n=0;do{const i=this.prerelease[n],r=t.prerelease[n];if(fr("prerelease compare",n,i,r),i===void 0&&r===void 0)return 0;if(r===void 0)return 1;if(i===void 0)return-1;if(i===r)continue;return Wn(i,r)}while(++n)}compareBuild(t){t instanceof ot||(t=new ot(t,this.options));let n=0;do{const i=this.build[n],r=t.build[n];if(fr("prerelease compare",n,i,r),i===void 0&&r===void 0)return 0;if(r===void 0)return 1;if(i===void 0)return-1;if(i===r)continue;return Wn(i,r)}while(++n)}inc(t,n,i){switch(t){case"premajor":this.prerelease.length=0,this.patch=0,this.minor=0,this.major++,this.inc("pre",n,i);break;case"preminor":this.prerelease.length=0,this.patch=0,this.minor++,this.inc("pre",n,i);break;case"prepatch":this.prerelease.length=0,this.inc("patch",n,i),this.inc("pre",n,i);break;case"prerelease":this.prerelease.length===0&&this.inc("patch",n,i),this.inc("pre",n,i);break;case"major":(this.minor!==0||this.patch!==0||this.prerelease.length===0)&&this.major++,this.minor=0,this.patch=0,this.prerelease=[];break;case"minor":(this.patch!==0||this.prerelease.length===0)&&this.minor++,this.patch=0,this.prerelease=[];break;case"patch":this.prerelease.length===0&&this.patch++,this.prerelease=[];break;case"pre":{const r=Number(i)?1:0;if(!n&&i===!1)throw new Error("invalid increment argument: identifier is empty");if(this.prerelease.length===0)this.prerelease=[r];else{let s=this.prerelease.length;for(;--s>=0;)typeof this.prerelease[s]=="number"&&(this.prerelease[s]++,s=-2);if(s===-1){if(n===this.prerelease.join(".")&&i===!1)throw new Error("invalid increment argument: identifier already exists");this.prerelease.push(r)}}if(n){let s=[n,r];i===!1&&(s=[n]),Wn(this.prerelease[0],n)===0?isNaN(this.prerelease[1])&&(this.prerelease=s):this.prerelease=s}break}default:throw new Error(`invalid increment argument: ${t}`)}return this.raw=this.format(),this.build.length&&(this.raw+=`+${this.build.join(".")}`),this}};var rc=S$;const Gl=rc,E$=(e,t,n)=>new Gl(e,n).compare(new Gl(t,n));var Si=E$;const $$=Si,w$=(e,t,n)=>$$(e,t,n)===0;var C$=w$;const N$=Si,F$=(e,t,n)=>N$(e,t,n)!==0;var _$=F$;const T$=Si,k$=(e,t,n)=>T$(e,t,n)>0;var O$=k$;const A$=Si,R$=(e,t,n)=>A$(e,t,n)>=0;var I$=R$;const L$=Si,P$=(e,t,n)=>L$(e,t,n)<0;var z$=P$;const D$=Si,j$=(e,t,n)=>D$(e,t,n)<=0;var M$=j$;const U$=C$,W$=_$,B$=O$,G$=I$,q$=z$,H$=M$,V$=(e,t,n,i)=>{switch(t){case"===":return typeof e=="object"&&(e=e.version),typeof n=="object"&&(n=n.version),e===n;case"!==":return typeof e=="object"&&(e=e.version),typeof n=="object"&&(n=n.version),e!==n;case"":case"=":case"==":return U$(e,n,i);case"!=":return W$(e,n,i);case">":return B$(e,n,i);case">=":return G$(e,n,i);case"<":return q$(e,n,i);case"<=":return H$(e,n,i);default:throw new TypeError(`Invalid operator: ${t}`)}};var X$=V$,Hs,ql;function Y$(){if(ql)return Hs;ql=1;const e=Symbol("SemVer ANY");class t{static get ANY(){return e}constructor(u,f){if(f=n(f),u instanceof t){if(u.loose===!!f.loose)return u;u=u.value}u=u.trim().split(/\s+/).join(" "),o("comparator",u,f),this.options=f,this.loose=!!f.loose,this.parse(u),this.semver===e?this.value="":this.value=this.operator+this.semver.version,o("comp",this)}parse(u){const f=this.options.loose?i[r.COMPARATORLOOSE]:i[r.COMPARATOR],d=u.match(f);if(!d)throw new TypeError(`Invalid comparator: ${u}`);this.operator=d[1]!==void 0?d[1]:"",this.operator==="="&&(this.operator=""),d[2]?this.semver=new a(d[2],this.options.loose):this.semver=e}toString(){return this.value}test(u){if(o("Comparator.test",u,this.options.loose),this.semver===e||u===e)return!0;if(typeof u=="string")try{u=new a(u,this.options)}catch{return!1}return s(u,this.operator,this.semver,this.options)}intersects(u,f){if(!(u instanceof t))throw new TypeError("a Comparator is required");return this.operator===""?this.value===""?!0:new c(u.value,f).test(this.value):u.operator===""?u.value===""?!0:new c(this.value,f).test(u.semver):(f=n(f),f.includePrerelease&&(this.value==="<0.0.0-0"||u.value==="<0.0.0-0")||!f.includePrerelease&&(this.value.startsWith("<0.0.0")||u.value.startsWith("<0.0.0"))?!1:!!(this.operator.startsWith(">")&&u.operator.startsWith(">")||this.operator.startsWith("<")&&u.operator.startsWith("<")||this.semver.version===u.semver.version&&this.operator.includes("=")&&u.operator.includes("=")||s(this.semver,"<",u.semver,f)&&this.operator.startsWith(">")&&u.operator.startsWith("<")||s(this.semver,">",u.semver,f)&&this.operator.startsWith("<")&&u.operator.startsWith(">")))}}Hs=t;const n=tc,{safeRe:i,t:r}=ic,s=X$,o=Es,a=rc,c=wp();return Hs}var Vs,Hl;function wp(){if(Hl)return Vs;Hl=1;class e{constructor($,I){if(I=i(I),$ instanceof e)return $.loose===!!I.loose&&$.includePrerelease===!!I.includePrerelease?$:new e($.raw,I);if($ instanceof r)return this.raw=$.value,this.set=[[$]],this.format(),this;if(this.options=I,this.loose=!!I.loose,this.includePrerelease=!!I.includePrerelease,this.raw=$.trim().split(/\s+/).join(" "),this.set=this.raw.split("||").map(A=>this.parseRange(A.trim())).filter(A=>A.length),!this.set.length)throw new TypeError(`Invalid SemVer Range: ${this.raw}`);if(this.set.length>1){const A=this.set[0];if(this.set=this.set.filter(L=>!g(L[0])),this.set.length===0)this.set=[A];else if(this.set.length>1){for(const L of this.set)if(L.length===1&&h(L[0])){this.set=[L];break}}}this.format()}format(){return this.range=this.set.map($=>$.join(" ").trim()).join("||").trim(),this.range}toString(){return this.range}parseRange($){const A=((this.options.includePrerelease&&d)|(this.options.loose&&p))+":"+$,L=n.get(A);if(L)return L;const k=this.options.loose,D=k?a[c.HYPHENRANGELOOSE]:a[c.HYPHENRANGE];$=$.replace(D,le(this.options.includePrerelease)),s("hyphen replace",$),$=$.replace(a[c.COMPARATORTRIM],l),s("comparator trim",$),$=$.replace(a[c.TILDETRIM],u),s("tilde trim",$),$=$.replace(a[c.CARETTRIM],f),s("caret trim",$);let H=$.split(" ").map(ie=>y(ie,this.options)).join(" ").split(/\s+/).map(ie=>X(ie,this.options));k&&(H=H.filter(ie=>(s("loose invalid filter",ie,this.options),!!ie.match(a[c.COMPARATORLOOSE])))),s("range list",H);const U=new Map,Z=H.map(ie=>new r(ie,this.options));for(const ie of Z){if(g(ie))return[ie];U.set(ie.value,ie)}U.size>1&&U.has("")&&U.delete("");const Ee=[...U.values()];return n.set(A,Ee),Ee}intersects($,I){if(!($ instanceof e))throw new TypeError("a Range is required");return this.set.some(A=>m(A,I)&&$.set.some(L=>m(L,I)&&A.every(k=>L.every(D=>k.intersects(D,I)))))}test($){if(!$)return!1;if(typeof $=="string")try{$=new o($,this.options)}catch{return!1}for(let I=0;I<this.set.length;I++)if(pe(this.set[I],$,this.options))return!0;return!1}}Vs=e;const t=c$,n=new t({max:1e3}),i=tc,r=Y$(),s=Es,o=rc,{safeRe:a,t:c,comparatorTrimReplace:l,tildeTrimReplace:u,caretTrimReplace:f}=ic,{FLAG_INCLUDE_PRERELEASE:d,FLAG_LOOSE:p}=nc,g=F=>F.value==="<0.0.0-0",h=F=>F.value==="",m=(F,$)=>{let I=!0;const A=F.slice();let L=A.pop();for(;I&&A.length;)I=A.every(k=>L.intersects(k,$)),L=A.pop();return I},y=(F,$)=>(s("comp",F,$),F=x(F,$),s("caret",F),F=N(F,$),s("tildes",F),F=w(F,$),s("xrange",F),F=W(F,$),s("stars",F),F),b=F=>!F||F.toLowerCase()==="x"||F==="*",N=(F,$)=>F.trim().split(/\s+/).map(I=>P(I,$)).join(" "),P=(F,$)=>{const I=$.loose?a[c.TILDELOOSE]:a[c.TILDE];return F.replace(I,(A,L,k,D,H)=>{s("tilde",F,A,L,k,D,H);let U;return b(L)?U="":b(k)?U=`>=${L}.0.0 <${+L+1}.0.0-0`:b(D)?U=`>=${L}.${k}.0 <${L}.${+k+1}.0-0`:H?(s("replaceTilde pr",H),U=`>=${L}.${k}.${D}-${H} <${L}.${+k+1}.0-0`):U=`>=${L}.${k}.${D} <${L}.${+k+1}.0-0`,s("tilde return",U),U})},x=(F,$)=>F.trim().split(/\s+/).map(I=>_(I,$)).join(" "),_=(F,$)=>{s("caret",F,$);const I=$.loose?a[c.CARETLOOSE]:a[c.CARET],A=$.includePrerelease?"-0":"";return F.replace(I,(L,k,D,H,U)=>{s("caret",F,L,k,D,H,U);let Z;return b(k)?Z="":b(D)?Z=`>=${k}.0.0${A} <${+k+1}.0.0-0`:b(H)?k==="0"?Z=`>=${k}.${D}.0${A} <${k}.${+D+1}.0-0`:Z=`>=${k}.${D}.0${A} <${+k+1}.0.0-0`:U?(s("replaceCaret pr",U),k==="0"?D==="0"?Z=`>=${k}.${D}.${H}-${U} <${k}.${D}.${+H+1}-0`:Z=`>=${k}.${D}.${H}-${U} <${k}.${+D+1}.0-0`:Z=`>=${k}.${D}.${H}-${U} <${+k+1}.0.0-0`):(s("no pr"),k==="0"?D==="0"?Z=`>=${k}.${D}.${H}${A} <${k}.${D}.${+H+1}-0`:Z=`>=${k}.${D}.${H}${A} <${k}.${+D+1}.0-0`:Z=`>=${k}.${D}.${H} <${+k+1}.0.0-0`),s("caret return",Z),Z})},w=(F,$)=>(s("replaceXRanges",F,$),F.split(/\s+/).map(I=>T(I,$)).join(" ")),T=(F,$)=>{F=F.trim();const I=$.loose?a[c.XRANGELOOSE]:a[c.XRANGE];return F.replace(I,(A,L,k,D,H,U)=>{s("xRange",F,A,L,k,D,H,U);const Z=b(k),Ee=Z||b(D),ie=Ee||b(H),sn=ie;return L==="="&&sn&&(L=""),U=$.includePrerelease?"-0":"",Z?L===">"||L==="<"?A="<0.0.0-0":A="*":L&&sn?(Ee&&(D=0),H=0,L===">"?(L=">=",Ee?(k=+k+1,D=0,H=0):(D=+D+1,H=0)):L==="<="&&(L="<",Ee?k=+k+1:D=+D+1),L==="<"&&(U="-0"),A=`${L+k}.${D}.${H}${U}`):Ee?A=`>=${k}.0.0${U} <${+k+1}.0.0-0`:ie&&(A=`>=${k}.${D}.0${U} <${k}.${+D+1}.0-0`),s("xRange return",A),A})},W=(F,$)=>(s("replaceStars",F,$),F.trim().replace(a[c.STAR],"")),X=(F,$)=>(s("replaceGTE0",F,$),F.trim().replace(a[$.includePrerelease?c.GTE0PRE:c.GTE0],"")),le=F=>($,I,A,L,k,D,H,U,Z,Ee,ie,sn,$s)=>(b(A)?I="":b(L)?I=`>=${A}.0.0${F?"-0":""}`:b(k)?I=`>=${A}.${L}.0${F?"-0":""}`:D?I=`>=${I}`:I=`>=${I}${F?"-0":""}`,b(Z)?U="":b(Ee)?U=`<${+Z+1}.0.0-0`:b(ie)?U=`<${Z}.${+Ee+1}.0-0`:sn?U=`<=${Z}.${Ee}.${ie}-${sn}`:F?U=`<${Z}.${Ee}.${+ie+1}-0`:U=`<=${U}`,`${I} ${U}`.trim()),pe=(F,$,I)=>{for(let A=0;A<F.length;A++)if(!F[A].test($))return!1;if($.prerelease.length&&!I.includePrerelease){for(let A=0;A<F.length;A++)if(s(F[A].semver),F[A].semver!==r.ANY&&F[A].semver.prerelease.length>0){const L=F[A].semver;if(L.major===$.major&&L.minor===$.minor&&L.patch===$.patch)return!0}return!1}return!0};return Vs}const K$=wp(),J$=(e,t,n)=>{try{t=new K$(t,n)}catch{return!1}return t.test(e)};var Q$=J$,Cp=Z1(Q$);function Z$(e,t,n){const i=e.open(t),r=1e4,s=250,{origin:o}=new URL(t);let a=~~(r/s);function c(u){u.source===i&&(a=0,e.removeEventListener("message",c,!1))}e.addEventListener("message",c,!1);function l(){a<=0||(i.postMessage(n,o),setTimeout(l,s),a-=1)}setTimeout(l,s)}var ew=`.vega-embed {
  position: relative;
  display: inline-block;
  box-sizing: border-box;
}
.vega-embed.has-actions {
  padding-right: 38px;
}
.vega-embed details:not([open]) > :not(summary) {
  display: none !important;
}
.vega-embed summary {
  list-style: none;
  position: absolute;
  top: 0;
  right: 0;
  padding: 6px;
  z-index: 1000;
  background: white;
  box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.1);
  color: #1b1e23;
  border: 1px solid #aaa;
  border-radius: 999px;
  opacity: 0.2;
  transition: opacity 0.4s ease-in;
  cursor: pointer;
  line-height: 0px;
}
.vega-embed summary::-webkit-details-marker {
  display: none;
}
.vega-embed summary:active {
  box-shadow: #aaa 0px 0px 0px 1px inset;
}
.vega-embed summary svg {
  width: 14px;
  height: 14px;
}
.vega-embed details[open] summary {
  opacity: 0.7;
}
.vega-embed:hover summary, .vega-embed:focus-within summary {
  opacity: 1 !important;
  transition: opacity 0.2s ease;
}
.vega-embed .vega-actions {
  position: absolute;
  z-index: 1001;
  top: 35px;
  right: -9px;
  display: flex;
  flex-direction: column;
  padding-bottom: 8px;
  padding-top: 8px;
  border-radius: 4px;
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.2);
  border: 1px solid #d9d9d9;
  background: white;
  animation-duration: 0.15s;
  animation-name: scale-in;
  animation-timing-function: cubic-bezier(0.2, 0, 0.13, 1.5);
  text-align: left;
}
.vega-embed .vega-actions a {
  padding: 8px 16px;
  font-family: sans-serif;
  font-size: 14px;
  font-weight: 600;
  white-space: nowrap;
  color: #434a56;
  text-decoration: none;
}
.vega-embed .vega-actions a:hover, .vega-embed .vega-actions a:focus {
  background-color: #f7f7f9;
  color: black;
}
.vega-embed .vega-actions::before, .vega-embed .vega-actions::after {
  content: "";
  display: inline-block;
  position: absolute;
}
.vega-embed .vega-actions::before {
  left: auto;
  right: 14px;
  top: -16px;
  border: 8px solid rgba(0, 0, 0, 0);
  border-bottom-color: #d9d9d9;
}
.vega-embed .vega-actions::after {
  left: auto;
  right: 15px;
  top: -14px;
  border: 7px solid rgba(0, 0, 0, 0);
  border-bottom-color: #fff;
}
.vega-embed .chart-wrapper.fit-x {
  width: 100%;
}
.vega-embed .chart-wrapper.fit-y {
  height: 100%;
}

.vega-embed-wrapper {
  max-width: 100%;
  overflow: auto;
  padding-right: 14px;
}

@keyframes scale-in {
  from {
    opacity: 0;
    transform: scale(0.6);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
`;function Np(e,...t){for(const n of t)tw(e,n);return e}function tw(e,t){for(const n of Object.keys(t))No(e,n,t[n],!0)}const at=Vp;let Wi=JE;const pr=typeof window<"u"?window:void 0;var Jl;Wi===void 0&&((Jl=pr==null?void 0:pr.vl)!=null&&Jl.compile)&&(Wi=pr.vl);const nw={export:{svg:!0,png:!0},source:!0,compiled:!0,editor:!0},iw={CLICK_TO_VIEW_ACTIONS:"Click to view actions",COMPILED_ACTION:"View Compiled Vega",EDITOR_ACTION:"Open in Vega Editor",PNG_ACTION:"Save as PNG",SOURCE_ACTION:"View Source",SVG_ACTION:"Save as SVG"},ki={vega:"Vega","vega-lite":"Vega-Lite"},Ar={vega:at.version,"vega-lite":Wi?Wi.version:"not available"},rw={vega:e=>e,"vega-lite":(e,t)=>Wi.compile(e,{config:t}).spec},sw=`
<svg viewBox="0 0 16 16" fill="currentColor" stroke="none" stroke-width="1" stroke-linecap="round" stroke-linejoin="round">
  <circle r="2" cy="8" cx="2"></circle>
  <circle r="2" cy="8" cx="8"></circle>
  <circle r="2" cy="8" cx="14"></circle>
</svg>`,ow="chart-wrapper";function aw(e){return typeof e=="function"}function Vl(e,t,n,i){const r=`<html><head>${t}</head><body><pre><code class="json">`,s=`</code></pre>${n}</body></html>`,o=window.open("");o.document.write(r+e+s),o.document.title=`${ki[i]} JSON Source`}function cw(e,t){if(e.$schema){const n=Zl(e.$schema);t&&t!==n.library&&console.warn(`The given visualization spec is written in ${ki[n.library]}, but mode argument sets ${ki[t]??t}.`);const i=n.library;return Cp(Ar[i],`^${n.version.slice(1)}`)||console.warn(`The input spec uses ${ki[i]} ${n.version}, but the current version of ${ki[i]} is v${Ar[i]}.`),i}return"mark"in e||"encoding"in e||"layer"in e||"hconcat"in e||"vconcat"in e||"facet"in e||"repeat"in e?"vega-lite":"marks"in e||"signals"in e||"scales"in e||"axes"in e?"vega":t??"vega"}function Fp(e){return!!(e&&"load"in e)}function Xl(e){return Fp(e)?e:at.loader(e)}function lw(e){var n;const t=((n=e.usermeta)==null?void 0:n.embedOptions)??{};return z(t.defaultStyle)&&(t.defaultStyle=!1),t}async function uw(e,t,n={}){let i,r;z(t)?(r=Xl(n.loader),i=JSON.parse(await r.load(t))):i=t;const s=lw(i),o=s.loader;(!r||o)&&(r=Xl(n.loader??o));const a=await Yl(s,r),c=await Yl(n,r),l={...Np(c,a),config:Rr(c.config??{},a.config??{})};return await dw(e,i,l,r)}async function Yl(e,t){const n=z(e.config)?JSON.parse(await t.load(e.config)):e.config??{},i=z(e.patch)?JSON.parse(await t.load(e.patch)):e.patch;return{...e,...i?{patch:i}:{},...n?{config:n}:{}}}function fw(e){const t=e.getRootNode?e.getRootNode():document;return t instanceof ShadowRoot?{root:t,rootContainer:t}:{root:document,rootContainer:document.head??document.body}}async function dw(e,t,n={},i){const r=n.theme?Rr(Q1[n.theme],n.config??{}):n.config,s=fi(n.actions)?n.actions:Np({},nw,n.actions??{}),o={...iw,...n.i18n},a=n.renderer??"canvas",c=n.logLevel??at.Warn,l=n.downloadFileName??"visualization",u=typeof e=="string"?document.querySelector(e):e;if(!u)throw new Error(`${e} does not exist`);if(n.defaultStyle!==!1){const x="vega-embed-style",{root:_,rootContainer:w}=fw(u);if(!_.getElementById(x)){const T=document.createElement("style");T.id=x,T.innerHTML=n.defaultStyle===void 0||n.defaultStyle===!0?ew.toString():n.defaultStyle,w.appendChild(T)}}const f=cw(t,n.mode);let d=rw[f](t,r);if(f==="vega-lite"&&d.$schema){const x=Zl(d.$schema);Cp(Ar.vega,`^${x.version.slice(1)}`)||console.warn(`The compiled spec uses Vega ${x.version}, but current version is v${Ar.vega}.`)}u.classList.add("vega-embed"),s&&u.classList.add("has-actions"),u.innerHTML="";let p=u;if(s){const x=document.createElement("div");x.classList.add(ow),u.appendChild(x),p=x}const g=n.patch;if(g&&(d=g instanceof Function?g(d):Xp(d,g,!0,!1).newDocument),n.formatLocale&&at.formatLocale(n.formatLocale),n.timeFormatLocale&&at.timeFormatLocale(n.timeFormatLocale),n.expressionFunctions)for(const x in n.expressionFunctions){const _=n.expressionFunctions[x];"fn"in _?at.expressionFunction(x,_.fn,_.visitor):_ instanceof Function&&at.expressionFunction(x,_)}const{ast:h}=n,m=at.parse(d,f==="vega-lite"?{}:r,{ast:h}),y=new(n.viewClass||at.View)(m,{loader:i,logLevel:c,renderer:a,...h?{expr:at.expressionInterpreter??n.expr??Yp}:{}});if(y.addSignalListener("autosize",(x,_)=>{const{type:w}=_;w=="fit-x"?(p.classList.add("fit-x"),p.classList.remove("fit-y")):w=="fit-y"?(p.classList.remove("fit-x"),p.classList.add("fit-y")):w=="fit"?p.classList.add("fit-x","fit-y"):p.classList.remove("fit-x","fit-y")}),n.tooltip!==!1){const{loader:x,tooltip:_}=n,w=x&&!Fp(x)?x==null?void 0:x.baseURL:void 0,T=aw(_)?_:new Kp({baseURL:w,..._===!0?{}:_}).call;y.tooltip(T)}let{hover:b}=n;if(b===void 0&&(b=f==="vega"),b){const{hoverSet:x,updateSet:_}=typeof b=="boolean"?{}:b;y.hover(x,_)}n&&(n.width!=null&&y.width(n.width),n.height!=null&&y.height(n.height),n.padding!=null&&y.padding(n.padding)),await y.initialize(p,n.bind).runAsync();let N;if(s!==!1){let x=u;if(n.defaultStyle!==!1||n.forceActionsMenu){const w=document.createElement("details");w.title=o.CLICK_TO_VIEW_ACTIONS,u.append(w),x=w;const T=document.createElement("summary");T.innerHTML=sw,w.append(T),N=W=>{w.contains(W.target)||w.removeAttribute("open")},document.addEventListener("click",N)}const _=document.createElement("div");if(x.append(_),_.classList.add("vega-actions"),s===!0||s.export!==!1){for(const w of["svg","png"])if(s===!0||s.export===!0||s.export[w]){const T=o[`${w.toUpperCase()}_ACTION`],W=document.createElement("a"),X=Y(n.scaleFactor)?n.scaleFactor[w]:n.scaleFactor;W.text=T,W.href="#",W.target="_blank",W.download=`${l}.${w}`,W.addEventListener("mousedown",async function(le){le.preventDefault();const pe=await y.toImageURL(w,X);this.href=pe}),_.append(W)}}if(s===!0||s.source!==!1){const w=document.createElement("a");w.text=o.SOURCE_ACTION,w.href="#",w.addEventListener("click",function(T){Vl(Cs(t),n.sourceHeader??"",n.sourceFooter??"",f),T.preventDefault()}),_.append(w)}if(f==="vega-lite"&&(s===!0||s.compiled!==!1)){const w=document.createElement("a");w.text=o.COMPILED_ACTION,w.href="#",w.addEventListener("click",function(T){Vl(Cs(d),n.sourceHeader??"",n.sourceFooter??"","vega"),T.preventDefault()}),_.append(w)}if(s===!0||s.editor!==!1){const w=n.editorUrl??"https://vega.github.io/editor/",T=document.createElement("a");T.text=o.EDITOR_ACTION,T.href="#",T.addEventListener("click",function(W){Z$(window,w,{config:r,mode:f,renderer:a,spec:Cs(t)}),W.preventDefault()}),_.append(T)}}function P(){N&&document.removeEventListener("click",N),y.finalize()}return{view:y,spec:t,vgSpec:d,finalize:P,embedOptions:n}}function Kl(e){let t,n;return{c(){t=Xs("div"),n=Dp(e[0]),this.h()},l(i){t=Ys(i,"DIV",{class:!0});var r=Ks(t);n=jp(r,e[0]),r.forEach(Oi),this.h()},h(){Js(t,"class","caption layout svelte-1qhqpn7")},m(i,r){Ql(i,t,r),Qs(t,n)},p(i,r){r&1&&Mp(n,i[0])},d(i){i&&Oi(t)}}}function pw(e){let t,n,i,r=e[0]&&Kl(e);return{c(){t=Xs("div"),n=Xs("div"),i=Ip(),r&&r.c(),this.h()},l(s){t=Ys(s,"DIV",{"data-testid":!0,class:!0});var o=Ks(t);n=Ys(o,"DIV",{}),Ks(n).forEach(Oi),i=Lp(o),r&&r.l(o),o.forEach(Oi),this.h()},h(){Js(t,"data-testid","altair"),Js(t,"class","altair layout svelte-1qhqpn7")},m(s,o){Ql(s,t,o),Qs(t,n),e[11](n),Qs(t,i),r&&r.m(t,null),e[12](t)},p(s,[o]){s[0]?r?r.p(s,o):(r=Kl(s),r.c(),r.m(t,null)):r&&(r.d(1),r=null)},i:uc,o:uc,d(s){s&&Oi(t),e[11](null),r&&r.d(),e[12](null)}}}function gw(e,t,n){let i,r,s,{value:o}=t,{colors:a=[]}=t,{caption:c}=t,{show_actions_button:l}=t,{gradio:u}=t,f,d,p,{_selectable:g}=t,h=window.getComputedStyle(document.body),m,y;const b=()=>Math.min(d.offsetWidth,y||d.offsetWidth);let N=()=>{};const P=()=>{s&&n(9,r.width=b(),r),uw(f,r,{actions:l}).then(function(T){if(p=T.view,N=()=>{p.signal("width",b()).run()},!g)return;const W=(X,le)=>{const pe=p.signal("brush");if(pe)if(Object.keys(pe).length===0)u.dispatch("select",{value:null,index:null,selected:!1});else{const F=Object.keys(pe)[0];let $=pe[F].map(I=>I/1e3);u.dispatch("select",{value:pe,index:$,selected:!0})}};p.addEventListener("mouseup",W),p.addEventListener("touchup",W)})};let x=new ResizeObserver(()=>{s&&r.width!==d.offsetWidth&&N()});Pp(()=>{P(),x.observe(d)}),zp(()=>{x.disconnect()});function _(T){fc[T?"unshift":"push"](()=>{f=T,n(1,f)})}function w(T){fc[T?"unshift":"push"](()=>{d=T,n(2,d)})}return e.$$set=T=>{"value"in T&&n(3,o=T.value),"colors"in T&&n(4,a=T.colors),"caption"in T&&n(0,c=T.caption),"show_actions_button"in T&&n(5,l=T.show_actions_button),"gradio"in T&&n(6,u=T.gradio),"_selectable"in T&&n(7,g=T._selectable)},e.$$.update=()=>{var T,W,X,le;e.$$.dirty&8&&n(10,i=o==null?void 0:o.plot),e.$$.dirty&1024&&n(9,r=JSON.parse(i)),e.$$.dirty&640&&r&&r.params&&!g&&n(9,r.params=r.params.filter(pe=>pe.name!=="brush"),r),e.$$.dirty&536&&o.chart&&n(9,r=Jp(r,h,o.chart,a)),e.$$.dirty&768&&m!==r&&(n(8,m=r),y=r.width),e.$$.dirty&520&&(s=!((W=(T=r.encoding)==null?void 0:T.column)!=null&&W.field||(le=(X=r.encoding)==null?void 0:X.row)!=null&&le.field||o.chart===void 0))},[c,f,d,o,a,l,u,g,m,r,i,_,w]}class n0 extends Op{constructor(t){super(),Ap(this,t,gw,pw,Rp,{value:3,colors:4,caption:0,show_actions_button:5,gradio:6,_selectable:7})}}export{n0 as default};
//# sourceMappingURL=AltairPlot.DRsfQZ4j.js.map
