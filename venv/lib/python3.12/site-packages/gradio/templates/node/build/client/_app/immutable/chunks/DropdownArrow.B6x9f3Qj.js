import{SvelteComponent as d,init as c,safe_not_equal as m,svg_element as h,claim_svg_element as i,children as l,detach as n,attr as e,insert_hydration as u,append_hydration as v,noop as a}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";import"./2.CXBv8kT_.js";function w(p){let t,r;return{c(){t=h("svg"),r=h("path"),this.h()},l(o){t=i(o,"svg",{class:!0,xmlns:!0,width:!0,height:!0,viewBox:!0});var s=l(t);r=i(s,"path",{d:!0}),l(r).forEach(n),s.forEach(n),this.h()},h(){e(r,"d","M5 8l4 4 4-4z"),e(t,"class","dropdown-arrow svelte-xjn76a"),e(t,"xmlns","http://www.w3.org/2000/svg"),e(t,"width","100%"),e(t,"height","100%"),e(t,"viewBox","0 0 18 18")},m(o,s){u(o,t,s),v(t,r)},p:a,i:a,o:a,d(o){o&&n(t)}}}class f extends d{constructor(t){super(),c(this,t,null,w,m,{})}}export{f as D};
//# sourceMappingURL=DropdownArrow.B6x9f3Qj.js.map
