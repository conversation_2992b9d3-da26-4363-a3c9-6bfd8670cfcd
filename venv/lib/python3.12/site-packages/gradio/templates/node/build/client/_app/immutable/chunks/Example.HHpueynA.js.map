{"version": 3, "file": "Example.HHpueynA.js", "sources": ["../../../../../../../code/Example.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\texport let value: string | null;\n\texport let type: \"gallery\" | \"table\";\n\texport let selected = false;\n\n\tfunction truncate_text(text: string | null, max_length = 60): string {\n\t\tif (!text) return \"\";\n\t\tconst str = String(text);\n\t\tif (str.length <= max_length) return str;\n\t\treturn str.slice(0, max_length) + \"...\";\n\t}\n</script>\n\n<pre\n\tclass:table={type === \"table\"}\n\tclass:gallery={type === \"gallery\"}\n\tclass:selected>{truncate_text(value)}</pre>\n\n<style>\n\tpre {\n\t\ttext-align: left;\n\t}\n\t.gallery {\n\t\tpadding: var(--size-1) var(--size-2);\n\t}\n</style>\n"], "names": ["t_value", "truncate_text", "ctx", "toggle_class", "pre", "insert_hydration", "target", "anchor", "dirty", "set_data", "t", "text", "max_length", "str", "value", "$$props", "type", "selected"], "mappings": "+UAgBiBA,EAAAC,EAAcC,EAAK,CAAA,CAAA,EAAA,qJAFtBC,EAAAC,EAAA,QAAAF,OAAS,OAAO,EACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,+BAFlCG,EAG2CC,EAAAF,EAAAG,CAAA,mBAA1BC,EAAA,GAAAR,KAAAA,EAAAC,EAAcC,EAAK,CAAA,CAAA,EAAA,KAAAO,EAAAC,EAAAV,CAAA,OAFtBG,EAAAC,EAAA,QAAAF,OAAS,OAAO,OACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,oDAVxB,SAAAD,EAAcU,EAAqBC,EAAa,GAAA,KACnDD,EAAa,MAAA,GACZ,MAAAE,EAAM,OAAOF,CAAI,EACnB,OAAAE,EAAI,QAAUD,EAAmBC,EAC9BA,EAAI,MAAM,EAAGD,CAAU,EAAI,wBARxB,GAAA,CAAA,MAAAE,CAAA,EAAAC,EACA,CAAA,KAAAC,CAAA,EAAAD,GACA,SAAAE,EAAW,EAAA,EAAAF"}