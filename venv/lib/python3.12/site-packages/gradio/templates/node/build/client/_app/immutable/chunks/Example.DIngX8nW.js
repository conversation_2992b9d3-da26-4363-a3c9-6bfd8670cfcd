import{SvelteComponent as _,init as h,safe_not_equal as d,element as g,claim_element as v,children as b,detach as u,attr as y,toggle_class as r,insert_hydration as p,transition_in as f,group_outros as k,transition_out as s,check_outros as w,create_component as j,claim_component as E,mount_component as J,destroy_component as S}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";import{J as q}from"./JSON.rCpj7Iqp.js";function c(a){let t,n;return t=new q({props:{value:a[0],open:!0,theme_mode:a[1],show_indices:D,label_height:I,interactive:!1,show_copy_button:!1}}),{c(){j(t.$$.fragment)},l(e){E(t.$$.fragment,e)},m(e,l){J(t,e,l),n=!0},p(e,l){const o={};l&1&&(o.value=e[0]),l&2&&(o.theme_mode=e[1]),t.$set(o)},i(e){n||(f(t.$$.fragment,e),n=!0)},o(e){s(t.$$.fragment,e),n=!1},d(e){S(t,e)}}}function C(a){let t,n,e=a[0]&&c(a);return{c(){t=g("div"),e&&e.c(),this.h()},l(l){t=v(l,"DIV",{class:!0});var o=b(t);e&&e.l(o),o.forEach(u),this.h()},h(){y(t,"class","container svelte-v7ph9u"),r(t,"table",a[2]==="table"),r(t,"gallery",a[2]==="gallery"),r(t,"selected",a[3]),r(t,"border",a[0])},m(l,o){p(l,t,o),e&&e.m(t,null),n=!0},p(l,[o]){l[0]?e?(e.p(l,o),o&1&&f(e,1)):(e=c(l),e.c(),f(e,1),e.m(t,null)):e&&(k(),s(e,1,1,()=>{e=null}),w()),(!n||o&4)&&r(t,"table",l[2]==="table"),(!n||o&4)&&r(t,"gallery",l[2]==="gallery"),(!n||o&8)&&r(t,"selected",l[3]),(!n||o&1)&&r(t,"border",l[0])},i(l){n||(f(e),n=!0)},o(l){s(e),n=!1},d(l){l&&u(t),e&&e.d()}}}let D=!1,I=0;function N(a,t,n){let{value:e}=t,{theme_mode:l="system"}=t,{type:o}=t,{selected:m=!1}=t;return a.$$set=i=>{"value"in i&&n(0,e=i.value),"theme_mode"in i&&n(1,l=i.theme_mode),"type"in i&&n(2,o=i.type),"selected"in i&&n(3,m=i.selected)},[e,l,o,m]}class A extends _{constructor(t){super(),h(this,t,N,C,d,{value:0,theme_mode:1,type:2,selected:3})}}export{A as default};
//# sourceMappingURL=Example.DIngX8nW.js.map
