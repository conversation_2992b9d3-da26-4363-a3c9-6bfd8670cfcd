import{SvelteComponent as d,init as f,safe_not_equal as v,svg_element as l,claim_svg_element as a,children as h,detach as s,attr as t,insert_hydration as g,append_hydration as p,noop as u}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";function m(c){let e,r,o;return{c(){e=l("svg"),r=l("path"),o=l("polyline"),this.h()},l(i){e=a(i,"svg",{xmlns:!0,width:!0,height:!0,viewBox:!0,fill:!0,stroke:!0,"stroke-width":!0,"stroke-linecap":!0,"stroke-linejoin":!0,class:!0});var n=h(e);r=a(n,"path",{d:!0}),h(r).forEach(s),o=a(n,"polyline",{points:!0}),h(o).forEach(s),n.forEach(s),this.h()},h(){t(r,"d","M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z"),t(o,"points","13 2 13 9 20 9"),t(e,"xmlns","http://www.w3.org/2000/svg"),t(e,"width","100%"),t(e,"height","100%"),t(e,"viewBox","0 0 24 24"),t(e,"fill","none"),t(e,"stroke","currentColor"),t(e,"stroke-width","1.5"),t(e,"stroke-linecap","round"),t(e,"stroke-linejoin","round"),t(e,"class","feather feather-file")},m(i,n){g(i,e,n),p(e,r),p(e,o)},p:u,i:u,o:u,d(i){i&&s(e)}}}class k extends d{constructor(e){super(),f(this,e,null,m,v,{})}}export{k as F};
//# sourceMappingURL=File.Dl9hvYLG.js.map
