import{S as g,Q as m,M as f,aQ as y,ai as x,an as b,ao as A}from"./index.BoI39RQH.js";import{_ as T}from"./environmentTextureTools.DZJEvUsQ.js";import{CubeTexture as S}from"./cubeTexture.CrR7pXZF.js";import{GLTFLoader as w,ArrayItem as h}from"./glTFLoader.BetPWe9U.js";class d extends S{constructor(t,e,a,r=5,n=0,s=!1,o=!1,l=3,i=null){super("",t),this._texture=t.getEngine().createRawCubeTexture(e,a,r,n,s,o,l,i)}update(t,e,a,r,n=null){this._texture.getEngine().updateRawCubeTexture(this._texture,t,e,a,r,n)}updateRGBDAsync(t,e=null,a=.8,r=0){return T(this._texture,t,e,a,r).then(()=>{})}clone(){return g.<PERSON>lone(()=>{const t=this.getScene(),e=this._texture,a=new d(t,e._bufferViewArray,e.width,e.format,e.type,e.generateMipMaps,e.invertY,e.samplingMode,e._compression);return e.source===13&&a.updateRGBDAsync(e._bufferViewArrayArray,e._sphericalPolynomial,e._lodGenerationScale,e._lodGenerationOffset),a},this)}}const c="EXT_lights_image_based";class I{constructor(t){this.name=c,this._loader=t,this.enabled=this._loader.isExtensionUsed(c)}dispose(){this._loader=null,delete this._lights}onLoading(){const t=this._loader.gltf.extensions;if(t&&t[this.name]){const e=t[this.name];this._lights=e.lights}}loadSceneAsync(t,e){return w.LoadExtensionAsync(t,e,this.name,(a,r)=>{this._loader._allMaterialsDirtyRequired=!0;const n=new Array;n.push(this._loader.loadSceneAsync(t,e)),this._loader.logOpen(`${a}`);const s=h.Get(`${a}/light`,this._lights,r.light);return n.push(this._loadLightAsync(`/extensions/${this.name}/lights/${r.light}`,s).then(o=>{this._loader.babylonScene.environmentTexture=o})),this._loader.logClose(),Promise.all(n).then(()=>{})})}_loadLightAsync(t,e){if(!e._loaded){const a=new Array;this._loader.logOpen(`${t}`);const r=new Array(e.specularImages.length);for(let n=0;n<e.specularImages.length;n++){const s=e.specularImages[n];r[n]=new Array(s.length);for(let o=0;o<s.length;o++){const l=`${t}/specularImages/${n}/${o}`;this._loader.logOpen(`${l}`);const i=s[o],_=h.Get(l,this._loader.gltf.images,i);a.push(this._loader.loadImageAsync(`/images/${i}`,_).then(p=>{r[n][o]=p})),this._loader.logClose()}}this._loader.logClose(),e._loaded=Promise.all(a).then(()=>{const n=new d(this._loader.babylonScene,null,e.specularImageSize);if(n.name=e.name||"environment",e._babylonTexture=n,e.intensity!=null&&(n.level=e.intensity),e.rotation){let i=m.FromArray(e.rotation);this._loader.babylonScene.useRightHandedSystem||(i=m.Inverse(i)),f.FromQuaternionToRef(i,n.getReflectionTextureMatrix())}if(!e.irradianceCoefficients)throw new Error(`${t}: Irradiance coefficients are missing`);const s=y.FromArray(e.irradianceCoefficients);s.scaleInPlace(e.intensity),s.convertIrradianceToLambertianRadiance();const o=x.FromHarmonics(s),l=(r.length-1)/Math.log2(e.specularImageSize);return n.updateRGBDAsync(r,o,l)})}return e._loaded.then(()=>e._babylonTexture)}}b(c);A(c,!0,u=>new I(u));export{I as EXT_lights_image_based};
//# sourceMappingURL=EXT_lights_image_based.CTLLHlq2.js.map
