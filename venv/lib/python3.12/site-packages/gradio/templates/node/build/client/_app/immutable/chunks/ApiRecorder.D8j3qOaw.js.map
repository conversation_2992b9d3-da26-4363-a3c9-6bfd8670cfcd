{"version": 3, "file": "ApiRecorder.D8j3qOaw.js", "sources": ["../../../../../../../core/src/api_docs/ApiRecorder.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport type { Payload, Dependency } from \"../types\";\n\timport { BaseButton as <PERSON><PERSON> } from \"@gradio/button\";\n\n\texport let api_calls: Payload[] = [];\n\texport let dependencies: Dependency[];\n</script>\n\n<div id=\"api-recorder\">\n\t<Button size=\"sm\" variant=\"secondary\">\n\t\t<div class=\"loading-dot self-baseline\"></div>\n\t\t<p class=\"self-baseline\">Recording API Calls:</p>\n\t\t<p class=\"self-baseline api-section\">\n\t\t\t<span class=\"api-count\">\n\t\t\t\t[{api_calls.length}]\n\t\t\t</span>\n\t\t\t{#if api_calls.length > 0}\n\t\t\t\t<span class=\"api-name\"\n\t\t\t\t\t>/{dependencies[api_calls[api_calls.length - 1].fn_index]\n\t\t\t\t\t\t.api_name}</span\n\t\t\t\t>\n\t\t\t{/if}\n\t\t</p>\n\t</Button>\n</div>\n\n<style>\n\t.api-name {\n\t\tfont-size: var(--text-sm);\n\t\tfont-family: var(--font-mono);\n\t\tcolor: #fd7b00;\n\t}\n\t.loading-dot {\n\t\tposition: relative;\n\t\tleft: -9999px;\n\t\twidth: 10px;\n\t\theight: 10px;\n\t\tborder-radius: 5px;\n\t\tbackground-color: #fd7b00;\n\t\tcolor: #fd7b00;\n\t\tbox-shadow: 9999px 0 0 -1px;\n\t\tanimation: loading-dot 2s infinite linear;\n\t\tanimation-delay: 0.25s;\n\t\tmargin-left: 0.25rem;\n\t\tmargin-right: 0.5rem;\n\t}\n\t:global(.docs-wrap .sm.secondary) {\n\t\tpadding-top: 1px;\n\t\tpadding-bottom: 1px;\n\t}\n\t.self-baseline {\n\t\talign-self: baseline;\n\t}\n\t@keyframes loading-dot {\n\t\t0% {\n\t\t\tbox-shadow: 9999px 0 0 -1px;\n\t\t}\n\t\t50% {\n\t\t\tbox-shadow: 9999px 0 0 2px;\n\t\t}\n\t\t100% {\n\t\t\tbox-shadow: 9999px 0 0 -1px;\n\t\t}\n\t}\n\t.api-count {\n\t\tfont-weight: bold;\n\t\tcolor: #fd7b00;\n\t\talign-self: baseline;\n\t\tfont-family: var(--font-mono);\n\t\tfont-size: var(--text-sm);\n\t}\n\t.api-section {\n\t\tmargin-left: 4px;\n\t}\n</style>\n"], "names": ["ctx", "insert_hydration", "target", "span", "anchor", "set_data", "t1", "t1_value", "t4_value", "create_if_block", "div", "p0", "p1", "append_hydration", "dirty", "t4", "api_calls", "$$props", "dependencies"], "mappings": "6gBAkBQA,EAAY,CAAA,EAACA,EAAS,CAAA,EAACA,EAAS,CAAA,EAAC,OAAS,CAAC,EAAE,QAAQ,EACtD,SAAQ,gCADT,GAAC,kEAAD,GAAC,oFADHC,EAGAC,EAAAC,EAAAC,CAAA,oCAFIJ,EAAY,CAAA,EAACA,EAAS,CAAA,EAACA,EAAS,CAAA,EAAC,OAAS,CAAC,EAAE,QAAQ,EACtD,SAAQ,KAAAK,EAAAC,EAAAC,CAAA,2EALTC,EAAAR,KAAU,OAAM,WAEdA,EAAS,CAAA,EAAC,OAAS,GAACS,EAAAT,CAAA,sFAHF,GACrB,aAAkB,GACpB,qPAFuB,GACrB,iBAAkB,GACpB,mQALDC,EAA4CC,EAAAQ,EAAAN,CAAA,WAC5CH,EAAgDC,EAAAS,EAAAP,CAAA,WAChDH,EAUGC,EAAAU,EAAAR,CAAA,EATFS,EAEMD,EAAAT,CAAA,qDADHW,EAAA,GAAAN,KAAAA,EAAAR,KAAU,OAAM,KAAAK,EAAAU,EAAAP,CAAA,EAEdR,EAAS,CAAA,EAAC,OAAS,+XAR3BC,EAgBKC,EAAAQ,EAAAN,CAAA,2LApBO,GAAA,CAAA,UAAAY,EAAA,EAAA,EAAAC,EACA,CAAA,aAAAC,CAAA,EAAAD"}