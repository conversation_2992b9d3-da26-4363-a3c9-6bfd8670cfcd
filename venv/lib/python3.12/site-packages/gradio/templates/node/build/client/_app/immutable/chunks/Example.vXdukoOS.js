import{SvelteComponent as m,init as _,safe_not_equal as g,element as d,claim_element as h,children as v,detach as c,attr as b,toggle_class as i,insert_hydration as p,transition_in as f,group_outros as y,transition_out as s,check_outros as k,create_component as E,claim_component as I,mount_component as q,destroy_component as w}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";import{r as z}from"./2.CXBv8kT_.js";/* empty css                                              */function u(a){let t,n;return t=new z({props:{src:a[0].url,alt:""}}),{c(){E(t.$$.fragment)},l(e){I(t.$$.fragment,e)},m(e,l){q(t,e,l),n=!0},p(e,l){const r={};l&1&&(r.src=e[0].url),t.$set(r)},i(e){n||(f(t.$$.fragment,e),n=!0)},o(e){s(t.$$.fragment,e),n=!1},d(e){w(t,e)}}}function C(a){let t,n,e=a[0]&&u(a);return{c(){t=d("div"),e&&e.c(),this.h()},l(l){t=h(l,"DIV",{class:!0});var r=v(t);e&&e.l(r),r.forEach(c),this.h()},h(){b(t,"class","container svelte-a9zvka"),i(t,"table",a[1]==="table"),i(t,"gallery",a[1]==="gallery"),i(t,"selected",a[2]),i(t,"border",a[0])},m(l,r){p(l,t,r),e&&e.m(t,null),n=!0},p(l,[r]){l[0]?e?(e.p(l,r),r&1&&f(e,1)):(e=u(l),e.c(),f(e,1),e.m(t,null)):e&&(y(),s(e,1,1,()=>{e=null}),k()),(!n||r&2)&&i(t,"table",l[1]==="table"),(!n||r&2)&&i(t,"gallery",l[1]==="gallery"),(!n||r&4)&&i(t,"selected",l[2]),(!n||r&1)&&i(t,"border",l[0])},i(l){n||(f(e),n=!0)},o(l){s(e),n=!1},d(l){l&&c(t),e&&e.d()}}}function D(a,t,n){let{value:e}=t,{type:l}=t,{selected:r=!1}=t;return a.$$set=o=>{"value"in o&&n(0,e=o.value),"type"in o&&n(1,l=o.type),"selected"in o&&n(2,r=o.selected)},[e,l,r]}class B extends m{constructor(t){super(),_(this,t,D,C,g,{value:0,type:1,selected:2})}}export{B as default};
//# sourceMappingURL=Example.vXdukoOS.js.map
