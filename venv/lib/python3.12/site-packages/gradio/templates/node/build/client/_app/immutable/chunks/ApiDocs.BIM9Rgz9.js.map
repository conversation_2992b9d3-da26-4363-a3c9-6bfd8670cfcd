{"version": 3, "file": "ApiDocs.BIM9Rgz9.js", "sources": ["../../../../../../../core/src/api_docs/NoApi.svelte", "../../../../../../../core/src/api_docs/ApiBanner.svelte", "../../../../../../../core/src/api_docs/utils.ts", "../../../../../../../core/src/api_docs/ParametersSnippet.svelte", "../../../../../../../core/src/api_docs/CopyButton.svelte", "../../../../../../../core/src/api_docs/InstallSnippet.svelte", "../../../../../../../core/src/api_docs/EndpointDetail.svelte", "../../../../../../../core/src/api_docs/CodeSnippet.svelte", "../../../../../../../core/src/api_docs/RecordingSnippet.svelte", "../../../../../../../core/src/api_docs/img/python.svg", "../../../../../../../core/src/api_docs/img/javascript.svg", "../../../../../../../core/src/api_docs/img/bash.svg", "../../../../../../../core/src/api_docs/ResponseSnippet.svelte", "../../../../../../../core/src/api_docs/img/mcp.svg", "../../../../../../../core/src/api_docs/ApiDocs.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport { createEventDispatcher } from \"svelte\";\n\timport Clear from \"./img/clear.svelte\";\n\n\tconst dispatch = createEventDispatcher();\n\n\texport let root: string;\n</script>\n\n<div class=\"wrap prose\">\n\t<h1>API Docs</h1>\n\t<p class=\"attention\">\n\t\tNo API Routes found for\n\t\t<code>\n\t\t\t{root}\n\t\t</code>\n\t</p>\n\t<p>\n\t\tTo expose an API endpoint of your app in this page, set the <code>\n\t\t\tapi_name\n\t\t</code>\n\t\tparameter of the event listener.\n\t\t<br />\n\t\tFor more information, visit the\n\t\t<a href=\"https://gradio.app/sharing_your_app/#api-page\" target=\"_blank\">\n\t\t\tAPI Page guide\n\t\t</a>\n\t\t. To hide the API documentation button and this page, set\n\t\t<code>show_api=False</code>\n\t\tin the\n\t\t<code>Blocks.launch()</code>\n\t\tmethod.\n\t</p>\n</div>\n\n<button on:click={() => dispatch(\"close\")}>\n\t<Clear />\n</button>\n\n<style>\n\t.wrap {\n\t\tpadding: var(--size-6);\n\t}\n\n\t.attention {\n\t\tfont-weight: var(--weight-bold);\n\t\tfont-size: var(--text-lg);\n\t}\n\n\t.attention code {\n\t\tborder: none;\n\t\tbackground: none;\n\t\tcolor: var(--color-accent);\n\t\tfont-weight: var(--weight-bold);\n\t}\n\n\tbutton {\n\t\tposition: absolute;\n\t\ttop: var(--size-5);\n\t\tright: var(--size-6);\n\t\twidth: var(--size-4);\n\t\tcolor: var(--body-text-color);\n\t}\n\n\tbutton:hover {\n\t\tcolor: var(--color-accent);\n\t}\n\n\t@media (--screen-md) {\n\t\tbutton {\n\t\t\ttop: var(--size-6);\n\t\t}\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { createEventDispatcher } from \"svelte\";\n\timport api_logo from \"./img/api-logo.svg\";\n\timport Clear from \"./img/clear.svelte\";\n\timport { BaseButton } from \"@gradio/button\";\n\n\texport let root: string;\n\texport let api_count: number;\n\texport let current_language: \"python\" | \"javascript\" | \"bash\" | \"mcp\" =\n\t\t\"python\";\n\n\tconst dispatch = createEventDispatcher();\n</script>\n\n<h2>\n\t<img src={api_logo} alt=\"\" />\n\t<div class=\"title\">\n\t\tAPI documentation\n\t\t<div class=\"url\">\n\t\t\t{root}\n\t\t</div>\n\t</div>\n\t<span class=\"counts\">\n\t\t{#if current_language !== \"mcp\"}\n\t\t\t<BaseButton\n\t\t\t\tsize=\"sm\"\n\t\t\t\tvariant=\"secondary\"\n\t\t\t\telem_id=\"start-api-recorder\"\n\t\t\t\ton:click={() => dispatch(\"close\", { api_recorder_visible: true })}\n\t\t\t>\n\t\t\t\t<div class=\"loading-dot self-baseline\"></div>\n\t\t\t\t<p class=\"self-baseline btn-text\">API Recorder</p>\n\t\t\t</BaseButton>\n\t\t{/if}\n\t\t<p>\n\t\t\t<span class=\"url\">{api_count}</span>\n\t\t\t{#if current_language !== \"mcp\"}API endpoint{:else}MCP Tool{/if}{#if api_count > 1}s{/if}<br\n\t\t\t/>\n\t\t</p>\n\t</span>\n</h2>\n\n<button on:click={() => dispatch(\"close\")}>\n\t<Clear />\n</button>\n\n<style>\n\th2 {\n\t\tdisplay: flex;\n\t\tcolor: var(--body-text-color);\n\t\tfont-weight: var(--weight-semibold);\n\t\tgap: var(--size-4);\n\t}\n\n\th2 img {\n\t\tmargin-right: var(--size-2);\n\t\twidth: var(--size-4);\n\t\tdisplay: inline-block;\n\t}\n\n\t.url {\n\t\tcolor: var(--color-accent);\n\t\tfont-weight: normal;\n\t}\n\n\tbutton {\n\t\tposition: absolute;\n\t\ttop: var(--size-5);\n\t\tright: var(--size-6);\n\t\twidth: var(--size-4);\n\t\tcolor: var(--body-text-color);\n\t}\n\n\tbutton:hover {\n\t\tcolor: var(--color-accent);\n\t}\n\n\t@media (--screen-md) {\n\t\tbutton {\n\t\t\ttop: var(--size-6);\n\t\t}\n\n\t\th2 img {\n\t\t\twidth: var(--size-5);\n\t\t}\n\t}\n\n\t.counts {\n\t\tmargin-top: auto;\n\t\tmargin-right: var(--size-8);\n\t\tmargin-bottom: auto;\n\t\tmargin-left: auto;\n\t\tcolor: var(--body-text-color);\n\t\tfont-weight: var(--weight-light);\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\tgap: 0.5rem;\n\t}\n\n\t.loading-dot {\n\t\tposition: relative;\n\t\tleft: -9999px;\n\t\twidth: 12px;\n\t\theight: 12px;\n\t\tborder-radius: 6px;\n\t\tbackground-color: #fd7b00;\n\t\tcolor: #fd7b00;\n\t\tbox-shadow: 9999px 0 0 -1px;\n\t\tmargin-right: 0.3rem;\n\t}\n\n\t.self-baseline {\n\t\talign-self: baseline;\n\t}\n\t.title {\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\tgap: 0.5rem;\n\t}\n\t.btn-text {\n\t\tfont-size: var(--text-lg);\n\t}\n</style>\n", "// eslint-disable-next-line complexity\nexport function represent_value(\n\tvalue: string,\n\ttype: string | undefined,\n\tlang: \"js\" | \"py\" | \"bash\" | null = null\n): string | null | number | boolean | Record<string, unknown> {\n\tif (type === undefined) {\n\t\treturn lang === \"py\" ? \"None\" : null;\n\t}\n\tif (value === null && lang === \"py\") {\n\t\treturn \"None\";\n\t}\n\tif (type === \"string\" || type === \"str\") {\n\t\treturn lang === null ? value : '\"' + value + '\"';\n\t} else if (type === \"number\") {\n\t\treturn lang === null ? parseFloat(value) : value;\n\t} else if (type === \"boolean\" || type == \"bool\") {\n\t\tif (lang === \"py\") {\n\t\t\tvalue = String(value);\n\t\t\treturn value === \"true\" ? \"True\" : \"False\";\n\t\t} else if (lang === \"js\" || lang === \"bash\") {\n\t\t\treturn value;\n\t\t}\n\t\treturn value === \"true\";\n\t} else if (type === \"List[str]\") {\n\t\tvalue = JSON.stringify(value);\n\t\treturn value;\n\t} else if (type.startsWith(\"Literal['\")) {\n\t\t// a literal of strings\n\t\treturn '\"' + value + '\"';\n\t}\n\t// assume object type\n\tif (lang === null) {\n\t\treturn value === \"\" ? null : JSON.parse(value);\n\t} else if (typeof value === \"string\") {\n\t\tif (value === \"\") {\n\t\t\treturn lang === \"py\" ? \"None\" : \"null\";\n\t\t}\n\t\treturn value;\n\t}\n\tif (lang === \"bash\") {\n\t\tvalue = simplify_file_data(value);\n\t}\n\tif (lang === \"py\") {\n\t\tvalue = replace_file_data_with_file_function(value);\n\t}\n\treturn stringify_except_file_function(value);\n}\n\nexport function is_potentially_nested_file_data(obj: any): boolean {\n\tif (typeof obj === \"object\" && obj !== null) {\n\t\tif (obj.hasOwnProperty(\"url\") && obj.hasOwnProperty(\"meta\")) {\n\t\t\tif (\n\t\t\t\ttypeof obj.meta === \"object\" &&\n\t\t\t\tobj.meta !== null &&\n\t\t\t\tobj.meta._type === \"gradio.FileData\"\n\t\t\t) {\n\t\t\t\treturn true;\n\t\t\t}\n\t\t}\n\t}\n\tif (typeof obj === \"object\" && obj !== null) {\n\t\tfor (let key in obj) {\n\t\t\tif (typeof obj[key] === \"object\") {\n\t\t\t\tlet result = is_potentially_nested_file_data(obj[key]);\n\t\t\t\tif (result) {\n\t\t\t\t\treturn true;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\treturn false;\n}\n\nfunction simplify_file_data(obj: any): any {\n\tif (typeof obj === \"object\" && obj !== null && !Array.isArray(obj)) {\n\t\tif (\n\t\t\t\"url\" in obj &&\n\t\t\tobj.url &&\n\t\t\t\"meta\" in obj &&\n\t\t\tobj.meta?._type === \"gradio.FileData\"\n\t\t) {\n\t\t\treturn { path: obj.url, meta: { _type: \"gradio.FileData\" } };\n\t\t}\n\t}\n\tif (Array.isArray(obj)) {\n\t\tobj.forEach((item, index) => {\n\t\t\tif (typeof item === \"object\" && item !== null) {\n\t\t\t\tobj[index] = simplify_file_data(item); // Recurse and update array elements\n\t\t\t}\n\t\t});\n\t} else if (typeof obj === \"object\" && obj !== null) {\n\t\tObject.keys(obj).forEach((key) => {\n\t\t\tobj[key] = simplify_file_data(obj[key]); // Recurse and update object properties\n\t\t});\n\t}\n\treturn obj;\n}\n\nfunction replace_file_data_with_file_function(obj: any): any {\n\tif (typeof obj === \"object\" && obj !== null && !Array.isArray(obj)) {\n\t\tif (\n\t\t\t\"url\" in obj &&\n\t\t\tobj.url &&\n\t\t\t\"meta\" in obj &&\n\t\t\tobj.meta?._type === \"gradio.FileData\"\n\t\t) {\n\t\t\treturn `handle_file('${obj.url}')`;\n\t\t}\n\t}\n\tif (Array.isArray(obj)) {\n\t\tobj.forEach((item, index) => {\n\t\t\tif (typeof item === \"object\" && item !== null) {\n\t\t\t\tobj[index] = replace_file_data_with_file_function(item); // Recurse and update array elements\n\t\t\t}\n\t\t});\n\t} else if (typeof obj === \"object\" && obj !== null) {\n\t\tObject.keys(obj).forEach((key) => {\n\t\t\tobj[key] = replace_file_data_with_file_function(obj[key]); // Recurse and update object properties\n\t\t});\n\t}\n\treturn obj;\n}\n\nfunction stringify_except_file_function(obj: any): string {\n\tlet jsonString = JSON.stringify(obj, (key, value) => {\n\t\tif (value === null) {\n\t\t\treturn \"UNQUOTEDNone\";\n\t\t}\n\t\tif (\n\t\t\ttypeof value === \"string\" &&\n\t\t\tvalue.startsWith(\"handle_file(\") &&\n\t\t\tvalue.endsWith(\")\")\n\t\t) {\n\t\t\treturn `UNQUOTED${value}`; // Flag the special strings\n\t\t}\n\t\treturn value;\n\t});\n\tconst regex = /\"UNQUOTEDhandle_file\\(([^)]*)\\)\"/g;\n\tjsonString = jsonString.replace(regex, (match, p1) => `handle_file(${p1})`);\n\tconst regexNone = /\"UNQUOTEDNone\"/g;\n\treturn jsonString.replace(regexNone, \"None\");\n}\n", "<script lang=\"ts\">\n\timport { Loader } from \"@gradio/statustracker\";\n\timport { represent_value } from \"./utils\";\n\n\texport let is_running: boolean;\n\texport let endpoint_returns: any;\n\texport let js_returns: any;\n\texport let current_language: \"python\" | \"javascript\" | \"bash\";\n</script>\n\n<h4>\n\t<div class=\"toggle-icon\">\n\t\t<div class=\"toggle-dot\" />\n\t</div>\n\tAccepts {endpoint_returns.length} parameter{#if endpoint_returns.length != 1}s{/if}:\n</h4>\n\n<div class:hide={is_running}>\n\t{#each endpoint_returns as { label, python_type, component, parameter_name, parameter_has_default, parameter_default }, i}\n\t\t<hr class=\"hr\" />\n\t\t<div style=\"margin:10px;\">\n\t\t\t<p style=\"white-space: nowrap; overflow-x: auto;\">\n\t\t\t\t<span class=\"code\" style=\"margin-right: 10px;\"\n\t\t\t\t\t>{current_language !== \"bash\" && parameter_name\n\t\t\t\t\t\t? parameter_name\n\t\t\t\t\t\t: \"[\" + i + \"]\"}</span\n\t\t\t\t>\n\t\t\t\t<span class=\"code highlight\" style=\"margin-right: 10px;\"\n\t\t\t\t\t>{#if current_language === \"python\"}{python_type.type}{#if parameter_has_default && parameter_default === null}&nbsp;|\n\t\t\t\t\t\t\tNone{/if}{:else}{js_returns[i].type || \"any\"}{/if}</span\n\t\t\t\t>\n\t\t\t\t{#if !parameter_has_default || current_language == \"bash\"}<span\n\t\t\t\t\t\tstyle=\"font-weight:bold\">Required</span\n\t\t\t\t\t>{:else}<span> Default: </span><span\n\t\t\t\t\t\tclass=\"code\"\n\t\t\t\t\t\tstyle=\"font-size: var(--text-sm);\"\n\t\t\t\t\t\t>{represent_value(parameter_default, python_type.type, \"py\")}</span\n\t\t\t\t\t>{/if}\n\t\t\t</p>\n\t\t\t<p class=\"desc\">\n\t\t\t\tThe input value that is provided in the \"{label}\" <!--\n\t-->{component}\n\t\t\t\tcomponent<!--\n\t-->. {python_type.description}\n\t\t\t</p>\n\t\t</div>\n\t{/each}\n</div>\n{#if is_running}\n\t<div class=\"load-wrap\">\n\t\t<Loader margin={false} />\n\t</div>\n{/if}\n\n<style>\n\t.hr {\n\t\tborder: 0;\n\t\theight: 1px;\n\t\tbackground: var(--color-accent-soft);\n\t\tmargin-bottom: 12px;\n\t}\n\n\t.code {\n\t\tfont-family: var(--font-mono);\n\t\tdisplay: inline;\n\t}\n\n\t.highlight {\n\t\tbackground: var(--color-accent-soft);\n\t\tcolor: var(--color-accent);\n\t\tpadding: var(--size-1);\n\t}\n\n\t.desc {\n\t\tcolor: var(--body-text-color-subdued);\n\t\tfont-size: var(--text-lg);\n\t\tmargin-top: var(--size-1);\n\t}\n\n\th4 {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tmargin-top: var(--size-6);\n\t\tmargin-bottom: var(--size-3);\n\t\tcolor: var(--body-text-color);\n\t\tfont-weight: var(--weight-bold);\n\t}\n\n\t.toggle-icon {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tmargin-right: var(--size-2);\n\t\tborder-radius: var(--radius-full);\n\t\tbackground: var(--color-grey-300);\n\t\twidth: 12px;\n\t\theight: 4px;\n\t}\n\n\t.toggle-dot {\n\t\tborder-radius: var(--radius-full);\n\t\tbackground: var(--color-grey-700);\n\t\twidth: 6px;\n\t\theight: 6px;\n\t\tmargin-right: auto;\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { BaseButton } from \"@gradio/button\";\n\texport let code: string;\n\tlet copy_text = \"copy\";\n\n\tfunction copy(): void {\n\t\tnavigator.clipboard.writeText(code);\n\t\tcopy_text = \"copied!\";\n\t\tsetTimeout(() => {\n\t\t\tcopy_text = \"copy\";\n\t\t}, 1500);\n\t}\n</script>\n\n<BaseButton size=\"sm\" on:click={copy}>\n\t{copy_text}\n</BaseButton>\n", "<script lang=\"ts\">\n\timport CopyButton from \"./CopyButton.svelte\";\n\timport { Block } from \"@gradio/atoms\";\n\n\texport let current_language: \"python\" | \"javascript\" | \"bash\" | \"mcp\";\n\n\tlet py_install = \"pip install gradio_client\";\n\tlet js_install = \"npm i -D @gradio/client\";\n\tlet bash_install = \"curl --version\";\n</script>\n\n<Block>\n\t<code>\n\t\t{#if current_language === \"python\"}\n\t\t\t<div class=\"copy\">\n\t\t\t\t<CopyButton code={py_install} />\n\t\t\t</div>\n\t\t\t<div>\n\t\t\t\t<pre>$ {py_install}</pre>\n\t\t\t</div>\n\t\t{:else if current_language === \"javascript\"}\n\t\t\t<div class=\"copy\">\n\t\t\t\t<CopyButton code={js_install} />\n\t\t\t</div>\n\t\t\t<div>\n\t\t\t\t<pre>$ {js_install}</pre>\n\t\t\t</div>\n\t\t{:else if current_language === \"bash\"}\n\t\t\t<div class=\"copy\">\n\t\t\t\t<CopyButton code={bash_install} />\n\t\t\t</div>\n\t\t\t<div>\n\t\t\t\t<pre>$ {bash_install}</pre>\n\t\t\t</div>\n\t\t{/if}\n\t</code>\n</Block>\n\n<style>\n\tcode pre {\n\t\toverflow-x: auto;\n\t\tcolor: var(--body-text-color);\n\t\tfont-family: var(--font-mono);\n\t\ttab-size: 2;\n\t}\n\n\tcode {\n\t\tposition: relative;\n\t\tdisplay: block;\n\t}\n\n\t.copy {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tright: 0;\n\t\tmargin-top: -5px;\n\t\tmargin-right: -5px;\n\t}\n</style>\n", "<script lang=\"ts\">\n\texport let api_name: string | null = null;\n\texport let description: string | null = null;\n</script>\n\n<h3>\n\tAPI name:\n\t<span class=\"post\">{\"/\" + api_name}</span>\n\t<span class=\"desc\">{description}</span>\n</h3>\n\n<style>\n\th3 {\n\t\tcolor: var(--body-text-color);\n\t\tfont-weight: var(--section-header-text-weight);\n\t\tfont-size: var(--text-lg);\n\t}\n\n\t.post {\n\t\tmargin-right: var(--size-2);\n\t\tborder: 1px solid var(--border-color-accent);\n\t\tborder-radius: var(--radius-sm);\n\t\tbackground: var(--color-accent-soft);\n\t\tpadding-right: var(--size-1);\n\t\tpadding-bottom: var(--size-1);\n\t\tpadding-left: var(--size-1);\n\t\tcolor: var(--color-accent);\n\t\tfont-weight: var(--weight-semibold);\n\t}\n\n\t.desc {\n\t\tcolor: var(--body-text-color-subdued);\n\t\tfont-size: var(--text-lg);\n\t\tmargin-top: var(--size-1);\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport type { ComponentMeta, Dependency } from \"../types\";\n\timport Copy<PERSON>utton from \"./CopyButton.svelte\";\n\timport { represent_value, is_potentially_nested_file_data } from \"./utils\";\n\timport { Block } from \"@gradio/atoms\";\n\timport EndpointDetail from \"./EndpointDetail.svelte\";\n\n\tinterface EndpointParameter {\n\t\tlabel: string;\n\t\ttype: string;\n\t\tpython_type: { type: string };\n\t\tcomponent: string;\n\t\texample_input: string;\n\t\tserializer: string;\n\t}\n\n\texport let dependency: Dependency;\n\texport let root: string;\n\texport let api_prefix: string;\n\texport let space_id: string | null;\n\texport let endpoint_parameters: any;\n\texport let username: string | null;\n\texport let current_language: \"python\" | \"javascript\" | \"bash\";\n\texport let api_description: string | null = null;\n\n\tlet python_code: HTMLElement;\n\tlet js_code: HTMLElement;\n\tlet bash_post_code: HTMLElement;\n\tlet bash_get_code: HTMLElement;\n\n\tlet has_file_path = endpoint_parameters.some((param: EndpointParameter) =>\n\t\tis_potentially_nested_file_data(param.example_input)\n\t);\n\tlet blob_components = [\"Audio\", \"File\", \"Image\", \"Video\"];\n\tlet blob_examples: any[] = endpoint_parameters.filter(\n\t\t(param: EndpointParameter) => blob_components.includes(param.component)\n\t);\n\n\t$: normalised_api_prefix = api_prefix ? api_prefix : \"/\";\n\t$: normalised_root = root.replace(/\\/$/, \"\");\n</script>\n\n<div class=\"container\">\n\t<EndpointDetail\n\t\tapi_name={dependency.api_name}\n\t\tdescription={api_description}\n\t/>\n\t{#if current_language === \"python\"}\n\t\t<Block>\n\t\t\t<code>\n\t\t\t\t<div class=\"copy\">\n\t\t\t\t\t<CopyButton code={python_code?.innerText} />\n\t\t\t\t</div>\n\t\t\t\t<div bind:this={python_code}>\n\t\t\t\t\t<pre><span class=\"highlight\">from</span> gradio_client <span\n\t\t\t\t\t\t\tclass=\"highlight\">import</span\n\t\t\t\t\t\t> Client{#if has_file_path}, handle_file{/if}\n\nclient = Client(<span class=\"token string\">\"{space_id || root}\"</span\n\t\t\t\t\t\t>{#if username !== null}, auth=(\"{username}\", **password**){/if})\nresult = client.<span class=\"highlight\">predict</span\n\t\t\t\t\t\t>(<!--\n-->{#each endpoint_parameters as { python_type, example_input, parameter_name, parameter_has_default, parameter_default }, i}<!--\n        -->\n\t\t{parameter_name\n\t\t\t\t\t\t\t\t? parameter_name + \"=\"\n\t\t\t\t\t\t\t\t: \"\"}<span\n\t\t\t\t\t\t\t\t>{represent_value(\n\t\t\t\t\t\t\t\t\tparameter_has_default ? parameter_default : example_input,\n\t\t\t\t\t\t\t\t\tpython_type.type,\n\t\t\t\t\t\t\t\t\t\"py\"\n\t\t\t\t\t\t\t\t)}</span\n\t\t\t\t\t\t\t>,{/each}<!--\n\n\t\t-->\n\t\tapi_name=<span class=\"api-name\">\"/{dependency.api_name}\"</span><!--\n\t\t-->\n)\n<span class=\"highlight\">print</span>(result)</pre>\n\t\t\t\t</div>\n\t\t\t</code>\n\t\t</Block>\n\t{:else if current_language === \"javascript\"}\n\t\t<Block>\n\t\t\t<code>\n\t\t\t\t<div class=\"copy\">\n\t\t\t\t\t<CopyButton code={js_code?.innerText} />\n\t\t\t\t</div>\n\t\t\t\t<div bind:this={js_code}>\n\t\t\t\t\t<pre>import &lbrace; Client &rbrace; from \"@gradio/client\";\n{#each blob_examples as { component, example_input }, i}<!--\n-->\nconst response_{i} = await fetch(\"{example_input.url}\");\nconst example{component} = await response_{i}.blob();\n\t\t\t\t\t\t{/each}<!--\n-->\nconst client = await Client.connect(<span class=\"token string\"\n\t\t\t\t\t\t\t>\"{space_id || root}\"</span\n\t\t\t\t\t\t>{#if username !== null}, &lbrace;auth: [\"{username}\", **password**]&rbrace;{/if});\nconst result = await client.predict(<span class=\"api-name\"\n\t\t\t\t\t\t\t>\"/{dependency.api_name}\"</span\n\t\t\t\t\t\t>, &lbrace; <!--\n-->{#each endpoint_parameters as { label, parameter_name, type, python_type, component, example_input, serializer }, i}<!--\n\t\t-->{#if blob_components.includes(component)}<!--\n\t-->\n\t\t\t\t<span\n\t\t\t\t\t\t\t\t\tclass=\"example-inputs\"\n\t\t\t\t\t\t\t\t\t>{parameter_name}: example{component}</span\n\t\t\t\t\t\t\t\t>, <!--\n\t\t--><span class=\"desc\"><!--\n\t\t--></span\n\t\t\t\t\t\t\t\t><!--\n\t\t-->{:else}<!--\n\t-->\t\t\n\t\t<span class=\"example-inputs\"\n\t\t\t\t\t\t\t\t\t>{parameter_name}: {represent_value(\n\t\t\t\t\t\t\t\t\t\texample_input,\n\t\t\t\t\t\t\t\t\t\tpython_type.type,\n\t\t\t\t\t\t\t\t\t\t\"js\"\n\t\t\t\t\t\t\t\t\t)}</span\n\t\t\t\t\t\t\t\t>, <!--\n--><!--\n-->{/if}\n\t\t\t\t\t\t{/each}\n&rbrace;);\n\nconsole.log(result.data);\n</pre>\n\t\t\t\t</div>\n\t\t\t</code>\n\t\t</Block>\n\t{:else if current_language === \"bash\"}\n\t\t<Block>\n\t\t\t<code>\n\t\t\t\t<div class=\"copy\">\n\t\t\t\t\t<CopyButton code={bash_post_code?.innerText}></CopyButton>\n\t\t\t\t</div>\n\n\t\t\t\t<div bind:this={bash_post_code}>\n\t\t\t\t\t<pre>curl -X POST {normalised_root}{normalised_api_prefix}/call/{dependency.api_name} -s -H \"Content-Type: application/json\" -d '{\"{\"}\n  \"data\": [{#each endpoint_parameters as { label, parameter_name, type, python_type, component, example_input, serializer }, i}\n\t\t\t\t\t\t\t<!-- \n-->{represent_value(\n\t\t\t\t\t\t\t\texample_input,\n\t\t\t\t\t\t\t\tpython_type.type,\n\t\t\t\t\t\t\t\t\"bash\"\n\t\t\t\t\t\t\t)}{#if i < endpoint_parameters.length - 1},\n\t\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t{/each}\n]{\"}\"}' \\\n  | awk -F'\"' '{\"{\"} print $4{\"}\"}'  \\\n  | read EVENT_ID; curl -N {normalised_root}{normalised_api_prefix}/call/{dependency.api_name}/$EVENT_ID</pre>\n\t\t\t\t</div>\n\t\t\t</code>\n\t\t</Block>\n\t{/if}\n</div>\n\n<style>\n\tcode pre {\n\t\toverflow-x: auto;\n\t\tcolor: var(--body-text-color);\n\t\tfont-family: var(--font-mono);\n\t\ttab-size: 2;\n\t}\n\n\t.token.string {\n\t\tdisplay: contents;\n\t\tcolor: var(--color-accent-base);\n\t}\n\n\tcode {\n\t\tposition: relative;\n\t\tdisplay: block;\n\t}\n\n\t.copy {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tright: 0;\n\t\tmargin-top: -5px;\n\t\tmargin-right: -5px;\n\t}\n\n\t.container {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tgap: var(--spacing-xxl);\n\t\tmargin-top: var(--size-3);\n\t\tmargin-bottom: var(--size-3);\n\t}\n\n\t.desc {\n\t\tcolor: var(--body-text-color-subdued);\n\t}\n\n\t.api-name {\n\t\tcolor: var(--color-accent);\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport type { Dependency, Payload } from \"../types\";\n\timport CopyButton from \"./CopyButton.svelte\";\n\timport { Block } from \"@gradio/atoms\";\n\timport { represent_value } from \"./utils\";\n\timport { onMount, tick } from \"svelte\";\n\n\texport let dependencies: Dependency[];\n\texport let short_root: string;\n\texport let root: string;\n\texport let api_prefix = \"\";\n\texport let current_language: \"python\" | \"javascript\" | \"bash\" | \"mcp\";\n\texport let username: string | null;\n\n\tlet python_code: HTMLElement;\n\tlet python_code_text: string;\n\tlet js_code: HTMLElement;\n\tlet bash_code: HTMLElement;\n\n\texport let api_calls: Payload[] = [];\n\n\tasync function get_info(): Promise<{\n\t\tnamed_endpoints: any;\n\t\tunnamed_endpoints: any;\n\t}> {\n\t\tlet response = await fetch(\n\t\t\troot.replace(/\\/$/, \"\") + api_prefix + \"/info/?all_endpoints=true\"\n\t\t);\n\t\tlet data = await response.json();\n\t\treturn data;\n\t}\n\n\tlet endpoints_info: any;\n\tlet py_zipped: { call: string; api_name: string }[] = [];\n\tlet js_zipped: { call: string; api_name: string }[] = [];\n\tlet bash_zipped: { call: string; api_name: string }[] = [];\n\n\tfunction format_api_call(call: Payload, lang: \"py\" | \"js\" | \"bash\"): string {\n\t\tconst api_name = `/${dependencies[call.fn_index].api_name}`;\n\t\t// If an input is undefined (distinct from null) then it corresponds to a State component.\n\t\tlet call_data_excluding_state = call.data.filter(\n\t\t\t(d) => typeof d !== \"undefined\"\n\t\t);\n\n\t\tconst params = call_data_excluding_state\n\t\t\t.map((param, index) => {\n\t\t\t\tif (endpoints_info[api_name]) {\n\t\t\t\t\tconst param_info = endpoints_info[api_name].parameters[index];\n\t\t\t\t\tif (!param_info) {\n\t\t\t\t\t\treturn undefined;\n\t\t\t\t\t}\n\t\t\t\t\tconst param_name = param_info.parameter_name;\n\t\t\t\t\tconst python_type = param_info.python_type.type;\n\t\t\t\t\tif (lang === \"py\") {\n\t\t\t\t\t\treturn `  ${param_name}=${represent_value(\n\t\t\t\t\t\t\tparam as string,\n\t\t\t\t\t\t\tpython_type,\n\t\t\t\t\t\t\t\"py\"\n\t\t\t\t\t\t)}`;\n\t\t\t\t\t} else if (lang === \"js\") {\n\t\t\t\t\t\treturn `    ${param_name}: ${represent_value(\n\t\t\t\t\t\t\tparam as string,\n\t\t\t\t\t\t\tpython_type,\n\t\t\t\t\t\t\t\"js\"\n\t\t\t\t\t\t)}`;\n\t\t\t\t\t} else if (lang === \"bash\") {\n\t\t\t\t\t\treturn `    ${represent_value(\n\t\t\t\t\t\t\tparam as string,\n\t\t\t\t\t\t\tpython_type,\n\t\t\t\t\t\t\t\"bash\"\n\t\t\t\t\t\t)}`;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn `  ${represent_value(param as string, undefined, lang)}`;\n\t\t\t})\n\t\t\t.filter((d) => typeof d !== \"undefined\")\n\t\t\t.join(\",\\n\");\n\t\tif (params) {\n\t\t\tif (lang === \"py\") {\n\t\t\t\treturn `${params},\\n`;\n\t\t\t} else if (lang === \"js\") {\n\t\t\t\treturn `{\\n${params},\\n}`;\n\t\t\t} else if (lang === \"bash\") {\n\t\t\t\treturn `\\n${params}\\n`;\n\t\t\t}\n\t\t}\n\t\tif (lang === \"py\") {\n\t\t\treturn \"\";\n\t\t}\n\t\treturn \"\\n\";\n\t}\n\n\tonMount(async () => {\n\t\tconst data = await get_info();\n\t\tendpoints_info = data[\"named_endpoints\"];\n\t\tlet py_api_calls: string[] = api_calls.map((call) =>\n\t\t\tformat_api_call(call, \"py\")\n\t\t);\n\t\tlet js_api_calls: string[] = api_calls.map((call) =>\n\t\t\tformat_api_call(call, \"js\")\n\t\t);\n\t\tlet bash_api_calls: string[] = api_calls.map((call) =>\n\t\t\tformat_api_call(call, \"bash\")\n\t\t);\n\t\tlet api_names: string[] = api_calls.map(\n\t\t\t(call) => dependencies[call.fn_index].api_name || \"\"\n\t\t);\n\t\tpy_zipped = py_api_calls.map((call, index) => ({\n\t\t\tcall,\n\t\t\tapi_name: api_names[index]\n\t\t}));\n\t\tjs_zipped = js_api_calls.map((call, index) => ({\n\t\t\tcall,\n\t\t\tapi_name: api_names[index]\n\t\t}));\n\t\tbash_zipped = bash_api_calls.map((call, index) => ({\n\t\t\tcall,\n\t\t\tapi_name: api_names[index]\n\t\t}));\n\n\t\tawait tick();\n\n\t\tpython_code_text = python_code.innerText;\n\t});\n</script>\n\n<div class=\"container\">\n\t<!-- <EndpointDetail {named} api_name={dependency.api_name} /> -->\n\t<Block border_mode={\"focus\"}>\n\t\t{#if current_language === \"python\"}\n\t\t\t<code>\n\t\t\t\t<div class=\"copy\">\n\t\t\t\t\t<CopyButton code={python_code_text} />\n\t\t\t\t</div>\n\t\t\t\t<div bind:this={python_code}>\n\t\t\t\t\t<pre><span class=\"highlight\">from</span> gradio_client <span\n\t\t\t\t\t\t\tclass=\"highlight\">import</span\n\t\t\t\t\t\t> Client, file\n\nclient = Client(<span class=\"token string\">\"{short_root}\"</span\n\t\t\t\t\t\t>{#if username !== null}, auth=(\"{username}\", **password**){/if})\n{#each py_zipped as { call, api_name }}<!--\n-->\nclient.<span class=\"highlight\"\n\t\t\t\t\t\t\t\t>predict(\n{call}  api_name=<span class=\"api-name\">\"/{api_name}\"</span>\n)\n</span>{/each}</pre>\n\t\t\t\t</div>\n\t\t\t</code>\n\t\t{:else if current_language === \"javascript\"}\n\t\t\t<code>\n\t\t\t\t<div class=\"copy\">\n\t\t\t\t\t<CopyButton code={js_code?.innerText} />\n\t\t\t\t</div>\n\t\t\t\t<div bind:this={js_code}>\n\t\t\t\t\t<pre>import &lbrace; Client &rbrace; from \"@gradio/client\";\n\nconst app = await Client.connect(<span class=\"token string\">\"{short_root}\"</span\n\t\t\t\t\t\t>{#if username !== null}, &lbrace;auth: [\"{username}\", **password**]&rbrace;{/if});\n\t\t\t\t\t{#each js_zipped as { call, api_name }}<!--\n\t\t\t\t\t-->\nawait client.predict(<span\n\t\t\t\t\t\t\t\tclass=\"api-name\">\n  \"/{api_name}\"</span\n\t\t\t\t\t\t\t>{#if call}, {call}{/if});\n\t\t\t\t\t\t{/each}</pre>\n\t\t\t\t</div>\n\t\t\t</code>\n\t\t{:else if current_language === \"bash\"}\n\t\t\t<code>\n\t\t\t\t<div class=\"copy\">\n\t\t\t\t\t<CopyButton code={bash_code?.innerText} />\n\t\t\t\t</div>\n\t\t\t\t<div bind:this={bash_code}>\n\t\t\t\t\t{#each bash_zipped as { call, api_name }}\n\t\t\t\t\t\t<pre>curl -X POST {short_root}call/{api_name} -s -H \"Content-Type: application/json\" -d '{\"{\"} \n\t\"data\": [{call}]{\"}\"}' \\\n  | awk -F'\"' '{\"{\"} print $4{\"}\"}' \\\n  | read EVENT_ID; curl -N {short_root}call/{api_name}/$EVENT_ID</pre>\n\t\t\t\t\t\t<br />\n\t\t\t\t\t{/each}\n\t\t\t\t</div>\n\t\t\t</code>\n\t\t{/if}\n\t</Block>\n</div>\n\n<style>\n\tcode pre {\n\t\toverflow-x: auto;\n\t\tcolor: var(--body-text-color);\n\t\tfont-family: var(--font-mono);\n\t\ttab-size: 2;\n\t}\n\n\t.token.string {\n\t\tdisplay: contents;\n\t\tcolor: var(--color-accent-base);\n\t}\n\n\tcode {\n\t\tposition: relative;\n\t\tdisplay: block;\n\t}\n\n\t.copy {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tright: 0;\n\t\tmargin-top: -5px;\n\t\tmargin-right: -5px;\n\t}\n\n\t.container {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tgap: var(--spacing-xxl);\n\t\tmargin-top: var(--size-3);\n\t\tmargin-bottom: var(--size-3);\n\t}\n\n\t.api-name {\n\t\tcolor: var(--color-accent);\n\t}\n</style>\n", "export default \"data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20aria-hidden='true'%20focusable='false'%20role='img'%20width='1em'%20height='1em'%20preserveAspectRatio='xMidYMid%20meet'%20viewBox='0%200%2032%2032'%20%3e%3cpath%20d='M15.84.5a16.4,16.4,0,0,0-3.57.32C9.1,1.39,8.53,2.53,8.53,4.64V7.48H16v1H5.77a4.73,4.73,0,0,0-4.7,3.74,14.82,14.82,0,0,0,0,7.54c.57,2.28,1.86,3.82,4,3.82h2.6V20.14a4.73,4.73,0,0,1,4.63-4.63h7.38a3.72,3.72,0,0,0,3.73-3.73V4.64A4.16,4.16,0,0,0,19.65.82,20.49,20.49,0,0,0,15.84.5ZM11.78,2.77a1.39,1.39,0,0,1,1.38,1.46,1.37,1.37,0,0,1-1.38,1.38A1.42,1.42,0,0,1,10.4,4.23,1.44,1.44,0,0,1,11.78,2.77Z'%20fill='%235a9fd4'%20%3e%3c/path%3e%3cpath%20d='M16.16,31.5a16.4,16.4,0,0,0,3.57-.32c3.17-.57,3.74-1.71,3.74-3.82V24.52H16v-1H26.23a4.73,4.73,0,0,0,4.7-3.74,14.82,14.82,0,0,0,0-7.54c-.57-2.28-1.86-3.82-4-3.82h-2.6v3.41a4.73,4.73,0,0,1-4.63,4.63H12.35a3.72,3.72,0,0,0-3.73,3.73v7.14a4.16,4.16,0,0,0,3.73,3.82A20.49,20.49,0,0,0,16.16,31.5Zm4.06-2.27a1.39,1.39,0,0,1-1.38-1.46,1.37,1.37,0,0,1,1.38-1.38,1.42,1.42,0,0,1,1.38,1.38A1.44,1.44,0,0,1,20.22,29.23Z'%20fill='%23ffd43b'%20%3e%3c/path%3e%3c/svg%3e\"", "export default \"data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20aria-hidden='true'%20focusable='false'%20role='img'%20width='1em'%20height='1em'%20preserveAspectRatio='xMidYMid%20meet'%20viewBox='0%200%2032%2032'%20%3e%3crect%20width='32'%20height='32'%20fill='%23f7df1e'%3e%3c/rect%3e%3cpath%20d='M21.5,25a3.27,3.27,0,0,0,3,1.83c1.25,0,2-.63,2-1.49,0-1-.81-1.39-2.19-2L23.56,23C21.39,22.1,20,20.94,20,18.49c0-2.25,1.72-4,4.41-4a4.44,4.44,0,0,1,4.27,2.41l-2.34,1.5a2,2,0,0,0-1.93-1.29,1.31,1.31,0,0,0-1.44,1.29c0,.9.56,1.27,1.85,1.83l.75.32c2.55,1.1,4,2.21,4,4.72,0,2.71-2.12,4.19-5,4.19a5.78,5.78,0,0,1-5.48-3.07Zm-10.63.26c.48.84.91,1.55,1.94,1.55s1.61-.39,1.61-1.89V14.69h3V25c0,3.11-1.83,4.53-4.49,4.53a4.66,4.66,0,0,1-4.51-2.75Z'%20%3e%3c/path%3e%3c/svg%3e\"", "export default \"data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20version='1.1'%20id='Layer_1'%20x='0px'%20y='0px'%20viewBox='0%200%20150%20150'%20style='enable-background:new%200%200%20150%20150;%20background-color:%20%2372a824;'%20xml:space='preserve'%3e%3cscript%20xmlns=''/%3e%3cstyle%20type='text/css'%3e%20.st0{fill:%23FFFFFF;}%20%3c/style%3e%3cg%3e%3cpath%20class='st0'%20d='M118.9,40.3L81.7,18.2c-2.2-1.3-4.7-2-7.2-2s-5,0.7-7.2,2L30.1,40.3c-4.4,2.6-7.2,7.5-7.2,12.8v44.2%20c0,5.3,2.7,10.1,7.2,12.8l37.2,22.1c2.2,1.3,4.7,2,7.2,2c2.5,0,5-0.7,7.2-2l37.2-22.1c4.4-2.6,7.2-7.5,7.2-12.8V53%20C126.1,47.8,123.4,42.9,118.9,40.3z%20M90.1,109.3l0.1,3.2c0,0.4-0.2,0.8-0.5,1l-1.9,1.1c-0.3,0.2-0.5,0-0.6-0.4l0-3.1%20c-1.6,0.7-3.2,0.8-4.3,0.4c-0.2-0.1-0.3-0.4-0.2-0.7l0.7-2.9c0.1-0.2,0.2-0.5,0.3-0.6c0.1-0.1,0.1-0.1,0.2-0.1%20c0.1-0.1,0.2-0.1,0.3,0c1.1,0.4,2.6,0.2,3.9-0.5c1.8-0.9,2.9-2.7,2.9-4.5c0-1.6-0.9-2.3-3-2.3c-2.7,0-5.2-0.5-5.3-4.5%20c0-3.3,1.7-6.7,4.4-8.8l0-3.2c0-0.4,0.2-0.8,0.5-1l1.8-1.2c0.3-0.2,0.5,0,0.6,0.4l0,3.2c1.3-0.5,2.5-0.7,3.6-0.4%20c0.2,0.1,0.3,0.4,0.2,0.7l-0.7,2.8c-0.1,0.2-0.2,0.4-0.3,0.6c-0.1,0.1-0.1,0.1-0.2,0.1c-0.1,0-0.2,0.1-0.3,0%20c-0.5-0.1-1.6-0.4-3.4,0.6c-1.9,1-2.6,2.6-2.5,3.8c0,1.5,0.8,1.9,3.3,1.9c3.4,0.1,4.9,1.6,5,5C94.7,103.4,92.9,107,90.1,109.3z%20M109.6,103.9c0,0.3,0,0.6-0.3,0.7l-9.4,5.7c-0.2,0.1-0.4,0-0.4-0.3v-2.4c0-0.3,0.2-0.5,0.4-0.6l9.3-5.5c0.2-0.1,0.4,0,0.4,0.3%20V103.9z%20M116.1,49.6L80.9,71.3c-4.4,2.6-7.6,5.4-7.6,10.7v43.4c0,3.2,1.3,5.2,3.2,5.8c-0.6,0.1-1.3,0.2-2,0.2%20c-2.1,0-4.1-0.6-5.9-1.6l-37.2-22.1c-3.6-2.2-5.9-6.2-5.9-10.5V53c0-4.3,2.3-8.4,5.9-10.5l37.2-22.1c1.8-1.1,3.8-1.6,5.9-1.6%20s4.1,0.6,5.9,1.6l37.2,22.1c3.1,1.8,5.1,5,5.7,8.5C122.1,48.4,119.3,47.7,116.1,49.6z'/%3e%3c/g%3e%3c/svg%3e\"", "<script lang=\"ts\">\n\timport { Loader } from \"@gradio/statustracker\";\n\n\texport let is_running: boolean;\n\texport let endpoint_returns: any;\n\texport let js_returns: any;\n\texport let current_language: \"python\" | \"javascript\" | \"bash\";\n</script>\n\n<h4>\n\t<div class=\"toggle-icon\">\n\t\t<div class=\"toggle-dot toggle-right\" />\n\t</div>\n\tReturns {#if endpoint_returns.length > 1}\n\t\t{current_language == \"python\" ? \"tuple\" : \"list\"} of {endpoint_returns.length}\n\t\telements{:else}\n\t\t1 element{/if}\n</h4>\n\n<div class:hide={is_running}>\n\t{#each endpoint_returns as { label, type, python_type, component, serializer }, i}\n\t\t<hr class=\"hr\" />\n\t\t<div style=\"margin:10px;\">\n\t\t\t<p>\n\t\t\t\t{#if endpoint_returns.length > 1}\n\t\t\t\t\t<span class=\"code\">[{i}]</span>\n\t\t\t\t{/if}\n\t\t\t\t<span class=\"code highlight\"\n\t\t\t\t\t>{#if current_language === \"python\"}{python_type.type}{:else}{js_returns[\n\t\t\t\t\t\t\ti\n\t\t\t\t\t\t].type}{/if}</span\n\t\t\t\t>\n\t\t\t</p>\n\t\t\t<p class=\"desc\">\n\t\t\t\tThe output value that appears in the \"{label}\" <!--\n\t-->{component}\n\t\t\t\tcomponent<!--\n\t-->.\n\t\t\t</p>\n\t\t</div>\n\t{/each}\n</div>\n{#if is_running}\n\t<div class=\"load-wrap\">\n\t\t<Loader margin={false} />\n\t</div>\n{/if}\n\n<style>\n\t.hr {\n\t\tborder: 0;\n\t\theight: 1px;\n\t\tbackground: var(--color-accent-soft);\n\t}\n\t.code {\n\t\tfont-family: var(--font-mono);\n\t\tmargin-right: 10px;\n\t}\n\n\t.highlight {\n\t\tbackground: var(--color-accent-soft);\n\t\tcolor: var(--color-accent);\n\t\tpadding: var(--size-1);\n\t}\n\n\t.desc {\n\t\tcolor: var(--body-text-color-subdued);\n\t\tfont-size: var(--text-lg);\n\t}\n\n\th4 {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tmargin-top: var(--size-6);\n\t\tmargin-bottom: var(--size-3);\n\t\tcolor: var(--body-text-color);\n\t\tfont-weight: var(--weight-bold);\n\t}\n\n\t.toggle-icon {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tmargin-right: var(--size-2);\n\t\tborder-radius: var(--radius-full);\n\t\tbackground: var(--color-grey-300);\n\t\twidth: 12px;\n\t\theight: 4px;\n\t}\n\n\t.toggle-dot {\n\t\tborder-radius: var(--radius-full);\n\t\tbackground: var(--color-grey-700);\n\t\twidth: 6px;\n\t\theight: 6px;\n\t\tmargin-left: auto;\n\t}\n</style>\n", "export default \"__VITE_ASSET__CKRBDa1e__\"", "<script lang=\"ts\">\n\t/* eslint-disable */\n\timport { onMount, createEventDispatcher } from \"svelte\";\n\timport type { ComponentMeta, Dependency } from \"../types\";\n\timport NoApi from \"./NoApi.svelte\";\n\timport type { Client } from \"@gradio/client\";\n\timport type { Payload } from \"../types\";\n\n\timport ApiBanner from \"./ApiBanner.svelte\";\n\timport { BaseButton as Button } from \"@gradio/button\";\n\timport ParametersSnippet from \"./ParametersSnippet.svelte\";\n\timport InstallSnippet from \"./InstallSnippet.svelte\";\n\timport CodeSnippet from \"./CodeSnippet.svelte\";\n\timport RecordingSnippet from \"./RecordingSnippet.svelte\";\n\timport CopyButton from \"./CopyButton.svelte\";\n\timport { Block } from \"@gradio/atoms\";\n\n\timport python from \"./img/python.svg\";\n\timport javascript from \"./img/javascript.svg\";\n\timport bash from \"./img/bash.svg\";\n\timport ResponseSnippet from \"./ResponseSnippet.svelte\";\n\timport mcp from \"./img/mcp.svg\";\n\n\texport let dependencies: Dependency[];\n\texport let root: string;\n\texport let app: Awaited<ReturnType<typeof Client.connect>>;\n\texport let space_id: string | null;\n\texport let root_node: ComponentMeta;\n\texport let username: string | null;\n\n\tconst js_docs =\n\t\t\"https://www.gradio.app/guides/getting-started-with-the-js-client\";\n\tconst py_docs =\n\t\t\"https://www.gradio.app/guides/getting-started-with-the-python-client\";\n\tconst bash_docs =\n\t\t\"https://www.gradio.app/guides/querying-gradio-apps-with-curl\";\n\tconst spaces_docs_suffix = \"#connecting-to-a-hugging-face-space\";\n\tconst mcp_docs =\n\t\t\"https://www.gradio.app/guides/building-mcp-server-with-gradio\";\n\n\tlet api_count = dependencies.filter(\n\t\t(dependency) => dependency.show_api\n\t).length;\n\n\tif (root === \"\") {\n\t\troot = location.protocol + \"//\" + location.host + location.pathname;\n\t}\n\tif (!root.endsWith(\"/\")) {\n\t\troot += \"/\";\n\t}\n\n\texport let api_calls: Payload[] = [];\n\tlet current_language: \"python\" | \"javascript\" | \"bash\" | \"mcp\" = \"python\";\n\n\tfunction set_query_param(key: string, value: string) {\n\t\tconst url = new URL(window.location.href);\n\t\turl.searchParams.set(key, value);\n\t\thistory.replaceState(null, \"\", url.toString());\n\t}\n\n\tfunction get_query_param(key: string): string | null {\n\t\tconst url = new URL(window.location.href);\n\t\treturn url.searchParams.get(key);\n\t}\n\n\tfunction is_valid_language(lang: string | null): boolean {\n\t\treturn [\"python\", \"javascript\", \"bash\", \"mcp\"].includes(lang ?? \"\");\n\t}\n\n\tconst langs = [\n\t\t[\"python\", \"Python\", python],\n\t\t[\"javascript\", \"JavaScript\", javascript],\n\t\t[\"bash\", \"cURL\", bash],\n\t\t[\"mcp\", \"MCP\", mcp]\n\t] as const;\n\n\tlet is_running = false;\n\tlet mcp_server_active = false;\n\n\tasync function get_info(): Promise<{\n\t\tnamed_endpoints: any;\n\t\tunnamed_endpoints: any;\n\t}> {\n\t\tlet response = await fetch(\n\t\t\troot.replace(/\\/$/, \"\") + app.api_prefix + \"/info\"\n\t\t);\n\t\tlet data = await response.json();\n\t\treturn data;\n\t}\n\tasync function get_js_info(): Promise<Record<string, any>> {\n\t\tlet js_api_info = await app.view_api();\n\t\treturn js_api_info;\n\t}\n\n\tlet info: {\n\t\tnamed_endpoints: any;\n\t\tunnamed_endpoints: any;\n\t};\n\n\tlet js_info: Record<string, any>;\n\n\tget_info().then((data) => {\n\t\tinfo = data;\n\t});\n\n\tget_js_info().then((js_api_info) => {\n\t\tjs_info = js_api_info;\n\t});\n\n\tconst dispatch = createEventDispatcher();\n\n\tconst mcp_server_url = `${root}gradio_api/mcp/sse`;\n\n\tinterface ToolParameter {\n\t\ttitle?: string;\n\t\ttype: string;\n\t\tdescription: string;\n\t\tformat?: string;\n\t\tdefault?: any;\n\t}\n\n\tinterface Tool {\n\t\tname: string;\n\t\tdescription: string;\n\t\tparameters: Record<string, ToolParameter>;\n\t\texpanded?: boolean;\n\t}\n\n\tlet tools: Tool[] = [];\n\tlet headers: string[] = [];\n\tlet mcp_json_sse: any;\n\tlet mcp_json_stdio: any;\n\tlet file_data_present = false;\n\n\tconst upload_file_mcp_server = {\n\t\tcommand: \"uvx\",\n\t\targs: [\n\t\t\t\"--from\",\n\t\t\t\"gradio[mcp]\",\n\t\t\t\"gradio\",\n\t\t\t\"upload-mcp\",\n\t\t\troot,\n\t\t\t\"<UPLOAD_DIRECTORY>\"\n\t\t]\n\t};\n\n\tasync function fetchMcpTools() {\n\t\ttry {\n\t\t\tconst response = await fetch(`${root}gradio_api/mcp/schema`);\n\t\t\tconst schema = await response.json();\n\t\t\tfile_data_present = schema\n\t\t\t\t.map((tool: any) => tool.meta?.file_data_present)\n\t\t\t\t.some((present: boolean) => present);\n\n\t\t\ttools = schema.map((tool: any) => ({\n\t\t\t\tname: tool.name,\n\t\t\t\tdescription: tool.description || \"\",\n\t\t\t\tparameters: tool.inputSchema?.properties || {},\n\t\t\t\texpanded: false\n\t\t\t}));\n\t\t\theaders = schema.map((tool: any) => tool.meta?.headers || []).flat();\n\t\t\tif (headers.length > 0) {\n\t\t\t\tmcp_json_sse = {\n\t\t\t\t\tmcpServers: {\n\t\t\t\t\t\tgradio: {\n\t\t\t\t\t\t\turl: mcp_server_url,\n\t\t\t\t\t\t\theaders: headers.reduce((accumulator, current_key) => {\n\t\t\t\t\t\t\t\t// @ts-ignore\n\t\t\t\t\t\t\t\taccumulator[current_key] = \"<YOUR_HEADER_VALUE>\";\n\t\t\t\t\t\t\t\treturn accumulator;\n\t\t\t\t\t\t\t}, {})\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t};\n\t\t\t\tmcp_json_stdio = {\n\t\t\t\t\tmcpServers: {\n\t\t\t\t\t\tgradio: {\n\t\t\t\t\t\t\tcommand: \"npx\",\n\t\t\t\t\t\t\targs: [\n\t\t\t\t\t\t\t\t\"mcp-remote\",\n\t\t\t\t\t\t\t\tmcp_server_url,\n\t\t\t\t\t\t\t\t\"--transport\",\n\t\t\t\t\t\t\t\t\"sse-only\",\n\t\t\t\t\t\t\t\t...headers\n\t\t\t\t\t\t\t\t\t.map((header) => [\n\t\t\t\t\t\t\t\t\t\t\"--header\",\n\t\t\t\t\t\t\t\t\t\t`${header}: <YOUR_HEADER_VALUE>`\n\t\t\t\t\t\t\t\t\t])\n\t\t\t\t\t\t\t\t\t.flat()\n\t\t\t\t\t\t\t]\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t};\n\t\t\t} else {\n\t\t\t\tmcp_json_sse = {\n\t\t\t\t\tmcpServers: {\n\t\t\t\t\t\tgradio: {\n\t\t\t\t\t\t\turl: mcp_server_url\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t};\n\t\t\t\tmcp_json_stdio = {\n\t\t\t\t\tmcpServers: {\n\t\t\t\t\t\tgradio: {\n\t\t\t\t\t\t\tcommand: \"npx\",\n\t\t\t\t\t\t\targs: [\"mcp-remote\", mcp_server_url, \"--transport\", \"sse-only\"]\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t};\n\t\t\t\tif (file_data_present) {\n\t\t\t\t\tmcp_json_sse.mcpServers.upload_files_to_gradio =\n\t\t\t\t\t\tupload_file_mcp_server;\n\t\t\t\t\tmcp_json_stdio.mcpServers.upload_files_to_gradio =\n\t\t\t\t\t\tupload_file_mcp_server;\n\t\t\t\t}\n\t\t\t}\n\t\t} catch (error) {\n\t\t\tconsole.error(\"Failed to fetch MCP tools:\", error);\n\t\t\ttools = [];\n\t\t}\n\t}\n\n\tonMount(() => {\n\t\tdocument.body.style.overflow = \"hidden\";\n\t\tif (\"parentIFrame\" in window) {\n\t\t\twindow.parentIFrame?.scrollTo(0, 0);\n\t\t}\n\n\t\tconst lang_param = get_query_param(\"lang\");\n\t\tif (is_valid_language(lang_param)) {\n\t\t\tcurrent_language = lang_param as \"python\" | \"javascript\" | \"bash\" | \"mcp\";\n\t\t}\n\n\t\t// Check MCP server status and fetch tools if active\n\t\tfetch(mcp_server_url)\n\t\t\t.then((response) => {\n\t\t\t\tmcp_server_active = response.ok;\n\t\t\t\tif (mcp_server_active) {\n\t\t\t\t\tfetchMcpTools();\n\t\t\t\t\tif (!is_valid_language(lang_param)) {\n\t\t\t\t\t\tcurrent_language = \"mcp\";\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tif (!is_valid_language(lang_param)) {\n\t\t\t\t\t\tcurrent_language = \"python\";\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t})\n\t\t\t.catch(() => {\n\t\t\t\tmcp_server_active = false;\n\t\t\t});\n\n\t\treturn () => {\n\t\t\tdocument.body.style.overflow = \"auto\";\n\t\t};\n\t});\n</script>\n\n{#if info}\n\t{#if api_count}\n\t\t<div class=\"banner-wrap\">\n\t\t\t<ApiBanner\n\t\t\t\ton:close\n\t\t\t\troot={space_id || root}\n\t\t\t\t{api_count}\n\t\t\t\t{current_language}\n\t\t\t/>\n\t\t</div>\n\n\t\t<div class=\"docs-wrap\">\n\t\t\t<div class=\"client-doc\">\n\t\t\t\t<p style=\"font-size: var(--text-lg);\">\n\t\t\t\t\tChoose one of the following ways to interact with the API.\n\t\t\t\t</p>\n\t\t\t</div>\n\t\t\t<div class=\"endpoint\">\n\t\t\t\t<div class=\"snippets\">\n\t\t\t\t\t{#each langs as [language, display_name, img]}\n\t\t\t\t\t\t<li\n\t\t\t\t\t\t\tclass=\"snippet\n\t\t\t\t\t\t{current_language === language ? 'current-lang' : 'inactive-lang'}\"\n\t\t\t\t\t\t\ton:click={() => {\n\t\t\t\t\t\t\t\tcurrent_language = language;\n\t\t\t\t\t\t\t\tset_query_param(\"lang\", language);\n\t\t\t\t\t\t\t}}\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<img src={img} alt=\"\" />\n\t\t\t\t\t\t\t{display_name}\n\t\t\t\t\t\t</li>\n\t\t\t\t\t{/each}\n\t\t\t\t</div>\n\t\t\t\t{#if api_calls.length}\n\t\t\t\t\t<div>\n\t\t\t\t\t\t<p\n\t\t\t\t\t\t\tid=\"num-recorded-api-calls\"\n\t\t\t\t\t\t\tstyle=\"font-size: var(--text-lg); font-weight:bold; margin: 10px 0px;\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t🪄 Recorded API Calls <span class=\"api-count\"\n\t\t\t\t\t\t\t\t>[{api_calls.length}]</span\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t</p>\n\t\t\t\t\t\t<p>\n\t\t\t\t\t\t\tHere is the code snippet to replay the most recently recorded API\n\t\t\t\t\t\t\tcalls using the {current_language}\n\t\t\t\t\t\t\tclient.\n\t\t\t\t\t\t</p>\n\n\t\t\t\t\t\t<RecordingSnippet\n\t\t\t\t\t\t\t{current_language}\n\t\t\t\t\t\t\t{api_calls}\n\t\t\t\t\t\t\t{dependencies}\n\t\t\t\t\t\t\t{root}\n\t\t\t\t\t\t\tapi_prefix={app.api_prefix}\n\t\t\t\t\t\t\tshort_root={space_id || root}\n\t\t\t\t\t\t\t{username}\n\t\t\t\t\t\t/>\n\t\t\t\t\t\t<p>\n\t\t\t\t\t\t\tNote: Some API calls only affect the UI, so when using the\n\t\t\t\t\t\t\tclients, the desired result may be achieved with only a subset of\n\t\t\t\t\t\t\tthe recorded calls.\n\t\t\t\t\t\t</p>\n\t\t\t\t\t</div>\n\t\t\t\t\t<p\n\t\t\t\t\t\tstyle=\"font-size: var(--text-lg); font-weight:bold; margin: 30px 0px 10px;\"\n\t\t\t\t\t>\n\t\t\t\t\t\tAPI Documentation\n\t\t\t\t\t</p>\n\t\t\t\t{:else}\n\t\t\t\t\t<p class=\"padded\">\n\t\t\t\t\t\t{#if current_language == \"python\" || current_language == \"javascript\"}\n\t\t\t\t\t\t\t1. Install the\n\t\t\t\t\t\t\t<span style=\"text-transform:capitalize\">{current_language}</span>\n\t\t\t\t\t\t\tclient (<a\n\t\t\t\t\t\t\t\thref={current_language == \"python\" ? py_docs : js_docs}\n\t\t\t\t\t\t\t\ttarget=\"_blank\">docs</a\n\t\t\t\t\t\t\t>) if you don't already have it installed.\n\t\t\t\t\t\t{:else if current_language == \"mcp\"}\n\t\t\t\t\t\t\t{#if mcp_server_active}\n\t\t\t\t\t\t\t\t<Block>\n\t\t\t\t\t\t\t\t\t<div class=\"mcp-url\">\n\t\t\t\t\t\t\t\t\t\t<label\n\t\t\t\t\t\t\t\t\t\t\t><span class=\"status-indicator active\">●</span>MCP Server\n\t\t\t\t\t\t\t\t\t\t\tURL (SSE)</label\n\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t<div class=\"textbox\">\n\t\t\t\t\t\t\t\t\t\t\t<input type=\"text\" readonly value={mcp_server_url} />\n\t\t\t\t\t\t\t\t\t\t\t<CopyButton code={mcp_server_url} />\n\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t</Block>\n\t\t\t\t\t\t\t\t<p>&nbsp;</p>\n\t\t\t\t\t\t\t\t<strong>Available MCP Tools</strong>\n\t\t\t\t\t\t\t\t<div class=\"mcp-tools\">\n\t\t\t\t\t\t\t\t\t{#each tools as tool}\n\t\t\t\t\t\t\t\t\t\t<div class=\"tool-item\">\n\t\t\t\t\t\t\t\t\t\t\t<button\n\t\t\t\t\t\t\t\t\t\t\t\tclass=\"tool-header\"\n\t\t\t\t\t\t\t\t\t\t\t\ton:click={() => (tool.expanded = !tool.expanded)}\n\t\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t\t<span\n\t\t\t\t\t\t\t\t\t\t\t\t\t><span class=\"tool-name\">{tool.name}</span> &nbsp;\n\t\t\t\t\t\t\t\t\t\t\t\t\t<span class=\"tool-description\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t>{tool.description\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t? tool.description\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t: \"⚠︎ No description provided in function docstring\"}</span\n\t\t\t\t\t\t\t\t\t\t\t\t\t></span\n\t\t\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t\t<span class=\"tool-arrow\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t>{tool.expanded ? \"▼\" : \"▶\"}</span\n\t\t\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t</button>\n\t\t\t\t\t\t\t\t\t\t\t{#if tool.expanded}\n\t\t\t\t\t\t\t\t\t\t\t\t<div class=\"tool-content\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t{#if Object.keys(tool.parameters).length > 0}\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div class=\"tool-parameters\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{#each Object.entries(tool.parameters) as [name, param]}\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div class=\"parameter\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<code>{name}</code>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<span class=\"parameter-type\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t({param.type}{param.default !== undefined\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t? `, default: ${JSON.stringify(param.default)}`\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t: \"\"})\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</span>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<p class=\"parameter-description\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{param.description\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t? param.description\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t: \"⚠︎ No description for this parameter in function docstring\"}\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</p>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{/each}\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t\t\t\t{:else}\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<p>Takes no input parameters</p>\n\t\t\t\t\t\t\t\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t{/each}\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t<p>&nbsp;</p>\n\n\t\t\t\t\t\t\t\t<strong>SSE Transport</strong>: To add this MCP to clients that\n\t\t\t\t\t\t\t\tsupport SSE (e.g. Cursor, Windsurf, Cline), simply add the\n\t\t\t\t\t\t\t\tfollowing configuration to your MCP config.\n\t\t\t\t\t\t\t\t<p>&nbsp;</p>\n\t\t\t\t\t\t\t\t<Block>\n\t\t\t\t\t\t\t\t\t<code>\n\t\t\t\t\t\t\t\t\t\t<div class=\"copy\">\n\t\t\t\t\t\t\t\t\t\t\t<CopyButton\n\t\t\t\t\t\t\t\t\t\t\t\tcode={JSON.stringify(mcp_json_sse, null, 2)}\n\t\t\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t<div>\n\t\t\t\t\t\t\t\t\t\t\t<pre>{JSON.stringify(mcp_json_sse, null, 2)}</pre>\n\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t</code>\n\t\t\t\t\t\t\t\t</Block>\n\t\t\t\t\t\t\t\t{#if file_data_present}\n\t\t\t\t\t\t\t\t\t<p>&nbsp;</p>\n\t\t\t\t\t\t\t\t\t<em>Note about files</em>: Gradio MCP servers that have files\n\t\t\t\t\t\t\t\t\tas inputs need the files as URLs, so the\n\t\t\t\t\t\t\t\t\t<code>upload_files_to_gradio</code>\n\t\t\t\t\t\t\t\t\ttool is included for your convenience. This tool can upload files\n\t\t\t\t\t\t\t\t\tlocated in the specified <code>UPLOAD_DIRECTORY</code>\n\t\t\t\t\t\t\t\t\targument (an absolute path in your local machine) or any of its\n\t\t\t\t\t\t\t\t\tsubdirectories to the Gradio app. You can omit this tool if you\n\t\t\t\t\t\t\t\t\tare fine manually uploading files yourself and providing the URLs.\n\t\t\t\t\t\t\t\t\tBefore using this tool, you must have\n\t\t\t\t\t\t\t\t\t<a\n\t\t\t\t\t\t\t\t\t\thref=\"https://docs.astral.sh/uv/getting-started/installation/\"\n\t\t\t\t\t\t\t\t\t\ttarget=\"_blank\">uv installed</a\n\t\t\t\t\t\t\t\t\t>.\n\t\t\t\t\t\t\t\t\t<p>&nbsp;</p>\n\t\t\t\t\t\t\t\t{/if}\n\n\t\t\t\t\t\t\t\t<strong>STDIO Transport</strong>: For clients that only support\n\t\t\t\t\t\t\t\tstdio (e.g. Claude Desktop), first\n\t\t\t\t\t\t\t\t<a href=\"https://nodejs.org/en/download/\" target=\"_blank\"\n\t\t\t\t\t\t\t\t\t>install Node.js</a\n\t\t\t\t\t\t\t\t>. Then, you can use the following command:\n\t\t\t\t\t\t\t\t<p>&nbsp;</p>\n\t\t\t\t\t\t\t\t<Block>\n\t\t\t\t\t\t\t\t\t<code>\n\t\t\t\t\t\t\t\t\t\t<div class=\"copy\">\n\t\t\t\t\t\t\t\t\t\t\t<CopyButton\n\t\t\t\t\t\t\t\t\t\t\t\tcode={JSON.stringify(mcp_json_stdio, null, 2)}\n\t\t\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t<div>\n\t\t\t\t\t\t\t\t\t\t\t<pre>{JSON.stringify(mcp_json_stdio, null, 2)}</pre>\n\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t</code>\n\t\t\t\t\t\t\t\t</Block>\n\t\t\t\t\t\t\t\t<p>&nbsp;</p>\n\t\t\t\t\t\t\t\t<p>\n\t\t\t\t\t\t\t\t\t<a href={mcp_docs} target=\"_blank\">\n\t\t\t\t\t\t\t\t\t\tRead more about MCP in the Gradio docs\n\t\t\t\t\t\t\t\t\t</a>\n\t\t\t\t\t\t\t\t</p>\n\t\t\t\t\t\t\t{:else}\n\t\t\t\t\t\t\t\tThis Gradio app can also serve as an MCP server, with an MCP\n\t\t\t\t\t\t\t\ttool corresponding to each API endpoint. To enable this, launch\n\t\t\t\t\t\t\t\tthis Gradio app with <code>.launch(mcp_server=True)</code> or\n\t\t\t\t\t\t\t\tset the <code>GRADIO_MCP_SERVER</code> env variable to\n\t\t\t\t\t\t\t\t<code>\"True\"</code>.\n\t\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t{:else}\n\t\t\t\t\t\t\t1. Confirm that you have cURL installed on your system.\n\t\t\t\t\t\t{/if}\n\t\t\t\t\t</p>\n\n\t\t\t\t\t{#if current_language !== \"mcp\"}\n\t\t\t\t\t\t<InstallSnippet {current_language} />\n\n\t\t\t\t\t\t<p class=\"padded\">\n\t\t\t\t\t\t\t2. Find the API endpoint below corresponding to your desired\n\t\t\t\t\t\t\tfunction in the app. Copy the code snippet, replacing the\n\t\t\t\t\t\t\tplaceholder values with your own input data.\n\t\t\t\t\t\t\t{#if space_id}If this is a private Space, you may need to pass\n\t\t\t\t\t\t\t\tyour Hugging Face token as well (<a\n\t\t\t\t\t\t\t\t\thref={current_language == \"python\"\n\t\t\t\t\t\t\t\t\t\t? py_docs + spaces_docs_suffix\n\t\t\t\t\t\t\t\t\t\t: current_language == \"javascript\"\n\t\t\t\t\t\t\t\t\t\t\t? js_docs + spaces_docs_suffix\n\t\t\t\t\t\t\t\t\t\t\t: bash_docs}\n\t\t\t\t\t\t\t\t\tclass=\"underline\"\n\t\t\t\t\t\t\t\t\ttarget=\"_blank\">read more</a\n\t\t\t\t\t\t\t\t>).{/if}\n\n\t\t\t\t\t\t\tOr use the\n\t\t\t\t\t\t\t<Button\n\t\t\t\t\t\t\t\tsize=\"sm\"\n\t\t\t\t\t\t\t\tvariant=\"secondary\"\n\t\t\t\t\t\t\t\ton:click={() =>\n\t\t\t\t\t\t\t\t\tdispatch(\"close\", { api_recorder_visible: true })}\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t<div class=\"loading-dot\"></div>\n\t\t\t\t\t\t\t\t<p class=\"self-baseline\">API Recorder</p>\n\t\t\t\t\t\t\t</Button>\n\t\t\t\t\t\t\tto automatically generate your API requests.\n\t\t\t\t\t\t\t{#if current_language == \"bash\"}<br />&nbsp;<br />Making a\n\t\t\t\t\t\t\t\tprediction and getting a result requires\n\t\t\t\t\t\t\t\t<strong>2 requests</strong>: a\n\t\t\t\t\t\t\t\t<code>POST</code>\n\t\t\t\t\t\t\t\tand a <code>GET</code> request. The <code>POST</code> request\n\t\t\t\t\t\t\t\treturns an <code>EVENT_ID</code>, which is used in the second\n\t\t\t\t\t\t\t\t<code>GET</code> request to fetch the results. In these\n\t\t\t\t\t\t\t\tsnippets, we've used <code>awk</code> and <code>read</code> to\n\t\t\t\t\t\t\t\tparse the results, combining these two requests into one command\n\t\t\t\t\t\t\t\tfor ease of use. {#if username !== null}\n\t\t\t\t\t\t\t\t\tNote: connecting to an authenticated app requires an\n\t\t\t\t\t\t\t\t\tadditional request.{/if} See\n\t\t\t\t\t\t\t\t<a href={bash_docs} target=\"_blank\">curl docs</a>.\n\t\t\t\t\t\t\t{/if}\n\n\t\t\t\t\t\t\t<!-- <span\n\t\t\t\t\t\t\tid=\"api-recorder\"\n\t\t\t\t\t\t\ton:click={() => dispatch(\"close\", { api_recorder_visible: true })}\n\t\t\t\t\t\t\t>🪄 API Recorder</span\n\t\t\t\t\t\t> to automatically generate your API requests! -->\n\t\t\t\t\t\t</p>\n\t\t\t\t\t{/if}\n\t\t\t\t{/if}\n\n\t\t\t\t{#if current_language !== \"mcp\"}\n\t\t\t\t\t{#each dependencies as dependency}\n\t\t\t\t\t\t{#if dependency.show_api && info.named_endpoints[\"/\" + dependency.api_name]}\n\t\t\t\t\t\t\t<div class=\"endpoint-container\">\n\t\t\t\t\t\t\t\t<CodeSnippet\n\t\t\t\t\t\t\t\t\tendpoint_parameters={info.named_endpoints[\n\t\t\t\t\t\t\t\t\t\t\"/\" + dependency.api_name\n\t\t\t\t\t\t\t\t\t].parameters}\n\t\t\t\t\t\t\t\t\t{dependency}\n\t\t\t\t\t\t\t\t\t{current_language}\n\t\t\t\t\t\t\t\t\t{root}\n\t\t\t\t\t\t\t\t\t{space_id}\n\t\t\t\t\t\t\t\t\t{username}\n\t\t\t\t\t\t\t\t\tapi_prefix={app.api_prefix}\n\t\t\t\t\t\t\t\t\tapi_description={info.named_endpoints[\n\t\t\t\t\t\t\t\t\t\t\"/\" + dependency.api_name\n\t\t\t\t\t\t\t\t\t].description}\n\t\t\t\t\t\t\t\t/>\n\n\t\t\t\t\t\t\t\t<ParametersSnippet\n\t\t\t\t\t\t\t\t\tendpoint_returns={info.named_endpoints[\n\t\t\t\t\t\t\t\t\t\t\"/\" + dependency.api_name\n\t\t\t\t\t\t\t\t\t].parameters}\n\t\t\t\t\t\t\t\t\tjs_returns={js_info.named_endpoints[\"/\" + dependency.api_name]\n\t\t\t\t\t\t\t\t\t\t.parameters}\n\t\t\t\t\t\t\t\t\t{is_running}\n\t\t\t\t\t\t\t\t\t{current_language}\n\t\t\t\t\t\t\t\t/>\n\n\t\t\t\t\t\t\t\t<ResponseSnippet\n\t\t\t\t\t\t\t\t\tendpoint_returns={info.named_endpoints[\n\t\t\t\t\t\t\t\t\t\t\"/\" + dependency.api_name\n\t\t\t\t\t\t\t\t\t].returns}\n\t\t\t\t\t\t\t\t\tjs_returns={js_info.named_endpoints[\"/\" + dependency.api_name]\n\t\t\t\t\t\t\t\t\t\t.returns}\n\t\t\t\t\t\t\t\t\t{is_running}\n\t\t\t\t\t\t\t\t\t{current_language}\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t{/if}\n\t\t\t\t\t{/each}\n\t\t\t\t{/if}\n\t\t\t</div>\n\t\t</div>\n\t{:else}\n\t\t<NoApi {root} on:close />\n\t{/if}\n{/if}\n\n<style>\n\t.banner-wrap {\n\t\tposition: relative;\n\t\tborder-bottom: 1px solid var(--border-color-primary);\n\t\tpadding: var(--size-4) var(--size-6);\n\t\tfont-size: var(--text-md);\n\t}\n\n\t@media (--screen-md) {\n\t\t.banner-wrap {\n\t\t\tfont-size: var(--text-xl);\n\t\t}\n\t}\n\n\t.docs-wrap {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tgap: var(--spacing-xxl);\n\t}\n\n\t.endpoint {\n\t\tborder-radius: var(--radius-md);\n\t\tbackground: var(--background-fill-primary);\n\t\tpadding: var(--size-6);\n\t\tpadding-top: var(--size-1);\n\t\tfont-size: var(--text-md);\n\t}\n\n\t.client-doc {\n\t\tpadding-top: var(--size-6);\n\t\tpadding-right: var(--size-6);\n\t\tpadding-left: var(--size-6);\n\t\tfont-size: var(--text-md);\n\t}\n\n\t.library {\n\t\tborder: 1px solid var(--border-color-accent);\n\t\tborder-radius: var(--radius-sm);\n\t\tbackground: var(--color-accent-soft);\n\t\tpadding: 0px var(--size-1);\n\t\tcolor: var(--color-accent);\n\t\tfont-size: var(--text-md);\n\t\ttext-decoration: none;\n\t}\n\n\t.snippets {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tmargin-bottom: var(--size-4);\n\t}\n\n\t.snippets > * + * {\n\t\tmargin-left: var(--size-2);\n\t}\n\n\t.snippet {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tborder: 1px solid var(--border-color-primary);\n\n\t\tborder-radius: var(--radius-md);\n\t\tpadding: var(--size-1) var(--size-1-5);\n\t\tcolor: var(--body-text-color-subdued);\n\t\tcolor: var(--body-text-color);\n\t\tline-height: 1;\n\t\tuser-select: none;\n\t}\n\n\t.current-lang {\n\t\tborder: 1px solid var(--body-text-color-subdued);\n\t\tcolor: var(--body-text-color);\n\t}\n\n\t.inactive-lang {\n\t\tcursor: pointer;\n\t\tcolor: var(--body-text-color-subdued);\n\t}\n\n\t.inactive-lang:hover,\n\t.inactive-lang:focus {\n\t\tbox-shadow: var(--shadow-drop);\n\t\tcolor: var(--body-text-color);\n\t}\n\n\t.snippet img {\n\t\tmargin-right: var(--size-1-5);\n\t\twidth: var(--size-3);\n\t}\n\n\t.header {\n\t\tmargin-top: var(--size-6);\n\t\tfont-size: var(--text-xl);\n\t}\n\n\t.endpoint-container {\n\t\tmargin-top: var(--size-3);\n\t\tmargin-bottom: var(--size-3);\n\t\tborder: 1px solid var(--block-border-color);\n\t\tborder-radius: var(--radius-xl);\n\t\tpadding: var(--size-3);\n\t\tpadding-top: 0;\n\t}\n\n\ta {\n\t\ttext-decoration: underline;\n\t}\n\n\tp.padded {\n\t\tpadding: 15px 0px;\n\t\tfont-size: var(--text-lg);\n\t}\n\n\t#api-recorder {\n\t\tborder: 1px solid var(--color-accent);\n\t\tbackground-color: var(--color-accent-soft);\n\t\tpadding: 0px var(--size-2);\n\t\tborder-radius: var(--size-1);\n\t\tcursor: pointer;\n\t}\n\n\tcode {\n\t\tfont-size: var(--text-md);\n\t}\n\t.loading-dot {\n\t\tposition: relative;\n\t\tleft: -9999px;\n\t\twidth: 10px;\n\t\theight: 10px;\n\t\tborder-radius: 5px;\n\t\tbackground-color: #fd7b00;\n\t\tcolor: #fd7b00;\n\t\tbox-shadow: 9999px 0 0 -1px;\n\t\tmargin-right: 0.25rem;\n\t}\n\t:global(.docs-wrap .sm.secondary) {\n\t\tpadding-top: 1px;\n\t\tpadding-bottom: 1px;\n\t}\n\t.self-baseline {\n\t\talign-self: baseline;\n\t}\n\t.api-count {\n\t\tfont-weight: bold;\n\t\tcolor: #fd7b00;\n\t\talign-self: baseline;\n\t\tfont-family: var(--font-mono);\n\t\tfont-size: var(--text-md);\n\t}\n\n\tcode pre {\n\t\toverflow-x: auto;\n\t\tcolor: var(--body-text-color);\n\t\tfont-family: var(--font-mono);\n\t\ttab-size: 2;\n\t}\n\n\t.token.string {\n\t\tdisplay: contents;\n\t\tcolor: var(--color-accent-base);\n\t}\n\n\t.copy {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tright: 0;\n\t\tmargin-top: 5px;\n\t\tmargin-right: 5px;\n\t\tz-index: 10;\n\t}\n\n\t.container {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tgap: var(--spacing-xxl);\n\t\tmargin-top: var(--size-3);\n\t\tmargin-bottom: var(--size-3);\n\t}\n\n\t.desc {\n\t\tcolor: var(--body-text-color-subdued);\n\t}\n\n\t.api-name {\n\t\tcolor: var(--color-accent);\n\t}\n\n\t.mcp-url {\n\t\tpadding: var(--size-2);\n\t\tposition: relative;\n\t}\n\n\t.mcp-url label {\n\t\tdisplay: block;\n\t\tmargin-bottom: var(--size-2);\n\t\tfont-weight: 600;\n\t\tcolor: var(--body-text-color);\n\t}\n\n\t.mcp-url .textbox {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tgap: var(--size-2);\n\t\tborder: 1px solid var(--border-color-primary);\n\t\tborder-radius: var(--radius-sm);\n\t\tpadding: var(--size-2);\n\t\tbackground: var(--background-fill-primary);\n\t}\n\n\t.mcp-url input {\n\t\tflex: 1;\n\t\tborder: none;\n\t\tbackground: none;\n\t\tcolor: var(--body-text-color);\n\t\tfont-family: var(--font-mono);\n\t\tfont-size: var(--text-md);\n\t\twidth: 100%;\n\t}\n\n\t.mcp-url input:focus {\n\t\toutline: none;\n\t}\n\n\t.status-indicator {\n\t\tdisplay: inline-block;\n\t\tmargin-right: var(--size-1-5);\n\t\tposition: relative;\n\t\ttop: -1px;\n\t\tfont-size: 0.8em;\n\t}\n\n\t.status-indicator.active {\n\t\tcolor: #4caf50;\n\t\tanimation: pulse 1s infinite;\n\t}\n\n\t@keyframes pulse {\n\t\t0% {\n\t\t\topacity: 1;\n\t\t}\n\t\t50% {\n\t\t\topacity: 0.6;\n\t\t}\n\t\t100% {\n\t\t\topacity: 1;\n\t\t}\n\t}\n\n\t.mcp-tools {\n\t\tmargin-top: var(--size-4);\n\t\tborder: 1px solid var(--border-color-primary);\n\t\tborder-radius: var(--radius-md);\n\t\toverflow: hidden;\n\t}\n\n\t.tool-item {\n\t\tborder-bottom: 1px solid var(--border-color-primary);\n\t}\n\n\t.tool-item:last-child {\n\t\tborder-bottom: none;\n\t}\n\n\t.tool-header {\n\t\twidth: 100%;\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tpadding: var(--size-3);\n\t\tbackground: var(--background-fill-primary);\n\t\tborder: none;\n\t\tcursor: pointer;\n\t\ttext-align: left;\n\t}\n\n\t.tool-header:hover {\n\t\tbackground: var(--background-fill-secondary);\n\t}\n\n\t.tool-name {\n\t\tfont-family: var(--font-mono);\n\t\tfont-weight: 600;\n\t}\n\n\t.tool-arrow {\n\t\tcolor: var(--body-text-color-subdued);\n\t}\n\n\t.tool-content {\n\t\tpadding: var(--size-3);\n\t\tbackground: var(--background-fill-secondary);\n\t}\n\n\t.tool-description {\n\t\tmargin-bottom: var(--size-3);\n\t\tcolor: var(--body-text-color);\n\t}\n\t.parameter {\n\t\tmargin-bottom: var(--size-2);\n\t\tpadding: var(--size-2);\n\t\tbackground: var(--background-fill-primary);\n\t\tborder-radius: var(--radius-sm);\n\t}\n\n\t.parameter code {\n\t\tfont-weight: 600;\n\t\tcolor: var(--color-accent);\n\t}\n\n\t.parameter-type {\n\t\tcolor: var(--body-text-color-subdued);\n\t\tmargin-left: var(--size-1);\n\t}\n\n\t.parameter-description {\n\t\tmargin-top: var(--size-1);\n\t\tcolor: var(--body-text-color);\n\t}\n</style>\n"], "names": ["ctx", "insert_hydration", "target", "div", "anchor", "append_hydration", "h1", "p0", "code0", "p1", "button", "dispatch", "createEventDispatcher", "root", "$$props", "click_handler", "p", "if_block0", "create_if_block_2", "create_if_block_1", "if_block2", "create_if_block", "api_logo", "attr", "img", "img_src_value", "h2", "div1", "div0", "span1", "span0", "br", "api_count", "current_language", "click_handler_1", "represent_value", "value", "type", "lang", "simplify_file_data", "replace_file_data_with_file_function", "stringify_except_file_function", "is_potentially_nested_file_data", "obj", "key", "_a", "item", "index", "jsonString", "regex", "match", "regexNone", "t_value", "dirty", "set_data", "create_if_block_3", "t1_value", "t1", "span", "t10_value", "hr", "t6", "t6_value", "t8", "t8_value", "t10", "create_if_block_4", "i", "h4", "div2", "current", "is_running", "endpoint_returns", "js_returns", "code", "copy_text", "copy", "bash_install", "pre", "js_install", "py_install", "h3", "api_name", "description", "if_block", "create_if_block_7", "t5_value", "t19_value", "copybutton_changes", "t5", "t19", "t3_value", "t3", "t4_value", "create_if_block_5", "t4", "t7_value", "t14_value", "if_block1", "span2", "span3", "span4", "span5", "t7", "t14", "endpointdetail_changes", "dependency", "api_prefix", "space_id", "endpoint_parameters", "username", "api_description", "python_code", "js_code", "bash_post_code", "has_file_path", "param", "blob_components", "blob_examples", "$$value", "$$invalidate", "normalised_api_prefix", "normalised_root", "t17", "t17_value", "t2", "t2_value", "dependencies", "short_root", "python_code_text", "bash_code", "api_calls", "get_info", "endpoints_info", "py_zipped", "js_zipped", "bash_zipped", "format_api_call", "call", "params", "d", "param_info", "param_name", "python_type", "onMount", "py_api_calls", "js_api_calls", "bash_api_calls", "api_names", "tick", "python", "javascript", "bash", "t0_value", "t0", "mcp", "div4", "div3", "apibanner_changes", "img_1", "img_1_src_value", "li", "li_class_value", "p2", "p3", "recordingsnippet_changes", "a", "a_href_value", "py_docs", "js_docs", "code1", "code2", "create_if_block_12", "mcp_docs", "strong0", "strong1", "strong2", "a0", "p4", "p5", "a1", "label", "input", "each_value_2", "ensure_array_like", "create_if_block_13", "em", "create_if_block_8", "create_if_block_6", "spaces_docs_suffix", "bash_docs", "br0", "br1", "strong", "code3", "code4", "code5", "code6", "each_blocks", "codesnippet_changes", "set_query_param", "url", "get_query_param", "is_valid_language", "app", "root_node", "langs", "mcp_server_active", "get_js_info", "info", "js_info", "data", "js_api_info", "mcp_server_url", "tools", "headers", "mcp_json_sse", "mcp_json_stdio", "file_data_present", "upload_file_mcp_server", "fetchMcpTools", "schema", "tool", "present", "accumulator", "current_key", "header", "error", "lang_param", "response", "language", "click_handler_2"], "mappings": ";;;;;;;;;wGAWqB;AAAA,GAEnB,kBACEA,EAAI,CAAA,CAAA,kPAHa;AAAA,GAEnB,4CACEA,EAAI,CAAA,CAAA,sWALRC,EAwBKC,EAAAC,EAAAC,CAAA,EAvBJC,EAAgBF,EAAAG,CAAA,SAChBD,EAKGF,EAAAI,CAAA,SAHFF,EAEME,EAAAC,CAAA,gBAEPH,EAeGF,EAAAM,CAAA,WAGJR,EAEQC,EAAAQ,EAAAN,CAAA,4EAvBJJ,EAAI,CAAA,CAAA,uIAVDW,EAAWC,KAEN,GAAA,CAAA,KAAAC,CAAA,EAAAC,EA6BY,MAAAC,EAAA,IAAAJ,EAAS,OAAO,s3BCLpCV,EAA4CC,EAAAC,EAAAC,CAAA,WAC5CH,EAAiDC,EAAAc,EAAAZ,CAAA,uEAKC,UAAQ,cAAR,UAAQ,uEAA3B,cAAY,cAAZ,cAAY,uEAAuC,GAAC,cAAD,GAAC,oGAbhFa,EAAAjB,OAAqB,OAAKkB,GAAAlB,CAAA,kBAazB,OAAAA,OAAqB,MAAKmB,wBAAsCC,EAAApB,KAAY,GAACqB,GAAA,qEApBlE;AAAA,GAEjB,iBACErB,EAAI,CAAA,CAAA,4DAgBcA,EAAS,CAAA,CAAA,qNAnBZ;AAAA,GAEjB,2CACEA,EAAI,CAAA,CAAA,2JAgBcA,EAAS,CAAA,CAAA,8MApBpBsB,EAAQ,GAAAC,EAAAC,EAAA,MAAAC,CAAA,+PADnBxB,EA0BIC,EAAAwB,EAAAtB,CAAA,EAzBHC,EAA4BqB,EAAAF,CAAA,SAC5BnB,EAKKqB,EAAAC,CAAA,SAHJtB,EAEKsB,EAAAC,CAAA,gBAENvB,EAiBMqB,EAAAG,CAAA,wBALLxB,EAIGwB,EAAAb,CAAA,EAHFX,EAAmCW,EAAAc,CAAA,kDACsDzB,EACxFW,EAAAe,CAAA,WAKJ9B,EAEQC,EAAAQ,EAAAN,CAAA,4EAzBJJ,EAAI,CAAA,CAAA,EAIDA,OAAqB,mHAYNA,EAAS,CAAA,CAAA,oDACyCA,KAAY,iNA9BxE,GAAA,CAAA,KAAAa,CAAA,EAAAC,EACA,CAAA,UAAAkB,CAAA,EAAAlB,GACA,iBAAAmB,EACV,QAAA,EAAAnB,QAEKH,EAAWC,KAiBEG,EAAA,IAAAJ,EAAS,QAAW,CAAA,qBAAsB,EAAI,CAAA,EAc1CuB,EAAA,IAAAvB,EAAS,OAAO,4PCzCjC,SAASwB,GACfC,EACAC,EACAC,EAAoC,KACyB,CAC7D,OAAID,IAAS,OACLC,IAAS,KAAO,OAAS,KAE7BF,IAAU,MAAQE,IAAS,KACvB,OAEJD,IAAS,UAAYA,IAAS,MAC1BC,IAAS,KAAOF,EAAQ,IAAMA,EAAQ,IACnCC,IAAS,SACZC,IAAS,KAAO,WAAWF,CAAK,EAAIA,EACjCC,IAAS,WAAaA,GAAQ,OACpCC,IAAS,MACZF,EAAQ,OAAOA,CAAK,EACbA,IAAU,OAAS,OAAS,SACzBE,IAAS,MAAQA,IAAS,OAC7BF,EAEDA,IAAU,OACPC,IAAS,aACXD,EAAA,KAAK,UAAUA,CAAK,EACrBA,GACGC,EAAK,WAAW,WAAW,EAE9B,IAAMD,EAAQ,IAGlBE,IAAS,KACLF,IAAU,GAAK,KAAO,KAAK,MAAMA,CAAK,EACnC,OAAOA,GAAU,SACvBA,IAAU,GACNE,IAAS,KAAO,OAAS,OAE1BF,GAEJE,IAAS,SACZF,EAAQG,GAAmBH,CAAK,GAE7BE,IAAS,OACZF,EAAQI,GAAqCJ,CAAK,GAE5CK,GAA+BL,CAAK,EAC5C,CAEO,SAASM,GAAgCC,EAAmB,CAClE,GAAI,OAAOA,GAAQ,UAAYA,IAAQ,MAClCA,EAAI,eAAe,KAAK,GAAKA,EAAI,eAAe,MAAM,GAExD,OAAOA,EAAI,MAAS,UACpBA,EAAI,OAAS,MACbA,EAAI,KAAK,QAAU,kBAEZ,MAAA,GAIV,GAAI,OAAOA,GAAQ,UAAYA,IAAQ,MACtC,QAASC,KAAOD,EACf,GAAI,OAAOA,EAAIC,CAAG,GAAM,UACVF,GAAgCC,EAAIC,CAAG,CAAC,EAE7C,MAAA,GAKJ,MAAA,EACR,CAEA,SAASL,GAAmBI,EAAe,OACtC,OAAA,OAAOA,GAAQ,UAAYA,IAAQ,MAAQ,CAAC,MAAM,QAAQA,CAAG,GAE/D,QAASA,GACTA,EAAI,KACJ,SAAUA,KACVE,EAAAF,EAAI,OAAJ,YAAAE,EAAU,SAAU,kBAEb,CAAE,KAAMF,EAAI,IAAK,KAAM,CAAE,MAAO,qBAGrC,MAAM,QAAQA,CAAG,EAChBA,EAAA,QAAQ,CAACG,EAAMC,IAAU,CACxB,OAAOD,GAAS,UAAYA,IAAS,OACpCH,EAAAI,CAAK,EAAIR,GAAmBO,CAAI,EACrC,CACA,EACS,OAAOH,GAAQ,UAAYA,IAAQ,MAC7C,OAAO,KAAKA,CAAG,EAAE,QAASC,GAAQ,CACjCD,EAAIC,CAAG,EAAIL,GAAmBI,EAAIC,CAAG,CAAC,CAAA,CACtC,EAEKD,EACR,CAEA,SAASH,GAAqCG,EAAe,OACxD,OAAA,OAAOA,GAAQ,UAAYA,IAAQ,MAAQ,CAAC,MAAM,QAAQA,CAAG,GAE/D,QAASA,GACTA,EAAI,KACJ,SAAUA,KACVE,EAAAF,EAAI,OAAJ,YAAAE,EAAU,SAAU,kBAEb,gBAAgBF,EAAI,GAAG,MAG5B,MAAM,QAAQA,CAAG,EAChBA,EAAA,QAAQ,CAACG,EAAMC,IAAU,CACxB,OAAOD,GAAS,UAAYA,IAAS,OACpCH,EAAAI,CAAK,EAAIP,GAAqCM,CAAI,EACvD,CACA,EACS,OAAOH,GAAQ,UAAYA,IAAQ,MAC7C,OAAO,KAAKA,CAAG,EAAE,QAASC,GAAQ,CACjCD,EAAIC,CAAG,EAAIJ,GAAqCG,EAAIC,CAAG,CAAC,CAAA,CACxD,EAEKD,EACR,CAEA,SAASF,GAA+BE,EAAkB,CACzD,IAAIK,EAAa,KAAK,UAAUL,EAAK,CAACC,EAAKR,IACtCA,IAAU,KACN,eAGP,OAAOA,GAAU,UACjBA,EAAM,WAAW,cAAc,GAC/BA,EAAM,SAAS,GAAG,EAEX,WAAWA,CAAK,GAEjBA,CACP,EACD,MAAMa,EAAQ,oCACDD,EAAAA,EAAW,QAAQC,EAAO,CAACC,EAAOzC,IAAO,eAAeA,CAAE,GAAG,EAC1E,MAAM0C,EAAY,kBACX,OAAAH,EAAW,QAAQG,EAAW,MAAM,CAC5C,0OChI8E,GAAC,cAAD,GAAC,kDAevD,IAAAC,GAAApD,EAAW,CAAA,EAAAA,EAAG,EAAA,CAAA,EAAA,MAAQ,OAAK,+DAA3BqD,EAAA,GAAAD,KAAAA,GAAApD,EAAW,CAAA,EAAAA,EAAG,EAAA,CAAA,EAAA,MAAQ,OAAK,KAAAsD,EAAA,EAAAF,CAAA,iCADT,IAAAA,EAAApD,KAAY,KAAI,SAAMA,EAAqB,CAAA,GAAIA,EAAiB,CAAA,IAAK,MAAIuD,GAAA,kHAAzEF,EAAA,GAAAD,KAAAA,EAAApD,KAAY,KAAI,KAAAsD,EAAA,EAAAF,CAAA,EAAMpD,EAAqB,CAAA,GAAIA,EAAiB,CAAA,IAAK,mIAAK;AAAA,YACzG,cADyG;AAAA,YACzG,wEAOHwD,EAAArB,GAAgBnC,EAAiB,CAAA,EAAEA,EAAY,CAAA,EAAA,KAAM,IAAI,EAAA,2TAHpDC,EAAuBC,EAAA4B,EAAA1B,CAAA,EAAAH,EAI9BC,EAAA2B,EAAAzB,CAAA,iBADEiD,EAAA,GAAAG,KAAAA,EAAArB,GAAgBnC,EAAiB,CAAA,EAAEA,EAAY,CAAA,EAAA,KAAM,IAAI,EAAA,KAAAsD,EAAAG,EAAAD,CAAA,yPALHvD,EAExDC,EAAAwD,EAAAtD,CAAA,uDAVCJ,EAAgB,CAAA,IAAK,QAAUA,EAAA,CAAA,EAC9BA,EAAA,CAAA,EACA,IAAMA,EAAC,EAAA,EAAG,KAAG,mBAeyBA,EAAK,CAAA,EAAA,SAC9CA,EAAS,CAAA,EAAA,OAEP2D,EAAA3D,KAAY,YAAW,uBAfnB,OAAAA,OAAqB,SAAQkB,+CAG9BlB,EAAqB,CAAA,GAAIA,EAAgB,CAAA,GAAI,OAAMmB,iJAQ3C,2CAC2B,aAAO,IAAE,aAGlD;AAAA,gBAAE,oVAJY,2CAC2B,iBAAO,IAAE,iBAGlD;AAAA,gBAAE,2VAxBJlB,EAAgBC,EAAA0D,EAAAxD,CAAA,WAChBH,EAyBKC,EAAAC,EAAAC,CAAA,EAxBJC,EAiBGF,EAAAI,CAAA,EAhBFF,EAIAE,EAAAuB,CAAA,gBACAzB,EAGAE,EAAAsB,CAAA,wCASDxB,EAKGF,EAAAM,CAAA,yEArBCT,EAAgB,CAAA,IAAK,QAAUA,EAAA,CAAA,EAC9BA,EAAA,CAAA,EACA,IAAMA,EAAC,EAAA,EAAG,KAAG,KAAAsD,EAAAG,EAAAD,CAAA,8IAeyBxD,EAAK,CAAA,EAAA,KAAAsD,EAAAO,EAAAC,CAAA,cAC9C9D,EAAS,CAAA,EAAA,KAAAsD,EAAAS,EAAAC,CAAA,EAEPX,EAAA,GAAAM,KAAAA,EAAA3D,KAAY,YAAW,KAAAsD,EAAAW,EAAAN,CAAA,kGAOZ,EAAK,CAAA,CAAA,sKADtB1D,EAEKC,EAAAC,EAAAC,CAAA,sLArCIoD,EAAAxD,KAAiB,OAAM,qBAAgBA,EAAgB,CAAA,EAAC,QAAU,GAACkE,GAAA,OAIrElE,EAAgB,CAAA,CAAA,uBAArB,OAAImE,GAAA,2BA8BFnE,EAAU,CAAA,GAAAqB,GAAA,oDAnCT;AAAA,UACG,aAAyB,YAAU,eAAwC,GACpF,0NAFM;AAAA,UACG,iBAAyB,YAAU,kBAAwC,GACpF,8NAEiBrB,EAAU,CAAA,CAAA,UAP3BC,EAKIC,EAAAkE,EAAAhE,CAAA,EAJHC,EAEK+D,EAAAzC,CAAA,sDAIN1B,EA8BKC,EAAAmE,EAAAjE,CAAA,oGAjCK,CAAAkE,GAAAjB,EAAA,IAAAG,KAAAA,EAAAxD,KAAiB,OAAM,KAAAsD,EAAAG,EAAAD,CAAA,EAAgBxD,EAAgB,CAAA,EAAC,QAAU,2DAIpEA,EAAgB,CAAA,CAAA,oBAArB,OAAImE,GAAA,EAAA,mHAAJ,8BADcnE,EAAU,CAAA,CAAA,EA+BtBA,EAAU,CAAA,wNA5CH,GAAA,CAAA,WAAAuE,CAAA,EAAAzD,EACA,CAAA,iBAAA0D,CAAA,EAAA1D,EACA,CAAA,WAAA2D,CAAA,EAAA3D,EACA,CAAA,iBAAAmB,CAAA,EAAAnB,sXCQVd,EAAS,CAAA,CAAA,cAATA,EAAS,CAAA,CAAA,oCAATA,EAAS,CAAA,CAAA,mIADqBA,EAAI,CAAA,CAAA,8OAZxB,GAAA,CAAA,KAAA0E,CAAA,EAAA5D,EACP6D,EAAY,OAEP,SAAAC,GAAA,CACR,UAAU,UAAU,UAAUF,CAAI,MAClCC,EAAY,SAAA,EACZ,oBACCA,EAAY,MAAA,GACV,qMCmBiBE,EAAY,CAAA,CAAA,oEAGzB,IAAE,MAACA,EAAY,gKAAf,IAAE,QAACA,EAAY,iHAJrB5E,EAEKC,EAAA0B,EAAAxB,CAAA,wBACLH,EAEKC,EAAAyB,EAAAvB,CAAA,EADJC,EAA0BsB,EAAAmD,CAAA,6LAVRC,EAAU,CAAA,CAAA,oEAGvB,IAAE,MAACA,EAAU,gKAAb,IAAE,QAACA,EAAU,iHAJnB9E,EAEKC,EAAA0B,EAAAxB,CAAA,wBACLH,EAEKC,EAAAyB,EAAAvB,CAAA,EADJC,EAAwBsB,EAAAmD,CAAA,6LAVNE,EAAU,CAAA,CAAA,oEAGvB,IAAE,MAACA,EAAU,gKAAb,IAAE,QAACA,EAAU,iHAJnB/E,EAEKC,EAAA0B,EAAAxB,CAAA,wBACLH,EAEKC,EAAAyB,EAAAvB,CAAA,EADJC,EAAwBsB,EAAAmD,CAAA,gMALrB,OAAA9E,OAAqB,SAAQ,EAOxBA,OAAqB,aAAY,EAOjCA,OAAqB,OAAM,8LAftCC,EAuBMC,EAAAwE,EAAAtE,CAAA,ojBA7BF4E,GAAa,4BACbD,GAAa,0BACbF,GAAe,oCAJR,GAAA,CAAA,iBAAA5C,CAAA,EAAAnB,0LCGS0C,EAAA,IAAMxD,EAAQ,CAAA,mCAFhC;AAAA,EAEF,2CACoBA,EAAW,CAAA,CAAA,yDAH7B;AAAA,EAEF,6GACoBA,EAAW,CAAA,CAAA,sJAHhCC,EAIIC,EAAA+E,EAAA7E,CAAA,SAFHC,EAAyC4E,EAAAnD,CAAA,gBACzCzB,EAAsC4E,EAAApD,CAAA,mBADlBwB,EAAA,GAAAG,KAAAA,EAAA,IAAMxD,EAAQ,CAAA,IAAAsD,EAAAG,EAAAD,CAAA,WACdxD,EAAW,CAAA,CAAA,mDAPpB,SAAAkF,EAA0B,IAAA,EAAApE,GAC1B,YAAAqE,EAA6B,IAAA,EAAArE,o1DCgJQ,GAC1C,cAD0C,GAC1C,wDALH0C,EAAArB,GACInC,EAAa,EAAA,EACbA,EAAY,EAAA,EAAA,KACZ,MAAK,EAAA,OACCoF,EAAApF,EAAI,EAAA,EAAAA,EAAoB,CAAA,EAAA,OAAS,GAACqF,GAAA,iBAN8E;AAAA,QACvH,qCADuH;AAAA,QACvH,mFACHhC,EAAA,CAAA,EAAA,GAAAG,KAAAA,EAAArB,GACInC,EAAa,EAAA,EACbA,EAAY,EAAA,EAAA,KACZ,MAAK,EAAA,KAAAsD,EAAAG,EAAAD,CAAA,EACCxD,EAAI,EAAA,EAAAA,EAAoB,CAAA,EAAA,OAAS,+IAPuBsF,EAAAtF,KAAW,SAAQ,SAA8C,YAUrI,UACc,UAAc,cAC4CuF,EAAAvF,KAAW,SAAQ,0BAhBtE,MAAA6C,GAAA7C,OAAA,YAAA6C,GAAgB,sBAKrB7C,EAAmB,CAAA,CAAA,uBAAxB,OAAImE,GAAA,yGADP,eAAa,MAACnE,EAAe,EAAA,CAAA,MAAEA,EAAqB,EAAA,CAAA,MAAC,QAAM,aAAqB,8CAA4C,aAAI;AAAA,YAC/H,2CAQC;AAAA,EACX,aAAK;AAAA,gBACS,aAAK,WAAS,aAAK;AAAA,4BACP,MAACA,EAAe,EAAA,CAAA,MAAEA,EAAqB,EAAA,CAAA,MAAC,QAAM,aAAqB,YAAU,6MAZ9F,eAAa,QAACA,EAAe,EAAA,CAAA,QAAEA,EAAqB,EAAA,CAAA,QAAC,QAAM,iBAAqB,8CAA4C,iBAAI;AAAA,YAC/H,kDAQC;AAAA,EACX,iBAAK;AAAA,gBACS,iBAAK,WAAS,iBAAK;AAAA,4BACP,QAACA,EAAe,EAAA,CAAA,QAAEA,EAAqB,EAAA,CAAA,QAAC,QAAM,iBAAqB,YAAU,iKAlBrGC,EAoBMC,EAAAwE,EAAAtE,EAAA,EAnBLC,EAEKqE,EAAA9C,CAAA,sBAELvB,EAcKqE,EAAA/C,CAAA,EAbJtB,EAYwGsB,EAAAmD,CAAA,iPAhBtFzB,GAAA,CAAA,EAAA,MAAAmC,GAAA,MAAA3C,GAAA7C,OAAA,YAAA6C,GAAgB,4CAIf7C,EAAe,EAAA,CAAA,wBAAEA,EAAqB,EAAA,CAAA,GAAQ,CAAAsE,GAAAjB,GAAA,CAAA,EAAA,IAAAiC,KAAAA,EAAAtF,KAAW,SAAQ,KAAAsD,EAAAmC,EAAAH,CAAA,gBACvEtF,EAAmB,CAAA,CAAA,oBAAxB,OAAImE,GAAA,EAAA,oHAAJ,6BAWenE,EAAe,EAAA,CAAA,wBAAEA,EAAqB,EAAA,CAAA,GAAQ,CAAAsE,GAAAjB,GAAA,CAAA,EAAA,IAAAkC,KAAAA,EAAAvF,KAAW,SAAQ,KAAAsD,EAAAoC,EAAAH,CAAA,sIA3D1DI,EAAA3F,MAAc,IAAG,SACtCA,EAAS,EAAA,EAAA,0BAFrB;AAAA,gBACa,MAACA,EAAC,EAAA,CAAA,MAAC,kBAAgB,aAAmB;AAAA,cACxC,aAAW,oBAAkB,MAACA,EAAC,EAAA,CAAA,MAAC;AAAA,OACvC,cAHJ;AAAA,gBACa,QAACA,EAAC,EAAA,CAAA,QAAC,kBAAgB,iBAAmB;AAAA,cACxC,iBAAW,oBAAkB,QAACA,EAAC,EAAA,CAAA,QAAC;AAAA,OACvC,kMAIwB,aAAkB,MAACA,EAAQ,CAAA,CAAA,MAAC,mBAAwB,cAApD,aAAkB,QAACA,EAAQ,CAAA,CAAA,QAAC,mBAAwB,0DAAjCA,EAAQ,CAAA,CAAA,uDAiB9CA,EAAc,EAAA,EAAA,OAAI2F,EAAAxD,GACnBnC,EAAa,EAAA,EACbA,EAAY,EAAA,EAAA,KACZ,IAAG,EAAA,sBALT;AAAA,GACF,yBACwB,IAAE,aAKnB,IAAE,uBAPP;AAAA,GACF,qDACwB,IAAE,8BAKnB,IAAE,+DANTC,EAMOC,EAAAwD,EAAAtD,CAAA,uDALEJ,EAAc,EAAA,EAAA,KAAAsD,EAAAG,EAAAD,CAAA,EAAIH,EAAA,CAAA,EAAA,GAAAsC,KAAAA,EAAAxD,GACnBnC,EAAa,EAAA,EACbA,EAAY,EAAA,EAAA,KACZ,IAAG,EAAA,KAAAsD,EAAAsC,EAAAD,CAAA,uDAXF3F,EAAc,EAAA,EAAA,SAAWA,EAAS,EAAA,EAAA,6BAH1C;AAAA,KACC,yBAEsB,WAAS,aAC1B,IAAE,iDAJR;AAAA,KACC,qDAEsB,WAAS,8BAC1B,IAAE,wLAHPC,EAGKC,EAAA4B,EAAA1B,CAAA,gCACJH,EAEIC,EAAA2B,EAAAzB,CAAA,yBAJEJ,EAAc,EAAA,EAAA,KAAAsD,EAAAG,EAAAD,CAAA,iBAAWxD,EAAS,EAAA,EAAA,KAAAsD,EAAAsC,EAAAD,CAAA,gHAJnC3F,EAAe,EAAA,EAAC,SAASA,EAAS,EAAA,CAAA,iQANlC6F,GAAA7F,MAAYA,EAAI,CAAA,GAAA,aAGfgE,EAAAhE,KAAW,SAAQ,uBAdP,MAAA,CAAA,MAAA6C,EAAA7C,OAAA,YAAA6C,EAAS,SAAS,aAIlC7C,EAAa,EAAA,CAAA,uBAAlB,OAAImE,GAAA,qBAQM,IAAAiB,EAAApF,OAAa,MAAI8F,GAAA9F,CAAA,OAInBA,EAAmB,CAAA,CAAA,uBAAxB,OAAImE,GAAA,yGAbC;AAAA,CACV,2CAKE;AAAA,qCACkC,kBAC5B,GAAC,aAAkB,GAAC,eAC2D;AAAA,qCACnD,kBAC5B,IAAE,aAAqB,GAAC,MACzB,MAAW,2CAsBN;AAAA;AAAA;AAAA;AAAA,CAIZ,oMAtCU;AAAA,CACV,kDAKE;AAAA,qCACkC,4CAC5B,GAAC,iBAAkB,GAAC,+BAC2D;AAAA,qCACnD,4CAC5B,IAAE,iBAAqB,GAAC,qBACzB,MAAW,kDAsBN;AAAA;AAAA;AAAA;AAAA,CAIZ,gPA3CGlE,EA6CMC,EAAAwE,EAAAtE,CAAA,EA5CLC,EAEKqE,EAAA9C,CAAA,sBACLvB,EAwCKqE,EAAA/C,CAAA,EAvCJtB,EAsCAsB,EAAAmD,CAAA,iEA/B+BzE,EAE7ByE,EAAAhD,CAAA,6CAC6BzB,EAE7ByE,EAAAjD,CAAA,gIAfgBwB,EAAA,CAAA,EAAA,MAAAmC,EAAA,MAAA3C,EAAA7C,OAAA,YAAA6C,EAAS,sCAIzB7C,EAAa,EAAA,CAAA,oBAAlB,OAAImE,GAAA,EAAA,gHAAJ,WAOQ,CAAAG,GAAAjB,EAAA,CAAA,EAAA,IAAAwC,KAAAA,GAAA7F,MAAYA,EAAI,CAAA,GAAA,KAAAsD,EAAAyC,EAAAF,CAAA,EACd7F,OAAa,6DAEd,CAAAsE,GAAAjB,EAAA,CAAA,EAAA,IAAAW,KAAAA,EAAAhE,KAAW,SAAQ,KAAAsD,EAAAS,EAAAC,CAAA,kBAEpBhE,EAAmB,CAAA,CAAA,oBAAxB,OAAImE,GAAA,EAAA,gHAAJ,wKA9C4B,eAAa,cAAb,eAAa,2EAGhB,WAAS,MAACnE,EAAQ,CAAA,CAAA,MAAC,kBAAgB,cAAnC,WAAS,QAACA,EAAQ,CAAA,CAAA,QAAC,kBAAgB,0DAAzBA,EAAQ,CAAA,CAAA,mDAK7CwD,EAAAxD,EAAA,EAAA,EACOA,MAAiB,IACjB,SACAmC,GACDnC,EAAqB,EAAA,EAAGA,EAAiB,EAAA,EAAGA,EAAa,EAAA,EACzDA,MAAY,KACZ,4BAPC;AAAA,GACR,gCAQM,GAAC,cATC;AAAA,GACR,mEAQM,GAAC,4BANIC,EAMLC,EAAAwD,EAAAtD,CAAA,0BARLiD,EAAA,CAAA,EAAA,GAAAG,KAAAA,EAAAxD,EAAA,EAAA,EACOA,MAAiB,IACjB,KAAEsD,EAAAG,EAAAD,CAAA,iBACFrB,GACDnC,EAAqB,EAAA,EAAGA,EAAiB,EAAA,EAAGA,EAAa,EAAA,EACzDA,MAAY,KACZ,2HAZoCgG,GAAAhG,MAAYA,EAAI,CAAA,GAAA,+BAiBxBiG,EAAAjG,KAAW,SAAQ,mCAxBjC,MAAA,CAAA,MAAA6C,GAAA7C,OAAA,YAAA6C,GAAa,SAAS,UAK1B7C,EAAa,EAAA,GAAAkB,GAAA,EAGpBgF,EAAAlG,OAAa,MAAImB,GAAAnB,CAAA,QAGnBA,EAAmB,CAAA,CAAA,0BAAxB,OAAImE,IAAA,wIARoC,iBAAe,kCAErD,SAAO,eAAoC;AAAA;AAAA,iBAElC,kBAA2B,GAAC,aAAkB,GAAC,eACO;AAAA,iBACtD,kCACT,GAAC,+CAaJ;AAAA,YACO,kBAAuB,IAAE,aAAqB,GAAC,MACtD;AAAA;AAAA,CAEJ,kCAAoC,UAAQ,uSAxBC,iBAAe,kGAErD,SAAO,oBAAoC;AAAA;AAAA,iBAElC,6CAA2B,GAAC,iBAAkB,GAAC,iCACO;AAAA,iBACtD,kGACT,GAAC,oDAaJ;AAAA,YACO,6CAAuB,IAAE,iBAAqB,GAAC,sBACtD;AAAA;AAAA,CAEJ,iGAAoC,UAAQ,yVA7BzClE,EA+BMC,GAAAwE,EAAAtE,EAAA,EA9BLC,EAEKqE,EAAA9C,CAAA,sBACLvB,EA0BKqE,EAAA/C,CAAA,EAzBJtB,EAwB4CsB,EAAAmD,CAAA,EAxBvCzE,EAAmCyE,EAAAhD,CAAA,SAAezB,EAErDyE,EAAAjD,CAAA,+BAESxB,EACTyE,EAAAqB,CAAA,6CACS9F,EACTyE,EAAAsB,CAAA,iEAcI/F,EAAsDyE,EAAAuB,CAAA,8BAGjEhG,EAAoCyE,EAAAwB,CAAA,sDA3BbjD,GAAA,CAAA,EAAA,MAAAmC,EAAA,MAAA3C,GAAA7C,QAAA,YAAA6C,GAAa,sBAOS,CAAAyB,GAAAjB,GAAA,CAAA,EAAA,IAAA2C,KAAAA,GAAAhG,OAAYA,GAAI,CAAA,GAAA,KAAAsD,EAAAiD,EAAAP,CAAA,EACjDhG,QAAa,8EAGfA,GAAmB,CAAA,CAAA,wBAAxB,OAAImE,IAAA,EAAA,6HAAJ,QAagC,CAAAG,GAAAjB,GAAA,CAAA,EAAA,IAAA4C,KAAAA,EAAAjG,MAAW,SAAQ,KAAAsD,EAAAkD,EAAAP,CAAA,sLA/B5C,SAAAjG,KAAW,qBACRA,EAAe,CAAA,6CAExB,OAAAA,OAAqB,SAAQ,EAmCxBA,OAAqB,aAAY,EAiDjCA,OAAqB,OAAM,0PAzFtCC,EAkHKC,EAAAC,EAAAC,CAAA,iEAhHOiD,EAAA,CAAA,EAAA,IAAAoD,EAAA,SAAAzG,KAAW,kCACRA,EAAe,CAAA,gTA7BlB,CAAA,WAAA0G,CAAA,EAAA5F,EACA,CAAA,KAAAD,CAAA,EAAAC,EACA,CAAA,WAAA6F,CAAA,EAAA7F,EACA,CAAA,SAAA8F,CAAA,EAAA9F,EACA,CAAA,oBAAA+F,CAAA,EAAA/F,EACA,CAAA,SAAAgG,CAAA,EAAAhG,EACA,CAAA,iBAAAmB,CAAA,EAAAnB,GACA,gBAAAiG,EAAiC,IAAA,EAAAjG,EAExCkG,EACAC,EACAC,EAGAC,EAAgBN,EAAoB,KAAMO,GAC7C1E,GAAgC0E,EAAM,aAAa,CAAA,EAEhDC,EAAmB,CAAA,QAAS,OAAQ,QAAS,OAAO,EACpDC,EAAuBT,EAAoB,OAC7CO,GAA6BC,EAAgB,SAASD,EAAM,SAAS,CAAA,4CAkBpDJ,EAAWO,qDAmCXN,EAAOM,qDAkDPL,EAAcK,0YApG9BC,EAAA,GAAAC,EAAwBd,GAA0B,GAAA,mBACrDa,EAAA,GAAGE,EAAkB7G,EAAK,QAAQ,MAAO,EAAE,CAAA,khBCqIrB,MAAA,CAAA,MAAAgC,EAAA7C,OAAA,YAAA6C,EAAW,SAAS,aAG/B7C,EAAW,CAAA,CAAA,uBAAhB,OAAImE,GAAA,6aALRlE,EAaMC,EAAAwE,EAAAtE,CAAA,EAZLC,EAEKqE,EAAA9C,CAAA,sBACLvB,EAQKqE,EAAA/C,CAAA,6FAVc0B,EAAA,KAAAmC,EAAA,MAAA3C,EAAA7C,OAAA,YAAA6C,EAAW,iCAGtB7C,EAAW,CAAA,CAAA,oBAAhB,OAAImE,GAAA,EAAA,mHAAJ,gLAtBgB,MAAA,CAAA,MAAAtB,EAAA7C,OAAA,YAAA6C,EAAS,SAAS,IAM7B,IAAAuC,EAAApF,OAAa,MAAIkE,GAAAlE,CAAA,OACjBA,EAAS,CAAA,CAAA,uBAAd,OAAImE,GAAA,yGAJD;AAAA;AAAA,kCAEuB,kBAA2B,GAAC,MAACnE,EAAU,CAAA,CAAA,MAAC,GAAC,eACa;AAAA,MAClF,yOAJK;AAAA;AAAA,kCAEuB,4CAA2B,GAAC,QAACA,EAAU,CAAA,CAAA,QAAC,GAAC,+BACa;AAAA,MAClF,2OATFC,EAiBMC,EAAAwE,EAAAtE,CAAA,EAhBLC,EAEKqE,EAAA9C,CAAA,sBACLvB,EAYKqE,EAAA/C,CAAA,EAXJtB,EAUasB,EAAAmD,CAAA,SARezE,EAC1ByE,EAAApB,CAAA,wIANgBL,EAAA,KAAAmC,EAAA,MAAA3C,EAAA7C,OAAA,YAAA6C,EAAS,oCAK8B7C,EAAU,CAAA,CAAA,EAC5DA,OAAa,wEACbA,EAAS,CAAA,CAAA,oBAAd,OAAImE,GAAA,EAAA,mHAAJ,yNA5BgBnE,EAAgB,CAAA,CAAA,IAQ3B,IAAAoF,EAAApF,OAAa,MAAImB,GAAAnB,CAAA,OACtBA,EAAS,CAAA,CAAA,uBAAd,OAAImE,GAAA,qIANuC,iBAAe,kCAErD;AAAA;AAAA,iBAES,kBAA2B,GAAC,MAACnE,EAAU,CAAA,CAAA,MAAC,GAAC,eACa;AAAA,CACtE,iUAN6C,iBAAe,gGAErD;AAAA;AAAA,iBAES,4CAA2B,GAAC,QAACA,EAAU,CAAA,CAAA,QAAC,GAAC,+BACa;AAAA,CACtE,6RAXGC,EAmBMC,EAAAwE,EAAAtE,CAAA,EAlBLC,EAEKqE,EAAA9C,CAAA,sBACLvB,EAcKqE,EAAA/C,CAAA,EAbJtB,EAYcsB,EAAAmD,CAAA,EAZTzE,EAAmCyE,EAAAhD,CAAA,SAAezB,EAErDyE,EAAAjD,CAAA,SAESxB,EACTyE,EAAAqB,CAAA,gJARgBnG,EAAgB,CAAA,4BAOMA,EAAU,CAAA,CAAA,EAC3CA,OAAa,wEAClBA,EAAS,CAAA,CAAA,oBAAd,OAAImE,GAAA,EAAA,mHAAJ,yJAmCwCnE,EAAQ,EAAA,EAAA,SAA8C,UACrFA,EAAI,EAAA,EAAA,SAAG,UACF,UAAc,cACeA,EAAQ,EAAA,EAAA,qCAH1C,eAAa,MAACA,EAAU,CAAA,CAAA,MAAC,OAAK,aAAU,8CAA4C,aAAK;AAAA,WAC1F,aAAM,GAAC,aAAK;AAAA,gBACP,aAAK,WAAS,aAAK;AAAA,4BACP,MAACA,EAAU,CAAA,CAAA,MAAC,OAAK,aAAU,YAAU,0EAHrD,eAAa,QAACA,EAAU,CAAA,CAAA,QAAC,OAAK,iBAAU,8CAA4C,iBAAK;AAAA,WAC1F,iBAAM,GAAC,iBAAK;AAAA,gBACP,iBAAK,WAAS,iBAAK;AAAA,4BACP,QAACA,EAAU,CAAA,CAAA,QAAC,OAAK,iBAAU,YAAU,wFAH1DC,EAG+DC,EAAA4E,EAAA1E,CAAA,gJAC/DH,EAAKC,EAAA6B,EAAA3B,CAAA,mBAJcJ,EAAU,CAAA,CAAA,gBAAOA,EAAQ,EAAA,EAAA,KAAAsD,EAAAsC,EAAAD,CAAA,gBACvC3F,EAAI,EAAA,EAAA,KAAAsD,EAAAiD,EAAAP,CAAA,WAEahG,EAAU,CAAA,CAAA,gBAAOA,EAAQ,EAAA,EAAA,KAAAsD,EAAAqE,EAAAC,CAAA,sEApBvB,aAAkB,MAAC5H,EAAQ,CAAA,CAAA,MAAC,mBAAwB,cAApD,aAAkB,QAACA,EAAQ,CAAA,CAAA,QAAC,mBAAwB,sDAAjCA,EAAQ,CAAA,CAAA,qDAMpCA,EAAI,EAAA,EAAA,oBAAP,IAAE,qBAAF,IAAE,2DAACA,EAAI,EAAA,EAAA,KAAAsD,EAAAG,EAAAD,CAAA,oDADpBxD,EAAQ,EAAA,EAAA,WACAA,EAAI,EAAA,GAAAuD,GAAAvD,CAAA,iBAJV;AAAA,sBACc,kBACG;AAAA,KACpB,aAAU,GAAC,eACgB;AAAA,OACzB,uBALC;AAAA,sBACc,4CACG;AAAA,KACpB,iBAAU,GAAC,+BACgB;AAAA,OACzB,uEAJeC,EAGbC,EAAAwD,EAAAtD,CAAA,kEADHJ,EAAQ,EAAA,EAAA,KAAAsD,EAAAuE,EAAAC,CAAA,EACA9H,EAAI,EAAA,kJAzBa,WAAS,MAACA,EAAQ,CAAA,CAAA,MAAC,kBAAgB,cAAnC,WAAS,QAACA,EAAQ,CAAA,CAAA,QAAC,kBAAgB,sDAAzBA,EAAQ,CAAA,CAAA,yDAK/CA,EAAI,EAAA,EAAA,aAAsCA,EAAQ,EAAA,EAAA,wBAHjD;AAAA,QACK,kBACE;AAAA,CACT,aAAM,aAAW,kBAAuB,IAAE,aAAU,GAAC,MAAM;AAAA;AAAA,CAE3D,uBALE;AAAA,QACK,4CACE;AAAA,CACT,iBAAM,aAAW,4CAAuB,IAAE,iBAAU,GAAC,qBAAM;AAAA;AAAA,CAE3D,6GAJOC,EAIAC,EAAA2B,EAAAzB,CAAA,uBAFUC,EAA0CwB,EAAAC,CAAA,oDAA1D9B,EAAI,EAAA,EAAA,KAAAsD,EAAAuE,EAAAC,CAAA,gBAAsC9H,EAAQ,EAAA,EAAA,KAAAsD,EAAAmC,EAAAH,CAAA,4FAhB5C,OAAAtF,OAAqB,SAAQ,EAqBxBA,OAAqB,aAAY,EAmBjCA,OAAqB,OAAM,maAzClB,oOAFrBC,EA4DKC,EAAAC,EAAAC,CAAA,sMAnLO,GAAA,CAAA,aAAA2H,CAAA,EAAAjH,EACA,CAAA,WAAAkH,CAAA,EAAAlH,EACA,CAAA,KAAAD,CAAA,EAAAC,GACA,WAAA6F,EAAa,EAAA,EAAA7F,EACb,CAAA,iBAAAmB,CAAA,EAAAnB,EACA,CAAA,SAAAgG,CAAA,EAAAhG,EAEPkG,EACAiB,EACAhB,EACAiB,EAEO,CAAA,UAAAC,EAAA,EAAA,EAAArH,EAEI,eAAAsH,GAAA,CAQP,OADH,MAHA,MAAiB,MACpBvH,EAAK,QAAQ,MAAO,EAAE,EAAI8F,EAAa,2BAAA,GAEd,OAIvB,IAAA0B,EACAC,EAAA,CAAA,EACAC,EAAA,CAAA,EACAC,EAAA,CAAA,EAEK,SAAAC,EAAgBC,EAAepG,EAAA,CACjC,MAAA4C,EAAA,IAAe6C,EAAaW,EAAK,QAAQ,EAAE,QAAQ,GAMnDC,EAJ0BD,EAAK,KAAK,OACxCE,GAAA,OAAaA,EAAM,GAAA,EAInB,KAAKxB,EAAOrE,IAAA,CACR,GAAAsF,EAAenD,CAAQ,EAAA,CACpB,MAAA2D,EAAaR,EAAenD,CAAQ,EAAE,WAAWnC,CAAK,EACvD,GAAA,CAAA8F,EACG,OAEF,MAAAC,EAAaD,EAAW,eACxBE,EAAcF,EAAW,YAAY,QACvCvG,IAAS,gBACAwG,CAAU,IAAI3G,GACzBiF,EACA2B,EACA,IAAA,CAAA,MAESzG,IAAS,kBACLwG,CAAU,KAAK3G,GAC5BiF,EACA2B,EACA,IAAA,CAAA,MAESzG,IAAS,OACL,MAAA,OAAAH,GACbiF,EACA2B,EACA,MAAA,CAAA,cAIS5G,GAAgBiF,EAAA,OAA4B9E,CAAI,CAAA,EAE5D,CAAA,EAAA,OAAQsG,GAAa,OAAAA,EAAM,GAAW,EACtC,KAAK;AAAA,CAAK,EACR,GAAAD,EAAA,IACCrG,IAAS,cACFqG,CAAM;AAAA,KACNrG,IAAS;EACNqG,CAAM;AAAA,MACTrG,IAAS;EACPqG,CAAM;AAAA,SAGhBrG,IAAS,KACL,GAED;AAAA,EAGR0G,GAAA,SAAA,CAECX,GADmB,MAAAD,KACG,gBAClB,IAAAa,EAAyBd,EAAU,IAAKO,GAC3CD,EAAgBC,EAAM,IAAI,CAAA,EAEvBQ,EAAyBf,EAAU,IAAKO,GAC3CD,EAAgBC,EAAM,IAAI,CAAA,EAEvBS,EAA2BhB,EAAU,IAAKO,GAC7CD,EAAgBC,EAAM,MAAM,CAAA,EAEzBU,EAAsBjB,EAAU,IAClCO,GAASX,EAAaW,EAAK,QAAQ,EAAE,UAAY,EAAA,EAEnDlB,EAAA,EAAAc,EAAYW,EAAa,IAAK,CAAAP,EAAM3F,MACnC,KAAA2F,EACA,SAAUU,EAAUrG,CAAK,CAAA,EAAA,CAAA,EAE1ByE,EAAA,EAAAe,EAAYW,EAAa,IAAK,CAAAR,EAAM3F,MACnC,KAAA2F,EACA,SAAUU,EAAUrG,CAAK,CAAA,EAAA,CAAA,EAE1ByE,EAAA,EAAAgB,EAAcW,EAAe,IAAK,CAAAT,EAAM3F,MACvC,KAAA2F,EACA,SAAUU,EAAUrG,CAAK,CAAA,EAAA,CAAA,EAGpB,MAAAsG,GAAA,EAEN7B,EAAA,EAAAS,EAAmBjB,EAAY,SAAA,8CAYbA,EAAWO,qDAqBXN,EAAOM,qDAmBPW,EAASX,+eC9K7B,MAAe+B,GAAA,wpCCAAC,GAAA,0yBCAAC,GAAA,o7DCeC,WACL,cADK,WACL,uDAFR,IAAAC,EAAAzJ,MAAoB,SAAW,QAAU,WAAY8H,EAAA9H,KAAiB,OAAM,6BAA5B,MAAI,aAAwB;AAAA,WACrE,uBADyC,MAAI,iBAAwB;AAAA,WACrE,sDADPqD,EAAA,GAAAoG,KAAAA,EAAAzJ,MAAoB,SAAW,QAAU,SAAMsD,EAAAoG,EAAAD,CAAA,EAAMpG,EAAA,GAAAyE,KAAAA,EAAA9H,KAAiB,OAAM,KAAAsD,EAAAuE,EAAAC,CAAA,yFAWvD,GAAC,MAAC9H,EAAC,EAAA,CAAA,MAAC,GAAC,2DAAL,GAAC,QAACA,EAAC,EAAA,CAAA,QAAC,GAAC,wEAAxBC,EAA8BC,EAAAwD,EAAAtD,CAAA,4DAGgCJ,EAAU,CAAA,EACtEA,OACC,KAAI,2EAFuDA,EAAU,CAAA,EACtEA,OACC,KAAI,KAAAsD,EAAA,EAAAF,CAAA,iCAF8B,IAAAA,EAAApD,KAAY,KAAI,+DAAhBqD,EAAA,GAAAD,KAAAA,EAAApD,KAAY,KAAI,KAAAsD,EAAA,EAAAF,CAAA,yDAMfpD,EAAK,CAAA,EAAA,SAC3CA,EAAS,CAAA,EAAA,WAXLA,EAAgB,CAAA,EAAC,OAAS,GAACkB,GAAAlB,CAAA,kBAIzB,OAAAA,OAAqB,SAAQmB,2HAKtB,wCACwB,aAAO,IAAE,aAG/C;AAAA,eACD,qPALe,wCACwB,iBAAO,IAAE,iBAG/C;AAAA,eACD,gMAjBDlB,EAAgBC,EAAA0D,EAAAxD,CAAA,WAChBH,EAiBKC,EAAAC,EAAAC,CAAA,EAhBJC,EASGF,EAAAI,CAAA,wBALFF,EAIAE,EAAAmD,CAAA,qBAEDrD,EAKGF,EAAAM,CAAA,oDAdGT,EAAgB,CAAA,EAAC,OAAS,6HAUQA,EAAK,CAAA,EAAA,KAAAsD,EAAAyC,EAAAF,CAAA,cAC3C7F,EAAS,CAAA,EAAA,KAAAsD,EAAAO,EAAAC,CAAA,qGASI,EAAK,CAAA,CAAA,sKADtB7D,EAEKC,EAAAC,EAAAC,CAAA,oOAhCQJ,EAAgB,CAAA,EAAC,OAAS,EAACuD,6BAOjCvD,EAAgB,CAAA,CAAA,uBAArB,OAAImE,GAAA,2BAsBFnE,EAAU,CAAA,GAAAqB,GAAA,oDA9BT;AAAA,UACG,gOADH;AAAA,UACG,qOAMQrB,EAAU,CAAA,CAAA,UAV3BC,EAQIC,EAAAkE,EAAAhE,CAAA,EAPHC,EAEK+D,EAAAzC,CAAA,8BAON1B,EAsBKC,EAAAmE,EAAAjE,CAAA,8KArBGJ,EAAgB,CAAA,CAAA,oBAArB,OAAImE,GAAA,EAAA,mHAAJ,8BADcnE,EAAU,CAAA,CAAA,EAuBtBA,EAAU,CAAA,qNAvCH,GAAA,CAAA,WAAAuE,CAAA,EAAAzD,EACA,CAAA,iBAAA0D,CAAA,EAAA1D,EACA,CAAA,WAAA2D,CAAA,EAAA3D,EACA,CAAA,iBAAAmB,CAAA,EAAAnB,kVCNZ,MAAe6I,GAAA,GAAA,IAAA,IAAA,6BAAA,YAAA,GAAA,EAAA,gXCmQT3J,EAAS,EAAA,EAAA,6nBAIL,KAAAA,MAAYA,EAAI,CAAA,yEAcdA,EAAK,EAAA,CAAA,uBAAV,OAAImE,GAAA,0DAcF,OAAAnE,KAAU,OAAM,0BAyOhB,IAAAkG,EAAAlG,OAAqB,OAAKkB,GAAAlB,CAAA,qxBAxQjCC,EAOKC,EAAA0B,EAAAxB,CAAA,wBAELH,EA0SKC,EAAA0J,EAAAxJ,CAAA,EAzSJC,EAIKuJ,EAAAjI,CAAA,SACLtB,EAmSKuJ,EAAAC,CAAA,EAlSJxJ,EAcKwJ,EAAAxF,CAAA,0HA3BChB,EAAA,CAAA,EAAA,IAAAyG,EAAA,KAAA9J,MAAYA,EAAI,CAAA,iEAcdA,EAAK,EAAA,CAAA,oBAAV,OAAImE,GAAA,EAAA,mHAAJ,yIAuPEnE,OAAqB,yRA7OtBA,EAAY,EAAA,EAAA,qPADHA,EAAG,EAAA,CAAA,GAAAuB,EAAAwI,EAAA,MAAAC,CAAA,6CANbzI,EAAA0I,EAAA,QAAAC,EAAA,YAAAlK,OAAqBA,EAAQ,EAAA,EAAG,eAAiB,iBAAe,gBAAA,UAFjEC,EAUIC,EAAA+J,EAAA7J,CAAA,EAFHC,EAAuB4J,EAAAF,CAAA,+DANvB1G,EAAA,CAAA,EAAA,IAAA6G,KAAAA,EAAA,YAAAlK,OAAqBA,EAAQ,EAAA,EAAG,eAAiB,iBAAe,kIAiD5D,OAAAA,EAAoB,CAAA,GAAA,UAAYA,MAAoB,aAAY,EAO3DA,MAAoB,MAAK,0BAuI/B,IAAAkG,EAAAlG,OAAqB,OAAK8F,GAAA9F,CAAA,wMA/I/BC,EA6IGC,EAAAc,EAAAZ,CAAA,iMAEEJ,OAAqB,8OA7KpB8H,EAAA9H,KAAU,OAAM;;iJAcR,WAAAA,KAAI,WACJ,WAAAA,MAAYA,EAAI,CAAA,gDAjB7B,wBACuB,kBACpB,GAAC,aAAkB,GAAC,qBAGrB;AAAA,wBAEe,MAACA,EAAgB,CAAA,CAAA,MAAA;AAAA,eAElC,+KATA,wBACuB,4CACpB,GAAC,iBAAkB,GAAC,kEAGrB;AAAA,wBAEe,QAACA,EAAgB,CAAA,CAAA,QAAA;AAAA,eAElC,0fAbDC,EA6BKC,EAAAC,EAAAC,CAAA,EA5BJC,EAOGF,EAAAI,CAAA,SAHoBF,EAEtBE,EAAAmD,CAAA,8BAEDrD,EAIGF,EAAAM,CAAA,kDAWHJ,EAIGF,EAAAgK,CAAA,WAEJlK,EAIGC,EAAAkK,EAAAhK,CAAA,gBA5BG,CAAAkE,GAAAjB,EAAA,CAAA,EAAA,KAAAyE,KAAAA,EAAA9H,KAAU,OAAM,KAAAsD,EAAAuE,EAAAC,CAAA,qBAKH9H,EAAgB,CAAA,CAAA,gIASrBqD,EAAA,CAAA,EAAA,IAAAgH,EAAA,WAAArK,KAAI,YACJqD,EAAA,CAAA,EAAA,IAAAgH,EAAA,WAAArK,MAAYA,EAAI,CAAA,+KAyJvB,yDAEN,cAFM,yDAEN,yHAnIMA,EAAiB,CAAA,EAAA,+WAR8C;AAAA,QAEpE,kBAAyCA,EAAgB,CAAA,CAAA,MAAO;AAAA,gBACxD,eAES,MAAI,MACpB,2CACF,uBAPqE;AAAA,QAEpE,4CAAyCA,EAAgB,CAAA,CAAA,qBAAO;AAAA,gBACxD,2DAES,MAAI,qBACpB,2CACF,oDAHQuB,EAAA+I,EAAA,OAAAC,EAAAvK,MAAoB,SAAWwK,GAAUC,EAAO,uEAFvDxK,EAAgEC,EAAAwD,EAAAtD,CAAA,kBACxDH,EAGPC,EAAAoK,EAAAlK,CAAA,uCAJwCJ,EAAgB,CAAA,CAAA,EAElDqD,EAAA,CAAA,EAAA,IAAAkH,KAAAA,EAAAvK,MAAoB,SAAWwK,GAAUC,iLA8H1C;AAAA;AAAA,8BAGgB,kCAAqC;AAAA,iBAClD,kCAA8B;AAAA,SACtC,kCAAmB,GACpB,uBANM;AAAA;AAAA,8BAGgB,+FAAqC;AAAA,iBAClD,+FAA8B;AAAA,SACtC,+FAAmB,GACpB,wHAHsBxK,EAAqCC,EAAAM,EAAAJ,CAAA,WAClDH,EAA8BC,EAAAwK,EAAAtK,CAAA,WACtCH,EAAmBC,EAAAyK,EAAAvK,CAAA,mVA/GXJ,EAAK,EAAA,CAAA,wBAAV,OAAImE,GAAA,wFAgEFnE,EAAiB,EAAA,GAAA4K,GAAA,6RAhBQ;AAAA;AAAA;AAAA,SAG9B,4GA+BgC;AAAA;AAAA,SAEhC,+BAEC;AAAA,SACD,iHAemC,wCAElC,ieAxD6B;AAAA;AAAA;AAAA,SAG9B,yNA+BgC;AAAA;AAAA,SAEhC,8GAEC;AAAA,SACD,2RAemC,wCAElC,iMAFSC,EAAQ,mFAzGlB5K,EAAYC,EAAAK,EAAAH,CAAA,WACZH,EAAmCC,EAAA4K,EAAA1K,CAAA,WACnCH,EA8CKC,EAAAC,EAAAC,CAAA,oEACLH,EAAYC,EAAAO,EAAAL,CAAA,WAEZH,EAA8BC,EAAA6K,EAAA3K,CAAA,WAG9BH,EAAYC,EAAAiK,EAAA/J,CAAA,qDA+BZH,EAAgCC,EAAA8K,EAAA5K,CAAA,WAEhCH,EAECC,EAAA+K,EAAA7K,CAAA,WACDH,EAAYC,EAAAkK,EAAAhK,CAAA,8BAaZH,EAAYC,EAAAgL,EAAA9K,CAAA,YACZH,EAIGC,EAAAiL,EAAA/K,CAAA,EAHFC,EAEG8K,EAAAC,EAAA,2GAxGIpL,EAAK,EAAA,CAAA,uBAAV,OAAImE,IAAA,EAAA,uIAAJ,gFAgEEnE,EAAiB,EAAA;+DAvEDA,EAAc,EAAA,CAAA,kcADGA,EAAc,EAAA,iHANnDC,EASKC,EAAAyB,EAAAvB,CAAA,EARJC,EAGAsB,EAAA0J,CAAA,SACAhL,EAGKsB,EAAAC,CAAA,EAFJvB,EAAoDuB,EAAA0J,CAAA,qMA4B7C,OAAO,KAAKtL,MAAK,UAAU,EAAE,OAAS,uMAD5CC,EAsBKC,EAAAC,EAAAC,CAAA,8RAFHH,EAA+BC,EAAAc,EAAAZ,CAAA,4CAjBvBmL,EAAAC,GAAA,OAAO,QAAQxL,MAAK,UAAU,CAAA,uBAAnC,OAAImE,GAAA,0OADPlE,EAgBKC,EAAAC,EAAAC,CAAA,yEAfGmL,EAAAC,GAAA,OAAO,QAAQxL,MAAK,UAAU,CAAA,oBAAnC,OAAImE,GAAA,EAAA,mHAAJ,yDAEOnE,EAAI,EAAA,EAAA,WAER2F,EAAA3F,MAAM,KAAI,OAAEA,EAAK,EAAA,EAAC,UAAY,OACf,cAAA,KAAK,UAAUA,MAAM,OAAO,CAAA,GAC1C,WAGFgG,GAAAhG,EAAM,EAAA,EAAA,YACJA,EAAM,EAAA,EAAA,YACN,8DAA4D,sEARpC,GAC1B,oBAEK,GACP,2LAJ4B,GAC1B,0BAEK,GACP,0RANDC,EAYKC,EAAAC,EAAAC,CAAA,EAXJC,EAAkBF,EAAAuE,CAAA,gBAClBrE,EAIMF,EAAAuD,CAAA,qCACNrD,EAIGF,EAAAa,CAAA,0CAVIhB,EAAI,EAAA,EAAA,KAAAsD,EAAAoG,EAAAD,CAAA,EAERpG,EAAA,CAAA,EAAA,MAAAsC,KAAAA,EAAA3F,MAAM,KAAI,KAAAsD,EAAAsC,EAAAD,CAAA,oBAAE3F,EAAK,EAAA,EAAC,UAAY,OACf,cAAA,KAAK,UAAUA,MAAM,OAAO,CAAA,GAC1C,KAAEsD,EAAAyC,EAAAF,CAAA,EAGJxC,EAAA,CAAA,EAAA,MAAA2C,KAAAA,GAAAhG,EAAM,EAAA,EAAA,YACJA,EAAM,EAAA,EAAA,YACN,8DAA4D,KAAAsD,EAAAiD,EAAAP,CAAA,6CA1B1CyD,EAAAzJ,MAAK,KAAI,SAEhC8H,GAAA9H,EAAK,EAAA,EAAA,YACJA,EAAK,EAAA,EAAA,YACL,oDAAkD,SAIpD6F,EAAA7F,EAAK,EAAA,EAAA,SAAW,IAAM,2DAGrB,IAAAoF,EAAApF,MAAK,UAAQyL,GAAAzL,CAAA,yEAX2B;AAAA,cAC3C,uPAD2C;AAAA,cAC3C,+ZAPHC,EA0CKC,EAAAC,EAAAC,CAAA,EAzCJC,EAeQF,EAAAO,CAAA,EAXPL,EAOAK,EAAAyF,CAAA,EANE9F,EAA0C8F,EAAArE,CAAA,gBAC3CzB,EAIC8F,EAAAtE,CAAA,gBAEFxB,EAEAK,EAAA0F,CAAA,8EAT2B/C,EAAA,CAAA,EAAA,MAAAoG,KAAAA,EAAAzJ,MAAK,KAAI,KAAAsD,EAAAoG,EAAAD,CAAA,EAEhCpG,EAAA,CAAA,EAAA,MAAAyE,KAAAA,GAAA9H,EAAK,EAAA,EAAA,YACJA,EAAK,EAAA,EAAA,YACL,oDAAkD,KAAAsD,EAAAuE,EAAAC,CAAA,EAIpDzE,EAAA,CAAA,EAAA,MAAAwC,KAAAA,EAAA7F,EAAK,EAAA,EAAA,SAAW,IAAM,MAAGsD,EAAAyC,EAAAF,CAAA,EAGxB7F,MAAK,iIA0CJwD,EAAA,KAAK,UAAUxD,EAAc,EAAA,EAAA,KAAM,CAAC,EAAA,+BAJnC,KAAA,KAAK,UAAUA,EAAc,EAAA,EAAA,KAAM,CAAC,obAH7CC,EASMC,EAAAwE,EAAAtE,CAAA,EARLC,EAIKqE,EAAA9C,CAAA,sBACLvB,EAEKqE,EAAA/C,CAAA,EADJtB,EAAiDsB,EAAAmD,CAAA,iCAJ1CzB,EAAA,CAAA,EAAA,OAAAmC,EAAA,KAAA,KAAK,UAAUxF,EAAc,EAAA,EAAA,KAAM,CAAC,cAIrC,CAAAsE,GAAAjB,EAAA,CAAA,EAAA,OAAAG,KAAAA,EAAA,KAAK,UAAUxD,EAAc,EAAA,EAAA,KAAM,CAAC,EAAA,KAAAsD,EAAAG,EAAAD,CAAA,4SAMnB;AAAA;AAAA,UAEzB,kCAAkC;AAAA;AAAA,mCAET,kCAA4B;AAAA;AAAA;AAAA;AAAA;AAAA,UAKrD,+BAGC;AAAA,UACD,8MAbyB;AAAA;AAAA,UAEzB,gGAAkC;AAAA;AAAA,mCAET,+FAA4B;AAAA;AAAA;AAAA;AAAA;AAAA,UAKrD,8GAGC;AAAA,UACD,uRAdAvD,EAAYC,EAAAK,EAAAH,CAAA,WACZH,EAAyBC,EAAAwL,EAAAtL,CAAA,WAEzBH,EAAkCC,EAAAM,EAAAJ,CAAA,WAETH,EAA4BC,EAAAwK,EAAAtK,CAAA,WAKrDH,EAGCC,EAAAoK,EAAAlK,CAAA,WACDH,EAAYC,EAAAO,EAAAL,CAAA,qGAiBJoD,EAAA,KAAK,UAAUxD,EAAgB,EAAA,EAAA,KAAM,CAAC,EAAA,+BAJrC,KAAA,KAAK,UAAUA,EAAgB,EAAA,EAAA,KAAM,CAAC,obAH/CC,EASMC,EAAAwE,EAAAtE,CAAA,EARLC,EAIKqE,EAAA9C,CAAA,sBACLvB,EAEKqE,EAAA/C,CAAA,EADJtB,EAAmDsB,EAAAmD,CAAA,iCAJ5CzB,EAAA,CAAA,EAAA,OAAAmC,EAAA,KAAA,KAAK,UAAUxF,EAAgB,EAAA,EAAA,KAAM,CAAC,cAIvC,CAAAsE,GAAAjB,EAAA,CAAA,EAAA,OAAAG,KAAAA,EAAA,KAAK,UAAUxD,EAAgB,EAAA,EAAA,KAAM,CAAC,EAAA,KAAAsD,EAAAG,EAAAD,CAAA,4KA6B3CxD,EAAQ,CAAA,GAAA2L,GAAA3L,CAAA,gHAsBR,IAAAkG,EAAAlG,MAAoB,QAAM4L,GAAA5L,CAAA,kDA1Bf;AAAA;AAAA;AAAA,QAIhB,eASQ;AAAA;AAAA;AAAA,QAGR,wBAQQ;AAAA;AAAA,QAER,4FA1BgB;AAAA;AAAA;AAAA,QAIhB,kBASQ;AAAA;AAAA;AAAA,QAGR,4BAQQ;AAAA;AAAA,QAER,sGA1BDC,EA8CGC,EAAAc,EAAAZ,CAAA,uIA1CGJ,EAAQ,CAAA,qHAsBRA,MAAoB,2QAtBX;AAAA,0CACoB,eAOhB,WAAS,MACzB,IAAE,uBATU;AAAA,0CACoB,2DAOhB,WAAS,qBACzB,IAAE,gBAPIuB,EAAA+I,EAAA,OAAAC,EAAAvK,MAAoB,SACvBwK,GAAUqB,GACV7L,MAAoB,aACnByK,GAAUoB,GACVC,EAAS,iFALmB7L,EAQhCC,EAAAoK,EAAAlK,CAAA,0BAPMiD,EAAA,CAAA,EAAA,IAAAkH,KAAAA,EAAAvK,MAAoB,SACvBwK,GAAUqB,GACV7L,MAAoB,aACnByK,GAAUoB,GACVC,2YAYL7L,EAA8BC,EAAAC,EAAAC,CAAA,WAC9BH,EAAwCC,EAAAc,EAAAZ,CAAA,qLAYlBgF,EAAApF,OAAa,MAAIqF,GAAA,2BATF,GAAM,gBAAM;AAAA;AAAA,SAEjD,oCAA2B;AAAA,SAC3B,kCAAgB;AAAA,eACV,kCAAgB,gBAAc,kCAAiB;AAAA,oBAC1C,kCAAqB;AAAA,SAChC,kCAAgB;AAAA,8BACK,kCAAgB,OAAK,kCAAiB;AAAA;AAAA,0BAE1C,eAEQ;AAAA,SACzB,eAAoC,WAAS,MAAI,GAClD,sCAbsC,GAAM,uBAAM;AAAA;AAAA,SAEjD,wFAA2B;AAAA,SAC3B,gGAAgB;AAAA,eACV,gGAAgB,gBAAc,gGAAiB;AAAA,oBAC1C,+FAAqB;AAAA,SAChC,gGAAgB;AAAA,8BACK,+FAAgB,OAAK,+FAAiB;AAAA;AAAA,0BAE1C,kBAEQ;AAAA,SACzB,2DAAoC,WAAS,qBAAI,GAClD,sOADUyG,EAAS,8DAZa7L,EAAMC,EAAA6L,EAAA3L,CAAA,WAAMH,EAAMC,EAAA8L,EAAA5L,CAAA,WAEjDH,EAA2BC,EAAA+L,EAAA7L,CAAA,WAC3BH,EAAgBC,EAAAM,EAAAJ,CAAA,WACVH,EAAgBC,EAAAwK,EAAAtK,CAAA,WAAcH,EAAiBC,EAAAyK,EAAAvK,CAAA,WAC1CH,EAAqBC,EAAAgM,EAAA9L,CAAA,WAChCH,EAAgBC,EAAAiM,EAAA/L,CAAA,WACKH,EAAgBC,EAAAkM,EAAAhM,CAAA,WAAKH,EAAiBC,EAAAmM,EAAAjM,CAAA,gCAK3DH,EAAiDC,EAAAoK,EAAAlK,CAAA,0BAH3BJ,OAAa,4OAAI;AAAA,6BAEnB,cAFmB;AAAA,6BAEnB,+DAchBA,EAAY,CAAA,CAAA,uBAAjB,OAAImE,GAAA,oQAACnE,EAAY,CAAA,CAAA,oBAAjB,OAAImE,GAAA,EAAA,oHAAJ,OAAIA,EAAAmI,EAAA,OAAAnI,GAAA,0CAAJ,OAAIA,GAAA,0LAImBnE,EAAI,CAAA,EAAC,gBACzB,IAAMA,EAAU,EAAA,EAAC,UAChB,wFAMU,WAAAA,KAAI,2BACCA,EAAI,CAAA,EAAC,gBACrB,IAAMA,EAAU,EAAA,EAAC,UAChB,iDAIgBA,EAAI,CAAA,EAAC,gBACtB,IAAMA,EAAU,EAAA,EAAC,UAChB,sBACUA,EAAO,CAAA,EAAC,gBAAgB,IAAMA,EAAU,EAAA,EAAC,QAAQ,EAC3D,oFAMgBA,EAAI,CAAA,EAAC,gBACtB,IAAMA,EAAU,EAAA,EAAC,UAChB,mBACUA,EAAO,CAAA,EAAC,gBAAgB,IAAMA,EAAU,EAAA,EAAC,QAAQ,EAC3D,6VA/BJC,EAmCKC,EAAAC,EAAAC,CAAA,uHAjCkBJ,EAAI,CAAA,EAAC,gBACzB,IAAMA,EAAU,EAAA,EAAC,UAChB,uJAMUqD,EAAA,CAAA,EAAA,IAAAkJ,EAAA,WAAAvM,KAAI,yCACCA,EAAI,CAAA,EAAC,gBACrB,IAAMA,EAAU,EAAA,EAAC,UAChB,gEAIgBA,EAAI,CAAA,EAAC,gBACtB,IAAMA,EAAU,EAAA,EAAC,UAChB,oCACUA,EAAO,CAAA,EAAC,gBAAgB,IAAMA,EAAU,EAAA,EAAC,QAAQ,EAC3D,kGAMgBA,EAAI,CAAA,EAAC,gBACtB,IAAMA,EAAU,EAAA,EAAC,UAChB,iCACUA,EAAO,CAAA,EAAC,gBAAgB,IAAMA,EAAU,EAAA,EAAC,QAAQ,EAC3D,8PAhCAA,EAAU,EAAA,EAAC,UAAYA,EAAI,CAAA,EAAC,gBAAgB,IAAMA,EAAU,EAAA,EAAC,QAAQ,GAAAuD,GAAAvD,CAAA,8FAArEA,EAAU,EAAA,EAAC,UAAYA,EAAI,CAAA,EAAC,gBAAgB,IAAMA,EAAU,EAAA,EAAC,QAAQ,wMA5Q3EA,EAAI,CAAA,GAAAqB,GAAArB,CAAA,8FAAJA,EAAI,CAAA,qLApOFyK,GACL,mEACKD,GACL,uEACKsB,GACL,+DACKD,GAAqB,sCACrBhB,GACL,oEAsCGtG,GAAa,GAtBR,SAAAiI,GAAgB5J,EAAaR,EAAA,OAC/BqK,EAAU,IAAA,IAAI,OAAO,SAAS,IAAI,EACxCA,EAAI,aAAa,IAAI7J,EAAKR,CAAK,EAC/B,QAAQ,aAAa,KAAM,GAAIqK,EAAI,SAAA,CAAA,WAG3BC,GAAgB9J,EAAA,QACR,IAAA,IAAI,OAAO,SAAS,IAAI,EAC7B,aAAa,IAAIA,CAAG,WAGvB+J,GAAkBrK,EAAA,CAClB,MAAA,CAAA,SAAU,aAAc,OAAQ,KAAK,EAAE,SAASA,GAAQ,EAAE,qBA3CxD,GAAA,CAAA,aAAAyF,CAAA,EAAAjH,EACA,CAAA,KAAAD,CAAA,EAAAC,EACA,CAAA,IAAA8L,CAAA,EAAA9L,EACA,CAAA,SAAA8F,CAAA,EAAA9F,EACA,CAAA,UAAA+L,CAAA,EAAA/L,EACA,CAAA,SAAAgG,CAAA,EAAAhG,EAYPkB,EAAY+F,EAAa,OAC3BrB,GAAeA,EAAW,QAC1B,EAAA,OAEE7F,IAAS,KACZA,EAAO,SAAS,SAAW,KAAO,SAAS,KAAO,SAAS,UAEvDA,EAAK,SAAS,GAAG,IACrBA,GAAQ,KAGE,GAAA,CAAA,UAAAsH,EAAA,EAAA,EAAArH,EACPmB,EAA6D,SAiB3D,MAAA6K,EAAA,CACJ,CAAA,SAAU,SAAUxD,EAAM,EAC1B,CAAA,aAAc,aAAcC,EAAU,EACtC,CAAA,OAAQ,OAAQC,EAAI,EACpB,CAAA,MAAO,MAAOG,EAAG,OAIfoD,EAAoB,GAET,eAAA3E,GAAA,CAQP,OADH,MAHA,MAAiB,MACpBvH,EAAK,QAAQ,MAAO,EAAE,EAAI+L,EAAI,WAAa,OAAA,GAElB,OAGZ,eAAAI,GAAA,CAEP,OADH,MAAoBJ,EAAI,WAIzB,IAAAK,EAKAC,EAEJ9E,EAAA,EAAW,KAAM+E,GAAA,KAChBF,EAAOE,CAAA,IAGRH,EAAA,EAAc,KAAMI,GAAA,KACnBF,EAAUE,CAAA,UAGLzM,EAAWC,KAEXyM,EAAA,GAAoBxM,CAAI,qBAiB1B,IAAAyM,EAAA,CAAA,EACAC,EAAA,CAAA,EACAC,EACAC,EACAC,EAAoB,GAElB,MAAAC,EAAA,CACL,QAAS,MACT,KAAA,CACC,SACA,cACA,SACA,aACA9M,EACA,oBAAA,GAIa,eAAA+M,GAAA,KAGP,MAAAC,EAAA,MADA,MAAiB,SAAShN,CAAI,uBAAA,GACN,YAC9B6M,EAAoBG,EAClB,IAAKC,GAAc,OAAA,OAAAjL,EAAAiL,EAAK,OAAL,YAAAjL,EAAW,kBAAiB,EAC/C,KAAMkL,GAAqBA,CAAO,CAAA,EAEpCvG,EAAA,GAAA8F,EAAQO,EAAO,IAAKC,GAAA,OAAA,OACnB,KAAMA,EAAK,KACX,YAAaA,EAAK,aAAe,GACjC,aAAYjL,EAAAiL,EAAK,cAAL,YAAAjL,EAAkB,aAAA,CAAA,EAC9B,SAAU,OAEX0K,EAAUM,EAAO,IAAKC,GAAc,OAAA,QAAAjL,EAAAiL,EAAK,OAAL,YAAAjL,EAAW,UAAe,CAAA,EAAA,EAAA,OAC1D0K,EAAQ,OAAS,GACpB/F,EAAA,GAAAgG,EAAA,CACC,WAAA,CACC,OAAA,CACC,IAAKH,EACL,QAASE,EAAQ,QAAQS,EAAaC,KAErCD,EAAYC,CAAW,EAAI,sBACpBD,WAKXxG,EAAA,GAAAiG,EAAA,CACC,WAAA,CACC,OAAA,CACC,QAAS,MACT,KAAA,CACC,aACAJ,EACA,cACA,WACG,GAAAE,EACD,IAAKW,GAAA,CACL,WACG,GAAAA,CAAM,uBAET,CAAA,EAAA,KAAA,SAMN1G,EAAA,GAAAgG,EAAA,CACC,WAAA,CACC,QACC,IAAKH,CAAA,CAAA,IAIR7F,EAAA,GAAAiG,EAAA,CACC,WAAA,CACC,OAAA,CACC,QAAS,MACT,KAAO,CAAA,aAAcJ,EAAgB,cAAe,UAAU,MAI7DK,IACHlG,EAAA,GAAAgG,EAAa,WAAW,uBACvBG,EAAAH,CAAA,EACDhG,EAAA,GAAAiG,EAAe,WAAW,uBACzBE,EAAAF,CAAA,GAGK,OAAAU,EAAA,CACR,QAAQ,MAAM,6BAA8BA,CAAK,EACjD3G,EAAA,GAAA8F,EAAA,CAAA,CAAA,GAIFtE,GAAA,IAAA,OACC,SAAS,KAAK,MAAM,SAAW,SAC3B,iBAAkB,UACrBnG,EAAA,OAAO,eAAP,MAAAA,EAAqB,SAAS,EAAG,IAG5B,MAAAuL,EAAa1B,GAAgB,MAAM,EACrC,OAAAC,GAAkByB,CAAU,OAC/BnM,EAAmBmM,CAAA,EAIpB,MAAMf,CAAc,EAClB,KAAMgB,GAAA,CACN7G,EAAA,EAAAuF,EAAoBsB,EAAS,EAAA,EACzBtB,GACHa,IACKjB,GAAkByB,CAAU,OAChCnM,EAAmB,KAAA,GAGf0K,GAAkByB,CAAU,OAChCnM,EAAmB,QAAA,CAIrB,CAAA,EAAA,MAAA,IAAA,KACA8K,EAAoB,EAAA,SAIrB,SAAS,KAAK,MAAM,SAAW,sDA6B1BvF,EAAA,EAAAvF,EAAmBqM,CAAQ,EAC3B9B,GAAgB,OAAQ8B,CAAQ,wBA0EN,SAAQ,CAAIR,EAAK,SAAQR,CAAA,EAyIlDiB,EAAA,IAAA5N,EAAS,QAAW,CAAA,qBAAsB,EAAI,CAAA"}