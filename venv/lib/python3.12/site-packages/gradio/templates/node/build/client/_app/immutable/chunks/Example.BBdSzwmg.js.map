{"version": 3, "file": "Example.BBdSzwmg.js", "sources": ["../../../../../../../html/Example.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\texport let value: string;\n\texport let type: \"gallery\" | \"table\";\n\texport let selected = false;\n</script>\n\n<div\n\tclass:table={type === \"table\"}\n\tclass:gallery={type === \"gallery\"}\n\tclass:selected\n\tclass=\"prose\"\n>\n\t{@html value}\n</div>\n\n<style>\n\t.gallery {\n\t\tpadding: var(--size-2);\n\t}\n</style>\n"], "names": ["toggle_class", "div", "ctx", "insert_hydration", "target", "anchor", "value", "$$props", "type", "selected"], "mappings": "keAOcA,EAAAC,EAAA,QAAAC,OAAS,OAAO,EACdF,EAAAC,EAAA,UAAAC,OAAS,SAAS,+BAFlCC,EAOKC,EAAAH,EAAAI,CAAA,MADGH,EAAK,CAAA,EAAAD,CAAA,qBAALC,EAAK,CAAA,CAAA,OALCF,EAAAC,EAAA,QAAAC,OAAS,OAAO,OACdF,EAAAC,EAAA,UAAAC,OAAS,SAAS,sEAPtB,GAAA,CAAA,MAAAI,CAAA,EAAAC,EACA,CAAA,KAAAC,CAAA,EAAAD,GACA,SAAAE,EAAW,EAAA,EAAAF"}