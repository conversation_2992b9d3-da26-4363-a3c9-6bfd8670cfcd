import{SvelteComponent as o,init as d,safe_not_equal as h,element as _,text as m,claim_element as g,children as y,claim_text as v,detach as c,attr as b,toggle_class as s,insert_hydration as p,append_hydration as E,set_data as S,noop as f}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";function q(n){let e,t=u(n[0])+"",r;return{c(){e=_("pre"),r=m(t),this.h()},l(l){e=g(l,"PRE",{class:!0});var a=y(e);r=v(a,t),a.forEach(c),this.h()},h(){b(e,"class","svelte-agpzo2"),s(e,"table",n[1]==="table"),s(e,"gallery",n[1]==="gallery"),s(e,"selected",n[2])},m(l,a){p(l,e,a),E(e,r)},p(l,[a]){a&1&&t!==(t=u(l[0])+"")&&S(r,t),a&2&&s(e,"table",l[1]==="table"),a&2&&s(e,"gallery",l[1]==="gallery"),a&4&&s(e,"selected",l[2])},i:f,o:f,d(l){l&&c(e)}}}function u(n,e=60){if(!n)return"";const t=String(n);return t.length<=e?t:t.slice(0,e)+"..."}function z(n,e,t){let{value:r}=e,{type:l}=e,{selected:a=!1}=e;return n.$$set=i=>{"value"in i&&t(0,r=i.value),"type"in i&&t(1,l=i.type),"selected"in i&&t(2,a=i.selected)},[r,l,a]}class R extends o{constructor(e){super(),d(this,e,z,q,h,{value:0,type:1,selected:2})}}export{R as default};
//# sourceMappingURL=Example.HHpueynA.js.map
