const o="简体中文",e={annotated_image:"标注图像"},r={allow_recording_access:"请允许访问麦克风以进行录音。",audio:"音频",record_from_microphone:"从麦克风录制",stop_recording:"停止录制",no_device_support:"无法访问媒体设备。请检查您是否在安全来源（https）或本地主机上运行（或者您已经通过 ssl_verify 传递了有效的 SSL 证书），并且您已经允许浏览器访问您的设备。",stop:"停止",resume:"继续",record:"录制",no_microphone:"找不到麦克风",pause:"暂停",play:"播放",waiting:"等待中",drop_to_upload:"将音频文件拖放到此处以上传"},t={connection_can_break:"在移动设备上，如果此标签页失去焦点或设备休眠，连接可能会中断，导致您在队列中失去位置。",long_requests_queue:"有一个长时间的待处理请求队列。复制此 Space 以跳过队列。",lost_connection:"由于离开页面，连接已丢失。正在重新加入队列...",waiting_for_inputs:"等待文件上传完成，请稍后重试。"},a={checkbox:"复选框",checkbox_group:"复选框组"},c={code:"代码"},_={color_picker:"颜色选择器"},n={built_with:"构建于",built_with_gradio:"使用 Gradio 构建",clear:"清除",download:"下载",edit:"编辑",empty:"空",error:"错误",hosted_on:"托管在",loading:"加载中",logo:"标志",or:"或",remove:"移除",share:"分享",submit:"提交",undo:"撤销",settings:"设置",no_devices:"未找到设备",language:"语言",display_theme:"显示主题",pwa:"渐进式 Web 应用"},d={incorrect_format:"格式不正确，仅支持 CSV 和 TSV 文件",new_column:"添加列",new_row:"新行",add_row_above:"在上方添加行",add_row_below:"在下方添加行",delete_row:"删除行",delete_column:"删除列",add_column_left:"在左侧添加列",add_column_right:"在右侧添加列",sort_column:"排序列",sort_ascending:"升序排序",sort_descending:"降序排序",drop_to_upload:"将 CSV 或 TSV 文件拖放到此处以将数据导入数据框",clear_sort:"清除排序"},i={dropdown:"下拉菜单"},s={build_error:"存在构建错误",config_error:"存在配置错误",contact_page_author:"请联系页面的作者并告知他们。",no_app_file:"不存在应用文件",runtime_error:"存在运行时错误",space_not_working:'"Space 无法工作，原因：" {0}',space_paused:"Space 已暂停",use_via_api:"通过 API 使用",use_via_api_or_mcp:"通过 API 或 MCP 使用"},l={uploading:"正在上传..."},p={highlighted_text:"高亮文本"},u={allow_webcam_access:"请允许访问网络摄像头以进行录制。",brush_color:"画笔颜色",brush_radius:"画笔大小",image:"图像",remove_image:"移除图像",select_brush_color:"选择画笔颜色",start_drawing:"开始绘画",use_brush:"使用画笔",drop_to_upload:"将图像文件拖放到此处以上传"},g={label:"标签"},m={enable_cookies:"如果您正在使用隐身模式访问 HuggingFace Space，您必须启用第三方 cookie。",incorrect_credentials:"凭据不正确",login:"登录",username:"用户名",password:"密码"},b={number:"数字"},h={plot:"图表"},w={radio:"单选框"},k={slider:"滑块"},f={click_to_upload:"点击上传",drop_audio:"将音频拖放到此处",drop_csv:"将 CSV 文件拖放到此处",drop_file:"将文件拖放到此处",drop_image:"将图像拖放到此处",drop_video:"将视频拖放到此处",drop_gallery:"在此处放置媒体文件",paste_clipboard:"从剪贴板粘贴"},v={drop_to_upload:"将视频文件拖放到此处以上传"},S={edit:"编辑",retry:"重试",undo:"撤销",submit:"发送",cancel:"取消",like:"赞",dislike:"踩",clear:"清空对话"},x={_name:o,"3D_model":{"3d_model":"3D模型",drop_to_upload:"将 3D 模型（.obj、.glb、.stl、.gltf、.splat 或 .ply）文件拖放到此处以上传"},annotated_image:e,audio:r,blocks:t,checkbox:a,code:c,color_picker:_,common:n,dataframe:d,dropdown:i,errors:s,file:l,highlighted_text:p,image:u,label:g,login:m,number:b,plot:h,radio:w,slider:k,upload_text:f,video:v,chatbot:S};export{o as _name,e as annotated_image,r as audio,t as blocks,S as chatbot,a as checkbox,c as code,_ as color_picker,n as common,d as dataframe,x as default,i as dropdown,s as errors,l as file,p as highlighted_text,u as image,g as label,m as login,b as number,h as plot,w as radio,k as slider,f as upload_text,v as video};
//# sourceMappingURL=zh-CN.C4t77Jdq.js.map
