import{SvelteComponent as m,init as v,safe_not_equal as y,element as u,text as g,claim_element as d,children as o,claim_text as b,detach as f,attr as E,toggle_class as r,insert_hydration as p,append_hydration as h,set_data as S,noop as _}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";function q(i){let e,a,n=JSON.stringify(i[0],null,2)+"",s;return{c(){e=u("div"),a=u("pre"),s=g(n),this.h()},l(l){e=d(l,"DIV",{class:!0});var t=o(e);a=d(t,"PRE",{});var c=o(a);s=b(c,n),c.forEach(f),t.forEach(f),this.h()},h(){E(e,"class","svelte-1ayixqk"),r(e,"table",i[1]==="table"),r(e,"gallery",i[1]==="gallery"),r(e,"selected",i[2])},m(l,t){p(l,e,t),h(e,a),h(a,s)},p(l,[t]){t&1&&n!==(n=JSON.stringify(l[0],null,2)+"")&&S(s,n),t&2&&r(e,"table",l[1]==="table"),t&2&&r(e,"gallery",l[1]==="gallery"),t&4&&r(e,"selected",l[2])},i:_,o:_,d(l){l&&f(e)}}}function J(i,e,a){let{value:n}=e,{type:s}=e,{selected:l=!1}=e;return i.$$set=t=>{"value"in t&&a(0,n=t.value),"type"in t&&a(1,s=t.type),"selected"in t&&a(2,l=t.selected)},[n,s,l]}class k extends m{constructor(e){super(),v(this,e,J,q,y,{value:0,type:1,selected:2})}}export{k as default};
//# sourceMappingURL=Example.qq1Un4eO.js.map
