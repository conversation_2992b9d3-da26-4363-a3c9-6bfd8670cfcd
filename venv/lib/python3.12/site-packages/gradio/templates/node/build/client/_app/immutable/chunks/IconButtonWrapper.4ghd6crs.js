import{SvelteComponent as c,init as u,safe_not_equal as f,create_slot as d,element as m,claim_element as h,children as v,detach as a,attr as _,null_to_empty as p,insert_hydration as y,update_slot_base as b,get_all_dirty_from_scope as g,get_slot_changes as I,transition_in as w,transition_out as q}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";import"./2.CXBv8kT_.js";function B(r){let n,s,o;const i=r[3].default,t=d(i,r,r[2],null);return{c(){n=m("div"),t&&t.c(),this.h()},l(e){n=h(e,"DIV",{class:!0});var l=v(n);t&&t.l(l),l.forEach(a),this.h()},h(){_(n,"class",s=p(`icon-button-wrapper ${r[0]?"top-panel":""} ${r[1]?"display-top-corner":"hide-top-corner"}`)+" svelte-9lsba8")},m(e,l){y(e,n,l),t&&t.m(n,null),o=!0},p(e,[l]){t&&t.p&&(!o||l&4)&&b(t,i,e,e[2],o?I(i,e[2],l,null):g(e[2]),null),(!o||l&3&&s!==(s=p(`icon-button-wrapper ${e[0]?"top-panel":""} ${e[1]?"display-top-corner":"hide-top-corner"}`)+" svelte-9lsba8"))&&_(n,"class",s)},i(e){o||(w(t,e),o=!0)},o(e){q(t,e),o=!1},d(e){e&&a(n),t&&t.d(e)}}}function C(r,n,s){let{$$slots:o={},$$scope:i}=n,{top_panel:t=!0}=n,{display_top_corner:e=!1}=n;return r.$$set=l=>{"top_panel"in l&&s(0,t=l.top_panel),"display_top_corner"in l&&s(1,e=l.display_top_corner),"$$scope"in l&&s(2,i=l.$$scope)},[t,e,i,o]}class V extends c{constructor(n){super(),u(this,n,C,B,f,{top_panel:0,display_top_corner:1})}}export{V as I};
//# sourceMappingURL=IconButtonWrapper.4ghd6crs.js.map
