import{SvelteComponent as F,init as G,safe_not_equal as H,ensure_array_like as I,element as w,text as A,space as J,claim_element as z,children as E,claim_text as B,detach as d,claim_space as K,attr as q,add_render_callback as L,toggle_class as h,insert_hydration as v,append_hydration as y,add_iframe_resize_listener as Q,set_data as C,transition_in as p,group_outros as M,check_outros as N,transition_out as b,destroy_each as R,onMount as T,empty as V,binding_callbacks as X,noop as k,src_url_equal as D,create_component as O,claim_component as U,mount_component as W,destroy_component as j}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";import{r as Y}from"./2.CXBv8kT_.js";import{V as Z}from"./Video.Ca-tCBrJ.js";function P(s,e,n){const t=s.slice();return t[7]=e[n],t}function $(s){let e=s[7].orig_name+"",n;return{c(){n=A(e)},l(t){n=B(t,e)},m(t,r){v(t,n,r)},p(t,r){r&1&&e!==(e=t[7].orig_name+"")&&C(n,e)},i:k,o:k,d(t){t&&d(n)}}}function x(s){let e,n;return{c(){e=w("audio"),this.h()},l(t){e=z(t,"AUDIO",{src:!0}),E(e).forEach(d),this.h()},h(){D(e.src,n=s[7].url)||q(e,"src",n),e.controls=!0},m(t,r){v(t,e,r)},p(t,r){r&1&&!D(e.src,n=t[7].url)&&q(e,"src",n)},i:k,o:k,d(t){t&&d(e)}}}function ee(s){let e,n;return e=new Z({props:{src:s[7].url,alt:"",loop:!0,is_stream:!1}}),{c(){O(e.$$.fragment)},l(t){U(e.$$.fragment,t)},m(t,r){W(e,t,r),n=!0},p(t,r){const c={};r&1&&(c.src=t[7].url),e.$set(c)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){b(e.$$.fragment,t),n=!1},d(t){j(e,t)}}}function te(s){let e,n;return e=new Y({props:{src:s[7].url,alt:""}}),{c(){O(e.$$.fragment)},l(t){U(e.$$.fragment,t)},m(t,r){W(e,t,r),n=!0},p(t,r){const c={};r&1&&(c.src=t[7].url),e.$set(c)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){b(e.$$.fragment,t),n=!1},d(t){j(e,t)}}}function S(s){let e,n,t,r,c,u,f;const m=[te,ee,x,$],i=[];function _(l,o){return o&1&&(e=null),o&1&&(n=null),o&1&&(t=null),e==null&&(e=!!(l[7].mime_type&&l[7].mime_type.includes("image"))),e?0:(n==null&&(n=!!(l[7].mime_type&&l[7].mime_type.includes("video"))),n?1:(t==null&&(t=!!(l[7].mime_type&&l[7].mime_type.includes("audio"))),t?2:3))}return r=_(s,-1),c=i[r]=m[r](s),{c(){c.c(),u=V()},l(l){c.l(l),u=V()},m(l,o){i[r].m(l,o),v(l,u,o),f=!0},p(l,o){let a=r;r=_(l,o),r===a?i[r].p(l,o):(M(),b(i[a],1,1,()=>{i[a]=null}),N(),c=i[r],c?c.p(l,o):(c=i[r]=m[r](l),c.c()),p(c,1),c.m(u.parentNode,u))},i(l){f||(p(c),f=!0)},o(l){b(c),f=!1},d(l){l&&d(u),i[r].d(l)}}}function le(s){let e,n,t=(s[0].text?s[0].text:"")+"",r,c,u,f,m=I(s[0].files),i=[];for(let l=0;l<m.length;l+=1)i[l]=S(P(s,m,l));const _=l=>b(i[l],1,1,()=>{i[l]=null});return{c(){e=w("div"),n=w("p"),r=A(t),c=J();for(let l=0;l<i.length;l+=1)i[l].c();this.h()},l(l){e=z(l,"DIV",{class:!0});var o=E(e);n=z(o,"P",{});var a=E(n);r=B(a,t),a.forEach(d),c=K(o);for(let g=0;g<i.length;g+=1)i[g].l(o);o.forEach(d),this.h()},h(){q(e,"class","container svelte-1cl8bqt"),L(()=>s[5].call(e)),h(e,"table",s[1]==="table"),h(e,"gallery",s[1]==="gallery"),h(e,"selected",s[2]),h(e,"border",s[0])},m(l,o){v(l,e,o),y(e,n),y(n,r),y(e,c);for(let a=0;a<i.length;a+=1)i[a]&&i[a].m(e,null);u=Q(e,s[5].bind(e)),s[6](e),f=!0},p(l,[o]){if((!f||o&1)&&t!==(t=(l[0].text?l[0].text:"")+"")&&C(r,t),o&1){m=I(l[0].files);let a;for(a=0;a<m.length;a+=1){const g=P(l,m,a);i[a]?(i[a].p(g,o),p(i[a],1)):(i[a]=S(g),i[a].c(),p(i[a],1),i[a].m(e,null))}for(M(),a=m.length;a<i.length;a+=1)_(a);N()}(!f||o&2)&&h(e,"table",l[1]==="table"),(!f||o&2)&&h(e,"gallery",l[1]==="gallery"),(!f||o&4)&&h(e,"selected",l[2]),(!f||o&1)&&h(e,"border",l[0])},i(l){if(!f){for(let o=0;o<m.length;o+=1)p(i[o]);f=!0}},o(l){i=i.filter(Boolean);for(let o=0;o<i.length;o+=1)b(i[o]);f=!1},d(l){l&&d(e),R(i,l),u(),s[6](null)}}}function ne(s,e){s.style.setProperty("--local-text-width",`${e&&e<150?e:200}px`),s.style.whiteSpace="unset"}function re(s,e,n){let{value:t={text:"",files:[]}}=e,{type:r}=e,{selected:c=!1}=e,u,f;T(()=>{ne(f,u)});function m(){u=this.clientWidth,n(3,u)}function i(_){X[_?"unshift":"push"](()=>{f=_,n(4,f)})}return s.$$set=_=>{"value"in _&&n(0,t=_.value),"type"in _&&n(1,r=_.type),"selected"in _&&n(2,c=_.selected)},[t,r,c,u,f,m,i]}class ce extends F{constructor(e){super(),G(this,e,re,le,H,{value:0,type:1,selected:2})}}export{ce as default};
//# sourceMappingURL=Example.CalqULdM.js.map
