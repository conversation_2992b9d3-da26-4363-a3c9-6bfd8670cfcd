import{SvelteComponent as d,init as c,safe_not_equal as p,svg_element as h,claim_svg_element as l,children as a,detach as n,attr as e,insert_hydration as f,append_hydration as g,noop as s}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";function m(u){let t,r;return{c(){t=h("svg"),r=h("path"),this.h()},l(o){t=l(o,"svg",{xmlns:!0,width:!0,height:!0,viewBox:!0,fill:!0,stroke:!0,"stroke-width":!0,"stroke-linecap":!0,"stroke-linejoin":!0,class:!0});var i=a(t);r=l(i,"path",{d:!0}),a(r).forEach(n),i.forEach(n),this.h()},h(){e(r,"d","M17 3a2.828 2.828 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5L17 3z"),e(t,"xmlns","http://www.w3.org/2000/svg"),e(t,"width","100%"),e(t,"height","100%"),e(t,"viewBox","0 0 24 24"),e(t,"fill","none"),e(t,"stroke","currentColor"),e(t,"stroke-width","1.5"),e(t,"stroke-linecap","round"),e(t,"stroke-linejoin","round"),e(t,"class","feather feather-edit-2")},m(o,i){f(o,t,i),g(t,r)},p:s,i:s,o:s,d(o){o&&n(t)}}}class _ extends d{constructor(t){super(),c(this,t,null,m,p,{})}}export{_ as E};
//# sourceMappingURL=Edit.jvyxqbov.js.map
