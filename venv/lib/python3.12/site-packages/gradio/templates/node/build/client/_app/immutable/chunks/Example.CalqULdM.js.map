{"version": 3, "file": "Example.CalqULdM.js", "sources": ["../../../../../../../multimodaltextbox/Example.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport { onMount } from \"svelte\";\n\timport { Image } from \"@gradio/image/shared\";\n\timport { Video } from \"@gradio/video/shared\";\n\timport type { FileData } from \"@gradio/client\";\n\n\texport let value: { text: string; files: FileData[] } = {\n\t\ttext: \"\",\n\t\tfiles: []\n\t};\n\texport let type: \"gallery\" | \"table\";\n\texport let selected = false;\n\n\tlet size: number;\n\tlet el: HTMLDivElement;\n\n\tfunction set_styles(element: HTMLElement, el_width: number): void {\n\t\telement.style.setProperty(\n\t\t\t\"--local-text-width\",\n\t\t\t`${el_width && el_width < 150 ? el_width : 200}px`\n\t\t);\n\t\telement.style.whiteSpace = \"unset\";\n\t}\n\n\tonMount(() => {\n\t\tset_styles(el, size);\n\t});\n</script>\n\n<div\n\tclass=\"container\"\n\tbind:clientWidth={size}\n\tbind:this={el}\n\tclass:table={type === \"table\"}\n\tclass:gallery={type === \"gallery\"}\n\tclass:selected\n\tclass:border={value}\n>\n\t<p>{value.text ? value.text : \"\"}</p>\n\t{#each value.files as file}\n\t\t{#if file.mime_type && file.mime_type.includes(\"image\")}\n\t\t\t<Image src={file.url} alt=\"\" />\n\t\t{:else if file.mime_type && file.mime_type.includes(\"video\")}\n\t\t\t<Video src={file.url} alt=\"\" loop={true} is_stream={false} />\n\t\t{:else if file.mime_type && file.mime_type.includes(\"audio\")}\n\t\t\t<audio src={file.url} controls />\n\t\t{:else}\n\t\t\t{file.orig_name}\n\t\t{/if}\n\t{/each}\n</div>\n\n<style>\n\t.gallery {\n\t\tpadding: var(--size-1) var(--size-2);\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tgap: 20px;\n\t\toverflow-x: auto;\n\t}\n\n\tdiv {\n\t\toverflow: hidden;\n\t\tmin-width: var(--local-text-width);\n\t\twhite-space: nowrap;\n\t}\n\n\t.container :global(img),\n\t.container :global(video) {\n\t\tobject-fit: contain;\n\t\twidth: 100px;\n\t\theight: 100px;\n\t}\n\n\t.container.selected {\n\t\tborder-color: var(--border-color-accent);\n\t}\n\t.border.table {\n\t\tborder: 2px solid var(--border-color-primary);\n\t}\n\n\t.container.table {\n\t\tmargin: 0 auto;\n\t\tborder-radius: var(--radius-lg);\n\t\toverflow-x: auto;\n\t\twidth: max-content;\n\t\theight: max-content;\n\t\tobject-fit: cover;\n\t\tpadding: var(--size-2);\n\t}\n\n\t.container.gallery {\n\t\tobject-fit: cover;\n\t}\n\n\tdiv > :global(p) {\n\t\tfont-size: var(--text-lg);\n\t\twhite-space: normal;\n\t}\n</style>\n"], "names": ["t_value", "ctx", "dirty", "set_data", "t", "src_url_equal", "audio", "audio_src_value", "attr", "insert_hydration", "target", "anchor", "video_changes", "image_changes", "t0_value", "each_value", "ensure_array_like", "i", "toggle_class", "div", "append_hydration", "p", "current", "t0", "each_blocks", "set_styles", "element", "el_width", "value", "$$props", "type", "selected", "size", "el", "onMount", "$$value"], "mappings": "8yBA+CI,IAAAA,EAAAC,KAAK,UAAS,+DAAdC,EAAA,GAAAF,KAAAA,EAAAC,KAAK,UAAS,KAAAE,EAAAC,EAAAJ,CAAA,6IAFHK,EAAAC,EAAA,IAAAC,EAAAN,KAAK,GAAG,GAAAO,EAAAF,EAAA,MAAAC,CAAA,wBAApBE,EAAgCC,EAAAJ,EAAAK,CAAA,UAApBT,EAAA,GAAA,CAAAG,EAAAC,EAAA,IAAAC,EAAAN,KAAK,GAAG,sFAFR,IAAAA,KAAK,gBAAkB,aAAiB,8FAAxCC,EAAA,IAAAU,EAAA,IAAAX,KAAK,sIAFL,MAAA,CAAA,IAAAA,KAAK,IAAG,IAAA,EAAA,4FAARC,EAAA,IAAAW,EAAA,IAAAZ,KAAK,wOADbA,EAAI,CAAA,EAAC,WAAaA,KAAK,UAAU,SAAS,OAAO,wBAE5CA,EAAI,CAAA,EAAC,WAAaA,KAAK,UAAU,SAAS,OAAO,wBAEjDA,EAAI,CAAA,EAAC,WAAaA,KAAK,UAAU,SAAS,OAAO,6VANxDa,GAAAb,KAAM,KAAOA,EAAM,CAAA,EAAA,KAAO,IAAE,WACzBc,EAAAC,EAAAf,KAAM,KAAK,uBAAhB,OAAIgB,GAAA,gXANOC,EAAAC,EAAA,QAAAlB,OAAS,OAAO,EACdiB,EAAAC,EAAA,UAAAlB,OAAS,SAAS,oCAEnBA,EAAK,CAAA,CAAA,UAPpBQ,EAqBKC,EAAAS,EAAAR,CAAA,EAZJS,EAAoCD,EAAAE,CAAA,gHAAhC,CAAAC,GAAApB,EAAA,IAAAY,KAAAA,GAAAb,KAAM,KAAOA,EAAM,CAAA,EAAA,KAAO,IAAE,KAAAE,EAAAoB,EAAAT,CAAA,OACzBC,EAAAC,EAAAf,KAAM,KAAK,oBAAhB,OAAIgB,GAAA,EAAA,yGAAJ,OAAIA,EAAAO,EAAA,OAAAP,GAAA,sBANOC,EAAAC,EAAA,QAAAlB,OAAS,OAAO,aACdiB,EAAAC,EAAA,UAAAlB,OAAS,SAAS,0DAEnBA,EAAK,CAAA,CAAA,+BAGjB,OAAIgB,GAAA,yHAvBG,SAAAQ,GAAWC,EAAsBC,EAAA,CACzCD,EAAQ,MAAM,YACb,qBAAA,GACGC,GAAYA,EAAW,IAAMA,EAAW,GAAG,IAAA,EAE/CD,EAAQ,MAAM,WAAa,2BAfjB,GAAA,CAAA,MAAAE,EAAA,CACV,KAAM,GACN,MAAA,CAAA,CAAA,CAAA,EAAAC,EAEU,CAAA,KAAAC,CAAA,EAAAD,GACA,SAAAE,EAAW,EAAA,EAAAF,EAElBG,EACAC,EAUJC,EAAA,IAAA,CACCT,GAAWQ,EAAID,CAAI,iBAMFA,EAAI,KAAA,4DACXC,EAAEE"}