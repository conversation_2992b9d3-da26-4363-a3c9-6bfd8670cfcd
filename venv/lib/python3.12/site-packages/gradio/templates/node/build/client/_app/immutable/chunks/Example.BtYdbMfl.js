import{SvelteComponent as u,init as d,safe_not_equal as h,element as g,claim_element as m,children as b,detach as r,set_style as o,attr as y,toggle_class as s,insert_hydration as _,noop as f}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";function v(a){let e;return{c(){e=g("div"),this.h()},l(l){e=m(l,"DIV",{style:!0,class:!0}),b(e).forEach(r),this.h()},h(){o(e,"background-color",a[0]?a[0]:"black"),y(e,"class","svelte-h6ogpl"),s(e,"table",a[1]==="table"),s(e,"gallery",a[1]==="gallery"),s(e,"selected",a[2])},m(l,t){_(l,e,t)},p(l,[t]){t&1&&o(e,"background-color",l[0]?l[0]:"black"),t&2&&s(e,"table",l[1]==="table"),t&2&&s(e,"gallery",l[1]==="gallery"),t&4&&s(e,"selected",l[2])},i:f,o:f,d(l){l&&r(e)}}}function k(a,e,l){let{value:t}=e,{type:i}=e,{selected:c=!1}=e;return a.$$set=n=>{"value"in n&&l(0,t=n.value),"type"in n&&l(1,i=n.type),"selected"in n&&l(2,c=n.selected)},[t,i,c]}class C extends u{constructor(e){super(),d(this,e,k,v,h,{value:0,type:1,selected:2})}}export{C as default};
//# sourceMappingURL=Example.BtYdbMfl.js.map
