{"version": 3, "file": "Gallery.Bvo9cGZ8.js", "sources": ["../../../../../../../gallery/shared/utils.ts", "../../../../../../../gallery/shared/Gallery.svelte"], "sourcesContent": ["import { uploadToHuggingFace } from \"@gradio/utils\";\nimport type { FileData } from \"@gradio/client\";\n\nexport async function format_gallery_for_sharing(\n\tvalue: [FileData, string | null][] | null\n): Promise<string> {\n\tif (!value) return \"\";\n\tlet urls = await Promise.all(\n\t\tvalue.map(async ([image, _]) => {\n\t\t\tif (image === null || !image.url) return \"\";\n\t\t\treturn await uploadToHuggingFace(image.url, \"url\");\n\t\t})\n\t);\n\n\treturn `<div style=\"display: flex; flex-wrap: wrap; gap: 16px\">${urls\n\t\t.map((url) => `<img src=\"${url}\" style=\"height: 400px\" />`)\n\t\t.join(\"\")}</div>`;\n}\n", "<script lang=\"ts\">\n\timport {\n\t\tBlockLabel,\n\t\tEmpty,\n\t\tShareButton,\n\t\tIconButton,\n\t\tIconButtonWrapper,\n\t\tFullscreenButton\n\t} from \"@gradio/atoms\";\n\timport { ModifyUpload } from \"@gradio/upload\";\n\timport type { SelectData } from \"@gradio/utils\";\n\timport { Image } from \"@gradio/image/shared\";\n\timport { Video } from \"@gradio/video/shared\";\n\timport { dequal } from \"dequal\";\n\timport { createEventDispatcher, onMount } from \"svelte\";\n\timport { tick } from \"svelte\";\n\timport type { GalleryImage, GalleryVideo } from \"../types\";\n\n\timport { Download, Image as ImageIcon, Clear, Play } from \"@gradio/icons\";\n\timport { FileData } from \"@gradio/client\";\n\timport { format_gallery_for_sharing } from \"./utils\";\n\timport type { I18nFormatter } from \"@gradio/utils\";\n\n\ttype GalleryData = GalleryImage | GalleryVideo;\n\n\texport let show_label = true;\n\texport let label: string;\n\texport let value: GalleryData[] | null = null;\n\texport let columns: number | number[] | undefined = [2];\n\texport let rows: number | number[] | undefined = undefined;\n\texport let height: number | \"auto\" = \"auto\";\n\texport let preview: boolean;\n\texport let allow_preview = true;\n\texport let object_fit: \"contain\" | \"cover\" | \"fill\" | \"none\" | \"scale-down\" =\n\t\t\"cover\";\n\texport let show_share_button = false;\n\texport let show_download_button = false;\n\texport let i18n: I18nFormatter;\n\texport let selected_index: number | null = null;\n\texport let interactive: boolean;\n\texport let _fetch: typeof fetch;\n\texport let mode: \"normal\" | \"minimal\" = \"normal\";\n\texport let show_fullscreen_button = true;\n\texport let display_icon_button_wrapper_top_corner = false;\n\texport let fullscreen = false;\n\n\tlet is_full_screen = false;\n\tlet image_container: HTMLElement;\n\n\tconst dispatch = createEventDispatcher<{\n\t\tchange: undefined;\n\t\tselect: SelectData;\n\t\tpreview_open: undefined;\n\t\tpreview_close: undefined;\n\t\tfullscreen: boolean;\n\t}>();\n\n\t// tracks whether the value of the gallery was reset\n\tlet was_reset = true;\n\n\t$: was_reset = value == null || value.length === 0 ? true : was_reset;\n\n\tlet resolved_value: GalleryData[] | null = null;\n\n\t$: resolved_value =\n\t\tvalue == null\n\t\t\t? null\n\t\t\t: (value.map((data) => {\n\t\t\t\t\tif (\"video\" in data) {\n\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\tvideo: data.video as FileData,\n\t\t\t\t\t\t\tcaption: data.caption\n\t\t\t\t\t\t};\n\t\t\t\t\t} else if (\"image\" in data) {\n\t\t\t\t\t\treturn { image: data.image as FileData, caption: data.caption };\n\t\t\t\t\t}\n\t\t\t\t\treturn {};\n\t\t\t\t}) as GalleryData[]);\n\n\tlet prev_value: GalleryData[] | null = value;\n\tif (selected_index == null && preview && value?.length) {\n\t\tselected_index = 0;\n\t}\n\tlet old_selected_index: number | null = selected_index;\n\n\t$: if (!dequal(prev_value, value)) {\n\t\t// When value is falsy (clear button or first load),\n\t\t// preview determines the selected image\n\t\tif (was_reset) {\n\t\t\tselected_index = preview && value?.length ? 0 : null;\n\t\t\twas_reset = false;\n\t\t\t// Otherwise we keep the selected_index the same if the\n\t\t\t// gallery has at least as many elements as it did before\n\t\t} else {\n\t\t\tif (selected_index !== null && value !== null) {\n\t\t\t\tselected_index = Math.max(\n\t\t\t\t\t0,\n\t\t\t\t\tMath.min(selected_index, value.length - 1)\n\t\t\t\t);\n\t\t\t} else {\n\t\t\t\tselected_index = null;\n\t\t\t}\n\t\t}\n\t\tdispatch(\"change\");\n\t\tprev_value = value;\n\t}\n\n\t$: previous =\n\t\t((selected_index ?? 0) + (resolved_value?.length ?? 0) - 1) %\n\t\t(resolved_value?.length ?? 0);\n\t$: next = ((selected_index ?? 0) + 1) % (resolved_value?.length ?? 0);\n\n\tfunction handle_preview_click(event: MouseEvent): void {\n\t\tconst element = event.target as HTMLElement;\n\t\tconst x = event.offsetX;\n\t\tconst width = element.offsetWidth;\n\t\tconst centerX = width / 2;\n\n\t\tif (x < centerX) {\n\t\t\tselected_index = previous;\n\t\t} else {\n\t\t\tselected_index = next;\n\t\t}\n\t}\n\n\tfunction on_keydown(e: KeyboardEvent): void {\n\t\tswitch (e.code) {\n\t\t\tcase \"Escape\":\n\t\t\t\te.preventDefault();\n\t\t\t\tselected_index = null;\n\t\t\t\tbreak;\n\t\t\tcase \"ArrowLeft\":\n\t\t\t\te.preventDefault();\n\t\t\t\tselected_index = previous;\n\t\t\t\tbreak;\n\t\t\tcase \"ArrowRight\":\n\t\t\t\te.preventDefault();\n\t\t\t\tselected_index = next;\n\t\t\t\tbreak;\n\t\t\tdefault:\n\t\t\t\tbreak;\n\t\t}\n\t}\n\n\t$: {\n\t\tif (selected_index !== old_selected_index) {\n\t\t\told_selected_index = selected_index;\n\t\t\tif (selected_index !== null) {\n\t\t\t\tif (resolved_value != null) {\n\t\t\t\t\tselected_index = Math.max(\n\t\t\t\t\t\t0,\n\t\t\t\t\t\tMath.min(selected_index, resolved_value.length - 1)\n\t\t\t\t\t);\n\t\t\t\t}\n\t\t\t\tdispatch(\"select\", {\n\t\t\t\t\tindex: selected_index,\n\t\t\t\t\tvalue: resolved_value?.[selected_index]\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\t}\n\n\t$: if (allow_preview) {\n\t\tscroll_to_img(selected_index);\n\t}\n\n\tlet el: HTMLButtonElement[] = [];\n\tlet container_element: HTMLDivElement;\n\n\tasync function scroll_to_img(index: number | null): Promise<void> {\n\t\tif (typeof index !== \"number\") return;\n\t\tawait tick();\n\n\t\tif (el[index] === undefined) return;\n\n\t\tel[index]?.focus();\n\n\t\tconst { left: container_left, width: container_width } =\n\t\t\tcontainer_element.getBoundingClientRect();\n\t\tconst { left, width } = el[index].getBoundingClientRect();\n\n\t\tconst relative_left = left - container_left;\n\n\t\tconst pos =\n\t\t\trelative_left +\n\t\t\twidth / 2 -\n\t\t\tcontainer_width / 2 +\n\t\t\tcontainer_element.scrollLeft;\n\n\t\tif (container_element && typeof container_element.scrollTo === \"function\") {\n\t\t\tcontainer_element.scrollTo({\n\t\t\t\tleft: pos < 0 ? 0 : pos,\n\t\t\t\tbehavior: \"smooth\"\n\t\t\t});\n\t\t}\n\t}\n\n\tlet window_height = 0;\n\n\t// Unlike `gr.Image()`, images specified via remote URLs are not cached in the server\n\t// and their remote URLs are directly passed to the client as `value[].image.url`.\n\t// The `download` attribute of the <a> tag doesn't work for remote URLs (https://developer.mozilla.org/en-US/docs/Web/HTML/Element/a#download),\n\t// so we need to download the image via JS as below.\n\tasync function download(file_url: string, name: string): Promise<void> {\n\t\tlet response;\n\t\ttry {\n\t\t\tresponse = await _fetch(file_url);\n\t\t} catch (error) {\n\t\t\tif (error instanceof TypeError) {\n\t\t\t\t// If CORS is not allowed (https://developer.mozilla.org/en-US/docs/Web/API/Fetch_API/Using_Fetch#checking_that_the_fetch_was_successful),\n\t\t\t\t// open the link in a new tab instead, mimicing the behavior of the `download` attribute for remote URLs,\n\t\t\t\t// which is not ideal, but a reasonable fallback.\n\t\t\t\twindow.open(file_url, \"_blank\", \"noreferrer\");\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tthrow error;\n\t\t}\n\t\tconst blob = await response.blob();\n\t\tconst url = URL.createObjectURL(blob);\n\t\tconst link = document.createElement(\"a\");\n\t\tlink.href = url;\n\t\tlink.download = name;\n\t\tlink.click();\n\t\tURL.revokeObjectURL(url);\n\t}\n\n\t$: selected_media =\n\t\tselected_index != null && resolved_value != null\n\t\t\t? resolved_value[selected_index]\n\t\t\t: null;\n\n\tlet thumbnails_overflow = false;\n\n\tfunction check_thumbnails_overflow(): void {\n\t\tif (container_element) {\n\t\t\tthumbnails_overflow =\n\t\t\t\tcontainer_element.scrollWidth > container_element.clientWidth;\n\t\t}\n\t}\n\n\tonMount(() => {\n\t\tcheck_thumbnails_overflow();\n\t\tdocument.addEventListener(\"fullscreenchange\", () => {\n\t\t\tis_full_screen = !!document.fullscreenElement;\n\t\t});\n\t\twindow.addEventListener(\"resize\", check_thumbnails_overflow);\n\t\treturn () =>\n\t\t\twindow.removeEventListener(\"resize\", check_thumbnails_overflow);\n\t});\n\n\t$: resolved_value, check_thumbnails_overflow();\n\t$: if (container_element) {\n\t\tcheck_thumbnails_overflow();\n\t}\n</script>\n\n<svelte:window bind:innerHeight={window_height} />\n\n{#if show_label}\n\t<BlockLabel {show_label} Icon={ImageIcon} label={label || \"Gallery\"} />\n{/if}\n{#if value == null || resolved_value == null || resolved_value.length === 0}\n\t<Empty unpadded_box={true} size=\"large\"><ImageIcon /></Empty>\n{:else}\n\t<div class=\"gallery-container\" bind:this={image_container}>\n\t\t{#if selected_media && allow_preview}\n\t\t\t<button\n\t\t\t\ton:keydown={on_keydown}\n\t\t\t\tclass=\"preview\"\n\t\t\t\tclass:minimal={mode === \"minimal\"}\n\t\t\t>\n\t\t\t\t<IconButtonWrapper\n\t\t\t\t\tdisplay_top_corner={display_icon_button_wrapper_top_corner}\n\t\t\t\t>\n\t\t\t\t\t{#if show_download_button}\n\t\t\t\t\t\t<IconButton\n\t\t\t\t\t\t\tIcon={Download}\n\t\t\t\t\t\t\tlabel={i18n(\"common.download\")}\n\t\t\t\t\t\t\ton:click={() => {\n\t\t\t\t\t\t\t\tconst image =\n\t\t\t\t\t\t\t\t\t\"image\" in selected_media\n\t\t\t\t\t\t\t\t\t\t? selected_media?.image\n\t\t\t\t\t\t\t\t\t\t: selected_media?.video;\n\t\t\t\t\t\t\t\tif (image == null) {\n\t\t\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tconst { url, orig_name } = image;\n\t\t\t\t\t\t\t\tif (url) {\n\t\t\t\t\t\t\t\t\tdownload(url, orig_name ?? \"image\");\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}}\n\t\t\t\t\t\t/>\n\t\t\t\t\t{/if}\n\n\t\t\t\t\t{#if show_fullscreen_button}\n\t\t\t\t\t\t<FullscreenButton {fullscreen} on:fullscreen />\n\t\t\t\t\t{/if}\n\n\t\t\t\t\t{#if show_share_button}\n\t\t\t\t\t\t<div class=\"icon-button\">\n\t\t\t\t\t\t\t<ShareButton\n\t\t\t\t\t\t\t\t{i18n}\n\t\t\t\t\t\t\t\ton:share\n\t\t\t\t\t\t\t\ton:error\n\t\t\t\t\t\t\t\tvalue={resolved_value}\n\t\t\t\t\t\t\t\tformatter={format_gallery_for_sharing}\n\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t{/if}\n\t\t\t\t\t{#if !is_full_screen}\n\t\t\t\t\t\t<IconButton\n\t\t\t\t\t\t\tIcon={Clear}\n\t\t\t\t\t\t\tlabel=\"Close\"\n\t\t\t\t\t\t\ton:click={() => {\n\t\t\t\t\t\t\t\tselected_index = null;\n\t\t\t\t\t\t\t\tdispatch(\"preview_close\");\n\t\t\t\t\t\t\t}}\n\t\t\t\t\t\t/>\n\t\t\t\t\t{/if}\n\t\t\t\t</IconButtonWrapper>\n\t\t\t\t<button\n\t\t\t\t\tclass=\"media-button\"\n\t\t\t\t\ton:click={\"image\" in selected_media\n\t\t\t\t\t\t? (event) => handle_preview_click(event)\n\t\t\t\t\t\t: null}\n\t\t\t\t\tstyle=\"height: calc(100% - {selected_media.caption\n\t\t\t\t\t\t? '80px'\n\t\t\t\t\t\t: '60px'})\"\n\t\t\t\t\taria-label=\"detailed view of selected image\"\n\t\t\t\t>\n\t\t\t\t\t{#if \"image\" in selected_media}\n\t\t\t\t\t\t<Image\n\t\t\t\t\t\t\tdata-testid=\"detailed-image\"\n\t\t\t\t\t\t\tsrc={selected_media.image.url}\n\t\t\t\t\t\t\talt={selected_media.caption || \"\"}\n\t\t\t\t\t\t\ttitle={selected_media.caption || null}\n\t\t\t\t\t\t\tclass={selected_media.caption && \"with-caption\"}\n\t\t\t\t\t\t\tloading=\"lazy\"\n\t\t\t\t\t\t/>\n\t\t\t\t\t{:else}\n\t\t\t\t\t\t<Video\n\t\t\t\t\t\t\tsrc={selected_media.video.url}\n\t\t\t\t\t\t\tdata-testid={\"detailed-video\"}\n\t\t\t\t\t\t\talt={selected_media.caption || \"\"}\n\t\t\t\t\t\t\tloading=\"lazy\"\n\t\t\t\t\t\t\tloop={false}\n\t\t\t\t\t\t\tis_stream={false}\n\t\t\t\t\t\t\tmuted={false}\n\t\t\t\t\t\t\tcontrols={true}\n\t\t\t\t\t\t/>\n\t\t\t\t\t{/if}\n\t\t\t\t</button>\n\t\t\t\t{#if selected_media?.caption}\n\t\t\t\t\t<caption class=\"caption\">\n\t\t\t\t\t\t{selected_media.caption}\n\t\t\t\t\t</caption>\n\t\t\t\t{/if}\n\t\t\t\t<div\n\t\t\t\t\tbind:this={container_element}\n\t\t\t\t\tclass=\"thumbnails scroll-hide\"\n\t\t\t\t\tdata-testid=\"container_el\"\n\t\t\t\t\tstyle=\"justify-content: {thumbnails_overflow\n\t\t\t\t\t\t? 'flex-start'\n\t\t\t\t\t\t: 'center'};\"\n\t\t\t\t>\n\t\t\t\t\t{#each resolved_value as media, i}\n\t\t\t\t\t\t<button\n\t\t\t\t\t\t\tbind:this={el[i]}\n\t\t\t\t\t\t\ton:click={() => (selected_index = i)}\n\t\t\t\t\t\t\tclass=\"thumbnail-item thumbnail-small\"\n\t\t\t\t\t\t\tclass:selected={selected_index === i && mode !== \"minimal\"}\n\t\t\t\t\t\t\taria-label={\"Thumbnail \" +\n\t\t\t\t\t\t\t\t(i + 1) +\n\t\t\t\t\t\t\t\t\" of \" +\n\t\t\t\t\t\t\t\tresolved_value.length}\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t{#if \"image\" in media}\n\t\t\t\t\t\t\t\t<Image\n\t\t\t\t\t\t\t\t\tsrc={media.image.url}\n\t\t\t\t\t\t\t\t\ttitle={media.caption || null}\n\t\t\t\t\t\t\t\t\tdata-testid={\"thumbnail \" + (i + 1)}\n\t\t\t\t\t\t\t\t\talt=\"\"\n\t\t\t\t\t\t\t\t\tloading=\"lazy\"\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t{:else}\n\t\t\t\t\t\t\t\t<Play />\n\t\t\t\t\t\t\t\t<Video\n\t\t\t\t\t\t\t\t\tsrc={media.video.url}\n\t\t\t\t\t\t\t\t\ttitle={media.caption || null}\n\t\t\t\t\t\t\t\t\tis_stream={false}\n\t\t\t\t\t\t\t\t\tdata-testid={\"thumbnail \" + (i + 1)}\n\t\t\t\t\t\t\t\t\talt=\"\"\n\t\t\t\t\t\t\t\t\tloading=\"lazy\"\n\t\t\t\t\t\t\t\t\tloop={false}\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t</button>\n\t\t\t\t\t{/each}\n\t\t\t\t</div>\n\t\t\t</button>\n\t\t{/if}\n\n\t\t<div\n\t\t\tclass=\"grid-wrap\"\n\t\t\tclass:minimal={mode === \"minimal\"}\n\t\t\tclass:fixed-height={mode !== \"minimal\" && (!height || height == \"auto\")}\n\t\t\tclass:hidden={is_full_screen}\n\t\t>\n\t\t\t{#if interactive && selected_index === null}\n\t\t\t\t<ModifyUpload {i18n} on:clear={() => (value = [])} />\n\t\t\t{/if}\n\t\t\t<div\n\t\t\t\tclass=\"grid-container\"\n\t\t\t\tstyle=\"--grid-cols:{columns}; --grid-rows:{rows}; --object-fit: {object_fit}; height: {height};\"\n\t\t\t\tclass:pt-6={show_label}\n\t\t\t>\n\t\t\t\t{#each resolved_value as entry, i}\n\t\t\t\t\t<button\n\t\t\t\t\t\tclass=\"thumbnail-item thumbnail-lg\"\n\t\t\t\t\t\tclass:selected={selected_index === i}\n\t\t\t\t\t\ton:click={() => {\n\t\t\t\t\t\t\tif (selected_index === null && allow_preview) {\n\t\t\t\t\t\t\t\tdispatch(\"preview_open\");\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tselected_index = i;\n\t\t\t\t\t\t}}\n\t\t\t\t\t\taria-label={\"Thumbnail \" + (i + 1) + \" of \" + resolved_value.length}\n\t\t\t\t\t>\n\t\t\t\t\t\t{#if \"image\" in entry}\n\t\t\t\t\t\t\t<Image\n\t\t\t\t\t\t\t\talt={entry.caption || \"\"}\n\t\t\t\t\t\t\t\tsrc={typeof entry.image === \"string\"\n\t\t\t\t\t\t\t\t\t? entry.image\n\t\t\t\t\t\t\t\t\t: entry.image.url}\n\t\t\t\t\t\t\t\tloading=\"lazy\"\n\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t{:else}\n\t\t\t\t\t\t\t<Play />\n\t\t\t\t\t\t\t<Video\n\t\t\t\t\t\t\t\tsrc={entry.video.url}\n\t\t\t\t\t\t\t\ttitle={entry.caption || null}\n\t\t\t\t\t\t\t\tis_stream={false}\n\t\t\t\t\t\t\t\tdata-testid={\"thumbnail \" + (i + 1)}\n\t\t\t\t\t\t\t\talt=\"\"\n\t\t\t\t\t\t\t\tloading=\"lazy\"\n\t\t\t\t\t\t\t\tloop={false}\n\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t{#if entry.caption}\n\t\t\t\t\t\t\t<div class=\"caption-label\">\n\t\t\t\t\t\t\t\t{entry.caption}\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t{/if}\n\t\t\t\t\t</button>\n\t\t\t\t{/each}\n\t\t\t</div>\n\t\t</div>\n\t</div>\n{/if}\n\n<style lang=\"postcss\">\n\t.image-container {\n\t\theight: 100%;\n\t\tposition: relative;\n\t}\n\t.image-container :global(img),\n\tbutton {\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t\tobject-fit: contain;\n\t\tdisplay: block;\n\t\tborder-radius: var(--radius-lg);\n\t}\n\n\t.preview {\n\t\tdisplay: flex;\n\t\tposition: absolute;\n\t\tflex-direction: column;\n\t\tz-index: var(--layer-2);\n\t\tborder-radius: calc(var(--block-radius) - var(--block-border-width));\n\t\t-webkit-backdrop-filter: blur(8px);\n\t\tbackdrop-filter: blur(8px);\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t}\n\n\t.preview.minimal {\n\t\twidth: fit-content;\n\t\theight: fit-content;\n\t}\n\n\t.preview::before {\n\t\tcontent: \"\";\n\t\tposition: absolute;\n\t\tz-index: var(--layer-below);\n\t\tbackground: var(--background-fill-primary);\n\t\topacity: 0.9;\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t}\n\n\t.fixed-height {\n\t\tmin-height: var(--size-80);\n\t\tmax-height: 55vh;\n\t}\n\n\t@media (--screen-xl) {\n\t\t.fixed-height {\n\t\t\tmin-height: 450px;\n\t\t}\n\t}\n\n\t.media-button {\n\t\theight: calc(100% - 60px);\n\t\twidth: 100%;\n\t\tdisplay: flex;\n\t}\n\t.media-button :global(img),\n\t.media-button :global(video) {\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t\tobject-fit: contain;\n\t}\n\t.thumbnails :global(img) {\n\t\tobject-fit: cover;\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t}\n\t.thumbnails :global(svg) {\n\t\tposition: absolute;\n\t\ttop: var(--size-2);\n\t\tleft: var(--size-2);\n\t\twidth: 50%;\n\t\theight: 50%;\n\t\topacity: 50%;\n\t}\n\t.preview :global(img.with-caption) {\n\t\theight: var(--size-full);\n\t}\n\n\t.preview.minimal :global(img.with-caption) {\n\t\theight: auto;\n\t}\n\n\t.selectable {\n\t\tcursor: crosshair;\n\t}\n\n\t.caption {\n\t\tpadding: var(--size-2) var(--size-3);\n\t\toverflow: hidden;\n\t\tcolor: var(--block-label-text-color);\n\t\tfont-weight: var(--weight-semibold);\n\t\ttext-align: center;\n\t\ttext-overflow: ellipsis;\n\t\twhite-space: nowrap;\n\t\talign-self: center;\n\t}\n\n\t.thumbnails {\n\t\tdisplay: flex;\n\t\tposition: absolute;\n\t\tbottom: 0;\n\t\tjustify-content: flex-start;\n\t\talign-items: center;\n\t\tgap: var(--spacing-lg);\n\t\twidth: var(--size-full);\n\t\theight: var(--size-14);\n\t\toverflow-x: scroll;\n\t}\n\n\t.thumbnail-item {\n\t\t--ring-color: transparent;\n\t\tposition: relative;\n\t\tbox-shadow:\n\t\t\tinset 0 0 0 1px var(--ring-color),\n\t\t\tvar(--shadow-drop);\n\t\tborder: 1px solid var(--border-color-primary);\n\t\tborder-radius: var(--button-small-radius);\n\t\tbackground: var(--background-fill-secondary);\n\t\taspect-ratio: var(--ratio-square);\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t\toverflow: clip;\n\t}\n\n\t.thumbnail-item:hover {\n\t\t--ring-color: var(--color-accent);\n\t\tborder-color: var(--color-accent);\n\t\tfilter: brightness(1.1);\n\t}\n\n\t.thumbnail-item.selected {\n\t\t--ring-color: var(--color-accent);\n\t\tborder-color: var(--color-accent);\n\t}\n\n\t.thumbnail-item :global(svg) {\n\t\tposition: absolute;\n\t\ttop: 50%;\n\t\tleft: 50%;\n\t\twidth: 50%;\n\t\theight: 50%;\n\t\topacity: 50%;\n\t\ttransform: translate(-50%, -50%);\n\t}\n\n\t.thumbnail-item :global(video) {\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t\toverflow: hidden;\n\t\tobject-fit: cover;\n\t}\n\n\t.thumbnail-small {\n\t\tflex: none;\n\t\ttransform: scale(0.9);\n\t\ttransition: 0.075s;\n\t\twidth: var(--size-9);\n\t\theight: var(--size-9);\n\t}\n\t.thumbnail-small.selected {\n\t\t--ring-color: var(--color-accent);\n\t\ttransform: scale(1);\n\t\tborder-color: var(--color-accent);\n\t}\n\n\t.thumbnail-small > img {\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t\toverflow: hidden;\n\t\tobject-fit: var(--object-fit);\n\t}\n\n\t.grid-wrap {\n\t\tposition: relative;\n\t\tpadding: var(--size-2);\n\t\theight: var(--size-full);\n\t\toverflow-y: scroll;\n\t}\n\n\t.grid-container {\n\t\tdisplay: grid;\n\t\tposition: relative;\n\t\tgrid-template-rows: repeat(var(--grid-rows), minmax(100px, 1fr));\n\t\tgrid-template-columns: repeat(var(--grid-cols), minmax(100px, 1fr));\n\t\tgrid-auto-rows: minmax(100px, 1fr);\n\t\tgap: var(--spacing-lg);\n\t}\n\n\t.thumbnail-lg > :global(img) {\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t\toverflow: hidden;\n\t\tobject-fit: var(--object-fit);\n\t}\n\n\t.thumbnail-lg:hover .caption-label {\n\t\topacity: 0.5;\n\t}\n\n\t.caption-label {\n\t\tposition: absolute;\n\t\tright: var(--block-label-margin);\n\t\tbottom: var(--block-label-margin);\n\t\tz-index: var(--layer-1);\n\t\tborder-top: 1px solid var(--border-color-primary);\n\t\tborder-left: 1px solid var(--border-color-primary);\n\t\tborder-radius: var(--block-label-radius);\n\t\tbackground: var(--background-fill-secondary);\n\t\tpadding: var(--block-label-padding);\n\t\tmax-width: 80%;\n\t\toverflow: hidden;\n\t\tfont-size: var(--block-label-text-size);\n\t\ttext-align: left;\n\t\ttext-overflow: ellipsis;\n\t\twhite-space: nowrap;\n\t}\n\n\t.grid-wrap.minimal {\n\t\tpadding: 0;\n\t}\n</style>\n"], "names": ["format_gallery_for_sharing", "value", "image", "_", "uploadToHuggingFace", "url", "ImageIcon", "ctx", "dirty", "blocklabel_changes", "if_block0", "create_if_block_4", "create_if_block_3", "i", "toggle_class", "div1", "insert_hydration", "target", "div2", "anchor", "append_hydration", "div0", "each_blocks", "if_block1", "_a", "create_if_block_6", "set_style", "button0", "div", "button1", "is_function", "Download", "iconbutton_changes", "Clear", "create_if_block_11", "create_if_block_10", "create_if_block_9", "create_if_block_8", "t_value", "caption", "set_data", "t", "button", "create_if_block_1", "create_if_block_12", "show_label", "$$props", "label", "columns", "rows", "height", "preview", "allow_preview", "object_fit", "show_share_button", "show_download_button", "i18n", "selected_index", "interactive", "_fetch", "mode", "show_fullscreen_button", "display_icon_button_wrapper_top_corner", "fullscreen", "is_full_screen", "image_container", "dispatch", "createEventDispatcher", "was_reset", "resolved_value", "prev_value", "old_selected_index", "handle_preview_click", "event", "element", "x", "centerX", "previous", "next", "on_keydown", "e", "el", "container_element", "scroll_to_img", "index", "tick", "container_left", "container_width", "left", "width", "pos", "window_height", "download", "file_url", "name", "response", "error", "blob", "link", "thumbnails_overflow", "check_thumbnails_overflow", "onMount", "$$invalidate", "selected_media", "orig_name", "$$value", "click_handler_3", "data", "dequal"], "mappings": "21CAGA,eAAsBA,GACrBC,EACkB,CAClB,OAAKA,EAQE,2DAPI,MAAM,QAAQ,IACxBA,EAAM,IAAI,MAAO,CAACC,EAAOC,CAAC,IACrBD,IAAU,MAAQ,CAACA,EAAM,IAAY,GAClC,MAAME,GAAoBF,EAAM,GAAU,CACjD,CAAA,GAIA,IAAKG,GAAQ,aAAaA,CAAG,4BAA4B,EACzD,KAAK,EAAE,CAAC,SAVS,EAWpB,mOCmPgCC,GAAkB,MAAAC,MAAS,iIAATC,EAAA,CAAA,EAAA,IAAAC,EAAA,MAAAF,MAAS,oIAMpDG,EAAAH,OAAkBA,EAAa,CAAA,GAAAI,GAAAJ,CAAA,IA+I9BA,EAAW,EAAA,GAAIA,EAAc,CAAA,IAAK,MAAIK,GAAAL,CAAA,OAQnCA,EAAc,EAAA,CAAA,uBAAnB,OAAIM,GAAA,weAHcN,EAAO,CAAA,CAAA,oBAAgBA,EAAI,CAAA,CAAA,qBAAkBA,EAAU,CAAA,CAAA,eAAYA,EAAM,CAAA,CAAA,aACjFA,EAAU,CAAA,CAAA,0CAVRO,EAAAC,EAAA,UAAAR,QAAS,SAAS,EACbO,EAAAC,EAAA,eAAAR,QAAS,YAAS,CAAMA,EAAU,CAAA,GAAAA,MAAU,OAAM,eACxDA,EAAc,EAAA,CAAA,2CA9I9BS,EAiMKC,EAAAC,EAAAC,CAAA,wBAvDJC,EAsDKF,EAAAH,CAAA,wBA7CJK,EA4CKL,EAAAM,CAAA,4EA9LDd,OAAkBA,EAAa,CAAA,wGA+I9BA,EAAW,EAAA,GAAIA,EAAc,CAAA,IAAK,2HAQ/BA,EAAc,EAAA,CAAA,oBAAnB,OAAIM,GAAA,EAAA,2GAAJ,OAAIA,EAAAS,EAAA,OAAAT,GAAA,4CAHcN,EAAO,CAAA,CAAA,mCAAgBA,EAAI,CAAA,CAAA,qCAAkBA,EAAU,CAAA,CAAA,8BAAYA,EAAM,CAAA,CAAA,2BACjFA,EAAU,CAAA,CAAA,mBAVRO,EAAAC,EAAA,UAAAR,QAAS,SAAS,mBACbO,EAAAC,EAAA,eAAAR,QAAS,YAAS,CAAMA,EAAU,CAAA,GAAAA,MAAU,OAAM,kCACxDA,EAAc,EAAA,CAAA,yCAUzB,OAAIM,GAAA,+MA1JY,4WAUGN,EAAsC,EAAA,iFA0DrD,MAAA,UAAWA,EAAc,EAAA,EAAA,0BAsB1B,IAAAgB,IAAAC,EAAAjB,QAAA,YAAAiB,EAAgB,UAAOC,GAAAlB,CAAA,OAapBA,EAAc,EAAA,CAAA,uBAAnB,OAAIM,GAAA,sjBAxCsBa,EAAAC,EAAA,SAAA,gBAAApB,EAAe,EAAA,EAAA,QACxC,OACA,QAAM,GAAA,6IAkCgBmB,EAAAE,EAAA,kBAAArB,EAAA,EAAA,EACtB,aACA,QAAQ,wCA9FGO,EAAAe,EAAA,UAAAtB,QAAS,SAAS,UAHlCS,EAqIQC,EAAAY,EAAAV,CAAA,qBA/EPC,EA+BQS,EAAAF,CAAA,8CAMRP,EAyCKS,EAAAD,CAAA,gGA5EME,GAAA,UAAWvB,EAAA,EAAA,QAElB,IAAI,IAFG,UAAWA,EAAA,EAAA,QAElB,MAAI,MAAA,KAAA,SAAA,mBAzDIA,EAAU,EAAA,CAAA,wEAKDA,EAAsC,EAAA,iOAqD9BmB,EAAAC,EAAA,SAAA,gBAAApB,EAAe,EAAA,EAAA,QACxC,OACA,QAAM,GAAA,GAyBLiB,EAAAjB,QAAA,MAAAiB,EAAgB,kFAabjB,EAAc,EAAA,CAAA,oBAAnB,OAAIM,GAAA,EAAA,2GAAJ,OAAIA,EAAAS,EAAA,OAAAT,GAAA,gCAJmBa,EAAAE,EAAA,kBAAArB,EAAA,EAAA,EACtB,aACA,QAAQ,mBA9FGO,EAAAe,EAAA,UAAAtB,QAAS,SAAS,uDAgG9B,OAAIM,GAAA,qOAzFEkB,GACC,MAAAxB,MAAK,iBAAiB,kHAAtBC,EAAA,CAAA,EAAA,OAAAwB,EAAA,MAAAzB,MAAK,iBAAiB,+cA2BrBA,EAAc,EAAA,YACVP,oNANbgB,EAQKC,EAAAW,EAAAT,CAAA,sFAHIZ,EAAc,EAAA,uJAOhB0B,GAAK,MAAA,OAAA,CAAA,CAAA,gNArCR1B,EAAoB,EAAA,GAAA2B,GAAA3B,CAAA,IAoBpBA,EAAsB,EAAA,GAAA4B,GAAA5B,CAAA,IAItBA,EAAiB,CAAA,GAAA6B,GAAA7B,CAAA,KAWhBA,EAAc,EAAA,GAAA8B,GAAA9B,CAAA,6PAnCfA,EAAoB,EAAA,+GAoBpBA,EAAsB,EAAA,gHAItBA,EAAiB,CAAA,8GAWhBA,EAAc,EAAA,gTAgCbA,EAAc,EAAA,EAAC,MAAM,kBACb,qBACRA,EAAc,EAAA,EAAC,SAAW,uBAEzB,aACK,SACJ,YACG,oHAPLA,EAAc,EAAA,EAAC,MAAM,2BAErBA,EAAc,EAAA,EAAC,SAAW,gLAV1BA,EAAc,EAAA,EAAC,MAAM,QACrBA,EAAc,EAAA,EAAC,SAAW,SACxBA,EAAc,EAAA,EAAC,SAAW,WAC1BA,EAAc,EAAA,EAAC,SAAW,+IAH5BA,EAAc,EAAA,EAAC,MAAM,2BACrBA,EAAc,EAAA,EAAC,SAAW,4BACxBA,EAAc,EAAA,EAAC,SAAW,8BAC1BA,EAAc,EAAA,EAAC,SAAW,+HAkBjC+B,EAAA/B,MAAe,QAAO,gLADxBS,EAESC,EAAAsB,EAAApB,CAAA,iBADPX,EAAA,CAAA,EAAA,UAAA8B,KAAAA,EAAA/B,MAAe,QAAO,KAAAiC,GAAAC,EAAAH,CAAA,sFAiCf/B,EAAK,EAAA,EAAC,MAAM,UACVA,EAAK,EAAA,EAAC,SAAW,eACb,iBACE,cAAgBA,EAAC,EAAA,EAAG,8BAG3B,qLANDA,EAAK,EAAA,EAAC,MAAM,2BACVA,EAAK,EAAA,EAAC,SAAW,wMAVnBA,EAAK,EAAA,EAAC,MAAM,UACVA,EAAK,EAAA,EAAC,SAAW,mBACX,cAAgBA,EAAC,EAAA,EAAG,wIAF5BA,EAAK,EAAA,EAAC,MAAM,2BACVA,EAAK,EAAA,EAAC,SAAW,gLAHrB,MAAA,UAAWA,EAAK,EAAA,EAAA,yUALT,cACVA,EAAI,EAAA,EAAA,GACL,OACAA,EAAc,EAAA,EAAC,MAAM,EAJNO,EAAA4B,EAAA,WAAAnC,EAAmB,CAAA,IAAAA,EAAK,EAAA,GAAAA,QAAS,SAAS,UAJ3DS,EA8BQC,EAAAyB,EAAAvB,CAAA,kOAzBK,cACVZ,EAAI,EAAA,EAAA,GACL,OACAA,EAAc,EAAA,EAAC,4EAJAO,EAAA4B,EAAA,WAAAnC,EAAmB,CAAA,IAAAA,EAAK,EAAA,GAAAA,QAAS,SAAS,+aAqEpDA,EAAK,EAAA,EAAC,MAAM,UACVA,EAAK,EAAA,EAAC,SAAW,eACb,iBACE,cAAgBA,EAAC,EAAA,EAAG,8BAG3B,qLANDA,EAAK,EAAA,EAAC,MAAM,2BACVA,EAAK,EAAA,EAAC,SAAW,wMAVnBA,EAAK,EAAA,EAAC,SAAW,cACVA,EAAK,EAAA,EAAC,OAAU,SACzBA,EAAM,EAAA,EAAA,MACNA,EAAK,EAAA,EAAC,MAAM,kIAHVA,EAAK,EAAA,EAAC,SAAW,+BACVA,EAAK,EAAA,EAAC,OAAU,SACzBA,EAAM,EAAA,EAAA,MACNA,EAAK,EAAA,EAAC,MAAM,oHAiBd+B,EAAA/B,MAAM,QAAO,8KADfS,EAEKC,EAAAW,EAAAT,CAAA,iBADHX,EAAA,CAAA,EAAA,QAAA8B,KAAAA,EAAA/B,MAAM,QAAO,KAAAiC,GAAAC,EAAAH,CAAA,4FAtBX,MAAA,UAAW/B,EAAK,EAAA,EAAA,0BAoBhB,IAAAgB,EAAAhB,MAAM,SAAOoC,GAAApC,CAAA,mSAtBN,cAAgBA,EAAI,EAAA,EAAA,GAAK,OAASA,EAAc,EAAA,EAAC,MAAM,EAPnDO,EAAA4B,EAAA,WAAAnC,OAAmBA,EAAC,EAAA,CAAA,UAFrCS,EAoCQC,EAAAyB,EAAAvB,CAAA,2NALFZ,MAAM,wFAtBC,cAAgBA,EAAI,EAAA,EAAA,GAAK,OAASA,EAAc,EAAA,EAAC,4CAP7CO,EAAA4B,EAAA,WAAAnC,OAAmBA,EAAC,EAAA,CAAA,mUAjKrCA,EAAU,CAAA,GAAAqC,GAAArC,CAAA,8CAGVA,EAAK,CAAA,GAAI,MAAQA,EAAc,EAAA,GAAI,MAAQA,EAAc,EAAA,EAAC,SAAW,EAAC,wMAHtEA,EAAU,CAAA,wXA1OH,WAAAsC,EAAa,EAAA,EAAAC,EACb,CAAA,MAAAC,CAAA,EAAAD,GACA,MAAA7C,EAA8B,IAAA,EAAA6C,EAC9B,CAAA,QAAAE,EAAA,CAA0C,CAAC,CAAA,EAAAF,GAC3C,KAAAG,EAAsC,MAAA,EAAAH,GACtC,OAAAI,EAA0B,MAAA,EAAAJ,EAC1B,CAAA,QAAAK,CAAA,EAAAL,GACA,cAAAM,EAAgB,EAAA,EAAAN,GAChB,WAAAO,EACV,OAAA,EAAAP,GACU,kBAAAQ,EAAoB,EAAA,EAAAR,GACpB,qBAAAS,EAAuB,EAAA,EAAAT,EACvB,CAAA,KAAAU,CAAA,EAAAV,GACA,eAAAW,EAAgC,IAAA,EAAAX,EAChC,CAAA,YAAAY,EAAA,EAAAZ,EACA,CAAA,OAAAa,CAAA,EAAAb,GACA,KAAAc,EAA6B,QAAA,EAAAd,GAC7B,uBAAAe,EAAyB,EAAA,EAAAf,GACzB,uCAAAgB,EAAyC,EAAA,EAAAhB,GACzC,WAAAiB,EAAa,EAAA,EAAAjB,EAEpBkB,EAAiB,GACjBC,QAEEC,EAAWC,SASbC,EAAY,GAIZC,EAAuC,KAiBvCC,GAAmCrE,EACnCwD,GAAkB,MAAQN,IAAWlD,GAAA,MAAAA,EAAO,UAC/CwD,EAAiB,OAEdc,GAAoCd,WA6B/Be,GAAqBC,EAAA,CACvB,MAAAC,EAAUD,EAAM,OAChBE,EAAIF,EAAM,QAEVG,EADQF,EAAQ,YACE,EAEpBC,EAAIC,MACPnB,EAAiBoB,CAAA,MAEjBpB,EAAiBqB,CAAA,WAIVC,GAAWC,EAAA,QACXA,EAAE,KAAA,CACJ,IAAA,SACJA,EAAE,eAAA,MACFvB,EAAiB,IAAA,QAEb,IAAA,YACJuB,EAAE,eAAA,MACFvB,EAAiBoB,CAAA,QAEb,IAAA,aACJG,EAAE,eAAA,MACFvB,EAAiBqB,CAAA,SA6BhB,IAAAG,EAAA,CAAA,EACAC,iBAEWC,GAAcC,EAAA,QAIxB,UAHOA,GAAU,WACf,MAAAC,GAAA,EAEFJ,EAAGG,CAAK,IAAM,QAAA,QAElB5D,GAAAyD,EAAGG,CAAK,IAAR,MAAA5D,GAAW,cAEH,KAAM8D,EAAgB,MAAOC,GACpCL,EAAkB,yBACX,KAAAM,GAAM,MAAAC,CAAA,EAAUR,EAAGG,CAAK,EAAE,wBAI5BM,GAFgBF,GAAOF,EAI5BG,EAAQ,EACRF,EAAkB,EAClBL,EAAkB,WAEfA,GAAA,OAA4BA,EAAkB,UAAa,YAC9DA,EAAkB,SAAA,CACjB,KAAMQ,GAAM,EAAI,EAAIA,GACpB,SAAU,eAKTC,GAAgB,EAML,eAAAC,GAASC,EAAkBC,EAAA,CACrC,IAAAC,MAEHA,EAAA,MAAiBpC,EAAOkC,CAAQ,CACxB,OAAAG,GAAA,IACJA,cAAiB,UAAA,CAIpB,OAAO,KAAKH,EAAU,SAAU,YAAY,SAIvC,MAAAG,GAED,MAAAC,GAAA,MAAaF,EAAS,OACtB1F,EAAM,IAAI,gBAAgB4F,EAAI,EAC9BC,GAAO,SAAS,cAAc,GAAG,EACvCA,GAAK,KAAO7F,EACZ6F,GAAK,SAAWJ,EAChBI,GAAK,MAAA,EACL,IAAI,gBAAgB7F,CAAG,MAQpB8F,GAAsB,GAEjB,SAAAC,IAAA,CACJlB,QACHiB,GACCjB,EAAkB,YAAcA,EAAkB,WAAA,EAIrDmB,GAAA,KACCD,KACA,SAAS,iBAAiB,mBAAA,IAAA,CACzBE,EAAA,GAAAtC,EAAA,CAAA,CAAmB,SAAS,iBAAA,IAE7B,OAAO,iBAAiB,SAAUoC,EAAyB,MAE1D,OAAO,oBAAoB,SAAUA,EAAyB,8DAgCnDlG,EACL,UAAWqG,EACRA,GAAA,YAAAA,EAAgB,MAChBA,GAAA,YAAAA,EAAgB,MAChB,GAAArG,GAAS,kBAGL,IAAAG,EAAK,UAAAmG,CAAS,EAAKtG,EACvBG,GACHuF,GAASvF,EAAKmG,GAAa,OAAO,oHA0BnCF,EAAA,EAAA7C,EAAiB,IAAI,EACrBS,EAAS,eAAe,MAQvBO,GAAUD,GAAqBC,CAAK,+CA4C3BQ,EAAGpE,CAAC,EAAA4F,YACE,MAAAC,GAAA7F,GAAAyF,EAAA,EAAA7C,EAAiB5C,CAAC,6CAV1BqE,EAAiBuB,6BAmDSxG,EAAK,CAAA,CAAA,SAYpCwD,IAAmB,MAAQL,GAC9Bc,EAAS,cAAc,EAExBoC,EAAA,EAAA7C,EAAiB5C,CAAC,8CAhKkBoD,EAAewC,41BA7MzDH,EAAA,GAAGlC,EAAYnE,GAAS,MAAQA,EAAM,SAAW,EAAI,GAAOmE,CAAA,mBAI5DkC,EAAA,GAAGjC,EACFpE,GAAS,KACN,KACCA,EAAM,IAAK0G,GACR,UAAWA,GAEb,MAAOA,EAAK,MACZ,QAASA,EAAK,SAEL,UAAWA,GACZ,MAAOA,EAAK,MAAmB,QAASA,EAAK,wDAWnDC,GAAOtC,GAAYrE,CAAK,IAG3BmE,OACHX,EAAiBN,IAAWlD,GAAA,MAAAA,EAAO,QAAS,EAAI,IAAA,OAChDmE,EAAY,EAAA,GAIRX,IAAmB,MAAQxD,IAAU,SACxCwD,EAAiB,KAAK,IACrB,EACA,KAAK,IAAIA,EAAgBxD,EAAM,OAAS,CAAC,CAAA,CAAA,MAG1CwD,EAAiB,IAAA,EAGnBS,EAAS,QAAQ,OACjBI,GAAarE,CAAA,0CAyCTwD,IAAmBc,UACtBA,GAAqBd,CAAA,EACjBA,IAAmB,OAClBY,GAAkB,UACrBZ,EAAiB,KAAK,IACrB,EACA,KAAK,IAAIA,EAAgBY,EAAe,OAAS,CAAC,CAAA,CAAA,EAGpDH,EAAS,SAAA,CACR,MAAOT,EACP,MAAOY,GAAA,YAAAA,EAAiBZ,8BAjDzBoB,IACApB,GAAkB,KAAMY,GAAA,YAAAA,EAAgB,SAAU,GAAK,KACxDA,GAAA,YAAAA,EAAgB,SAAU,2BACzBS,IAASrB,GAAkB,GAAK,KAAMY,GAAA,YAAAA,EAAgB,SAAU,uBAoD5DjB,GACN+B,GAAc1B,CAAc,wBAgE1B6C,EAAA,GAAAC,EACF9C,GAAkB,MAAQY,GAAkB,KACzCA,EAAeZ,CAAc,EAC7B,IAAA,wBAqBe2C,GAAA,wBACZlB,GACNkB"}