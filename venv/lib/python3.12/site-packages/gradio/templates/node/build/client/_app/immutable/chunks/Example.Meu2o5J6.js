import{SvelteComponent as z,init as F,safe_not_equal as G,empty as y,insert_hydration as o,noop as k,detach as a,element as d,claim_element as m,children as v,attr as g,toggle_class as h,listen as E,run_all as J,ensure_array_like as b,space as H,claim_space as I,append_hydration as p,destroy_each as M,get_svelte_dataset as V,text as q,claim_text as R,set_data as S}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";function T(r,e,t){const l=r.slice();return l[9]=e[t],l[11]=t,l}function C(r,e,t){const l=r.slice();return l[12]=e[t],l[14]=t,l}function K(r){let e,t,l;function n(c,i){return typeof c[0]=="string"?P:c[6]?O:N}let f=n(r),s=f(r);return{c(){e=d("div"),s.c(),this.h()},l(c){e=m(c,"DIV",{class:!0});var i=v(e);s.l(i),i.forEach(a),this.h()},h(){g(e,"class","svelte-hn96gn"),h(e,"table",r[1]==="table"),h(e,"gallery",r[1]==="gallery"),h(e,"selected",r[2])},m(c,i){o(c,e,i),s.m(e,null),t||(l=[E(e,"mouseenter",r[7]),E(e,"mouseleave",r[8])],t=!0)},p(c,i){f===(f=n(c))&&s?s.p(c,i):(s.d(1),s=f(c),s&&(s.c(),s.m(e,null))),i&2&&h(e,"table",c[1]==="table"),i&2&&h(e,"gallery",c[1]==="gallery"),i&4&&h(e,"selected",c[2])},d(c){c&&a(e),s.d(),t=!1,J(l)}}}function N(r){let e,t,l=b(r[0].slice(0,3)),n=[];for(let s=0;s<l.length;s+=1)n[s]=L(T(r,l,s));let f=r[0].length>3&&B(r);return{c(){e=d("table");for(let s=0;s<n.length;s+=1)n[s].c();t=H(),f&&f.c(),this.h()},l(s){e=m(s,"TABLE",{class:!0});var c=v(e);for(let i=0;i<n.length;i+=1)n[i].l(c);t=I(c),f&&f.l(c),c.forEach(a),this.h()},h(){g(e,"class"," svelte-hn96gn")},m(s,c){o(s,e,c);for(let i=0;i<n.length;i+=1)n[i]&&n[i].m(e,null);p(e,t),f&&f.m(e,null)},p(s,c){if(c&1){l=b(s[0].slice(0,3));let i;for(i=0;i<l.length;i+=1){const _=T(s,l,i);n[i]?n[i].p(_,c):(n[i]=L(_),n[i].c(),n[i].m(e,t))}for(;i<n.length;i+=1)n[i].d(1);n.length=l.length}s[0].length>3?f?f.p(s,c):(f=B(s),f.c(),f.m(e,null)):f&&(f.d(1),f=null)},d(s){s&&a(e),M(n,s),f&&f.d()}}}function O(r){let e,t='<tr><td class="svelte-hn96gn">Empty</td></tr>';return{c(){e=d("table"),e.innerHTML=t,this.h()},l(l){e=m(l,"TABLE",{class:!0,"data-svelte-h":!0}),V(e)!=="svelte-1bpl05m"&&(e.innerHTML=t),this.h()},h(){g(e,"class"," svelte-hn96gn")},m(l,n){o(l,e,n)},p:k,d(l){l&&a(e)}}}function P(r){let e;return{c(){e=q(r[0])},l(t){e=R(t,r[0])},m(t,l){o(t,e,l)},p(t,l){l&1&&S(e,t[0])},d(t){t&&a(e)}}}function A(r){let e,t=r[12]+"",l;return{c(){e=d("td"),l=q(t),this.h()},l(n){e=m(n,"TD",{class:!0});var f=v(e);l=R(f,t),f.forEach(a),this.h()},h(){g(e,"class","svelte-hn96gn")},m(n,f){o(n,e,f),p(e,l)},p(n,f){f&1&&t!==(t=n[12]+"")&&S(l,t)},d(n){n&&a(e)}}}function D(r){let e,t="…";return{c(){e=d("td"),e.textContent=t,this.h()},l(l){e=m(l,"TD",{class:!0,"data-svelte-h":!0}),V(e)!=="svelte-1o35md4"&&(e.textContent=t),this.h()},h(){g(e,"class","svelte-hn96gn")},m(l,n){o(l,e,n)},d(l){l&&a(e)}}}function L(r){let e,t,l=b(r[9].slice(0,3)),n=[];for(let s=0;s<l.length;s+=1)n[s]=A(C(r,l,s));let f=r[9].length>3&&D();return{c(){e=d("tr");for(let s=0;s<n.length;s+=1)n[s].c();t=H(),f&&f.c()},l(s){e=m(s,"TR",{});var c=v(e);for(let i=0;i<n.length;i+=1)n[i].l(c);t=I(c),f&&f.l(c),c.forEach(a)},m(s,c){o(s,e,c);for(let i=0;i<n.length;i+=1)n[i]&&n[i].m(e,null);p(e,t),f&&f.m(e,null)},p(s,c){if(c&1){l=b(s[9].slice(0,3));let i;for(i=0;i<l.length;i+=1){const _=C(s,l,i);n[i]?n[i].p(_,c):(n[i]=A(_),n[i].c(),n[i].m(e,t))}for(;i<n.length;i+=1)n[i].d(1);n.length=l.length}s[9].length>3?f||(f=D(),f.c(),f.m(e,null)):f&&(f.d(1),f=null)},d(s){s&&a(e),M(n,s),f&&f.d()}}}function B(r){let e;return{c(){e=d("div"),this.h()},l(t){e=m(t,"DIV",{class:!0}),v(e).forEach(a),this.h()},h(){g(e,"class","overlay svelte-hn96gn"),h(e,"odd",r[3]%2!=0),h(e,"even",r[3]%2==0),h(e,"button",r[1]==="gallery")},m(t,l){o(t,e,l)},p(t,l){l&8&&h(e,"odd",t[3]%2!=0),l&8&&h(e,"even",t[3]%2==0),l&2&&h(e,"button",t[1]==="gallery")},d(t){t&&a(e)}}}function Q(r){let e,t=r[5]&&K(r);return{c(){t&&t.c(),e=y()},l(l){t&&t.l(l),e=y()},m(l,n){t&&t.m(l,n),o(l,e,n)},p(l,[n]){l[5]&&t.p(l,n)},i:k,o:k,d(l){l&&a(e),t&&t.d(l)}}}function U(r,e,t){let{value:l}=e,{type:n}=e,{selected:f=!1}=e,{index:s}=e,c=!1,i=Array.isArray(l),_=i&&(l.length===0||l[0].length===0);const j=()=>t(4,c=!0),w=()=>t(4,c=!1);return r.$$set=u=>{"value"in u&&t(0,l=u.value),"type"in u&&t(1,n=u.type),"selected"in u&&t(2,f=u.selected),"index"in u&&t(3,s=u.index)},[l,n,f,s,c,i,_,j,w]}class Y extends z{constructor(e){super(),F(this,e,U,Q,G,{value:0,type:1,selected:2,index:3})}}export{Y as default};
//# sourceMappingURL=Example.Meu2o5J6.js.map
