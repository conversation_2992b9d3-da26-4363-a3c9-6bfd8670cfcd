import{an as n,ao as o}from"./index.BoI39RQH.js";const r="ExtrasAsMetadata";class d{_assignExtras(s,t){if(t.extras&&Object.keys(t.extras).length>0){const e=s.metadata=s.metadata||{},a=e.gltf=e.gltf||{};a.extras=t.extras}}constructor(s){this.name=r,this.enabled=!0,this._loader=s}dispose(){this._loader=null}loadNodeAsync(s,t,e){return this._loader.loadNodeAsync(s,t,a=>{this._assignExtras(a,t),e(a)})}loadCameraAsync(s,t,e){return this._loader.loadCameraAsync(s,t,a=>{this._assignExtras(a,t),e(a)})}createMaterial(s,t,e){const a=this._loader.createMaterial(s,t,e);return this._assignExtras(a,t),a}}n(r);o(r,!1,i=>new d(i));export{d as ExtrasAsMetadata};
//# sourceMappingURL=ExtrasAsMetadata.Dux_ho8M.js.map
