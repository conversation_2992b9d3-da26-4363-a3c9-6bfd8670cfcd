{"version": 3, "file": "Example.B-yMNQmV.js", "sources": ["../../../../../../../nativeplot/Example.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\texport let title: string | null;\n\texport let x: string;\n\texport let y: string;\n</script>\n\n{#if title}\n\t{title}\n{:else}\n\t{x} x {y}\n{/if}\n"], "names": ["ctx", "create_if_block", "title", "$$props", "x", "y"], "mappings": "6QASEA,EAAC,CAAA,CAAA,MAAC,KAAG,MAACA,EAAC,CAAA,CAAA,cAAPA,EAAC,CAAA,CAAA,QAAC,KAAG,QAACA,EAAC,CAAA,CAAA,sDAAPA,EAAC,CAAA,CAAA,WAAKA,EAAC,CAAA,CAAA,iEAFPA,EAAK,CAAA,CAAA,cAALA,EAAK,CAAA,CAAA,oCAALA,EAAK,CAAA,CAAA,6DADFA,EAAK,CAAA,EAAAC,6NALE,GAAA,CAAA,MAAAC,CAAA,EAAAC,EACA,CAAA,EAAAC,CAAA,EAAAD,EACA,CAAA,EAAAE,CAAA,EAAAF"}