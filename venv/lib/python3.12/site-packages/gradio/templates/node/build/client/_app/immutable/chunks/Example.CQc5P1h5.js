import{SvelteComponent as m,init as y,safe_not_equal as g,element as b,text as v,claim_element as p,children as z,claim_text as S,detach as f,attr as k,add_render_callback as q,toggle_class as r,insert_hydration as E,append_hydration as C,add_iframe_resize_listener as D,set_data as I,noop as d,onMount as M,binding_callbacks as P}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";import"./2.CXBv8kT_.js";function V(t){let e,l=o(t[0])+"",a,c;return{c(){e=b("div"),a=v(l),this.h()},l(i){e=p(i,"DIV",{class:!0});var n=z(e);a=S(n,l),n.forEach(f),this.h()},h(){k(e,"class","svelte-1oitfqa"),q(()=>t[5].call(e)),r(e,"table",t[1]==="table"),r(e,"gallery",t[1]==="gallery"),r(e,"selected",t[2])},m(i,n){E(i,e,n),C(e,a),c=D(e,t[5].bind(e)),t[6](e)},p(i,[n]){n&1&&l!==(l=o(i[0])+"")&&I(a,l),n&2&&r(e,"table",i[1]==="table"),n&2&&r(e,"gallery",i[1]==="gallery"),n&4&&r(e,"selected",i[2])},i:d,o:d,d(i){i&&f(e),c(),t[6](null)}}}function W(t,e){t.style.setProperty("--local-text-width",`${e&&e<150?e:200}px`),t.style.whiteSpace="unset"}function o(t,e=60){if(!t)return"";const l=String(t);return l.length<=e?l:l.slice(0,e)+"..."}function j(t,e,l){let{value:a}=e,{type:c}=e,{selected:i=!1}=e,n,u;M(()=>{W(u,n)});function _(){n=this.clientWidth,l(3,n)}function h(s){P[s?"unshift":"push"](()=>{u=s,l(4,u)})}return t.$$set=s=>{"value"in s&&l(0,a=s.value),"type"in s&&l(1,c=s.type),"selected"in s&&l(2,i=s.selected)},[a,c,i,n,u,_,h]}class F extends m{constructor(e){super(),y(this,e,j,V,g,{value:0,type:1,selected:2})}}export{F as default};
//# sourceMappingURL=Example.CQc5P1h5.js.map
