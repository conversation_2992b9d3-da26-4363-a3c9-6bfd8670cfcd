{"version": 3, "file": "Example.C3O8QWdg.js", "sources": ["../../../../../../../imageslider/Example.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\texport let value: [string, string];\n\texport let samples_dir: string;\n\texport let type: \"gallery\" | \"table\";\n\texport let selected = false;\n</script>\n\n<!-- TODO: fix -->\n<!-- svelte-ignore a11y-missing-attribute -->\n<div\n\tclass=\"wrap\"\n\tclass:table={type === \"table\"}\n\tclass:gallery={type === \"gallery\"}\n\tclass:selected\n>\n\t<img src={samples_dir + value[0]} />\n\n\t<img src={samples_dir + value[1]} />\n\t<span></span>\n</div>\n\n<style>\n\t.wrap {\n\t\tposition: relative;\n\t\theight: var(--size-64);\n\t\twidth: var(--size-40);\n\t\toverflow: hidden;\n\t\tborder-radius: var(--radius-lg);\n\t}\n\timg {\n\t\theight: var(--size-64);\n\t\twidth: var(--size-40);\n\t\tposition: absolute;\n\t\t/* border-radius: var(--radius-lg); */\n\t\t/* max-width: none; */\n\t\tobject-fit: cover;\n\t}\n\n\t.wrap.selected {\n\t\tborder-color: var(--color-accent);\n\t}\n\t.wrap img:first-child {\n\t\tclip-path: inset(0 50% 0 0%);\n\t}\n\n\t.wrap img:nth-of-type(2) {\n\t\tclip-path: inset(0 0 0 50%);\n\t}\n\tspan {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: calc(50% - 0.75px);\n\t\theight: var(--size-64);\n\t\twidth: 1.5px;\n\t\tbackground: var(--border-color-primary);\n\t}\n\n\t.table {\n\t\tmargin: 0 auto;\n\t\tborder: 2px solid var(--border-color-primary);\n\t\tborder-radius: var(--radius-lg);\n\t}\n\n\t.gallery {\n\t\tborder: 2px solid var(--border-color-primary);\n\t\t/* max-height: var(--size-20); */\n\t\tobject-fit: cover;\n\t}\n</style>\n"], "names": ["ctx", "attr", "img0", "img0_src_value", "img1", "img1_src_value", "toggle_class", "div", "insert_hydration", "target", "anchor", "append_hydration", "span", "value", "$$props", "samples_dir", "type", "selected"], "mappings": "onBAeWA,EAAW,CAAA,EAAGA,EAAK,CAAA,EAAC,CAAC,CAAA,GAAAC,EAAAC,EAAA,MAAAC,CAAA,0CAErBH,EAAW,CAAA,EAAGA,EAAK,CAAA,EAAC,CAAC,CAAA,GAAAC,EAAAG,EAAA,MAAAC,CAAA,iGANlBC,EAAAC,EAAA,QAAAP,OAAS,OAAO,EACdM,EAAAC,EAAA,UAAAP,OAAS,SAAS,+BAHlCQ,EAUKC,EAAAF,EAAAG,CAAA,EAJJC,EAAmCJ,EAAAL,CAAA,SAEnCS,EAAmCJ,EAAAH,CAAA,SACnCO,EAAYJ,EAAAK,CAAA,4BAHFZ,EAAW,CAAA,EAAGA,EAAK,CAAA,EAAC,CAAC,CAAA,gCAErBA,EAAW,CAAA,EAAGA,EAAK,CAAA,EAAC,CAAC,CAAA,qBANlBM,EAAAC,EAAA,QAAAP,OAAS,OAAO,OACdM,EAAAC,EAAA,UAAAP,OAAS,SAAS,sEAXtB,GAAA,CAAA,MAAAa,CAAA,EAAAC,EACA,CAAA,YAAAC,CAAA,EAAAD,EACA,CAAA,KAAAE,CAAA,EAAAF,GACA,SAAAG,EAAW,EAAA,EAAAH"}