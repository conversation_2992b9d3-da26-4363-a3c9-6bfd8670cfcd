import{SvelteComponent as d,init as u,safe_not_equal as o,element as h,text as m,claim_element as _,children as v,claim_text as y,detach as r,attr as g,toggle_class as f,insert_hydration as b,append_hydration as q,set_data as E,noop as c}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";function k(i){let e,a=(i[0]?i[0]:"")+"",n;return{c(){e=h("div"),n=m(a),this.h()},l(t){e=_(t,"DIV",{class:!0});var l=v(e);n=y(l,a),l.forEach(r),this.h()},h(){g(e,"class","svelte-1ayixqk"),f(e,"table",i[1]==="table"),f(e,"gallery",i[1]==="gallery"),f(e,"selected",i[2])},m(t,l){b(t,e,l),q(e,n)},p(t,[l]){l&1&&a!==(a=(t[0]?t[0]:"")+"")&&E(n,a),l&2&&f(e,"table",t[1]==="table"),l&2&&f(e,"gallery",t[1]==="gallery"),l&4&&f(e,"selected",t[2])},i:c,o:c,d(t){t&&r(e)}}}function C(i,e,a){let{value:n}=e,{type:t}=e,{selected:l=!1}=e;return i.$$set=s=>{"value"in s&&a(0,n=s.value),"type"in s&&a(1,t=s.type),"selected"in s&&a(2,l=s.selected)},[n,t,l]}class S extends d{constructor(e){super(),u(this,e,C,k,o,{value:0,type:1,selected:2})}}export{S as default};
//# sourceMappingURL=Example.V12cx6oG.js.map
