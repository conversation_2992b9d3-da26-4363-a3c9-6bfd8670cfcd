import{SvelteComponent as b,init as d,safe_not_equal as k,empty as y,insert_hydration as c,noop as a,detach as r,text as u,claim_text as o,set_data as m}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";function s(f){let t,l,n;return{c(){t=u(f[1]),l=u(" x "),n=u(f[2])},l(e){t=o(e,f[1]),l=o(e," x "),n=o(e,f[2])},m(e,i){c(e,t,i),c(e,l,i),c(e,n,i)},p(e,i){i&2&&m(t,e[1]),i&4&&m(n,e[2])},d(e){e&&(r(t),r(l),r(n))}}}function x(f){let t;return{c(){t=u(f[0])},l(l){t=o(l,f[0])},m(l,n){c(l,t,n)},p(l,n){n&1&&m(t,l[0])},d(l){l&&r(t)}}}function p(f){let t;function l(i,_){return i[0]?x:s}let n=l(f),e=n(f);return{c(){e.c(),t=y()},l(i){e.l(i),t=y()},m(i,_){e.m(i,_),c(i,t,_)},p(i,[_]){n===(n=l(i))&&e?e.p(i,_):(e.d(1),e=n(i),e&&(e.c(),e.m(t.parentNode,t)))},i:a,o:a,d(i){i&&r(t),e.d(i)}}}function q(f,t,l){let{title:n}=t,{x:e}=t,{y:i}=t;return f.$$set=_=>{"title"in _&&l(0,n=_.title),"x"in _&&l(1,e=_.x),"y"in _&&l(2,i=_.y)},[n,e,i]}class N extends b{constructor(t){super(),d(this,t,q,p,k,{title:0,x:1,y:2})}}export{N as default};
//# sourceMappingURL=Example.B-yMNQmV.js.map
