import{SvelteComponent as u,init as _,safe_not_equal as y,element as g,text as v,claim_element as b,children as q,claim_text as E,detach as h,attr as j,toggle_class as s,insert_hydration as k,append_hydration as C,noop as m}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";function D(a){let e,n;return{c(){e=g("div"),n=v(a[2]),this.h()},l(t){e=b(t,"DIV",{class:!0});var i=q(e);n=E(i,a[2]),i.forEach(h),this.h()},h(){j(e,"class","svelte-1ayixqk"),s(e,"table",a[0]==="table"),s(e,"gallery",a[0]==="gallery"),s(e,"selected",a[1])},m(t,i){k(t,e,i),C(e,n)},p(t,[i]){i&1&&s(e,"table",t[0]==="table"),i&1&&s(e,"gallery",t[0]==="gallery"),i&2&&s(e,"selected",t[1])},i:m,o:m,d(t){t&&h(e)}}}function I(a,e,n){let{value:t}=e,{type:i}=e,{selected:f=!1}=e,{choices:c}=e,o=t.map(l=>{var d;return(d=c.find(r=>r[1]===l))==null?void 0:d[0]}).filter(l=>l!==void 0).join(", ");return a.$$set=l=>{"value"in l&&n(3,t=l.value),"type"in l&&n(0,i=l.type),"selected"in l&&n(1,f=l.selected),"choices"in l&&n(4,c=l.choices)},[i,f,o,t,c]}class x extends u{constructor(e){super(),_(this,e,I,D,y,{value:3,type:0,selected:1,choices:4})}}export{x as default};
//# sourceMappingURL=Example.CO-COJlt.js.map
