const __vite__fileDeps=["./dagre-QXRM2OYR.BQYt6s-8.js","./graph.DlqXvFLT.js","./_baseUniq.B7Rtop1b.js","./layout.CRn3nOBS.js","./_basePickBy.CDwq2MpB.js","./clone.D5no-sH7.js","./c4Diagram-7JAJQR3Y.Go8wi4bE.js","./chunk-PLTTB2RT.U99BjYJQ.js","./select.BigU4G0v.js","./flowDiagram-27HWSH3H.CIsWH93n.js","./chunk-2O5F6CEG.BYEkavuV.js","./channel.YXEN2ZYg.js","./erDiagram-MVNNDQJ5.JPDLzYK_.js","./gitGraphDiagram-ISGV4O2Y.DFeXFmhq.js","./chunk-IUKPXING.CeKJjTNb.js","./chunk-66XRIAFR.CawiuIjV.js","./mermaid-parser.core.BTiWe88f.js","./preload-helper.D6kgxu3v.js","./ganttDiagram-ZCE2YOAT.CtV-LEr3.js","./2.CXBv8kT_.js","./stores.BOVhuIJR.js","./client.CVMEu4Wy.js","../assets/2.D-_MaO7c.css","./time.Ddv6ux-b.js","./step.Ce-xBr2D.js","./linear.BVb3otZY.js","./init.Dmth1JHB.js","./infoDiagram-SDLB2J7W.B-j-JqLF.js","./pieDiagram-OC6WZ2SS.C0ROfH8a.js","./arc.CL2LCPr_.js","./ordinal.BJp8kCrd.js","./quadrantDiagram-OT6RYTWY.BNidm50O.js","./xychartDiagram-NJOKMNIP.CtYY9Gpc.js","./range.OtVwhkKS.js","./requirementDiagram-BKGUWIPO.QpoQx3Nq.js","./sequenceDiagram-C4VUPXDP.Cd5azHM2.js","./classDiagram-L266QK7U.D3YN8amE.js","./chunk-5V4FS25O.BGn0R5SU.js","./classDiagram-v2-JRWBCVM4.D3YN8amE.js","./stateDiagram-BVO7J4UH.DRVlTaN2.js","./chunk-4IRHCMPZ.B8XETl7Y.js","./stateDiagram-v2-WR7QG3WR.Dl5SqiWB.js","./journeyDiagram-D7A75E63.DtPwmuPl.js","./timeline-definition-WOTUTIAU.gyxjTqh7.js","./mindmap-definition-7EJRZJGK.D4JLXad2.js","./cytoscape.esm.BlEmlE64.js","./sankeyDiagram-3MH5UGAL.Fi0ZOIw0.js","./diagram-DHSB7DV3.scXJlDpG.js","./blockDiagram-5JUZGEFE.ByWfu6po.js","./architectureDiagram-PQUH6ZAG.CSxtIQ3_.js"],__vite__mapDeps=i=>i.map(i=>__vite__fileDeps[i]);
var _d=Object.defineProperty;var Bd=(e,t,r)=>t in e?_d(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r;var dt=(e,t,r)=>(Bd(e,typeof t!="symbol"?t+"":t,r),r);import{_ as gt}from"./preload-helper.D6kgxu3v.js";import{D as Ld,f as Ad}from"./2.CXBv8kT_.js";import{d as Md}from"./dispatch.kxCwF96_.js";import{T as Ed,N as Fd,y as $d,z as Ao,A as Mo,B as Od,L as Dd,K as Rd,M as Id,k as Xi,S as Pd,U as Nd,V as zd,Y as Wd,X as qd,W as jl,_ as Hd,$ as jd,Z as Ud,O as An,a0 as Yd,a2 as Gd,a1 as Vd,a3 as Xd,a4 as Zd,a5 as Kd,a6 as Qd,l as Jd}from"./step.Ce-xBr2D.js";import{n as Ul,m as tg,a as eg,b as rg,c as $a,d as ci,s as lt}from"./select.BigU4G0v.js";var Yl={exports:{}};(function(e,t){(function(r,i){e.exports=i()})(Ld,function(){var r=1e3,i=6e4,a=36e5,n="millisecond",o="second",s="minute",l="hour",c="day",h="week",u="month",f="quarter",p="year",g="date",m="Invalid Date",y=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,x=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,b={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),ordinal:function(E){var A=["th","st","nd","rd"],_=E%100;return"["+E+(A[(_-20)%10]||A[_]||A[0])+"]"}},C=function(E,A,_){var F=String(E);return!F||F.length>=A?E:""+Array(A+1-F.length).join(_)+E},S={s:C,z:function(E){var A=-E.utcOffset(),_=Math.abs(A),F=Math.floor(_/60),B=_%60;return(A<=0?"+":"-")+C(F,2,"0")+":"+C(B,2,"0")},m:function E(A,_){if(A.date()<_.date())return-E(_,A);var F=12*(_.year()-A.year())+(_.month()-A.month()),B=A.clone().add(F,u),W=_-B<0,U=A.clone().add(F+(W?-1:1),u);return+(-(F+(_-B)/(W?B-U:U-B))||0)},a:function(E){return E<0?Math.ceil(E)||0:Math.floor(E)},p:function(E){return{M:u,y:p,w:h,d:c,D:g,h:l,m:s,s:o,ms:n,Q:f}[E]||String(E||"").toLowerCase().replace(/s$/,"")},u:function(E){return E===void 0}},v="en",M={};M[v]=b;var T="$isDayjsObject",D=function(E){return E instanceof z||!(!E||!E[T])},P=function E(A,_,F){var B;if(!A)return v;if(typeof A=="string"){var W=A.toLowerCase();M[W]&&(B=W),_&&(M[W]=_,B=W);var U=A.split("-");if(!B&&U.length>1)return E(U[0])}else{var J=A.name;M[J]=A,B=J}return!F&&B&&(v=B),B||!F&&v},$=function(E,A){if(D(E))return E.clone();var _=typeof A=="object"?A:{};return _.date=E,_.args=arguments,new z(_)},L=S;L.l=P,L.i=D,L.w=function(E,A){return $(E,{locale:A.$L,utc:A.$u,x:A.$x,$offset:A.$offset})};var z=function(){function E(_){this.$L=P(_.locale,null,!0),this.parse(_),this.$x=this.$x||_.x||{},this[T]=!0}var A=E.prototype;return A.parse=function(_){this.$d=function(F){var B=F.date,W=F.utc;if(B===null)return new Date(NaN);if(L.u(B))return new Date;if(B instanceof Date)return new Date(B);if(typeof B=="string"&&!/Z$/i.test(B)){var U=B.match(y);if(U){var J=U[2]-1||0,tt=(U[7]||"0").substring(0,3);return W?new Date(Date.UTC(U[1],J,U[3]||1,U[4]||0,U[5]||0,U[6]||0,tt)):new Date(U[1],J,U[3]||1,U[4]||0,U[5]||0,U[6]||0,tt)}}return new Date(B)}(_),this.init()},A.init=function(){var _=this.$d;this.$y=_.getFullYear(),this.$M=_.getMonth(),this.$D=_.getDate(),this.$W=_.getDay(),this.$H=_.getHours(),this.$m=_.getMinutes(),this.$s=_.getSeconds(),this.$ms=_.getMilliseconds()},A.$utils=function(){return L},A.isValid=function(){return this.$d.toString()!==m},A.isSame=function(_,F){var B=$(_);return this.startOf(F)<=B&&B<=this.endOf(F)},A.isAfter=function(_,F){return $(_)<this.startOf(F)},A.isBefore=function(_,F){return this.endOf(F)<$(_)},A.$g=function(_,F,B){return L.u(_)?this[F]:this.set(B,_)},A.unix=function(){return Math.floor(this.valueOf()/1e3)},A.valueOf=function(){return this.$d.getTime()},A.startOf=function(_,F){var B=this,W=!!L.u(F)||F,U=L.p(_),J=function(Qt,bt){var oe=L.w(B.$u?Date.UTC(B.$y,bt,Qt):new Date(B.$y,bt,Qt),B);return W?oe:oe.endOf(c)},tt=function(Qt,bt){return L.w(B.toDate()[Qt].apply(B.toDate("s"),(W?[0,0,0,0]:[23,59,59,999]).slice(bt)),B)},et=this.$W,ht=this.$M,at=this.$D,St="set"+(this.$u?"UTC":"");switch(U){case p:return W?J(1,0):J(31,11);case u:return W?J(1,ht):J(0,ht+1);case h:var Bt=this.$locale().weekStart||0,se=(et<Bt?et+7:et)-Bt;return J(W?at-se:at+(6-se),ht);case c:case g:return tt(St+"Hours",0);case l:return tt(St+"Minutes",1);case s:return tt(St+"Seconds",2);case o:return tt(St+"Milliseconds",3);default:return this.clone()}},A.endOf=function(_){return this.startOf(_,!1)},A.$set=function(_,F){var B,W=L.p(_),U="set"+(this.$u?"UTC":""),J=(B={},B[c]=U+"Date",B[g]=U+"Date",B[u]=U+"Month",B[p]=U+"FullYear",B[l]=U+"Hours",B[s]=U+"Minutes",B[o]=U+"Seconds",B[n]=U+"Milliseconds",B)[W],tt=W===c?this.$D+(F-this.$W):F;if(W===u||W===p){var et=this.clone().set(g,1);et.$d[J](tt),et.init(),this.$d=et.set(g,Math.min(this.$D,et.daysInMonth())).$d}else J&&this.$d[J](tt);return this.init(),this},A.set=function(_,F){return this.clone().$set(_,F)},A.get=function(_){return this[L.p(_)]()},A.add=function(_,F){var B,W=this;_=Number(_);var U=L.p(F),J=function(ht){var at=$(W);return L.w(at.date(at.date()+Math.round(ht*_)),W)};if(U===u)return this.set(u,this.$M+_);if(U===p)return this.set(p,this.$y+_);if(U===c)return J(1);if(U===h)return J(7);var tt=(B={},B[s]=i,B[l]=a,B[o]=r,B)[U]||1,et=this.$d.getTime()+_*tt;return L.w(et,this)},A.subtract=function(_,F){return this.add(-1*_,F)},A.format=function(_){var F=this,B=this.$locale();if(!this.isValid())return B.invalidDate||m;var W=_||"YYYY-MM-DDTHH:mm:ssZ",U=L.z(this),J=this.$H,tt=this.$m,et=this.$M,ht=B.weekdays,at=B.months,St=B.meridiem,Bt=function(bt,oe,Jt,De){return bt&&(bt[oe]||bt(F,W))||Jt[oe].slice(0,De)},se=function(bt){return L.s(J%12||12,bt,"0")},Qt=St||function(bt,oe,Jt){var De=bt<12?"AM":"PM";return Jt?De.toLowerCase():De};return W.replace(x,function(bt,oe){return oe||function(Jt){switch(Jt){case"YY":return String(F.$y).slice(-2);case"YYYY":return L.s(F.$y,4,"0");case"M":return et+1;case"MM":return L.s(et+1,2,"0");case"MMM":return Bt(B.monthsShort,et,at,3);case"MMMM":return Bt(at,et);case"D":return F.$D;case"DD":return L.s(F.$D,2,"0");case"d":return String(F.$W);case"dd":return Bt(B.weekdaysMin,F.$W,ht,2);case"ddd":return Bt(B.weekdaysShort,F.$W,ht,3);case"dddd":return ht[F.$W];case"H":return String(J);case"HH":return L.s(J,2,"0");case"h":return se(1);case"hh":return se(2);case"a":return Qt(J,tt,!0);case"A":return Qt(J,tt,!1);case"m":return String(tt);case"mm":return L.s(tt,2,"0");case"s":return String(F.$s);case"ss":return L.s(F.$s,2,"0");case"SSS":return L.s(F.$ms,3,"0");case"Z":return U}return null}(bt)||U.replace(":","")})},A.utcOffset=function(){return 15*-Math.round(this.$d.getTimezoneOffset()/15)},A.diff=function(_,F,B){var W,U=this,J=L.p(F),tt=$(_),et=(tt.utcOffset()-this.utcOffset())*i,ht=this-tt,at=function(){return L.m(U,tt)};switch(J){case p:W=at()/12;break;case u:W=at();break;case f:W=at()/3;break;case h:W=(ht-et)/6048e5;break;case c:W=(ht-et)/864e5;break;case l:W=ht/a;break;case s:W=ht/i;break;case o:W=ht/r;break;default:W=ht}return B?W:L.a(W)},A.daysInMonth=function(){return this.endOf(u).$D},A.$locale=function(){return M[this.$L]},A.locale=function(_,F){if(!_)return this.$L;var B=this.clone(),W=P(_,F,!0);return W&&(B.$L=W),B},A.clone=function(){return L.w(this.$d,this)},A.toDate=function(){return new Date(this.valueOf())},A.toJSON=function(){return this.isValid()?this.toISOString():null},A.toISOString=function(){return this.$d.toISOString()},A.toString=function(){return this.$d.toUTCString()},E}(),R=z.prototype;return $.prototype=R,[["$ms",n],["$s",o],["$m",s],["$H",l],["$W",c],["$M",u],["$y",p],["$D",g]].forEach(function(E){R[E[1]]=function(A){return this.$g(A,E[0],E[1])}}),$.extend=function(E,A){return E.$i||(E(A,z,$),E.$i=!0),$},$.locale=P,$.isDayjs=D,$.unix=function(E){return $(1e3*E)},$.en=M[v],$.Ls=M,$.p={},$})})(Yl);var ig=Yl.exports;const ag=Ad(ig),Zi={min:{r:0,g:0,b:0,s:0,l:0,a:0},max:{r:255,g:255,b:255,h:360,s:100,l:100,a:1},clamp:{r:e=>e>=255?255:e<0?0:e,g:e=>e>=255?255:e<0?0:e,b:e=>e>=255?255:e<0?0:e,h:e=>e%360,s:e=>e>=100?100:e<0?0:e,l:e=>e>=100?100:e<0?0:e,a:e=>e>=1?1:e<0?0:e},toLinear:e=>{const t=e/255;return e>.03928?Math.pow((t+.055)/1.055,2.4):t/12.92},hue2rgb:(e,t,r)=>(r<0&&(r+=1),r>1&&(r-=1),r<1/6?e+(t-e)*6*r:r<1/2?t:r<2/3?e+(t-e)*(2/3-r)*6:e),hsl2rgb:({h:e,s:t,l:r},i)=>{if(!t)return r*2.55;e/=360,t/=100,r/=100;const a=r<.5?r*(1+t):r+t-r*t,n=2*r-a;switch(i){case"r":return Zi.hue2rgb(n,a,e+1/3)*255;case"g":return Zi.hue2rgb(n,a,e)*255;case"b":return Zi.hue2rgb(n,a,e-1/3)*255}},rgb2hsl:({r:e,g:t,b:r},i)=>{e/=255,t/=255,r/=255;const a=Math.max(e,t,r),n=Math.min(e,t,r),o=(a+n)/2;if(i==="l")return o*100;if(a===n)return 0;const s=a-n,l=o>.5?s/(2-a-n):s/(a+n);if(i==="s")return l*100;switch(a){case e:return((t-r)/s+(t<r?6:0))*60;case t:return((r-e)/s+2)*60;case r:return((e-t)/s+4)*60;default:return-1}}},ng={clamp:(e,t,r)=>t>r?Math.min(t,Math.max(r,e)):Math.min(r,Math.max(t,e)),round:e=>Math.round(e*1e10)/1e10},sg={dec2hex:e=>{const t=Math.round(e).toString(16);return t.length>1?t:`0${t}`}},nt={channel:Zi,lang:ng,unit:sg},Ie={};for(let e=0;e<=255;e++)Ie[e]=nt.unit.dec2hex(e);const $t={ALL:0,RGB:1,HSL:2};class og{constructor(){this.type=$t.ALL}get(){return this.type}set(t){if(this.type&&this.type!==t)throw new Error("Cannot change both RGB and HSL channels at the same time");this.type=t}reset(){this.type=$t.ALL}is(t){return this.type===t}}class lg{constructor(t,r){this.color=r,this.changed=!1,this.data=t,this.type=new og}set(t,r){return this.color=r,this.changed=!1,this.data=t,this.type.type=$t.ALL,this}_ensureHSL(){const t=this.data,{h:r,s:i,l:a}=t;r===void 0&&(t.h=nt.channel.rgb2hsl(t,"h")),i===void 0&&(t.s=nt.channel.rgb2hsl(t,"s")),a===void 0&&(t.l=nt.channel.rgb2hsl(t,"l"))}_ensureRGB(){const t=this.data,{r,g:i,b:a}=t;r===void 0&&(t.r=nt.channel.hsl2rgb(t,"r")),i===void 0&&(t.g=nt.channel.hsl2rgb(t,"g")),a===void 0&&(t.b=nt.channel.hsl2rgb(t,"b"))}get r(){const t=this.data,r=t.r;return!this.type.is($t.HSL)&&r!==void 0?r:(this._ensureHSL(),nt.channel.hsl2rgb(t,"r"))}get g(){const t=this.data,r=t.g;return!this.type.is($t.HSL)&&r!==void 0?r:(this._ensureHSL(),nt.channel.hsl2rgb(t,"g"))}get b(){const t=this.data,r=t.b;return!this.type.is($t.HSL)&&r!==void 0?r:(this._ensureHSL(),nt.channel.hsl2rgb(t,"b"))}get h(){const t=this.data,r=t.h;return!this.type.is($t.RGB)&&r!==void 0?r:(this._ensureRGB(),nt.channel.rgb2hsl(t,"h"))}get s(){const t=this.data,r=t.s;return!this.type.is($t.RGB)&&r!==void 0?r:(this._ensureRGB(),nt.channel.rgb2hsl(t,"s"))}get l(){const t=this.data,r=t.l;return!this.type.is($t.RGB)&&r!==void 0?r:(this._ensureRGB(),nt.channel.rgb2hsl(t,"l"))}get a(){return this.data.a}set r(t){this.type.set($t.RGB),this.changed=!0,this.data.r=t}set g(t){this.type.set($t.RGB),this.changed=!0,this.data.g=t}set b(t){this.type.set($t.RGB),this.changed=!0,this.data.b=t}set h(t){this.type.set($t.HSL),this.changed=!0,this.data.h=t}set s(t){this.type.set($t.HSL),this.changed=!0,this.data.s=t}set l(t){this.type.set($t.HSL),this.changed=!0,this.data.l=t}set a(t){this.changed=!0,this.data.a=t}}const Oa=new lg({r:0,g:0,b:0,a:0},"transparent"),br={re:/^#((?:[a-f0-9]{2}){2,4}|[a-f0-9]{3})$/i,parse:e=>{if(e.charCodeAt(0)!==35)return;const t=e.match(br.re);if(!t)return;const r=t[1],i=parseInt(r,16),a=r.length,n=a%4===0,o=a>4,s=o?1:17,l=o?8:4,c=n?0:-1,h=o?255:15;return Oa.set({r:(i>>l*(c+3)&h)*s,g:(i>>l*(c+2)&h)*s,b:(i>>l*(c+1)&h)*s,a:n?(i&h)*s/255:1},e)},stringify:e=>{const{r:t,g:r,b:i,a}=e;return a<1?`#${Ie[Math.round(t)]}${Ie[Math.round(r)]}${Ie[Math.round(i)]}${Ie[Math.round(a*255)]}`:`#${Ie[Math.round(t)]}${Ie[Math.round(r)]}${Ie[Math.round(i)]}`}},Ze={re:/^hsla?\(\s*?(-?(?:\d+(?:\.\d+)?|(?:\.\d+))(?:e-?\d+)?(?:deg|grad|rad|turn)?)\s*?(?:,|\s)\s*?(-?(?:\d+(?:\.\d+)?|(?:\.\d+))(?:e-?\d+)?%)\s*?(?:,|\s)\s*?(-?(?:\d+(?:\.\d+)?|(?:\.\d+))(?:e-?\d+)?%)(?:\s*?(?:,|\/)\s*?\+?(-?(?:\d+(?:\.\d+)?|(?:\.\d+))(?:e-?\d+)?(%)?))?\s*?\)$/i,hueRe:/^(.+?)(deg|grad|rad|turn)$/i,_hue2deg:e=>{const t=e.match(Ze.hueRe);if(t){const[,r,i]=t;switch(i){case"grad":return nt.channel.clamp.h(parseFloat(r)*.9);case"rad":return nt.channel.clamp.h(parseFloat(r)*180/Math.PI);case"turn":return nt.channel.clamp.h(parseFloat(r)*360)}}return nt.channel.clamp.h(parseFloat(e))},parse:e=>{const t=e.charCodeAt(0);if(t!==104&&t!==72)return;const r=e.match(Ze.re);if(!r)return;const[,i,a,n,o,s]=r;return Oa.set({h:Ze._hue2deg(i),s:nt.channel.clamp.s(parseFloat(a)),l:nt.channel.clamp.l(parseFloat(n)),a:o?nt.channel.clamp.a(s?parseFloat(o)/100:parseFloat(o)):1},e)},stringify:e=>{const{h:t,s:r,l:i,a}=e;return a<1?`hsla(${nt.lang.round(t)}, ${nt.lang.round(r)}%, ${nt.lang.round(i)}%, ${a})`:`hsl(${nt.lang.round(t)}, ${nt.lang.round(r)}%, ${nt.lang.round(i)}%)`}},ri={colors:{aliceblue:"#f0f8ff",antiquewhite:"#faebd7",aqua:"#00ffff",aquamarine:"#7fffd4",azure:"#f0ffff",beige:"#f5f5dc",bisque:"#ffe4c4",black:"#000000",blanchedalmond:"#ffebcd",blue:"#0000ff",blueviolet:"#8a2be2",brown:"#a52a2a",burlywood:"#deb887",cadetblue:"#5f9ea0",chartreuse:"#7fff00",chocolate:"#d2691e",coral:"#ff7f50",cornflowerblue:"#6495ed",cornsilk:"#fff8dc",crimson:"#dc143c",cyanaqua:"#00ffff",darkblue:"#00008b",darkcyan:"#008b8b",darkgoldenrod:"#b8860b",darkgray:"#a9a9a9",darkgreen:"#006400",darkgrey:"#a9a9a9",darkkhaki:"#bdb76b",darkmagenta:"#8b008b",darkolivegreen:"#556b2f",darkorange:"#ff8c00",darkorchid:"#9932cc",darkred:"#8b0000",darksalmon:"#e9967a",darkseagreen:"#8fbc8f",darkslateblue:"#483d8b",darkslategray:"#2f4f4f",darkslategrey:"#2f4f4f",darkturquoise:"#00ced1",darkviolet:"#9400d3",deeppink:"#ff1493",deepskyblue:"#00bfff",dimgray:"#696969",dimgrey:"#696969",dodgerblue:"#1e90ff",firebrick:"#b22222",floralwhite:"#fffaf0",forestgreen:"#228b22",fuchsia:"#ff00ff",gainsboro:"#dcdcdc",ghostwhite:"#f8f8ff",gold:"#ffd700",goldenrod:"#daa520",gray:"#808080",green:"#008000",greenyellow:"#adff2f",grey:"#808080",honeydew:"#f0fff0",hotpink:"#ff69b4",indianred:"#cd5c5c",indigo:"#4b0082",ivory:"#fffff0",khaki:"#f0e68c",lavender:"#e6e6fa",lavenderblush:"#fff0f5",lawngreen:"#7cfc00",lemonchiffon:"#fffacd",lightblue:"#add8e6",lightcoral:"#f08080",lightcyan:"#e0ffff",lightgoldenrodyellow:"#fafad2",lightgray:"#d3d3d3",lightgreen:"#90ee90",lightgrey:"#d3d3d3",lightpink:"#ffb6c1",lightsalmon:"#ffa07a",lightseagreen:"#20b2aa",lightskyblue:"#87cefa",lightslategray:"#778899",lightslategrey:"#778899",lightsteelblue:"#b0c4de",lightyellow:"#ffffe0",lime:"#00ff00",limegreen:"#32cd32",linen:"#faf0e6",magenta:"#ff00ff",maroon:"#800000",mediumaquamarine:"#66cdaa",mediumblue:"#0000cd",mediumorchid:"#ba55d3",mediumpurple:"#9370db",mediumseagreen:"#3cb371",mediumslateblue:"#7b68ee",mediumspringgreen:"#00fa9a",mediumturquoise:"#48d1cc",mediumvioletred:"#c71585",midnightblue:"#191970",mintcream:"#f5fffa",mistyrose:"#ffe4e1",moccasin:"#ffe4b5",navajowhite:"#ffdead",navy:"#000080",oldlace:"#fdf5e6",olive:"#808000",olivedrab:"#6b8e23",orange:"#ffa500",orangered:"#ff4500",orchid:"#da70d6",palegoldenrod:"#eee8aa",palegreen:"#98fb98",paleturquoise:"#afeeee",palevioletred:"#db7093",papayawhip:"#ffefd5",peachpuff:"#ffdab9",peru:"#cd853f",pink:"#ffc0cb",plum:"#dda0dd",powderblue:"#b0e0e6",purple:"#800080",rebeccapurple:"#663399",red:"#ff0000",rosybrown:"#bc8f8f",royalblue:"#4169e1",saddlebrown:"#8b4513",salmon:"#fa8072",sandybrown:"#f4a460",seagreen:"#2e8b57",seashell:"#fff5ee",sienna:"#a0522d",silver:"#c0c0c0",skyblue:"#87ceeb",slateblue:"#6a5acd",slategray:"#708090",slategrey:"#708090",snow:"#fffafa",springgreen:"#00ff7f",tan:"#d2b48c",teal:"#008080",thistle:"#d8bfd8",transparent:"#00000000",turquoise:"#40e0d0",violet:"#ee82ee",wheat:"#f5deb3",white:"#ffffff",whitesmoke:"#f5f5f5",yellow:"#ffff00",yellowgreen:"#9acd32"},parse:e=>{e=e.toLowerCase();const t=ri.colors[e];if(t)return br.parse(t)},stringify:e=>{const t=br.stringify(e);for(const r in ri.colors)if(ri.colors[r]===t)return r}},Kr={re:/^rgba?\(\s*?(-?(?:\d+(?:\.\d+)?|(?:\.\d+))(?:e\d+)?(%?))\s*?(?:,|\s)\s*?(-?(?:\d+(?:\.\d+)?|(?:\.\d+))(?:e\d+)?(%?))\s*?(?:,|\s)\s*?(-?(?:\d+(?:\.\d+)?|(?:\.\d+))(?:e\d+)?(%?))(?:\s*?(?:,|\/)\s*?\+?(-?(?:\d+(?:\.\d+)?|(?:\.\d+))(?:e\d+)?(%?)))?\s*?\)$/i,parse:e=>{const t=e.charCodeAt(0);if(t!==114&&t!==82)return;const r=e.match(Kr.re);if(!r)return;const[,i,a,n,o,s,l,c,h]=r;return Oa.set({r:nt.channel.clamp.r(a?parseFloat(i)*2.55:parseFloat(i)),g:nt.channel.clamp.g(o?parseFloat(n)*2.55:parseFloat(n)),b:nt.channel.clamp.b(l?parseFloat(s)*2.55:parseFloat(s)),a:c?nt.channel.clamp.a(h?parseFloat(c)/100:parseFloat(c)):1},e)},stringify:e=>{const{r:t,g:r,b:i,a}=e;return a<1?`rgba(${nt.lang.round(t)}, ${nt.lang.round(r)}, ${nt.lang.round(i)}, ${nt.lang.round(a)})`:`rgb(${nt.lang.round(t)}, ${nt.lang.round(r)}, ${nt.lang.round(i)})`}},xe={format:{keyword:ri,hex:br,rgb:Kr,rgba:Kr,hsl:Ze,hsla:Ze},parse:e=>{if(typeof e!="string")return e;const t=br.parse(e)||Kr.parse(e)||Ze.parse(e)||ri.parse(e);if(t)return t;throw new Error(`Unsupported color format: "${e}"`)},stringify:e=>!e.changed&&e.color?e.color:e.type.is($t.HSL)||e.data.r===void 0?Ze.stringify(e):e.a<1||!Number.isInteger(e.r)||!Number.isInteger(e.g)||!Number.isInteger(e.b)?Kr.stringify(e):br.stringify(e)},Gl=(e,t)=>{const r=xe.parse(e);for(const i in t)r[i]=nt.channel.clamp[i](t[i]);return xe.stringify(r)},ii=(e,t,r=0,i=1)=>{if(typeof e!="number")return Gl(e,{a:t});const a=Oa.set({r:nt.channel.clamp.r(e),g:nt.channel.clamp.g(t),b:nt.channel.clamp.b(r),a:nt.channel.clamp.a(i)});return xe.stringify(a)},cg=e=>{const{r:t,g:r,b:i}=xe.parse(e),a=.2126*nt.channel.toLinear(t)+.7152*nt.channel.toLinear(r)+.0722*nt.channel.toLinear(i);return nt.lang.round(a)},hg=e=>cg(e)>=.5,_i=e=>!hg(e),Vl=(e,t,r)=>{const i=xe.parse(e),a=i[t],n=nt.channel.clamp[t](a+r);return a!==n&&(i[t]=n),xe.stringify(i)},q=(e,t)=>Vl(e,"l",t),Q=(e,t)=>Vl(e,"l",-t),w=(e,t)=>{const r=xe.parse(e),i={};for(const a in t)t[a]&&(i[a]=r[a]+t[a]);return Gl(e,i)},ug=(e,t,r=50)=>{const{r:i,g:a,b:n,a:o}=xe.parse(e),{r:s,g:l,b:c,a:h}=xe.parse(t),u=r/100,f=u*2-1,p=o-h,m=((f*p===-1?f:(f+p)/(1+f*p))+1)/2,y=1-m,x=i*m+s*y,b=a*m+l*y,C=n*m+c*y,S=o*u+h*(1-u);return ii(x,b,C,S)},N=(e,t=100)=>{const r=xe.parse(e);return r.r=255-r.r,r.g=255-r.g,r.b=255-r.b,ug(r,e,t)};/*! @license DOMPurify 3.2.4 | (c) Cure53 and other contributors | Released under the Apache license 2.0 and Mozilla Public License 2.0 | github.com/cure53/DOMPurify/blob/3.2.4/LICENSE */const{entries:Xl,setPrototypeOf:Eo,isFrozen:fg,getPrototypeOf:pg,getOwnPropertyDescriptor:dg}=Object;let{freeze:Ht,seal:ae,create:Zl}=Object,{apply:Mn,construct:En}=typeof Reflect<"u"&&Reflect;Ht||(Ht=function(t){return t});ae||(ae=function(t){return t});Mn||(Mn=function(t,r,i){return t.apply(r,i)});En||(En=function(t,r){return new t(...r)});const Wi=jt(Array.prototype.forEach),gg=jt(Array.prototype.lastIndexOf),Fo=jt(Array.prototype.pop),zr=jt(Array.prototype.push),mg=jt(Array.prototype.splice),Ki=jt(String.prototype.toLowerCase),fn=jt(String.prototype.toString),$o=jt(String.prototype.match),Wr=jt(String.prototype.replace),yg=jt(String.prototype.indexOf),xg=jt(String.prototype.trim),le=jt(Object.prototype.hasOwnProperty),Pt=jt(RegExp.prototype.test),qr=bg(TypeError);function jt(e){return function(t){for(var r=arguments.length,i=new Array(r>1?r-1:0),a=1;a<r;a++)i[a-1]=arguments[a];return Mn(e,t,i)}}function bg(e){return function(){for(var t=arguments.length,r=new Array(t),i=0;i<t;i++)r[i]=arguments[i];return En(e,r)}}function ot(e,t){let r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:Ki;Eo&&Eo(e,null);let i=t.length;for(;i--;){let a=t[i];if(typeof a=="string"){const n=r(a);n!==a&&(fg(t)||(t[i]=n),a=n)}e[a]=!0}return e}function Cg(e){for(let t=0;t<e.length;t++)le(e,t)||(e[t]=null);return e}function Ge(e){const t=Zl(null);for(const[r,i]of Xl(e))le(e,r)&&(Array.isArray(i)?t[r]=Cg(i):i&&typeof i=="object"&&i.constructor===Object?t[r]=Ge(i):t[r]=i);return t}function Hr(e,t){for(;e!==null;){const i=dg(e,t);if(i){if(i.get)return jt(i.get);if(typeof i.value=="function")return jt(i.value)}e=pg(e)}function r(){return null}return r}const Oo=Ht(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),pn=Ht(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),dn=Ht(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),kg=Ht(["animate","color-profile","cursor","discard","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),gn=Ht(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover","mprescripts"]),wg=Ht(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),Do=Ht(["#text"]),Ro=Ht(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","popover","popovertarget","popovertargetaction","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","wrap","xmlns","slot"]),mn=Ht(["accent-height","accumulate","additive","alignment-baseline","amplitude","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","exponent","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","intercept","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","slope","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","tablevalues","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),Io=Ht(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),qi=Ht(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),vg=ae(/\{\{[\w\W]*|[\w\W]*\}\}/gm),Sg=ae(/<%[\w\W]*|[\w\W]*%>/gm),Tg=ae(/\$\{[\w\W]*/gm),_g=ae(/^data-[\-\w.\u00B7-\uFFFF]+$/),Bg=ae(/^aria-[\-\w]+$/),Kl=ae(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),Lg=ae(/^(?:\w+script|data):/i),Ag=ae(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),Ql=ae(/^html$/i),Mg=ae(/^[a-z][.\w]*(-[.\w]+)+$/i);var Po=Object.freeze({__proto__:null,ARIA_ATTR:Bg,ATTR_WHITESPACE:Ag,CUSTOM_ELEMENT:Mg,DATA_ATTR:_g,DOCTYPE_NAME:Ql,ERB_EXPR:Sg,IS_ALLOWED_URI:Kl,IS_SCRIPT_OR_DATA:Lg,MUSTACHE_EXPR:vg,TMPLIT_EXPR:Tg});const jr={element:1,attribute:2,text:3,cdataSection:4,entityReference:5,entityNode:6,progressingInstruction:7,comment:8,document:9,documentType:10,documentFragment:11,notation:12},Eg=function(){return typeof window>"u"?null:window},Fg=function(t,r){if(typeof t!="object"||typeof t.createPolicy!="function")return null;let i=null;const a="data-tt-policy-suffix";r&&r.hasAttribute(a)&&(i=r.getAttribute(a));const n="dompurify"+(i?"#"+i:"");try{return t.createPolicy(n,{createHTML(o){return o},createScriptURL(o){return o}})}catch{return console.warn("TrustedTypes policy "+n+" could not be created."),null}},No=function(){return{afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]}};function Jl(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:Eg();const t=K=>Jl(K);if(t.version="3.2.4",t.removed=[],!e||!e.document||e.document.nodeType!==jr.document||!e.Element)return t.isSupported=!1,t;let{document:r}=e;const i=r,a=i.currentScript,{DocumentFragment:n,HTMLTemplateElement:o,Node:s,Element:l,NodeFilter:c,NamedNodeMap:h=e.NamedNodeMap||e.MozNamedAttrMap,HTMLFormElement:u,DOMParser:f,trustedTypes:p}=e,g=l.prototype,m=Hr(g,"cloneNode"),y=Hr(g,"remove"),x=Hr(g,"nextSibling"),b=Hr(g,"childNodes"),C=Hr(g,"parentNode");if(typeof o=="function"){const K=r.createElement("template");K.content&&K.content.ownerDocument&&(r=K.content.ownerDocument)}let S,v="";const{implementation:M,createNodeIterator:T,createDocumentFragment:D,getElementsByTagName:P}=r,{importNode:$}=i;let L=No();t.isSupported=typeof Xl=="function"&&typeof C=="function"&&M&&M.createHTMLDocument!==void 0;const{MUSTACHE_EXPR:z,ERB_EXPR:R,TMPLIT_EXPR:E,DATA_ATTR:A,ARIA_ATTR:_,IS_SCRIPT_OR_DATA:F,ATTR_WHITESPACE:B,CUSTOM_ELEMENT:W}=Po;let{IS_ALLOWED_URI:U}=Po,J=null;const tt=ot({},[...Oo,...pn,...dn,...gn,...Do]);let et=null;const ht=ot({},[...Ro,...mn,...Io,...qi]);let at=Object.seal(Zl(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),St=null,Bt=null,se=!0,Qt=!0,bt=!1,oe=!0,Jt=!1,De=!0,Ue=!1,an=!1,nn=!1,hr=!1,Oi=!1,Di=!1,ho=!0,uo=!1;const xd="user-content-";let sn=!0,Ir=!1,ur={},fr=null;const fo=ot({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]);let po=null;const go=ot({},["audio","video","img","source","image","track"]);let on=null;const mo=ot({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),Ri="http://www.w3.org/1998/Math/MathML",Ii="http://www.w3.org/2000/svg",we="http://www.w3.org/1999/xhtml";let pr=we,ln=!1,cn=null;const bd=ot({},[Ri,Ii,we],fn);let Pi=ot({},["mi","mo","mn","ms","mtext"]),Ni=ot({},["annotation-xml"]);const Cd=ot({},["title","style","font","a","script"]);let Pr=null;const kd=["application/xhtml+xml","text/html"],wd="text/html";let Tt=null,dr=null;const vd=r.createElement("form"),yo=function(k){return k instanceof RegExp||k instanceof Function},hn=function(){let k=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};if(!(dr&&dr===k)){if((!k||typeof k!="object")&&(k={}),k=Ge(k),Pr=kd.indexOf(k.PARSER_MEDIA_TYPE)===-1?wd:k.PARSER_MEDIA_TYPE,Tt=Pr==="application/xhtml+xml"?fn:Ki,J=le(k,"ALLOWED_TAGS")?ot({},k.ALLOWED_TAGS,Tt):tt,et=le(k,"ALLOWED_ATTR")?ot({},k.ALLOWED_ATTR,Tt):ht,cn=le(k,"ALLOWED_NAMESPACES")?ot({},k.ALLOWED_NAMESPACES,fn):bd,on=le(k,"ADD_URI_SAFE_ATTR")?ot(Ge(mo),k.ADD_URI_SAFE_ATTR,Tt):mo,po=le(k,"ADD_DATA_URI_TAGS")?ot(Ge(go),k.ADD_DATA_URI_TAGS,Tt):go,fr=le(k,"FORBID_CONTENTS")?ot({},k.FORBID_CONTENTS,Tt):fo,St=le(k,"FORBID_TAGS")?ot({},k.FORBID_TAGS,Tt):{},Bt=le(k,"FORBID_ATTR")?ot({},k.FORBID_ATTR,Tt):{},ur=le(k,"USE_PROFILES")?k.USE_PROFILES:!1,se=k.ALLOW_ARIA_ATTR!==!1,Qt=k.ALLOW_DATA_ATTR!==!1,bt=k.ALLOW_UNKNOWN_PROTOCOLS||!1,oe=k.ALLOW_SELF_CLOSE_IN_ATTR!==!1,Jt=k.SAFE_FOR_TEMPLATES||!1,De=k.SAFE_FOR_XML!==!1,Ue=k.WHOLE_DOCUMENT||!1,hr=k.RETURN_DOM||!1,Oi=k.RETURN_DOM_FRAGMENT||!1,Di=k.RETURN_TRUSTED_TYPE||!1,nn=k.FORCE_BODY||!1,ho=k.SANITIZE_DOM!==!1,uo=k.SANITIZE_NAMED_PROPS||!1,sn=k.KEEP_CONTENT!==!1,Ir=k.IN_PLACE||!1,U=k.ALLOWED_URI_REGEXP||Kl,pr=k.NAMESPACE||we,Pi=k.MATHML_TEXT_INTEGRATION_POINTS||Pi,Ni=k.HTML_INTEGRATION_POINTS||Ni,at=k.CUSTOM_ELEMENT_HANDLING||{},k.CUSTOM_ELEMENT_HANDLING&&yo(k.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(at.tagNameCheck=k.CUSTOM_ELEMENT_HANDLING.tagNameCheck),k.CUSTOM_ELEMENT_HANDLING&&yo(k.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(at.attributeNameCheck=k.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),k.CUSTOM_ELEMENT_HANDLING&&typeof k.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements=="boolean"&&(at.allowCustomizedBuiltInElements=k.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),Jt&&(Qt=!1),Oi&&(hr=!0),ur&&(J=ot({},Do),et=[],ur.html===!0&&(ot(J,Oo),ot(et,Ro)),ur.svg===!0&&(ot(J,pn),ot(et,mn),ot(et,qi)),ur.svgFilters===!0&&(ot(J,dn),ot(et,mn),ot(et,qi)),ur.mathMl===!0&&(ot(J,gn),ot(et,Io),ot(et,qi))),k.ADD_TAGS&&(J===tt&&(J=Ge(J)),ot(J,k.ADD_TAGS,Tt)),k.ADD_ATTR&&(et===ht&&(et=Ge(et)),ot(et,k.ADD_ATTR,Tt)),k.ADD_URI_SAFE_ATTR&&ot(on,k.ADD_URI_SAFE_ATTR,Tt),k.FORBID_CONTENTS&&(fr===fo&&(fr=Ge(fr)),ot(fr,k.FORBID_CONTENTS,Tt)),sn&&(J["#text"]=!0),Ue&&ot(J,["html","head","body"]),J.table&&(ot(J,["tbody"]),delete St.tbody),k.TRUSTED_TYPES_POLICY){if(typeof k.TRUSTED_TYPES_POLICY.createHTML!="function")throw qr('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.');if(typeof k.TRUSTED_TYPES_POLICY.createScriptURL!="function")throw qr('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.');S=k.TRUSTED_TYPES_POLICY,v=S.createHTML("")}else S===void 0&&(S=Fg(p,a)),S!==null&&typeof v=="string"&&(v=S.createHTML(""));Ht&&Ht(k),dr=k}},xo=ot({},[...pn,...dn,...kg]),bo=ot({},[...gn,...wg]),Sd=function(k){let I=C(k);(!I||!I.tagName)&&(I={namespaceURI:pr,tagName:"template"});const Y=Ki(k.tagName),yt=Ki(I.tagName);return cn[k.namespaceURI]?k.namespaceURI===Ii?I.namespaceURI===we?Y==="svg":I.namespaceURI===Ri?Y==="svg"&&(yt==="annotation-xml"||Pi[yt]):!!xo[Y]:k.namespaceURI===Ri?I.namespaceURI===we?Y==="math":I.namespaceURI===Ii?Y==="math"&&Ni[yt]:!!bo[Y]:k.namespaceURI===we?I.namespaceURI===Ii&&!Ni[yt]||I.namespaceURI===Ri&&!Pi[yt]?!1:!bo[Y]&&(Cd[Y]||!xo[Y]):!!(Pr==="application/xhtml+xml"&&cn[k.namespaceURI]):!1},fe=function(k){zr(t.removed,{element:k});try{C(k).removeChild(k)}catch{y(k)}},zi=function(k,I){try{zr(t.removed,{attribute:I.getAttributeNode(k),from:I})}catch{zr(t.removed,{attribute:null,from:I})}if(I.removeAttribute(k),k==="is")if(hr||Oi)try{fe(I)}catch{}else try{I.setAttribute(k,"")}catch{}},Co=function(k){let I=null,Y=null;if(nn)k="<remove></remove>"+k;else{const Lt=$o(k,/^[\r\n\t ]+/);Y=Lt&&Lt[0]}Pr==="application/xhtml+xml"&&pr===we&&(k='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+k+"</body></html>");const yt=S?S.createHTML(k):k;if(pr===we)try{I=new f().parseFromString(yt,Pr)}catch{}if(!I||!I.documentElement){I=M.createDocument(pr,"template",null);try{I.documentElement.innerHTML=ln?v:yt}catch{}}const Ft=I.body||I.documentElement;return k&&Y&&Ft.insertBefore(r.createTextNode(Y),Ft.childNodes[0]||null),pr===we?P.call(I,Ue?"html":"body")[0]:Ue?I.documentElement:Ft},ko=function(k){return T.call(k.ownerDocument||k,k,c.SHOW_ELEMENT|c.SHOW_COMMENT|c.SHOW_TEXT|c.SHOW_PROCESSING_INSTRUCTION|c.SHOW_CDATA_SECTION,null)},un=function(k){return k instanceof u&&(typeof k.nodeName!="string"||typeof k.textContent!="string"||typeof k.removeChild!="function"||!(k.attributes instanceof h)||typeof k.removeAttribute!="function"||typeof k.setAttribute!="function"||typeof k.namespaceURI!="string"||typeof k.insertBefore!="function"||typeof k.hasChildNodes!="function")},wo=function(k){return typeof s=="function"&&k instanceof s};function ve(K,k,I){Wi(K,Y=>{Y.call(t,k,I,dr)})}const vo=function(k){let I=null;if(ve(L.beforeSanitizeElements,k,null),un(k))return fe(k),!0;const Y=Tt(k.nodeName);if(ve(L.uponSanitizeElement,k,{tagName:Y,allowedTags:J}),k.hasChildNodes()&&!wo(k.firstElementChild)&&Pt(/<[/\w]/g,k.innerHTML)&&Pt(/<[/\w]/g,k.textContent)||k.nodeType===jr.progressingInstruction||De&&k.nodeType===jr.comment&&Pt(/<[/\w]/g,k.data))return fe(k),!0;if(!J[Y]||St[Y]){if(!St[Y]&&To(Y)&&(at.tagNameCheck instanceof RegExp&&Pt(at.tagNameCheck,Y)||at.tagNameCheck instanceof Function&&at.tagNameCheck(Y)))return!1;if(sn&&!fr[Y]){const yt=C(k)||k.parentNode,Ft=b(k)||k.childNodes;if(Ft&&yt){const Lt=Ft.length;for(let Ut=Lt-1;Ut>=0;--Ut){const pe=m(Ft[Ut],!0);pe.__removalCount=(k.__removalCount||0)+1,yt.insertBefore(pe,x(k))}}}return fe(k),!0}return k instanceof l&&!Sd(k)||(Y==="noscript"||Y==="noembed"||Y==="noframes")&&Pt(/<\/no(script|embed|frames)/i,k.innerHTML)?(fe(k),!0):(Jt&&k.nodeType===jr.text&&(I=k.textContent,Wi([z,R,E],yt=>{I=Wr(I,yt," ")}),k.textContent!==I&&(zr(t.removed,{element:k.cloneNode()}),k.textContent=I)),ve(L.afterSanitizeElements,k,null),!1)},So=function(k,I,Y){if(ho&&(I==="id"||I==="name")&&(Y in r||Y in vd))return!1;if(!(Qt&&!Bt[I]&&Pt(A,I))){if(!(se&&Pt(_,I))){if(!et[I]||Bt[I]){if(!(To(k)&&(at.tagNameCheck instanceof RegExp&&Pt(at.tagNameCheck,k)||at.tagNameCheck instanceof Function&&at.tagNameCheck(k))&&(at.attributeNameCheck instanceof RegExp&&Pt(at.attributeNameCheck,I)||at.attributeNameCheck instanceof Function&&at.attributeNameCheck(I))||I==="is"&&at.allowCustomizedBuiltInElements&&(at.tagNameCheck instanceof RegExp&&Pt(at.tagNameCheck,Y)||at.tagNameCheck instanceof Function&&at.tagNameCheck(Y))))return!1}else if(!on[I]){if(!Pt(U,Wr(Y,B,""))){if(!((I==="src"||I==="xlink:href"||I==="href")&&k!=="script"&&yg(Y,"data:")===0&&po[k])){if(!(bt&&!Pt(F,Wr(Y,B,"")))){if(Y)return!1}}}}}}return!0},To=function(k){return k!=="annotation-xml"&&$o(k,W)},_o=function(k){ve(L.beforeSanitizeAttributes,k,null);const{attributes:I}=k;if(!I||un(k))return;const Y={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:et,forceKeepAttr:void 0};let yt=I.length;for(;yt--;){const Ft=I[yt],{name:Lt,namespaceURI:Ut,value:pe}=Ft,Nr=Tt(Lt);let It=Lt==="value"?pe:xg(pe);if(Y.attrName=Nr,Y.attrValue=It,Y.keepAttr=!0,Y.forceKeepAttr=void 0,ve(L.uponSanitizeAttribute,k,Y),It=Y.attrValue,uo&&(Nr==="id"||Nr==="name")&&(zi(Lt,k),It=xd+It),De&&Pt(/((--!?|])>)|<\/(style|title)/i,It)){zi(Lt,k);continue}if(Y.forceKeepAttr||(zi(Lt,k),!Y.keepAttr))continue;if(!oe&&Pt(/\/>/i,It)){zi(Lt,k);continue}Jt&&Wi([z,R,E],Lo=>{It=Wr(It,Lo," ")});const Bo=Tt(k.nodeName);if(So(Bo,Nr,It)){if(S&&typeof p=="object"&&typeof p.getAttributeType=="function"&&!Ut)switch(p.getAttributeType(Bo,Nr)){case"TrustedHTML":{It=S.createHTML(It);break}case"TrustedScriptURL":{It=S.createScriptURL(It);break}}try{Ut?k.setAttributeNS(Ut,Lt,It):k.setAttribute(Lt,It),un(k)?fe(k):Fo(t.removed)}catch{}}}ve(L.afterSanitizeAttributes,k,null)},Td=function K(k){let I=null;const Y=ko(k);for(ve(L.beforeSanitizeShadowDOM,k,null);I=Y.nextNode();)ve(L.uponSanitizeShadowNode,I,null),vo(I),_o(I),I.content instanceof n&&K(I.content);ve(L.afterSanitizeShadowDOM,k,null)};return t.sanitize=function(K){let k=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},I=null,Y=null,yt=null,Ft=null;if(ln=!K,ln&&(K="<!-->"),typeof K!="string"&&!wo(K))if(typeof K.toString=="function"){if(K=K.toString(),typeof K!="string")throw qr("dirty is not a string, aborting")}else throw qr("toString is not a function");if(!t.isSupported)return K;if(an||hn(k),t.removed=[],typeof K=="string"&&(Ir=!1),Ir){if(K.nodeName){const pe=Tt(K.nodeName);if(!J[pe]||St[pe])throw qr("root node is forbidden and cannot be sanitized in-place")}}else if(K instanceof s)I=Co("<!---->"),Y=I.ownerDocument.importNode(K,!0),Y.nodeType===jr.element&&Y.nodeName==="BODY"||Y.nodeName==="HTML"?I=Y:I.appendChild(Y);else{if(!hr&&!Jt&&!Ue&&K.indexOf("<")===-1)return S&&Di?S.createHTML(K):K;if(I=Co(K),!I)return hr?null:Di?v:""}I&&nn&&fe(I.firstChild);const Lt=ko(Ir?K:I);for(;yt=Lt.nextNode();)vo(yt),_o(yt),yt.content instanceof n&&Td(yt.content);if(Ir)return K;if(hr){if(Oi)for(Ft=D.call(I.ownerDocument);I.firstChild;)Ft.appendChild(I.firstChild);else Ft=I;return(et.shadowroot||et.shadowrootmode)&&(Ft=$.call(i,Ft,!0)),Ft}let Ut=Ue?I.outerHTML:I.innerHTML;return Ue&&J["!doctype"]&&I.ownerDocument&&I.ownerDocument.doctype&&I.ownerDocument.doctype.name&&Pt(Ql,I.ownerDocument.doctype.name)&&(Ut="<!DOCTYPE "+I.ownerDocument.doctype.name+`>
`+Ut),Jt&&Wi([z,R,E],pe=>{Ut=Wr(Ut,pe," ")}),S&&Di?S.createHTML(Ut):Ut},t.setConfig=function(){let K=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};hn(K),an=!0},t.clearConfig=function(){dr=null,an=!1},t.isValidAttribute=function(K,k,I){dr||hn({});const Y=Tt(K),yt=Tt(k);return So(Y,yt,I)},t.addHook=function(K,k){typeof k=="function"&&zr(L[K],k)},t.removeHook=function(K,k){if(k!==void 0){const I=gg(L[K],k);return I===-1?void 0:mg(L[K],I,1)[0]}return Fo(L[K])},t.removeHooks=function(K){L[K]=[]},t.removeAllHooks=function(){L=No()},t}var kr=Jl(),tc=Object.defineProperty,d=(e,t)=>tc(e,"name",{value:t,configurable:!0}),$g=(e,t)=>{for(var r in t)tc(e,r,{get:t[r],enumerable:!0})},Se={trace:0,debug:1,info:2,warn:3,error:4,fatal:5},O={trace:d((...e)=>{},"trace"),debug:d((...e)=>{},"debug"),info:d((...e)=>{},"info"),warn:d((...e)=>{},"warn"),error:d((...e)=>{},"error"),fatal:d((...e)=>{},"fatal")},ys=d(function(e="fatal"){let t=Se.fatal;typeof e=="string"?e.toLowerCase()in Se&&(t=Se[e]):typeof e=="number"&&(t=e),O.trace=()=>{},O.debug=()=>{},O.info=()=>{},O.warn=()=>{},O.error=()=>{},O.fatal=()=>{},t<=Se.fatal&&(O.fatal=console.error?console.error.bind(console,te("FATAL"),"color: orange"):console.log.bind(console,"\x1B[35m",te("FATAL"))),t<=Se.error&&(O.error=console.error?console.error.bind(console,te("ERROR"),"color: orange"):console.log.bind(console,"\x1B[31m",te("ERROR"))),t<=Se.warn&&(O.warn=console.warn?console.warn.bind(console,te("WARN"),"color: orange"):console.log.bind(console,"\x1B[33m",te("WARN"))),t<=Se.info&&(O.info=console.info?console.info.bind(console,te("INFO"),"color: lightblue"):console.log.bind(console,"\x1B[34m",te("INFO"))),t<=Se.debug&&(O.debug=console.debug?console.debug.bind(console,te("DEBUG"),"color: lightgreen"):console.log.bind(console,"\x1B[32m",te("DEBUG"))),t<=Se.trace&&(O.trace=console.debug?console.debug.bind(console,te("TRACE"),"color: lightgreen"):console.log.bind(console,"\x1B[32m",te("TRACE")))},"setLogLevel"),te=d(e=>`%c${ag().format("ss.SSS")} : ${e} : `,"format"),ec=/^-{3}\s*[\n\r](.*?)[\n\r]-{3}\s*[\n\r]+/s,ai=/%{2}{\s*(?:(\w+)\s*:|(\w+))\s*(?:(\w+)|((?:(?!}%{2}).|\r?\n)*))?\s*(?:}%{2})?/gi,Og=/\s*%%.*\n/gm,xi,rc=(xi=class extends Error{constructor(t){super(t),this.name="UnknownDiagramError"}},d(xi,"UnknownDiagramError"),xi),wr={},xs=d(function(e,t){e=e.replace(ec,"").replace(ai,"").replace(Og,`
`);for(const[r,{detector:i}]of Object.entries(wr))if(i(e,t))return r;throw new rc(`No diagram type detected matching given configuration for text: ${e}`)},"detectType"),ic=d((...e)=>{for(const{id:t,detector:r,loader:i}of e)ac(t,r,i)},"registerLazyLoadedDiagrams"),ac=d((e,t,r)=>{wr[e]&&O.warn(`Detector with key ${e} already exists. Overwriting.`),wr[e]={detector:t,loader:r},O.debug(`Detector with key ${e} added${r?" with loader":""}`)},"addDetector"),Dg=d(e=>wr[e].loader,"getDiagramLoader"),Fn=d((e,t,{depth:r=2,clobber:i=!1}={})=>{const a={depth:r,clobber:i};return Array.isArray(t)&&!Array.isArray(e)?(t.forEach(n=>Fn(e,n,a)),e):Array.isArray(t)&&Array.isArray(e)?(t.forEach(n=>{e.includes(n)||e.push(n)}),e):e===void 0||r<=0?e!=null&&typeof e=="object"&&typeof t=="object"?Object.assign(e,t):t:(t!==void 0&&typeof e=="object"&&typeof t=="object"&&Object.keys(t).forEach(n=>{typeof t[n]=="object"&&(e[n]===void 0||typeof e[n]=="object")?(e[n]===void 0&&(e[n]=Array.isArray(t[n])?[]:{}),e[n]=Fn(e[n],t[n],{depth:r-1,clobber:i})):(i||typeof e[n]!="object"&&typeof t[n]!="object")&&(e[n]=t[n])}),e)},"assignWithDepth"),Et=Fn,Da="#ffffff",Ra="#f2f2f2",Nt=d((e,t)=>t?w(e,{s:-40,l:10}):w(e,{s:-40,l:-10}),"mkBorder"),bi,Rg=(bi=class{constructor(){this.background="#f4f4f4",this.primaryColor="#fff4dd",this.noteBkgColor="#fff5ad",this.noteTextColor="#333",this.THEME_COLOR_LIMIT=12,this.fontFamily='"trebuchet ms", verdana, arial, sans-serif',this.fontSize="16px"}updateColors(){var r,i,a,n,o,s,l,c,h,u,f;if(this.primaryTextColor=this.primaryTextColor||(this.darkMode?"#eee":"#333"),this.secondaryColor=this.secondaryColor||w(this.primaryColor,{h:-120}),this.tertiaryColor=this.tertiaryColor||w(this.primaryColor,{h:180,l:5}),this.primaryBorderColor=this.primaryBorderColor||Nt(this.primaryColor,this.darkMode),this.secondaryBorderColor=this.secondaryBorderColor||Nt(this.secondaryColor,this.darkMode),this.tertiaryBorderColor=this.tertiaryBorderColor||Nt(this.tertiaryColor,this.darkMode),this.noteBorderColor=this.noteBorderColor||Nt(this.noteBkgColor,this.darkMode),this.noteBkgColor=this.noteBkgColor||"#fff5ad",this.noteTextColor=this.noteTextColor||"#333",this.secondaryTextColor=this.secondaryTextColor||N(this.secondaryColor),this.tertiaryTextColor=this.tertiaryTextColor||N(this.tertiaryColor),this.lineColor=this.lineColor||N(this.background),this.arrowheadColor=this.arrowheadColor||N(this.background),this.textColor=this.textColor||this.primaryTextColor,this.border2=this.border2||this.tertiaryBorderColor,this.nodeBkg=this.nodeBkg||this.primaryColor,this.mainBkg=this.mainBkg||this.primaryColor,this.nodeBorder=this.nodeBorder||this.primaryBorderColor,this.clusterBkg=this.clusterBkg||this.tertiaryColor,this.clusterBorder=this.clusterBorder||this.tertiaryBorderColor,this.defaultLinkColor=this.defaultLinkColor||this.lineColor,this.titleColor=this.titleColor||this.tertiaryTextColor,this.edgeLabelBackground=this.edgeLabelBackground||(this.darkMode?Q(this.secondaryColor,30):this.secondaryColor),this.nodeTextColor=this.nodeTextColor||this.primaryTextColor,this.actorBorder=this.actorBorder||this.primaryBorderColor,this.actorBkg=this.actorBkg||this.mainBkg,this.actorTextColor=this.actorTextColor||this.primaryTextColor,this.actorLineColor=this.actorLineColor||this.actorBorder,this.labelBoxBkgColor=this.labelBoxBkgColor||this.actorBkg,this.signalColor=this.signalColor||this.textColor,this.signalTextColor=this.signalTextColor||this.textColor,this.labelBoxBorderColor=this.labelBoxBorderColor||this.actorBorder,this.labelTextColor=this.labelTextColor||this.actorTextColor,this.loopTextColor=this.loopTextColor||this.actorTextColor,this.activationBorderColor=this.activationBorderColor||Q(this.secondaryColor,10),this.activationBkgColor=this.activationBkgColor||this.secondaryColor,this.sequenceNumberColor=this.sequenceNumberColor||N(this.lineColor),this.sectionBkgColor=this.sectionBkgColor||this.tertiaryColor,this.altSectionBkgColor=this.altSectionBkgColor||"white",this.sectionBkgColor=this.sectionBkgColor||this.secondaryColor,this.sectionBkgColor2=this.sectionBkgColor2||this.primaryColor,this.excludeBkgColor=this.excludeBkgColor||"#eeeeee",this.taskBorderColor=this.taskBorderColor||this.primaryBorderColor,this.taskBkgColor=this.taskBkgColor||this.primaryColor,this.activeTaskBorderColor=this.activeTaskBorderColor||this.primaryColor,this.activeTaskBkgColor=this.activeTaskBkgColor||q(this.primaryColor,23),this.gridColor=this.gridColor||"lightgrey",this.doneTaskBkgColor=this.doneTaskBkgColor||"lightgrey",this.doneTaskBorderColor=this.doneTaskBorderColor||"grey",this.critBorderColor=this.critBorderColor||"#ff8888",this.critBkgColor=this.critBkgColor||"red",this.todayLineColor=this.todayLineColor||"red",this.taskTextColor=this.taskTextColor||this.textColor,this.taskTextOutsideColor=this.taskTextOutsideColor||this.textColor,this.taskTextLightColor=this.taskTextLightColor||this.textColor,this.taskTextColor=this.taskTextColor||this.primaryTextColor,this.taskTextDarkColor=this.taskTextDarkColor||this.textColor,this.taskTextClickableColor=this.taskTextClickableColor||"#003163",this.personBorder=this.personBorder||this.primaryBorderColor,this.personBkg=this.personBkg||this.mainBkg,this.darkMode?(this.rowOdd=this.rowOdd||Q(this.mainBkg,5)||"#ffffff",this.rowEven=this.rowEven||Q(this.mainBkg,10)):(this.rowOdd=this.rowOdd||q(this.mainBkg,75)||"#ffffff",this.rowEven=this.rowEven||q(this.mainBkg,5)),this.transitionColor=this.transitionColor||this.lineColor,this.transitionLabelColor=this.transitionLabelColor||this.textColor,this.stateLabelColor=this.stateLabelColor||this.stateBkg||this.primaryTextColor,this.stateBkg=this.stateBkg||this.mainBkg,this.labelBackgroundColor=this.labelBackgroundColor||this.stateBkg,this.compositeBackground=this.compositeBackground||this.background||this.tertiaryColor,this.altBackground=this.altBackground||this.tertiaryColor,this.compositeTitleBackground=this.compositeTitleBackground||this.mainBkg,this.compositeBorder=this.compositeBorder||this.nodeBorder,this.innerEndBackground=this.nodeBorder,this.errorBkgColor=this.errorBkgColor||this.tertiaryColor,this.errorTextColor=this.errorTextColor||this.tertiaryTextColor,this.transitionColor=this.transitionColor||this.lineColor,this.specialStateColor=this.lineColor,this.cScale0=this.cScale0||this.primaryColor,this.cScale1=this.cScale1||this.secondaryColor,this.cScale2=this.cScale2||this.tertiaryColor,this.cScale3=this.cScale3||w(this.primaryColor,{h:30}),this.cScale4=this.cScale4||w(this.primaryColor,{h:60}),this.cScale5=this.cScale5||w(this.primaryColor,{h:90}),this.cScale6=this.cScale6||w(this.primaryColor,{h:120}),this.cScale7=this.cScale7||w(this.primaryColor,{h:150}),this.cScale8=this.cScale8||w(this.primaryColor,{h:210,l:150}),this.cScale9=this.cScale9||w(this.primaryColor,{h:270}),this.cScale10=this.cScale10||w(this.primaryColor,{h:300}),this.cScale11=this.cScale11||w(this.primaryColor,{h:330}),this.darkMode)for(let p=0;p<this.THEME_COLOR_LIMIT;p++)this["cScale"+p]=Q(this["cScale"+p],75);else for(let p=0;p<this.THEME_COLOR_LIMIT;p++)this["cScale"+p]=Q(this["cScale"+p],25);for(let p=0;p<this.THEME_COLOR_LIMIT;p++)this["cScaleInv"+p]=this["cScaleInv"+p]||N(this["cScale"+p]);for(let p=0;p<this.THEME_COLOR_LIMIT;p++)this.darkMode?this["cScalePeer"+p]=this["cScalePeer"+p]||q(this["cScale"+p],10):this["cScalePeer"+p]=this["cScalePeer"+p]||Q(this["cScale"+p],10);this.scaleLabelColor=this.scaleLabelColor||this.labelTextColor;for(let p=0;p<this.THEME_COLOR_LIMIT;p++)this["cScaleLabel"+p]=this["cScaleLabel"+p]||this.scaleLabelColor;const t=this.darkMode?-4:-1;for(let p=0;p<5;p++)this["surface"+p]=this["surface"+p]||w(this.mainBkg,{h:180,s:-15,l:t*(5+p*3)}),this["surfacePeer"+p]=this["surfacePeer"+p]||w(this.mainBkg,{h:180,s:-15,l:t*(8+p*3)});this.classText=this.classText||this.textColor,this.fillType0=this.fillType0||this.primaryColor,this.fillType1=this.fillType1||this.secondaryColor,this.fillType2=this.fillType2||w(this.primaryColor,{h:64}),this.fillType3=this.fillType3||w(this.secondaryColor,{h:64}),this.fillType4=this.fillType4||w(this.primaryColor,{h:-64}),this.fillType5=this.fillType5||w(this.secondaryColor,{h:-64}),this.fillType6=this.fillType6||w(this.primaryColor,{h:128}),this.fillType7=this.fillType7||w(this.secondaryColor,{h:128}),this.pie1=this.pie1||this.primaryColor,this.pie2=this.pie2||this.secondaryColor,this.pie3=this.pie3||this.tertiaryColor,this.pie4=this.pie4||w(this.primaryColor,{l:-10}),this.pie5=this.pie5||w(this.secondaryColor,{l:-10}),this.pie6=this.pie6||w(this.tertiaryColor,{l:-10}),this.pie7=this.pie7||w(this.primaryColor,{h:60,l:-10}),this.pie8=this.pie8||w(this.primaryColor,{h:-60,l:-10}),this.pie9=this.pie9||w(this.primaryColor,{h:120,l:0}),this.pie10=this.pie10||w(this.primaryColor,{h:60,l:-20}),this.pie11=this.pie11||w(this.primaryColor,{h:-60,l:-20}),this.pie12=this.pie12||w(this.primaryColor,{h:120,l:-10}),this.pieTitleTextSize=this.pieTitleTextSize||"25px",this.pieTitleTextColor=this.pieTitleTextColor||this.taskTextDarkColor,this.pieSectionTextSize=this.pieSectionTextSize||"17px",this.pieSectionTextColor=this.pieSectionTextColor||this.textColor,this.pieLegendTextSize=this.pieLegendTextSize||"17px",this.pieLegendTextColor=this.pieLegendTextColor||this.taskTextDarkColor,this.pieStrokeColor=this.pieStrokeColor||"black",this.pieStrokeWidth=this.pieStrokeWidth||"2px",this.pieOuterStrokeWidth=this.pieOuterStrokeWidth||"2px",this.pieOuterStrokeColor=this.pieOuterStrokeColor||"black",this.pieOpacity=this.pieOpacity||"0.7",this.archEdgeColor=this.archEdgeColor||"#777",this.archEdgeArrowColor=this.archEdgeArrowColor||"#777",this.archEdgeWidth=this.archEdgeWidth||"3",this.archGroupBorderColor=this.archGroupBorderColor||"#000",this.archGroupBorderWidth=this.archGroupBorderWidth||"2px",this.quadrant1Fill=this.quadrant1Fill||this.primaryColor,this.quadrant2Fill=this.quadrant2Fill||w(this.primaryColor,{r:5,g:5,b:5}),this.quadrant3Fill=this.quadrant3Fill||w(this.primaryColor,{r:10,g:10,b:10}),this.quadrant4Fill=this.quadrant4Fill||w(this.primaryColor,{r:15,g:15,b:15}),this.quadrant1TextFill=this.quadrant1TextFill||this.primaryTextColor,this.quadrant2TextFill=this.quadrant2TextFill||w(this.primaryTextColor,{r:-5,g:-5,b:-5}),this.quadrant3TextFill=this.quadrant3TextFill||w(this.primaryTextColor,{r:-10,g:-10,b:-10}),this.quadrant4TextFill=this.quadrant4TextFill||w(this.primaryTextColor,{r:-15,g:-15,b:-15}),this.quadrantPointFill=this.quadrantPointFill||_i(this.quadrant1Fill)?q(this.quadrant1Fill):Q(this.quadrant1Fill),this.quadrantPointTextFill=this.quadrantPointTextFill||this.primaryTextColor,this.quadrantXAxisTextFill=this.quadrantXAxisTextFill||this.primaryTextColor,this.quadrantYAxisTextFill=this.quadrantYAxisTextFill||this.primaryTextColor,this.quadrantInternalBorderStrokeFill=this.quadrantInternalBorderStrokeFill||this.primaryBorderColor,this.quadrantExternalBorderStrokeFill=this.quadrantExternalBorderStrokeFill||this.primaryBorderColor,this.quadrantTitleFill=this.quadrantTitleFill||this.primaryTextColor,this.xyChart={backgroundColor:((r=this.xyChart)==null?void 0:r.backgroundColor)||this.background,titleColor:((i=this.xyChart)==null?void 0:i.titleColor)||this.primaryTextColor,xAxisTitleColor:((a=this.xyChart)==null?void 0:a.xAxisTitleColor)||this.primaryTextColor,xAxisLabelColor:((n=this.xyChart)==null?void 0:n.xAxisLabelColor)||this.primaryTextColor,xAxisTickColor:((o=this.xyChart)==null?void 0:o.xAxisTickColor)||this.primaryTextColor,xAxisLineColor:((s=this.xyChart)==null?void 0:s.xAxisLineColor)||this.primaryTextColor,yAxisTitleColor:((l=this.xyChart)==null?void 0:l.yAxisTitleColor)||this.primaryTextColor,yAxisLabelColor:((c=this.xyChart)==null?void 0:c.yAxisLabelColor)||this.primaryTextColor,yAxisTickColor:((h=this.xyChart)==null?void 0:h.yAxisTickColor)||this.primaryTextColor,yAxisLineColor:((u=this.xyChart)==null?void 0:u.yAxisLineColor)||this.primaryTextColor,plotColorPalette:((f=this.xyChart)==null?void 0:f.plotColorPalette)||"#FFF4DD,#FFD8B1,#FFA07A,#ECEFF1,#D6DBDF,#C3E0A8,#FFB6A4,#FFD74D,#738FA7,#FFFFF0"},this.requirementBackground=this.requirementBackground||this.primaryColor,this.requirementBorderColor=this.requirementBorderColor||this.primaryBorderColor,this.requirementBorderSize=this.requirementBorderSize||"1",this.requirementTextColor=this.requirementTextColor||this.primaryTextColor,this.relationColor=this.relationColor||this.lineColor,this.relationLabelBackground=this.relationLabelBackground||(this.darkMode?Q(this.secondaryColor,30):this.secondaryColor),this.relationLabelColor=this.relationLabelColor||this.actorTextColor,this.git0=this.git0||this.primaryColor,this.git1=this.git1||this.secondaryColor,this.git2=this.git2||this.tertiaryColor,this.git3=this.git3||w(this.primaryColor,{h:-30}),this.git4=this.git4||w(this.primaryColor,{h:-60}),this.git5=this.git5||w(this.primaryColor,{h:-90}),this.git6=this.git6||w(this.primaryColor,{h:60}),this.git7=this.git7||w(this.primaryColor,{h:120}),this.darkMode?(this.git0=q(this.git0,25),this.git1=q(this.git1,25),this.git2=q(this.git2,25),this.git3=q(this.git3,25),this.git4=q(this.git4,25),this.git5=q(this.git5,25),this.git6=q(this.git6,25),this.git7=q(this.git7,25)):(this.git0=Q(this.git0,25),this.git1=Q(this.git1,25),this.git2=Q(this.git2,25),this.git3=Q(this.git3,25),this.git4=Q(this.git4,25),this.git5=Q(this.git5,25),this.git6=Q(this.git6,25),this.git7=Q(this.git7,25)),this.gitInv0=this.gitInv0||N(this.git0),this.gitInv1=this.gitInv1||N(this.git1),this.gitInv2=this.gitInv2||N(this.git2),this.gitInv3=this.gitInv3||N(this.git3),this.gitInv4=this.gitInv4||N(this.git4),this.gitInv5=this.gitInv5||N(this.git5),this.gitInv6=this.gitInv6||N(this.git6),this.gitInv7=this.gitInv7||N(this.git7),this.branchLabelColor=this.branchLabelColor||(this.darkMode?"black":this.labelTextColor),this.gitBranchLabel0=this.gitBranchLabel0||this.branchLabelColor,this.gitBranchLabel1=this.gitBranchLabel1||this.branchLabelColor,this.gitBranchLabel2=this.gitBranchLabel2||this.branchLabelColor,this.gitBranchLabel3=this.gitBranchLabel3||this.branchLabelColor,this.gitBranchLabel4=this.gitBranchLabel4||this.branchLabelColor,this.gitBranchLabel5=this.gitBranchLabel5||this.branchLabelColor,this.gitBranchLabel6=this.gitBranchLabel6||this.branchLabelColor,this.gitBranchLabel7=this.gitBranchLabel7||this.branchLabelColor,this.tagLabelColor=this.tagLabelColor||this.primaryTextColor,this.tagLabelBackground=this.tagLabelBackground||this.primaryColor,this.tagLabelBorder=this.tagBorder||this.primaryBorderColor,this.tagLabelFontSize=this.tagLabelFontSize||"10px",this.commitLabelColor=this.commitLabelColor||this.secondaryTextColor,this.commitLabelBackground=this.commitLabelBackground||this.secondaryColor,this.commitLabelFontSize=this.commitLabelFontSize||"10px",this.attributeBackgroundColorOdd=this.attributeBackgroundColorOdd||Da,this.attributeBackgroundColorEven=this.attributeBackgroundColorEven||Ra}calculate(t){if(typeof t!="object"){this.updateColors();return}const r=Object.keys(t);r.forEach(i=>{this[i]=t[i]}),this.updateColors(),r.forEach(i=>{this[i]=t[i]})}},d(bi,"Theme"),bi),Ig=d(e=>{const t=new Rg;return t.calculate(e),t},"getThemeVariables"),Ci,Pg=(Ci=class{constructor(){this.background="#333",this.primaryColor="#1f2020",this.secondaryColor=q(this.primaryColor,16),this.tertiaryColor=w(this.primaryColor,{h:-160}),this.primaryBorderColor=N(this.background),this.secondaryBorderColor=Nt(this.secondaryColor,this.darkMode),this.tertiaryBorderColor=Nt(this.tertiaryColor,this.darkMode),this.primaryTextColor=N(this.primaryColor),this.secondaryTextColor=N(this.secondaryColor),this.tertiaryTextColor=N(this.tertiaryColor),this.lineColor=N(this.background),this.textColor=N(this.background),this.mainBkg="#1f2020",this.secondBkg="calculated",this.mainContrastColor="lightgrey",this.darkTextColor=q(N("#323D47"),10),this.lineColor="calculated",this.border1="#ccc",this.border2=ii(255,255,255,.25),this.arrowheadColor="calculated",this.fontFamily='"trebuchet ms", verdana, arial, sans-serif',this.fontSize="16px",this.labelBackground="#181818",this.textColor="#ccc",this.THEME_COLOR_LIMIT=12,this.nodeBkg="calculated",this.nodeBorder="calculated",this.clusterBkg="calculated",this.clusterBorder="calculated",this.defaultLinkColor="calculated",this.titleColor="#F9FFFE",this.edgeLabelBackground="calculated",this.actorBorder="calculated",this.actorBkg="calculated",this.actorTextColor="calculated",this.actorLineColor="calculated",this.signalColor="calculated",this.signalTextColor="calculated",this.labelBoxBkgColor="calculated",this.labelBoxBorderColor="calculated",this.labelTextColor="calculated",this.loopTextColor="calculated",this.noteBorderColor="calculated",this.noteBkgColor="#fff5ad",this.noteTextColor="calculated",this.activationBorderColor="calculated",this.activationBkgColor="calculated",this.sequenceNumberColor="black",this.sectionBkgColor=Q("#EAE8D9",30),this.altSectionBkgColor="calculated",this.sectionBkgColor2="#EAE8D9",this.excludeBkgColor=Q(this.sectionBkgColor,10),this.taskBorderColor=ii(255,255,255,70),this.taskBkgColor="calculated",this.taskTextColor="calculated",this.taskTextLightColor="calculated",this.taskTextOutsideColor="calculated",this.taskTextClickableColor="#003163",this.activeTaskBorderColor=ii(255,255,255,50),this.activeTaskBkgColor="#81B1DB",this.gridColor="calculated",this.doneTaskBkgColor="calculated",this.doneTaskBorderColor="grey",this.critBorderColor="#E83737",this.critBkgColor="#E83737",this.taskTextDarkColor="calculated",this.todayLineColor="#DB5757",this.personBorder=this.primaryBorderColor,this.personBkg=this.mainBkg,this.archEdgeColor="calculated",this.archEdgeArrowColor="calculated",this.archEdgeWidth="3",this.archGroupBorderColor=this.primaryBorderColor,this.archGroupBorderWidth="2px",this.rowOdd=this.rowOdd||q(this.mainBkg,5)||"#ffffff",this.rowEven=this.rowEven||Q(this.mainBkg,10),this.labelColor="calculated",this.errorBkgColor="#a44141",this.errorTextColor="#ddd"}updateColors(){var t,r,i,a,n,o,s,l,c,h,u;this.secondBkg=q(this.mainBkg,16),this.lineColor=this.mainContrastColor,this.arrowheadColor=this.mainContrastColor,this.nodeBkg=this.mainBkg,this.nodeBorder=this.border1,this.clusterBkg=this.secondBkg,this.clusterBorder=this.border2,this.defaultLinkColor=this.lineColor,this.edgeLabelBackground=q(this.labelBackground,25),this.actorBorder=this.border1,this.actorBkg=this.mainBkg,this.actorTextColor=this.mainContrastColor,this.actorLineColor=this.actorBorder,this.signalColor=this.mainContrastColor,this.signalTextColor=this.mainContrastColor,this.labelBoxBkgColor=this.actorBkg,this.labelBoxBorderColor=this.actorBorder,this.labelTextColor=this.mainContrastColor,this.loopTextColor=this.mainContrastColor,this.noteBorderColor=this.secondaryBorderColor,this.noteBkgColor=this.secondBkg,this.noteTextColor=this.secondaryTextColor,this.activationBorderColor=this.border1,this.activationBkgColor=this.secondBkg,this.altSectionBkgColor=this.background,this.taskBkgColor=q(this.mainBkg,23),this.taskTextColor=this.darkTextColor,this.taskTextLightColor=this.mainContrastColor,this.taskTextOutsideColor=this.taskTextLightColor,this.gridColor=this.mainContrastColor,this.doneTaskBkgColor=this.mainContrastColor,this.taskTextDarkColor=this.darkTextColor,this.archEdgeColor=this.lineColor,this.archEdgeArrowColor=this.lineColor,this.transitionColor=this.transitionColor||this.lineColor,this.transitionLabelColor=this.transitionLabelColor||this.textColor,this.stateLabelColor=this.stateLabelColor||this.stateBkg||this.primaryTextColor,this.stateBkg=this.stateBkg||this.mainBkg,this.labelBackgroundColor=this.labelBackgroundColor||this.stateBkg,this.compositeBackground=this.compositeBackground||this.background||this.tertiaryColor,this.altBackground=this.altBackground||"#555",this.compositeTitleBackground=this.compositeTitleBackground||this.mainBkg,this.compositeBorder=this.compositeBorder||this.nodeBorder,this.innerEndBackground=this.primaryBorderColor,this.specialStateColor="#f4f4f4",this.errorBkgColor=this.errorBkgColor||this.tertiaryColor,this.errorTextColor=this.errorTextColor||this.tertiaryTextColor,this.fillType0=this.primaryColor,this.fillType1=this.secondaryColor,this.fillType2=w(this.primaryColor,{h:64}),this.fillType3=w(this.secondaryColor,{h:64}),this.fillType4=w(this.primaryColor,{h:-64}),this.fillType5=w(this.secondaryColor,{h:-64}),this.fillType6=w(this.primaryColor,{h:128}),this.fillType7=w(this.secondaryColor,{h:128}),this.cScale1=this.cScale1||"#0b0000",this.cScale2=this.cScale2||"#4d1037",this.cScale3=this.cScale3||"#3f5258",this.cScale4=this.cScale4||"#4f2f1b",this.cScale5=this.cScale5||"#6e0a0a",this.cScale6=this.cScale6||"#3b0048",this.cScale7=this.cScale7||"#995a01",this.cScale8=this.cScale8||"#154706",this.cScale9=this.cScale9||"#161722",this.cScale10=this.cScale10||"#00296f",this.cScale11=this.cScale11||"#01629c",this.cScale12=this.cScale12||"#010029",this.cScale0=this.cScale0||this.primaryColor,this.cScale1=this.cScale1||this.secondaryColor,this.cScale2=this.cScale2||this.tertiaryColor,this.cScale3=this.cScale3||w(this.primaryColor,{h:30}),this.cScale4=this.cScale4||w(this.primaryColor,{h:60}),this.cScale5=this.cScale5||w(this.primaryColor,{h:90}),this.cScale6=this.cScale6||w(this.primaryColor,{h:120}),this.cScale7=this.cScale7||w(this.primaryColor,{h:150}),this.cScale8=this.cScale8||w(this.primaryColor,{h:210}),this.cScale9=this.cScale9||w(this.primaryColor,{h:270}),this.cScale10=this.cScale10||w(this.primaryColor,{h:300}),this.cScale11=this.cScale11||w(this.primaryColor,{h:330});for(let f=0;f<this.THEME_COLOR_LIMIT;f++)this["cScaleInv"+f]=this["cScaleInv"+f]||N(this["cScale"+f]);for(let f=0;f<this.THEME_COLOR_LIMIT;f++)this["cScalePeer"+f]=this["cScalePeer"+f]||q(this["cScale"+f],10);for(let f=0;f<5;f++)this["surface"+f]=this["surface"+f]||w(this.mainBkg,{h:30,s:-30,l:-(-10+f*4)}),this["surfacePeer"+f]=this["surfacePeer"+f]||w(this.mainBkg,{h:30,s:-30,l:-(-7+f*4)});this.scaleLabelColor=this.scaleLabelColor||(this.darkMode?"black":this.labelTextColor);for(let f=0;f<this.THEME_COLOR_LIMIT;f++)this["cScaleLabel"+f]=this["cScaleLabel"+f]||this.scaleLabelColor;for(let f=0;f<this.THEME_COLOR_LIMIT;f++)this["pie"+f]=this["cScale"+f];this.pieTitleTextSize=this.pieTitleTextSize||"25px",this.pieTitleTextColor=this.pieTitleTextColor||this.taskTextDarkColor,this.pieSectionTextSize=this.pieSectionTextSize||"17px",this.pieSectionTextColor=this.pieSectionTextColor||this.textColor,this.pieLegendTextSize=this.pieLegendTextSize||"17px",this.pieLegendTextColor=this.pieLegendTextColor||this.taskTextDarkColor,this.pieStrokeColor=this.pieStrokeColor||"black",this.pieStrokeWidth=this.pieStrokeWidth||"2px",this.pieOuterStrokeWidth=this.pieOuterStrokeWidth||"2px",this.pieOuterStrokeColor=this.pieOuterStrokeColor||"black",this.pieOpacity=this.pieOpacity||"0.7",this.quadrant1Fill=this.quadrant1Fill||this.primaryColor,this.quadrant2Fill=this.quadrant2Fill||w(this.primaryColor,{r:5,g:5,b:5}),this.quadrant3Fill=this.quadrant3Fill||w(this.primaryColor,{r:10,g:10,b:10}),this.quadrant4Fill=this.quadrant4Fill||w(this.primaryColor,{r:15,g:15,b:15}),this.quadrant1TextFill=this.quadrant1TextFill||this.primaryTextColor,this.quadrant2TextFill=this.quadrant2TextFill||w(this.primaryTextColor,{r:-5,g:-5,b:-5}),this.quadrant3TextFill=this.quadrant3TextFill||w(this.primaryTextColor,{r:-10,g:-10,b:-10}),this.quadrant4TextFill=this.quadrant4TextFill||w(this.primaryTextColor,{r:-15,g:-15,b:-15}),this.quadrantPointFill=this.quadrantPointFill||_i(this.quadrant1Fill)?q(this.quadrant1Fill):Q(this.quadrant1Fill),this.quadrantPointTextFill=this.quadrantPointTextFill||this.primaryTextColor,this.quadrantXAxisTextFill=this.quadrantXAxisTextFill||this.primaryTextColor,this.quadrantYAxisTextFill=this.quadrantYAxisTextFill||this.primaryTextColor,this.quadrantInternalBorderStrokeFill=this.quadrantInternalBorderStrokeFill||this.primaryBorderColor,this.quadrantExternalBorderStrokeFill=this.quadrantExternalBorderStrokeFill||this.primaryBorderColor,this.quadrantTitleFill=this.quadrantTitleFill||this.primaryTextColor,this.xyChart={backgroundColor:((t=this.xyChart)==null?void 0:t.backgroundColor)||this.background,titleColor:((r=this.xyChart)==null?void 0:r.titleColor)||this.primaryTextColor,xAxisTitleColor:((i=this.xyChart)==null?void 0:i.xAxisTitleColor)||this.primaryTextColor,xAxisLabelColor:((a=this.xyChart)==null?void 0:a.xAxisLabelColor)||this.primaryTextColor,xAxisTickColor:((n=this.xyChart)==null?void 0:n.xAxisTickColor)||this.primaryTextColor,xAxisLineColor:((o=this.xyChart)==null?void 0:o.xAxisLineColor)||this.primaryTextColor,yAxisTitleColor:((s=this.xyChart)==null?void 0:s.yAxisTitleColor)||this.primaryTextColor,yAxisLabelColor:((l=this.xyChart)==null?void 0:l.yAxisLabelColor)||this.primaryTextColor,yAxisTickColor:((c=this.xyChart)==null?void 0:c.yAxisTickColor)||this.primaryTextColor,yAxisLineColor:((h=this.xyChart)==null?void 0:h.yAxisLineColor)||this.primaryTextColor,plotColorPalette:((u=this.xyChart)==null?void 0:u.plotColorPalette)||"#3498db,#2ecc71,#e74c3c,#f1c40f,#bdc3c7,#ffffff,#34495e,#9b59b6,#1abc9c,#e67e22"},this.packet={startByteColor:this.primaryTextColor,endByteColor:this.primaryTextColor,labelColor:this.primaryTextColor,titleColor:this.primaryTextColor,blockStrokeColor:this.primaryTextColor,blockFillColor:this.background},this.classText=this.primaryTextColor,this.requirementBackground=this.requirementBackground||this.primaryColor,this.requirementBorderColor=this.requirementBorderColor||this.primaryBorderColor,this.requirementBorderSize=this.requirementBorderSize||"1",this.requirementTextColor=this.requirementTextColor||this.primaryTextColor,this.relationColor=this.relationColor||this.lineColor,this.relationLabelBackground=this.relationLabelBackground||(this.darkMode?Q(this.secondaryColor,30):this.secondaryColor),this.relationLabelColor=this.relationLabelColor||this.actorTextColor,this.git0=q(this.secondaryColor,20),this.git1=q(this.pie2||this.secondaryColor,20),this.git2=q(this.pie3||this.tertiaryColor,20),this.git3=q(this.pie4||w(this.primaryColor,{h:-30}),20),this.git4=q(this.pie5||w(this.primaryColor,{h:-60}),20),this.git5=q(this.pie6||w(this.primaryColor,{h:-90}),10),this.git6=q(this.pie7||w(this.primaryColor,{h:60}),10),this.git7=q(this.pie8||w(this.primaryColor,{h:120}),20),this.gitInv0=this.gitInv0||N(this.git0),this.gitInv1=this.gitInv1||N(this.git1),this.gitInv2=this.gitInv2||N(this.git2),this.gitInv3=this.gitInv3||N(this.git3),this.gitInv4=this.gitInv4||N(this.git4),this.gitInv5=this.gitInv5||N(this.git5),this.gitInv6=this.gitInv6||N(this.git6),this.gitInv7=this.gitInv7||N(this.git7),this.gitBranchLabel0=this.gitBranchLabel0||N(this.labelTextColor),this.gitBranchLabel1=this.gitBranchLabel1||this.labelTextColor,this.gitBranchLabel2=this.gitBranchLabel2||this.labelTextColor,this.gitBranchLabel3=this.gitBranchLabel3||N(this.labelTextColor),this.gitBranchLabel4=this.gitBranchLabel4||this.labelTextColor,this.gitBranchLabel5=this.gitBranchLabel5||this.labelTextColor,this.gitBranchLabel6=this.gitBranchLabel6||this.labelTextColor,this.gitBranchLabel7=this.gitBranchLabel7||this.labelTextColor,this.tagLabelColor=this.tagLabelColor||this.primaryTextColor,this.tagLabelBackground=this.tagLabelBackground||this.primaryColor,this.tagLabelBorder=this.tagBorder||this.primaryBorderColor,this.tagLabelFontSize=this.tagLabelFontSize||"10px",this.commitLabelColor=this.commitLabelColor||this.secondaryTextColor,this.commitLabelBackground=this.commitLabelBackground||this.secondaryColor,this.commitLabelFontSize=this.commitLabelFontSize||"10px",this.attributeBackgroundColorOdd=this.attributeBackgroundColorOdd||q(this.background,12),this.attributeBackgroundColorEven=this.attributeBackgroundColorEven||q(this.background,2),this.nodeBorder=this.nodeBorder||"#999"}calculate(t){if(typeof t!="object"){this.updateColors();return}const r=Object.keys(t);r.forEach(i=>{this[i]=t[i]}),this.updateColors(),r.forEach(i=>{this[i]=t[i]})}},d(Ci,"Theme"),Ci),Ng=d(e=>{const t=new Pg;return t.calculate(e),t},"getThemeVariables"),ki,zg=(ki=class{constructor(){this.background="#f4f4f4",this.primaryColor="#ECECFF",this.secondaryColor=w(this.primaryColor,{h:120}),this.secondaryColor="#ffffde",this.tertiaryColor=w(this.primaryColor,{h:-160}),this.primaryBorderColor=Nt(this.primaryColor,this.darkMode),this.secondaryBorderColor=Nt(this.secondaryColor,this.darkMode),this.tertiaryBorderColor=Nt(this.tertiaryColor,this.darkMode),this.primaryTextColor=N(this.primaryColor),this.secondaryTextColor=N(this.secondaryColor),this.tertiaryTextColor=N(this.tertiaryColor),this.lineColor=N(this.background),this.textColor=N(this.background),this.background="white",this.mainBkg="#ECECFF",this.secondBkg="#ffffde",this.lineColor="#333333",this.border1="#9370DB",this.border2="#aaaa33",this.arrowheadColor="#333333",this.fontFamily='"trebuchet ms", verdana, arial, sans-serif',this.fontSize="16px",this.labelBackground="rgba(232,232,232, 0.8)",this.textColor="#333",this.THEME_COLOR_LIMIT=12,this.nodeBkg="calculated",this.nodeBorder="calculated",this.clusterBkg="calculated",this.clusterBorder="calculated",this.defaultLinkColor="calculated",this.titleColor="calculated",this.edgeLabelBackground="calculated",this.actorBorder="calculated",this.actorBkg="calculated",this.actorTextColor="black",this.actorLineColor="calculated",this.signalColor="calculated",this.signalTextColor="calculated",this.labelBoxBkgColor="calculated",this.labelBoxBorderColor="calculated",this.labelTextColor="calculated",this.loopTextColor="calculated",this.noteBorderColor="calculated",this.noteBkgColor="#fff5ad",this.noteTextColor="calculated",this.activationBorderColor="#666",this.activationBkgColor="#f4f4f4",this.sequenceNumberColor="white",this.sectionBkgColor="calculated",this.altSectionBkgColor="calculated",this.sectionBkgColor2="calculated",this.excludeBkgColor="#eeeeee",this.taskBorderColor="calculated",this.taskBkgColor="calculated",this.taskTextLightColor="calculated",this.taskTextColor=this.taskTextLightColor,this.taskTextDarkColor="calculated",this.taskTextOutsideColor=this.taskTextDarkColor,this.taskTextClickableColor="calculated",this.activeTaskBorderColor="calculated",this.activeTaskBkgColor="calculated",this.gridColor="calculated",this.doneTaskBkgColor="calculated",this.doneTaskBorderColor="calculated",this.critBorderColor="calculated",this.critBkgColor="calculated",this.todayLineColor="calculated",this.sectionBkgColor=ii(102,102,255,.49),this.altSectionBkgColor="white",this.sectionBkgColor2="#fff400",this.taskBorderColor="#534fbc",this.taskBkgColor="#8a90dd",this.taskTextLightColor="white",this.taskTextColor="calculated",this.taskTextDarkColor="black",this.taskTextOutsideColor="calculated",this.taskTextClickableColor="#003163",this.activeTaskBorderColor="#534fbc",this.activeTaskBkgColor="#bfc7ff",this.gridColor="lightgrey",this.doneTaskBkgColor="lightgrey",this.doneTaskBorderColor="grey",this.critBorderColor="#ff8888",this.critBkgColor="red",this.todayLineColor="red",this.personBorder=this.primaryBorderColor,this.personBkg=this.mainBkg,this.archEdgeColor="calculated",this.archEdgeArrowColor="calculated",this.archEdgeWidth="3",this.archGroupBorderColor=this.primaryBorderColor,this.archGroupBorderWidth="2px",this.rowOdd="calculated",this.rowEven="calculated",this.labelColor="black",this.errorBkgColor="#552222",this.errorTextColor="#552222",this.updateColors()}updateColors(){var t,r,i,a,n,o,s,l,c,h,u;this.cScale0=this.cScale0||this.primaryColor,this.cScale1=this.cScale1||this.secondaryColor,this.cScale2=this.cScale2||this.tertiaryColor,this.cScale3=this.cScale3||w(this.primaryColor,{h:30}),this.cScale4=this.cScale4||w(this.primaryColor,{h:60}),this.cScale5=this.cScale5||w(this.primaryColor,{h:90}),this.cScale6=this.cScale6||w(this.primaryColor,{h:120}),this.cScale7=this.cScale7||w(this.primaryColor,{h:150}),this.cScale8=this.cScale8||w(this.primaryColor,{h:210}),this.cScale9=this.cScale9||w(this.primaryColor,{h:270}),this.cScale10=this.cScale10||w(this.primaryColor,{h:300}),this.cScale11=this.cScale11||w(this.primaryColor,{h:330}),this.cScalePeer1=this.cScalePeer1||Q(this.secondaryColor,45),this.cScalePeer2=this.cScalePeer2||Q(this.tertiaryColor,40);for(let f=0;f<this.THEME_COLOR_LIMIT;f++)this["cScale"+f]=Q(this["cScale"+f],10),this["cScalePeer"+f]=this["cScalePeer"+f]||Q(this["cScale"+f],25);for(let f=0;f<this.THEME_COLOR_LIMIT;f++)this["cScaleInv"+f]=this["cScaleInv"+f]||w(this["cScale"+f],{h:180});for(let f=0;f<5;f++)this["surface"+f]=this["surface"+f]||w(this.mainBkg,{h:30,l:-(5+f*5)}),this["surfacePeer"+f]=this["surfacePeer"+f]||w(this.mainBkg,{h:30,l:-(7+f*5)});if(this.scaleLabelColor=this.scaleLabelColor!=="calculated"&&this.scaleLabelColor?this.scaleLabelColor:this.labelTextColor,this.labelTextColor!=="calculated"){this.cScaleLabel0=this.cScaleLabel0||N(this.labelTextColor),this.cScaleLabel3=this.cScaleLabel3||N(this.labelTextColor);for(let f=0;f<this.THEME_COLOR_LIMIT;f++)this["cScaleLabel"+f]=this["cScaleLabel"+f]||this.labelTextColor}this.nodeBkg=this.mainBkg,this.nodeBorder=this.border1,this.clusterBkg=this.secondBkg,this.clusterBorder=this.border2,this.defaultLinkColor=this.lineColor,this.titleColor=this.textColor,this.edgeLabelBackground=this.labelBackground,this.actorBorder=q(this.border1,23),this.actorBkg=this.mainBkg,this.labelBoxBkgColor=this.actorBkg,this.signalColor=this.textColor,this.signalTextColor=this.textColor,this.labelBoxBorderColor=this.actorBorder,this.labelTextColor=this.actorTextColor,this.loopTextColor=this.actorTextColor,this.noteBorderColor=this.border2,this.noteTextColor=this.actorTextColor,this.actorLineColor=this.actorBorder,this.taskTextColor=this.taskTextLightColor,this.taskTextOutsideColor=this.taskTextDarkColor,this.archEdgeColor=this.lineColor,this.archEdgeArrowColor=this.lineColor,this.rowOdd=this.rowOdd||q(this.primaryColor,75)||"#ffffff",this.rowEven=this.rowEven||q(this.primaryColor,1),this.transitionColor=this.transitionColor||this.lineColor,this.transitionLabelColor=this.transitionLabelColor||this.textColor,this.stateLabelColor=this.stateLabelColor||this.stateBkg||this.primaryTextColor,this.stateBkg=this.stateBkg||this.mainBkg,this.labelBackgroundColor=this.labelBackgroundColor||this.stateBkg,this.compositeBackground=this.compositeBackground||this.background||this.tertiaryColor,this.altBackground=this.altBackground||"#f0f0f0",this.compositeTitleBackground=this.compositeTitleBackground||this.mainBkg,this.compositeBorder=this.compositeBorder||this.nodeBorder,this.innerEndBackground=this.nodeBorder,this.specialStateColor=this.lineColor,this.errorBkgColor=this.errorBkgColor||this.tertiaryColor,this.errorTextColor=this.errorTextColor||this.tertiaryTextColor,this.transitionColor=this.transitionColor||this.lineColor,this.classText=this.primaryTextColor,this.fillType0=this.primaryColor,this.fillType1=this.secondaryColor,this.fillType2=w(this.primaryColor,{h:64}),this.fillType3=w(this.secondaryColor,{h:64}),this.fillType4=w(this.primaryColor,{h:-64}),this.fillType5=w(this.secondaryColor,{h:-64}),this.fillType6=w(this.primaryColor,{h:128}),this.fillType7=w(this.secondaryColor,{h:128}),this.pie1=this.pie1||this.primaryColor,this.pie2=this.pie2||this.secondaryColor,this.pie3=this.pie3||w(this.tertiaryColor,{l:-40}),this.pie4=this.pie4||w(this.primaryColor,{l:-10}),this.pie5=this.pie5||w(this.secondaryColor,{l:-30}),this.pie6=this.pie6||w(this.tertiaryColor,{l:-20}),this.pie7=this.pie7||w(this.primaryColor,{h:60,l:-20}),this.pie8=this.pie8||w(this.primaryColor,{h:-60,l:-40}),this.pie9=this.pie9||w(this.primaryColor,{h:120,l:-40}),this.pie10=this.pie10||w(this.primaryColor,{h:60,l:-40}),this.pie11=this.pie11||w(this.primaryColor,{h:-90,l:-40}),this.pie12=this.pie12||w(this.primaryColor,{h:120,l:-30}),this.pieTitleTextSize=this.pieTitleTextSize||"25px",this.pieTitleTextColor=this.pieTitleTextColor||this.taskTextDarkColor,this.pieSectionTextSize=this.pieSectionTextSize||"17px",this.pieSectionTextColor=this.pieSectionTextColor||this.textColor,this.pieLegendTextSize=this.pieLegendTextSize||"17px",this.pieLegendTextColor=this.pieLegendTextColor||this.taskTextDarkColor,this.pieStrokeColor=this.pieStrokeColor||"black",this.pieStrokeWidth=this.pieStrokeWidth||"2px",this.pieOuterStrokeWidth=this.pieOuterStrokeWidth||"2px",this.pieOuterStrokeColor=this.pieOuterStrokeColor||"black",this.pieOpacity=this.pieOpacity||"0.7",this.quadrant1Fill=this.quadrant1Fill||this.primaryColor,this.quadrant2Fill=this.quadrant2Fill||w(this.primaryColor,{r:5,g:5,b:5}),this.quadrant3Fill=this.quadrant3Fill||w(this.primaryColor,{r:10,g:10,b:10}),this.quadrant4Fill=this.quadrant4Fill||w(this.primaryColor,{r:15,g:15,b:15}),this.quadrant1TextFill=this.quadrant1TextFill||this.primaryTextColor,this.quadrant2TextFill=this.quadrant2TextFill||w(this.primaryTextColor,{r:-5,g:-5,b:-5}),this.quadrant3TextFill=this.quadrant3TextFill||w(this.primaryTextColor,{r:-10,g:-10,b:-10}),this.quadrant4TextFill=this.quadrant4TextFill||w(this.primaryTextColor,{r:-15,g:-15,b:-15}),this.quadrantPointFill=this.quadrantPointFill||_i(this.quadrant1Fill)?q(this.quadrant1Fill):Q(this.quadrant1Fill),this.quadrantPointTextFill=this.quadrantPointTextFill||this.primaryTextColor,this.quadrantXAxisTextFill=this.quadrantXAxisTextFill||this.primaryTextColor,this.quadrantYAxisTextFill=this.quadrantYAxisTextFill||this.primaryTextColor,this.quadrantInternalBorderStrokeFill=this.quadrantInternalBorderStrokeFill||this.primaryBorderColor,this.quadrantExternalBorderStrokeFill=this.quadrantExternalBorderStrokeFill||this.primaryBorderColor,this.quadrantTitleFill=this.quadrantTitleFill||this.primaryTextColor,this.xyChart={backgroundColor:((t=this.xyChart)==null?void 0:t.backgroundColor)||this.background,titleColor:((r=this.xyChart)==null?void 0:r.titleColor)||this.primaryTextColor,xAxisTitleColor:((i=this.xyChart)==null?void 0:i.xAxisTitleColor)||this.primaryTextColor,xAxisLabelColor:((a=this.xyChart)==null?void 0:a.xAxisLabelColor)||this.primaryTextColor,xAxisTickColor:((n=this.xyChart)==null?void 0:n.xAxisTickColor)||this.primaryTextColor,xAxisLineColor:((o=this.xyChart)==null?void 0:o.xAxisLineColor)||this.primaryTextColor,yAxisTitleColor:((s=this.xyChart)==null?void 0:s.yAxisTitleColor)||this.primaryTextColor,yAxisLabelColor:((l=this.xyChart)==null?void 0:l.yAxisLabelColor)||this.primaryTextColor,yAxisTickColor:((c=this.xyChart)==null?void 0:c.yAxisTickColor)||this.primaryTextColor,yAxisLineColor:((h=this.xyChart)==null?void 0:h.yAxisLineColor)||this.primaryTextColor,plotColorPalette:((u=this.xyChart)==null?void 0:u.plotColorPalette)||"#ECECFF,#8493A6,#FFC3A0,#DCDDE1,#B8E994,#D1A36F,#C3CDE6,#FFB6C1,#496078,#F8F3E3"},this.requirementBackground=this.requirementBackground||this.primaryColor,this.requirementBorderColor=this.requirementBorderColor||this.primaryBorderColor,this.requirementBorderSize=this.requirementBorderSize||"1",this.requirementTextColor=this.requirementTextColor||this.primaryTextColor,this.relationColor=this.relationColor||this.lineColor,this.relationLabelBackground=this.relationLabelBackground||this.labelBackground,this.relationLabelColor=this.relationLabelColor||this.actorTextColor,this.git0=this.git0||this.primaryColor,this.git1=this.git1||this.secondaryColor,this.git2=this.git2||this.tertiaryColor,this.git3=this.git3||w(this.primaryColor,{h:-30}),this.git4=this.git4||w(this.primaryColor,{h:-60}),this.git5=this.git5||w(this.primaryColor,{h:-90}),this.git6=this.git6||w(this.primaryColor,{h:60}),this.git7=this.git7||w(this.primaryColor,{h:120}),this.darkMode?(this.git0=q(this.git0,25),this.git1=q(this.git1,25),this.git2=q(this.git2,25),this.git3=q(this.git3,25),this.git4=q(this.git4,25),this.git5=q(this.git5,25),this.git6=q(this.git6,25),this.git7=q(this.git7,25)):(this.git0=Q(this.git0,25),this.git1=Q(this.git1,25),this.git2=Q(this.git2,25),this.git3=Q(this.git3,25),this.git4=Q(this.git4,25),this.git5=Q(this.git5,25),this.git6=Q(this.git6,25),this.git7=Q(this.git7,25)),this.gitInv0=this.gitInv0||Q(N(this.git0),25),this.gitInv1=this.gitInv1||N(this.git1),this.gitInv2=this.gitInv2||N(this.git2),this.gitInv3=this.gitInv3||N(this.git3),this.gitInv4=this.gitInv4||N(this.git4),this.gitInv5=this.gitInv5||N(this.git5),this.gitInv6=this.gitInv6||N(this.git6),this.gitInv7=this.gitInv7||N(this.git7),this.gitBranchLabel0=this.gitBranchLabel0||N(this.labelTextColor),this.gitBranchLabel1=this.gitBranchLabel1||this.labelTextColor,this.gitBranchLabel2=this.gitBranchLabel2||this.labelTextColor,this.gitBranchLabel3=this.gitBranchLabel3||N(this.labelTextColor),this.gitBranchLabel4=this.gitBranchLabel4||this.labelTextColor,this.gitBranchLabel5=this.gitBranchLabel5||this.labelTextColor,this.gitBranchLabel6=this.gitBranchLabel6||this.labelTextColor,this.gitBranchLabel7=this.gitBranchLabel7||this.labelTextColor,this.tagLabelColor=this.tagLabelColor||this.primaryTextColor,this.tagLabelBackground=this.tagLabelBackground||this.primaryColor,this.tagLabelBorder=this.tagBorder||this.primaryBorderColor,this.tagLabelFontSize=this.tagLabelFontSize||"10px",this.commitLabelColor=this.commitLabelColor||this.secondaryTextColor,this.commitLabelBackground=this.commitLabelBackground||this.secondaryColor,this.commitLabelFontSize=this.commitLabelFontSize||"10px",this.attributeBackgroundColorOdd=this.attributeBackgroundColorOdd||Da,this.attributeBackgroundColorEven=this.attributeBackgroundColorEven||Ra}calculate(t){if(Object.keys(this).forEach(i=>{this[i]==="calculated"&&(this[i]=void 0)}),typeof t!="object"){this.updateColors();return}const r=Object.keys(t);r.forEach(i=>{this[i]=t[i]}),this.updateColors(),r.forEach(i=>{this[i]=t[i]})}},d(ki,"Theme"),ki),Wg=d(e=>{const t=new zg;return t.calculate(e),t},"getThemeVariables"),wi,qg=(wi=class{constructor(){this.background="#f4f4f4",this.primaryColor="#cde498",this.secondaryColor="#cdffb2",this.background="white",this.mainBkg="#cde498",this.secondBkg="#cdffb2",this.lineColor="green",this.border1="#13540c",this.border2="#6eaa49",this.arrowheadColor="green",this.fontFamily='"trebuchet ms", verdana, arial, sans-serif',this.fontSize="16px",this.tertiaryColor=q("#cde498",10),this.primaryBorderColor=Nt(this.primaryColor,this.darkMode),this.secondaryBorderColor=Nt(this.secondaryColor,this.darkMode),this.tertiaryBorderColor=Nt(this.tertiaryColor,this.darkMode),this.primaryTextColor=N(this.primaryColor),this.secondaryTextColor=N(this.secondaryColor),this.tertiaryTextColor=N(this.primaryColor),this.lineColor=N(this.background),this.textColor=N(this.background),this.THEME_COLOR_LIMIT=12,this.nodeBkg="calculated",this.nodeBorder="calculated",this.clusterBkg="calculated",this.clusterBorder="calculated",this.defaultLinkColor="calculated",this.titleColor="#333",this.edgeLabelBackground="#e8e8e8",this.actorBorder="calculated",this.actorBkg="calculated",this.actorTextColor="black",this.actorLineColor="calculated",this.signalColor="#333",this.signalTextColor="#333",this.labelBoxBkgColor="calculated",this.labelBoxBorderColor="#326932",this.labelTextColor="calculated",this.loopTextColor="calculated",this.noteBorderColor="calculated",this.noteBkgColor="#fff5ad",this.noteTextColor="calculated",this.activationBorderColor="#666",this.activationBkgColor="#f4f4f4",this.sequenceNumberColor="white",this.sectionBkgColor="#6eaa49",this.altSectionBkgColor="white",this.sectionBkgColor2="#6eaa49",this.excludeBkgColor="#eeeeee",this.taskBorderColor="calculated",this.taskBkgColor="#487e3a",this.taskTextLightColor="white",this.taskTextColor="calculated",this.taskTextDarkColor="black",this.taskTextOutsideColor="calculated",this.taskTextClickableColor="#003163",this.activeTaskBorderColor="calculated",this.activeTaskBkgColor="calculated",this.gridColor="lightgrey",this.doneTaskBkgColor="lightgrey",this.doneTaskBorderColor="grey",this.critBorderColor="#ff8888",this.critBkgColor="red",this.todayLineColor="red",this.personBorder=this.primaryBorderColor,this.personBkg=this.mainBkg,this.archEdgeColor="calculated",this.archEdgeArrowColor="calculated",this.archEdgeWidth="3",this.archGroupBorderColor=this.primaryBorderColor,this.archGroupBorderWidth="2px",this.labelColor="black",this.errorBkgColor="#552222",this.errorTextColor="#552222"}updateColors(){var t,r,i,a,n,o,s,l,c,h,u;this.actorBorder=Q(this.mainBkg,20),this.actorBkg=this.mainBkg,this.labelBoxBkgColor=this.actorBkg,this.labelTextColor=this.actorTextColor,this.loopTextColor=this.actorTextColor,this.noteBorderColor=this.border2,this.noteTextColor=this.actorTextColor,this.actorLineColor=this.actorBorder,this.cScale0=this.cScale0||this.primaryColor,this.cScale1=this.cScale1||this.secondaryColor,this.cScale2=this.cScale2||this.tertiaryColor,this.cScale3=this.cScale3||w(this.primaryColor,{h:30}),this.cScale4=this.cScale4||w(this.primaryColor,{h:60}),this.cScale5=this.cScale5||w(this.primaryColor,{h:90}),this.cScale6=this.cScale6||w(this.primaryColor,{h:120}),this.cScale7=this.cScale7||w(this.primaryColor,{h:150}),this.cScale8=this.cScale8||w(this.primaryColor,{h:210}),this.cScale9=this.cScale9||w(this.primaryColor,{h:270}),this.cScale10=this.cScale10||w(this.primaryColor,{h:300}),this.cScale11=this.cScale11||w(this.primaryColor,{h:330}),this.cScalePeer1=this.cScalePeer1||Q(this.secondaryColor,45),this.cScalePeer2=this.cScalePeer2||Q(this.tertiaryColor,40);for(let f=0;f<this.THEME_COLOR_LIMIT;f++)this["cScale"+f]=Q(this["cScale"+f],10),this["cScalePeer"+f]=this["cScalePeer"+f]||Q(this["cScale"+f],25);for(let f=0;f<this.THEME_COLOR_LIMIT;f++)this["cScaleInv"+f]=this["cScaleInv"+f]||w(this["cScale"+f],{h:180});this.scaleLabelColor=this.scaleLabelColor!=="calculated"&&this.scaleLabelColor?this.scaleLabelColor:this.labelTextColor;for(let f=0;f<this.THEME_COLOR_LIMIT;f++)this["cScaleLabel"+f]=this["cScaleLabel"+f]||this.scaleLabelColor;for(let f=0;f<5;f++)this["surface"+f]=this["surface"+f]||w(this.mainBkg,{h:30,s:-30,l:-(5+f*5)}),this["surfacePeer"+f]=this["surfacePeer"+f]||w(this.mainBkg,{h:30,s:-30,l:-(8+f*5)});this.nodeBkg=this.mainBkg,this.nodeBorder=this.border1,this.clusterBkg=this.secondBkg,this.clusterBorder=this.border2,this.defaultLinkColor=this.lineColor,this.taskBorderColor=this.border1,this.taskTextColor=this.taskTextLightColor,this.taskTextOutsideColor=this.taskTextDarkColor,this.activeTaskBorderColor=this.taskBorderColor,this.activeTaskBkgColor=this.mainBkg,this.archEdgeColor=this.lineColor,this.archEdgeArrowColor=this.lineColor,this.rowOdd=this.rowOdd||q(this.mainBkg,75)||"#ffffff",this.rowEven=this.rowEven||q(this.mainBkg,20),this.transitionColor=this.transitionColor||this.lineColor,this.transitionLabelColor=this.transitionLabelColor||this.textColor,this.stateLabelColor=this.stateLabelColor||this.stateBkg||this.primaryTextColor,this.stateBkg=this.stateBkg||this.mainBkg,this.labelBackgroundColor=this.labelBackgroundColor||this.stateBkg,this.compositeBackground=this.compositeBackground||this.background||this.tertiaryColor,this.altBackground=this.altBackground||"#f0f0f0",this.compositeTitleBackground=this.compositeTitleBackground||this.mainBkg,this.compositeBorder=this.compositeBorder||this.nodeBorder,this.innerEndBackground=this.primaryBorderColor,this.specialStateColor=this.lineColor,this.errorBkgColor=this.errorBkgColor||this.tertiaryColor,this.errorTextColor=this.errorTextColor||this.tertiaryTextColor,this.transitionColor=this.transitionColor||this.lineColor,this.classText=this.primaryTextColor,this.fillType0=this.primaryColor,this.fillType1=this.secondaryColor,this.fillType2=w(this.primaryColor,{h:64}),this.fillType3=w(this.secondaryColor,{h:64}),this.fillType4=w(this.primaryColor,{h:-64}),this.fillType5=w(this.secondaryColor,{h:-64}),this.fillType6=w(this.primaryColor,{h:128}),this.fillType7=w(this.secondaryColor,{h:128}),this.pie1=this.pie1||this.primaryColor,this.pie2=this.pie2||this.secondaryColor,this.pie3=this.pie3||this.tertiaryColor,this.pie4=this.pie4||w(this.primaryColor,{l:-30}),this.pie5=this.pie5||w(this.secondaryColor,{l:-30}),this.pie6=this.pie6||w(this.tertiaryColor,{h:40,l:-40}),this.pie7=this.pie7||w(this.primaryColor,{h:60,l:-10}),this.pie8=this.pie8||w(this.primaryColor,{h:-60,l:-10}),this.pie9=this.pie9||w(this.primaryColor,{h:120,l:0}),this.pie10=this.pie10||w(this.primaryColor,{h:60,l:-50}),this.pie11=this.pie11||w(this.primaryColor,{h:-60,l:-50}),this.pie12=this.pie12||w(this.primaryColor,{h:120,l:-50}),this.pieTitleTextSize=this.pieTitleTextSize||"25px",this.pieTitleTextColor=this.pieTitleTextColor||this.taskTextDarkColor,this.pieSectionTextSize=this.pieSectionTextSize||"17px",this.pieSectionTextColor=this.pieSectionTextColor||this.textColor,this.pieLegendTextSize=this.pieLegendTextSize||"17px",this.pieLegendTextColor=this.pieLegendTextColor||this.taskTextDarkColor,this.pieStrokeColor=this.pieStrokeColor||"black",this.pieStrokeWidth=this.pieStrokeWidth||"2px",this.pieOuterStrokeWidth=this.pieOuterStrokeWidth||"2px",this.pieOuterStrokeColor=this.pieOuterStrokeColor||"black",this.pieOpacity=this.pieOpacity||"0.7",this.quadrant1Fill=this.quadrant1Fill||this.primaryColor,this.quadrant2Fill=this.quadrant2Fill||w(this.primaryColor,{r:5,g:5,b:5}),this.quadrant3Fill=this.quadrant3Fill||w(this.primaryColor,{r:10,g:10,b:10}),this.quadrant4Fill=this.quadrant4Fill||w(this.primaryColor,{r:15,g:15,b:15}),this.quadrant1TextFill=this.quadrant1TextFill||this.primaryTextColor,this.quadrant2TextFill=this.quadrant2TextFill||w(this.primaryTextColor,{r:-5,g:-5,b:-5}),this.quadrant3TextFill=this.quadrant3TextFill||w(this.primaryTextColor,{r:-10,g:-10,b:-10}),this.quadrant4TextFill=this.quadrant4TextFill||w(this.primaryTextColor,{r:-15,g:-15,b:-15}),this.quadrantPointFill=this.quadrantPointFill||_i(this.quadrant1Fill)?q(this.quadrant1Fill):Q(this.quadrant1Fill),this.quadrantPointTextFill=this.quadrantPointTextFill||this.primaryTextColor,this.quadrantXAxisTextFill=this.quadrantXAxisTextFill||this.primaryTextColor,this.quadrantYAxisTextFill=this.quadrantYAxisTextFill||this.primaryTextColor,this.quadrantInternalBorderStrokeFill=this.quadrantInternalBorderStrokeFill||this.primaryBorderColor,this.quadrantExternalBorderStrokeFill=this.quadrantExternalBorderStrokeFill||this.primaryBorderColor,this.quadrantTitleFill=this.quadrantTitleFill||this.primaryTextColor,this.packet={startByteColor:this.primaryTextColor,endByteColor:this.primaryTextColor,labelColor:this.primaryTextColor,titleColor:this.primaryTextColor,blockStrokeColor:this.primaryTextColor,blockFillColor:this.mainBkg},this.xyChart={backgroundColor:((t=this.xyChart)==null?void 0:t.backgroundColor)||this.background,titleColor:((r=this.xyChart)==null?void 0:r.titleColor)||this.primaryTextColor,xAxisTitleColor:((i=this.xyChart)==null?void 0:i.xAxisTitleColor)||this.primaryTextColor,xAxisLabelColor:((a=this.xyChart)==null?void 0:a.xAxisLabelColor)||this.primaryTextColor,xAxisTickColor:((n=this.xyChart)==null?void 0:n.xAxisTickColor)||this.primaryTextColor,xAxisLineColor:((o=this.xyChart)==null?void 0:o.xAxisLineColor)||this.primaryTextColor,yAxisTitleColor:((s=this.xyChart)==null?void 0:s.yAxisTitleColor)||this.primaryTextColor,yAxisLabelColor:((l=this.xyChart)==null?void 0:l.yAxisLabelColor)||this.primaryTextColor,yAxisTickColor:((c=this.xyChart)==null?void 0:c.yAxisTickColor)||this.primaryTextColor,yAxisLineColor:((h=this.xyChart)==null?void 0:h.yAxisLineColor)||this.primaryTextColor,plotColorPalette:((u=this.xyChart)==null?void 0:u.plotColorPalette)||"#CDE498,#FF6B6B,#A0D2DB,#D7BDE2,#F0F0F0,#FFC3A0,#7FD8BE,#FF9A8B,#FAF3E0,#FFF176"},this.requirementBackground=this.requirementBackground||this.primaryColor,this.requirementBorderColor=this.requirementBorderColor||this.primaryBorderColor,this.requirementBorderSize=this.requirementBorderSize||"1",this.requirementTextColor=this.requirementTextColor||this.primaryTextColor,this.relationColor=this.relationColor||this.lineColor,this.relationLabelBackground=this.relationLabelBackground||this.edgeLabelBackground,this.relationLabelColor=this.relationLabelColor||this.actorTextColor,this.git0=this.git0||this.primaryColor,this.git1=this.git1||this.secondaryColor,this.git2=this.git2||this.tertiaryColor,this.git3=this.git3||w(this.primaryColor,{h:-30}),this.git4=this.git4||w(this.primaryColor,{h:-60}),this.git5=this.git5||w(this.primaryColor,{h:-90}),this.git6=this.git6||w(this.primaryColor,{h:60}),this.git7=this.git7||w(this.primaryColor,{h:120}),this.darkMode?(this.git0=q(this.git0,25),this.git1=q(this.git1,25),this.git2=q(this.git2,25),this.git3=q(this.git3,25),this.git4=q(this.git4,25),this.git5=q(this.git5,25),this.git6=q(this.git6,25),this.git7=q(this.git7,25)):(this.git0=Q(this.git0,25),this.git1=Q(this.git1,25),this.git2=Q(this.git2,25),this.git3=Q(this.git3,25),this.git4=Q(this.git4,25),this.git5=Q(this.git5,25),this.git6=Q(this.git6,25),this.git7=Q(this.git7,25)),this.gitInv0=this.gitInv0||N(this.git0),this.gitInv1=this.gitInv1||N(this.git1),this.gitInv2=this.gitInv2||N(this.git2),this.gitInv3=this.gitInv3||N(this.git3),this.gitInv4=this.gitInv4||N(this.git4),this.gitInv5=this.gitInv5||N(this.git5),this.gitInv6=this.gitInv6||N(this.git6),this.gitInv7=this.gitInv7||N(this.git7),this.gitBranchLabel0=this.gitBranchLabel0||N(this.labelTextColor),this.gitBranchLabel1=this.gitBranchLabel1||this.labelTextColor,this.gitBranchLabel2=this.gitBranchLabel2||this.labelTextColor,this.gitBranchLabel3=this.gitBranchLabel3||N(this.labelTextColor),this.gitBranchLabel4=this.gitBranchLabel4||this.labelTextColor,this.gitBranchLabel5=this.gitBranchLabel5||this.labelTextColor,this.gitBranchLabel6=this.gitBranchLabel6||this.labelTextColor,this.gitBranchLabel7=this.gitBranchLabel7||this.labelTextColor,this.tagLabelColor=this.tagLabelColor||this.primaryTextColor,this.tagLabelBackground=this.tagLabelBackground||this.primaryColor,this.tagLabelBorder=this.tagBorder||this.primaryBorderColor,this.tagLabelFontSize=this.tagLabelFontSize||"10px",this.commitLabelColor=this.commitLabelColor||this.secondaryTextColor,this.commitLabelBackground=this.commitLabelBackground||this.secondaryColor,this.commitLabelFontSize=this.commitLabelFontSize||"10px",this.attributeBackgroundColorOdd=this.attributeBackgroundColorOdd||Da,this.attributeBackgroundColorEven=this.attributeBackgroundColorEven||Ra}calculate(t){if(typeof t!="object"){this.updateColors();return}const r=Object.keys(t);r.forEach(i=>{this[i]=t[i]}),this.updateColors(),r.forEach(i=>{this[i]=t[i]})}},d(wi,"Theme"),wi),Hg=d(e=>{const t=new qg;return t.calculate(e),t},"getThemeVariables"),vi,jg=(vi=class{constructor(){this.primaryColor="#eee",this.contrast="#707070",this.secondaryColor=q(this.contrast,55),this.background="#ffffff",this.tertiaryColor=w(this.primaryColor,{h:-160}),this.primaryBorderColor=Nt(this.primaryColor,this.darkMode),this.secondaryBorderColor=Nt(this.secondaryColor,this.darkMode),this.tertiaryBorderColor=Nt(this.tertiaryColor,this.darkMode),this.primaryTextColor=N(this.primaryColor),this.secondaryTextColor=N(this.secondaryColor),this.tertiaryTextColor=N(this.tertiaryColor),this.lineColor=N(this.background),this.textColor=N(this.background),this.mainBkg="#eee",this.secondBkg="calculated",this.lineColor="#666",this.border1="#999",this.border2="calculated",this.note="#ffa",this.text="#333",this.critical="#d42",this.done="#bbb",this.arrowheadColor="#333333",this.fontFamily='"trebuchet ms", verdana, arial, sans-serif',this.fontSize="16px",this.THEME_COLOR_LIMIT=12,this.nodeBkg="calculated",this.nodeBorder="calculated",this.clusterBkg="calculated",this.clusterBorder="calculated",this.defaultLinkColor="calculated",this.titleColor="calculated",this.edgeLabelBackground="white",this.actorBorder="calculated",this.actorBkg="calculated",this.actorTextColor="calculated",this.actorLineColor=this.actorBorder,this.signalColor="calculated",this.signalTextColor="calculated",this.labelBoxBkgColor="calculated",this.labelBoxBorderColor="calculated",this.labelTextColor="calculated",this.loopTextColor="calculated",this.noteBorderColor="calculated",this.noteBkgColor="calculated",this.noteTextColor="calculated",this.activationBorderColor="#666",this.activationBkgColor="#f4f4f4",this.sequenceNumberColor="white",this.sectionBkgColor="calculated",this.altSectionBkgColor="white",this.sectionBkgColor2="calculated",this.excludeBkgColor="#eeeeee",this.taskBorderColor="calculated",this.taskBkgColor="calculated",this.taskTextLightColor="white",this.taskTextColor="calculated",this.taskTextDarkColor="calculated",this.taskTextOutsideColor="calculated",this.taskTextClickableColor="#003163",this.activeTaskBorderColor="calculated",this.activeTaskBkgColor="calculated",this.gridColor="calculated",this.doneTaskBkgColor="calculated",this.doneTaskBorderColor="calculated",this.critBkgColor="calculated",this.critBorderColor="calculated",this.todayLineColor="calculated",this.personBorder=this.primaryBorderColor,this.personBkg=this.mainBkg,this.archEdgeColor="calculated",this.archEdgeArrowColor="calculated",this.archEdgeWidth="3",this.archGroupBorderColor=this.primaryBorderColor,this.archGroupBorderWidth="2px",this.rowOdd=this.rowOdd||q(this.mainBkg,75)||"#ffffff",this.rowEven=this.rowEven||"#f4f4f4",this.labelColor="black",this.errorBkgColor="#552222",this.errorTextColor="#552222"}updateColors(){var t,r,i,a,n,o,s,l,c,h,u;this.secondBkg=q(this.contrast,55),this.border2=this.contrast,this.actorBorder=q(this.border1,23),this.actorBkg=this.mainBkg,this.actorTextColor=this.text,this.actorLineColor=this.actorBorder,this.signalColor=this.text,this.signalTextColor=this.text,this.labelBoxBkgColor=this.actorBkg,this.labelBoxBorderColor=this.actorBorder,this.labelTextColor=this.text,this.loopTextColor=this.text,this.noteBorderColor="#999",this.noteBkgColor="#666",this.noteTextColor="#fff",this.cScale0=this.cScale0||"#555",this.cScale1=this.cScale1||"#F4F4F4",this.cScale2=this.cScale2||"#555",this.cScale3=this.cScale3||"#BBB",this.cScale4=this.cScale4||"#777",this.cScale5=this.cScale5||"#999",this.cScale6=this.cScale6||"#DDD",this.cScale7=this.cScale7||"#FFF",this.cScale8=this.cScale8||"#DDD",this.cScale9=this.cScale9||"#BBB",this.cScale10=this.cScale10||"#999",this.cScale11=this.cScale11||"#777";for(let f=0;f<this.THEME_COLOR_LIMIT;f++)this["cScaleInv"+f]=this["cScaleInv"+f]||N(this["cScale"+f]);for(let f=0;f<this.THEME_COLOR_LIMIT;f++)this.darkMode?this["cScalePeer"+f]=this["cScalePeer"+f]||q(this["cScale"+f],10):this["cScalePeer"+f]=this["cScalePeer"+f]||Q(this["cScale"+f],10);this.scaleLabelColor=this.scaleLabelColor||(this.darkMode?"black":this.labelTextColor),this.cScaleLabel0=this.cScaleLabel0||this.cScale1,this.cScaleLabel2=this.cScaleLabel2||this.cScale1;for(let f=0;f<this.THEME_COLOR_LIMIT;f++)this["cScaleLabel"+f]=this["cScaleLabel"+f]||this.scaleLabelColor;for(let f=0;f<5;f++)this["surface"+f]=this["surface"+f]||w(this.mainBkg,{l:-(5+f*5)}),this["surfacePeer"+f]=this["surfacePeer"+f]||w(this.mainBkg,{l:-(8+f*5)});this.nodeBkg=this.mainBkg,this.nodeBorder=this.border1,this.clusterBkg=this.secondBkg,this.clusterBorder=this.border2,this.defaultLinkColor=this.lineColor,this.titleColor=this.text,this.sectionBkgColor=q(this.contrast,30),this.sectionBkgColor2=q(this.contrast,30),this.taskBorderColor=Q(this.contrast,10),this.taskBkgColor=this.contrast,this.taskTextColor=this.taskTextLightColor,this.taskTextDarkColor=this.text,this.taskTextOutsideColor=this.taskTextDarkColor,this.activeTaskBorderColor=this.taskBorderColor,this.activeTaskBkgColor=this.mainBkg,this.gridColor=q(this.border1,30),this.doneTaskBkgColor=this.done,this.doneTaskBorderColor=this.lineColor,this.critBkgColor=this.critical,this.critBorderColor=Q(this.critBkgColor,10),this.todayLineColor=this.critBkgColor,this.archEdgeColor=this.lineColor,this.archEdgeArrowColor=this.lineColor,this.transitionColor=this.transitionColor||"#000",this.transitionLabelColor=this.transitionLabelColor||this.textColor,this.stateLabelColor=this.stateLabelColor||this.stateBkg||this.primaryTextColor,this.stateBkg=this.stateBkg||this.mainBkg,this.labelBackgroundColor=this.labelBackgroundColor||this.stateBkg,this.compositeBackground=this.compositeBackground||this.background||this.tertiaryColor,this.altBackground=this.altBackground||"#f4f4f4",this.compositeTitleBackground=this.compositeTitleBackground||this.mainBkg,this.stateBorder=this.stateBorder||"#000",this.innerEndBackground=this.primaryBorderColor,this.specialStateColor="#222",this.errorBkgColor=this.errorBkgColor||this.tertiaryColor,this.errorTextColor=this.errorTextColor||this.tertiaryTextColor,this.classText=this.primaryTextColor,this.fillType0=this.primaryColor,this.fillType1=this.secondaryColor,this.fillType2=w(this.primaryColor,{h:64}),this.fillType3=w(this.secondaryColor,{h:64}),this.fillType4=w(this.primaryColor,{h:-64}),this.fillType5=w(this.secondaryColor,{h:-64}),this.fillType6=w(this.primaryColor,{h:128}),this.fillType7=w(this.secondaryColor,{h:128});for(let f=0;f<this.THEME_COLOR_LIMIT;f++)this["pie"+f]=this["cScale"+f];this.pie12=this.pie0,this.pieTitleTextSize=this.pieTitleTextSize||"25px",this.pieTitleTextColor=this.pieTitleTextColor||this.taskTextDarkColor,this.pieSectionTextSize=this.pieSectionTextSize||"17px",this.pieSectionTextColor=this.pieSectionTextColor||this.textColor,this.pieLegendTextSize=this.pieLegendTextSize||"17px",this.pieLegendTextColor=this.pieLegendTextColor||this.taskTextDarkColor,this.pieStrokeColor=this.pieStrokeColor||"black",this.pieStrokeWidth=this.pieStrokeWidth||"2px",this.pieOuterStrokeWidth=this.pieOuterStrokeWidth||"2px",this.pieOuterStrokeColor=this.pieOuterStrokeColor||"black",this.pieOpacity=this.pieOpacity||"0.7",this.quadrant1Fill=this.quadrant1Fill||this.primaryColor,this.quadrant2Fill=this.quadrant2Fill||w(this.primaryColor,{r:5,g:5,b:5}),this.quadrant3Fill=this.quadrant3Fill||w(this.primaryColor,{r:10,g:10,b:10}),this.quadrant4Fill=this.quadrant4Fill||w(this.primaryColor,{r:15,g:15,b:15}),this.quadrant1TextFill=this.quadrant1TextFill||this.primaryTextColor,this.quadrant2TextFill=this.quadrant2TextFill||w(this.primaryTextColor,{r:-5,g:-5,b:-5}),this.quadrant3TextFill=this.quadrant3TextFill||w(this.primaryTextColor,{r:-10,g:-10,b:-10}),this.quadrant4TextFill=this.quadrant4TextFill||w(this.primaryTextColor,{r:-15,g:-15,b:-15}),this.quadrantPointFill=this.quadrantPointFill||_i(this.quadrant1Fill)?q(this.quadrant1Fill):Q(this.quadrant1Fill),this.quadrantPointTextFill=this.quadrantPointTextFill||this.primaryTextColor,this.quadrantXAxisTextFill=this.quadrantXAxisTextFill||this.primaryTextColor,this.quadrantYAxisTextFill=this.quadrantYAxisTextFill||this.primaryTextColor,this.quadrantInternalBorderStrokeFill=this.quadrantInternalBorderStrokeFill||this.primaryBorderColor,this.quadrantExternalBorderStrokeFill=this.quadrantExternalBorderStrokeFill||this.primaryBorderColor,this.quadrantTitleFill=this.quadrantTitleFill||this.primaryTextColor,this.xyChart={backgroundColor:((t=this.xyChart)==null?void 0:t.backgroundColor)||this.background,titleColor:((r=this.xyChart)==null?void 0:r.titleColor)||this.primaryTextColor,xAxisTitleColor:((i=this.xyChart)==null?void 0:i.xAxisTitleColor)||this.primaryTextColor,xAxisLabelColor:((a=this.xyChart)==null?void 0:a.xAxisLabelColor)||this.primaryTextColor,xAxisTickColor:((n=this.xyChart)==null?void 0:n.xAxisTickColor)||this.primaryTextColor,xAxisLineColor:((o=this.xyChart)==null?void 0:o.xAxisLineColor)||this.primaryTextColor,yAxisTitleColor:((s=this.xyChart)==null?void 0:s.yAxisTitleColor)||this.primaryTextColor,yAxisLabelColor:((l=this.xyChart)==null?void 0:l.yAxisLabelColor)||this.primaryTextColor,yAxisTickColor:((c=this.xyChart)==null?void 0:c.yAxisTickColor)||this.primaryTextColor,yAxisLineColor:((h=this.xyChart)==null?void 0:h.yAxisLineColor)||this.primaryTextColor,plotColorPalette:((u=this.xyChart)==null?void 0:u.plotColorPalette)||"#EEE,#6BB8E4,#8ACB88,#C7ACD6,#E8DCC2,#FFB2A8,#FFF380,#7E8D91,#FFD8B1,#FAF3E0"},this.requirementBackground=this.requirementBackground||this.primaryColor,this.requirementBorderColor=this.requirementBorderColor||this.primaryBorderColor,this.requirementBorderSize=this.requirementBorderSize||"1",this.requirementTextColor=this.requirementTextColor||this.primaryTextColor,this.relationColor=this.relationColor||this.lineColor,this.relationLabelBackground=this.relationLabelBackground||this.edgeLabelBackground,this.relationLabelColor=this.relationLabelColor||this.actorTextColor,this.git0=Q(this.pie1,25)||this.primaryColor,this.git1=this.pie2||this.secondaryColor,this.git2=this.pie3||this.tertiaryColor,this.git3=this.pie4||w(this.primaryColor,{h:-30}),this.git4=this.pie5||w(this.primaryColor,{h:-60}),this.git5=this.pie6||w(this.primaryColor,{h:-90}),this.git6=this.pie7||w(this.primaryColor,{h:60}),this.git7=this.pie8||w(this.primaryColor,{h:120}),this.gitInv0=this.gitInv0||N(this.git0),this.gitInv1=this.gitInv1||N(this.git1),this.gitInv2=this.gitInv2||N(this.git2),this.gitInv3=this.gitInv3||N(this.git3),this.gitInv4=this.gitInv4||N(this.git4),this.gitInv5=this.gitInv5||N(this.git5),this.gitInv6=this.gitInv6||N(this.git6),this.gitInv7=this.gitInv7||N(this.git7),this.branchLabelColor=this.branchLabelColor||this.labelTextColor,this.gitBranchLabel0=this.branchLabelColor,this.gitBranchLabel1="white",this.gitBranchLabel2=this.branchLabelColor,this.gitBranchLabel3="white",this.gitBranchLabel4=this.branchLabelColor,this.gitBranchLabel5=this.branchLabelColor,this.gitBranchLabel6=this.branchLabelColor,this.gitBranchLabel7=this.branchLabelColor,this.tagLabelColor=this.tagLabelColor||this.primaryTextColor,this.tagLabelBackground=this.tagLabelBackground||this.primaryColor,this.tagLabelBorder=this.tagBorder||this.primaryBorderColor,this.tagLabelFontSize=this.tagLabelFontSize||"10px",this.commitLabelColor=this.commitLabelColor||this.secondaryTextColor,this.commitLabelBackground=this.commitLabelBackground||this.secondaryColor,this.commitLabelFontSize=this.commitLabelFontSize||"10px",this.attributeBackgroundColorOdd=this.attributeBackgroundColorOdd||Da,this.attributeBackgroundColorEven=this.attributeBackgroundColorEven||Ra}calculate(t){if(typeof t!="object"){this.updateColors();return}const r=Object.keys(t);r.forEach(i=>{this[i]=t[i]}),this.updateColors(),r.forEach(i=>{this[i]=t[i]})}},d(vi,"Theme"),vi),Ug=d(e=>{const t=new jg;return t.calculate(e),t},"getThemeVariables"),Le={base:{getThemeVariables:Ig},dark:{getThemeVariables:Ng},default:{getThemeVariables:Wg},forest:{getThemeVariables:Hg},neutral:{getThemeVariables:Ug}},Re={flowchart:{useMaxWidth:!0,titleTopMargin:25,subGraphTitleMargin:{top:0,bottom:0},diagramPadding:8,htmlLabels:!0,nodeSpacing:50,rankSpacing:50,curve:"basis",padding:15,defaultRenderer:"dagre-wrapper",wrappingWidth:200},sequence:{useMaxWidth:!0,hideUnusedParticipants:!1,activationWidth:10,diagramMarginX:50,diagramMarginY:10,actorMargin:50,width:150,height:65,boxMargin:10,boxTextMargin:5,noteMargin:10,messageMargin:35,messageAlign:"center",mirrorActors:!0,forceMenus:!1,bottomMarginAdj:1,rightAngles:!1,showSequenceNumbers:!1,actorFontSize:14,actorFontFamily:'"Open Sans", sans-serif',actorFontWeight:400,noteFontSize:14,noteFontFamily:'"trebuchet ms", verdana, arial, sans-serif',noteFontWeight:400,noteAlign:"center",messageFontSize:16,messageFontFamily:'"trebuchet ms", verdana, arial, sans-serif',messageFontWeight:400,wrap:!1,wrapPadding:10,labelBoxWidth:50,labelBoxHeight:20},gantt:{useMaxWidth:!0,titleTopMargin:25,barHeight:20,barGap:4,topPadding:50,rightPadding:75,leftPadding:75,gridLineStartPadding:35,fontSize:11,sectionFontSize:11,numberSectionStyles:4,axisFormat:"%Y-%m-%d",topAxis:!1,displayMode:"",weekday:"sunday"},journey:{useMaxWidth:!0,diagramMarginX:50,diagramMarginY:10,leftMargin:150,width:150,height:50,boxMargin:10,boxTextMargin:5,noteMargin:10,messageMargin:35,messageAlign:"center",bottomMarginAdj:1,rightAngles:!1,taskFontSize:14,taskFontFamily:'"Open Sans", sans-serif',taskMargin:50,activationWidth:10,textPlacement:"fo",actorColours:["#8FBC8F","#7CFC00","#00FFFF","#20B2AA","#B0E0E6","#FFFFE0"],sectionFills:["#191970","#8B008B","#4B0082","#2F4F4F","#800000","#8B4513","#00008B"],sectionColours:["#fff"]},class:{useMaxWidth:!0,titleTopMargin:25,arrowMarkerAbsolute:!1,dividerMargin:10,padding:5,textHeight:10,defaultRenderer:"dagre-wrapper",htmlLabels:!1,hideEmptyMembersBox:!1},state:{useMaxWidth:!0,titleTopMargin:25,dividerMargin:10,sizeUnit:5,padding:8,textHeight:10,titleShift:-15,noteMargin:10,forkWidth:70,forkHeight:7,miniPadding:2,fontSizeFactor:5.02,fontSize:24,labelHeight:16,edgeLengthFactor:"20",compositTitleSize:35,radius:5,defaultRenderer:"dagre-wrapper"},er:{useMaxWidth:!0,titleTopMargin:25,diagramPadding:20,layoutDirection:"TB",minEntityWidth:100,minEntityHeight:75,entityPadding:15,nodeSpacing:140,rankSpacing:80,stroke:"gray",fill:"honeydew",fontSize:12},pie:{useMaxWidth:!0,textPosition:.75},quadrantChart:{useMaxWidth:!0,chartWidth:500,chartHeight:500,titleFontSize:20,titlePadding:10,quadrantPadding:5,xAxisLabelPadding:5,yAxisLabelPadding:5,xAxisLabelFontSize:16,yAxisLabelFontSize:16,quadrantLabelFontSize:16,quadrantTextTopPadding:5,pointTextPadding:5,pointLabelFontSize:12,pointRadius:5,xAxisPosition:"top",yAxisPosition:"left",quadrantInternalBorderStrokeWidth:1,quadrantExternalBorderStrokeWidth:2},xyChart:{useMaxWidth:!0,width:700,height:500,titleFontSize:20,titlePadding:10,showTitle:!0,xAxis:{$ref:"#/$defs/XYChartAxisConfig",showLabel:!0,labelFontSize:14,labelPadding:5,showTitle:!0,titleFontSize:16,titlePadding:5,showTick:!0,tickLength:5,tickWidth:2,showAxisLine:!0,axisLineWidth:2},yAxis:{$ref:"#/$defs/XYChartAxisConfig",showLabel:!0,labelFontSize:14,labelPadding:5,showTitle:!0,titleFontSize:16,titlePadding:5,showTick:!0,tickLength:5,tickWidth:2,showAxisLine:!0,axisLineWidth:2},chartOrientation:"vertical",plotReservedSpacePercent:50},requirement:{useMaxWidth:!0,rect_fill:"#f9f9f9",text_color:"#333",rect_border_size:"0.5px",rect_border_color:"#bbb",rect_min_width:200,rect_min_height:200,fontSize:14,rect_padding:10,line_height:20},mindmap:{useMaxWidth:!0,padding:10,maxNodeWidth:200},kanban:{useMaxWidth:!0,padding:8,sectionWidth:200,ticketBaseUrl:""},timeline:{useMaxWidth:!0,diagramMarginX:50,diagramMarginY:10,leftMargin:150,width:150,height:50,boxMargin:10,boxTextMargin:5,noteMargin:10,messageMargin:35,messageAlign:"center",bottomMarginAdj:1,rightAngles:!1,taskFontSize:14,taskFontFamily:'"Open Sans", sans-serif',taskMargin:50,activationWidth:10,textPlacement:"fo",actorColours:["#8FBC8F","#7CFC00","#00FFFF","#20B2AA","#B0E0E6","#FFFFE0"],sectionFills:["#191970","#8B008B","#4B0082","#2F4F4F","#800000","#8B4513","#00008B"],sectionColours:["#fff"],disableMulticolor:!1},gitGraph:{useMaxWidth:!0,titleTopMargin:25,diagramPadding:8,nodeLabel:{width:75,height:100,x:-25,y:0},mainBranchName:"main",mainBranchOrder:0,showCommitLabel:!0,showBranches:!0,rotateCommitLabel:!0,parallelCommits:!1,arrowMarkerAbsolute:!1},c4:{useMaxWidth:!0,diagramMarginX:50,diagramMarginY:10,c4ShapeMargin:50,c4ShapePadding:20,width:216,height:60,boxMargin:10,c4ShapeInRow:4,nextLinePaddingX:0,c4BoundaryInRow:2,personFontSize:14,personFontFamily:'"Open Sans", sans-serif',personFontWeight:"normal",external_personFontSize:14,external_personFontFamily:'"Open Sans", sans-serif',external_personFontWeight:"normal",systemFontSize:14,systemFontFamily:'"Open Sans", sans-serif',systemFontWeight:"normal",external_systemFontSize:14,external_systemFontFamily:'"Open Sans", sans-serif',external_systemFontWeight:"normal",system_dbFontSize:14,system_dbFontFamily:'"Open Sans", sans-serif',system_dbFontWeight:"normal",external_system_dbFontSize:14,external_system_dbFontFamily:'"Open Sans", sans-serif',external_system_dbFontWeight:"normal",system_queueFontSize:14,system_queueFontFamily:'"Open Sans", sans-serif',system_queueFontWeight:"normal",external_system_queueFontSize:14,external_system_queueFontFamily:'"Open Sans", sans-serif',external_system_queueFontWeight:"normal",boundaryFontSize:14,boundaryFontFamily:'"Open Sans", sans-serif',boundaryFontWeight:"normal",messageFontSize:12,messageFontFamily:'"Open Sans", sans-serif',messageFontWeight:"normal",containerFontSize:14,containerFontFamily:'"Open Sans", sans-serif',containerFontWeight:"normal",external_containerFontSize:14,external_containerFontFamily:'"Open Sans", sans-serif',external_containerFontWeight:"normal",container_dbFontSize:14,container_dbFontFamily:'"Open Sans", sans-serif',container_dbFontWeight:"normal",external_container_dbFontSize:14,external_container_dbFontFamily:'"Open Sans", sans-serif',external_container_dbFontWeight:"normal",container_queueFontSize:14,container_queueFontFamily:'"Open Sans", sans-serif',container_queueFontWeight:"normal",external_container_queueFontSize:14,external_container_queueFontFamily:'"Open Sans", sans-serif',external_container_queueFontWeight:"normal",componentFontSize:14,componentFontFamily:'"Open Sans", sans-serif',componentFontWeight:"normal",external_componentFontSize:14,external_componentFontFamily:'"Open Sans", sans-serif',external_componentFontWeight:"normal",component_dbFontSize:14,component_dbFontFamily:'"Open Sans", sans-serif',component_dbFontWeight:"normal",external_component_dbFontSize:14,external_component_dbFontFamily:'"Open Sans", sans-serif',external_component_dbFontWeight:"normal",component_queueFontSize:14,component_queueFontFamily:'"Open Sans", sans-serif',component_queueFontWeight:"normal",external_component_queueFontSize:14,external_component_queueFontFamily:'"Open Sans", sans-serif',external_component_queueFontWeight:"normal",wrap:!0,wrapPadding:10,person_bg_color:"#08427B",person_border_color:"#073B6F",external_person_bg_color:"#686868",external_person_border_color:"#8A8A8A",system_bg_color:"#1168BD",system_border_color:"#3C7FC0",system_db_bg_color:"#1168BD",system_db_border_color:"#3C7FC0",system_queue_bg_color:"#1168BD",system_queue_border_color:"#3C7FC0",external_system_bg_color:"#999999",external_system_border_color:"#8A8A8A",external_system_db_bg_color:"#999999",external_system_db_border_color:"#8A8A8A",external_system_queue_bg_color:"#999999",external_system_queue_border_color:"#8A8A8A",container_bg_color:"#438DD5",container_border_color:"#3C7FC0",container_db_bg_color:"#438DD5",container_db_border_color:"#3C7FC0",container_queue_bg_color:"#438DD5",container_queue_border_color:"#3C7FC0",external_container_bg_color:"#B3B3B3",external_container_border_color:"#A6A6A6",external_container_db_bg_color:"#B3B3B3",external_container_db_border_color:"#A6A6A6",external_container_queue_bg_color:"#B3B3B3",external_container_queue_border_color:"#A6A6A6",component_bg_color:"#85BBF0",component_border_color:"#78A8D8",component_db_bg_color:"#85BBF0",component_db_border_color:"#78A8D8",component_queue_bg_color:"#85BBF0",component_queue_border_color:"#78A8D8",external_component_bg_color:"#CCCCCC",external_component_border_color:"#BFBFBF",external_component_db_bg_color:"#CCCCCC",external_component_db_border_color:"#BFBFBF",external_component_queue_bg_color:"#CCCCCC",external_component_queue_border_color:"#BFBFBF"},sankey:{useMaxWidth:!0,width:600,height:400,linkColor:"gradient",nodeAlignment:"justify",showValues:!0,prefix:"",suffix:""},block:{useMaxWidth:!0,padding:8},packet:{useMaxWidth:!0,rowHeight:32,bitWidth:32,bitsPerRow:32,showBits:!0,paddingX:5,paddingY:5},architecture:{useMaxWidth:!0,padding:40,iconSize:80,fontSize:16},theme:"default",look:"classic",handDrawnSeed:0,layout:"dagre",maxTextSize:5e4,maxEdges:500,darkMode:!1,fontFamily:'"trebuchet ms", verdana, arial, sans-serif;',logLevel:5,securityLevel:"strict",startOnLoad:!0,arrowMarkerAbsolute:!1,secure:["secure","securityLevel","startOnLoad","maxTextSize","suppressErrorRendering","maxEdges"],legacyMathML:!1,forceLegacyMathML:!1,deterministicIds:!1,fontSize:16,markdownAutoWrap:!0,suppressErrorRendering:!1},nc={...Re,deterministicIDSeed:void 0,elk:{mergeEdges:!1,nodePlacementStrategy:"BRANDES_KOEPF"},themeCSS:void 0,themeVariables:Le.default.getThemeVariables(),sequence:{...Re.sequence,messageFont:d(function(){return{fontFamily:this.messageFontFamily,fontSize:this.messageFontSize,fontWeight:this.messageFontWeight}},"messageFont"),noteFont:d(function(){return{fontFamily:this.noteFontFamily,fontSize:this.noteFontSize,fontWeight:this.noteFontWeight}},"noteFont"),actorFont:d(function(){return{fontFamily:this.actorFontFamily,fontSize:this.actorFontSize,fontWeight:this.actorFontWeight}},"actorFont")},class:{hideEmptyMembersBox:!1},gantt:{...Re.gantt,tickInterval:void 0,useWidth:void 0},c4:{...Re.c4,useWidth:void 0,personFont:d(function(){return{fontFamily:this.personFontFamily,fontSize:this.personFontSize,fontWeight:this.personFontWeight}},"personFont"),external_personFont:d(function(){return{fontFamily:this.external_personFontFamily,fontSize:this.external_personFontSize,fontWeight:this.external_personFontWeight}},"external_personFont"),systemFont:d(function(){return{fontFamily:this.systemFontFamily,fontSize:this.systemFontSize,fontWeight:this.systemFontWeight}},"systemFont"),external_systemFont:d(function(){return{fontFamily:this.external_systemFontFamily,fontSize:this.external_systemFontSize,fontWeight:this.external_systemFontWeight}},"external_systemFont"),system_dbFont:d(function(){return{fontFamily:this.system_dbFontFamily,fontSize:this.system_dbFontSize,fontWeight:this.system_dbFontWeight}},"system_dbFont"),external_system_dbFont:d(function(){return{fontFamily:this.external_system_dbFontFamily,fontSize:this.external_system_dbFontSize,fontWeight:this.external_system_dbFontWeight}},"external_system_dbFont"),system_queueFont:d(function(){return{fontFamily:this.system_queueFontFamily,fontSize:this.system_queueFontSize,fontWeight:this.system_queueFontWeight}},"system_queueFont"),external_system_queueFont:d(function(){return{fontFamily:this.external_system_queueFontFamily,fontSize:this.external_system_queueFontSize,fontWeight:this.external_system_queueFontWeight}},"external_system_queueFont"),containerFont:d(function(){return{fontFamily:this.containerFontFamily,fontSize:this.containerFontSize,fontWeight:this.containerFontWeight}},"containerFont"),external_containerFont:d(function(){return{fontFamily:this.external_containerFontFamily,fontSize:this.external_containerFontSize,fontWeight:this.external_containerFontWeight}},"external_containerFont"),container_dbFont:d(function(){return{fontFamily:this.container_dbFontFamily,fontSize:this.container_dbFontSize,fontWeight:this.container_dbFontWeight}},"container_dbFont"),external_container_dbFont:d(function(){return{fontFamily:this.external_container_dbFontFamily,fontSize:this.external_container_dbFontSize,fontWeight:this.external_container_dbFontWeight}},"external_container_dbFont"),container_queueFont:d(function(){return{fontFamily:this.container_queueFontFamily,fontSize:this.container_queueFontSize,fontWeight:this.container_queueFontWeight}},"container_queueFont"),external_container_queueFont:d(function(){return{fontFamily:this.external_container_queueFontFamily,fontSize:this.external_container_queueFontSize,fontWeight:this.external_container_queueFontWeight}},"external_container_queueFont"),componentFont:d(function(){return{fontFamily:this.componentFontFamily,fontSize:this.componentFontSize,fontWeight:this.componentFontWeight}},"componentFont"),external_componentFont:d(function(){return{fontFamily:this.external_componentFontFamily,fontSize:this.external_componentFontSize,fontWeight:this.external_componentFontWeight}},"external_componentFont"),component_dbFont:d(function(){return{fontFamily:this.component_dbFontFamily,fontSize:this.component_dbFontSize,fontWeight:this.component_dbFontWeight}},"component_dbFont"),external_component_dbFont:d(function(){return{fontFamily:this.external_component_dbFontFamily,fontSize:this.external_component_dbFontSize,fontWeight:this.external_component_dbFontWeight}},"external_component_dbFont"),component_queueFont:d(function(){return{fontFamily:this.component_queueFontFamily,fontSize:this.component_queueFontSize,fontWeight:this.component_queueFontWeight}},"component_queueFont"),external_component_queueFont:d(function(){return{fontFamily:this.external_component_queueFontFamily,fontSize:this.external_component_queueFontSize,fontWeight:this.external_component_queueFontWeight}},"external_component_queueFont"),boundaryFont:d(function(){return{fontFamily:this.boundaryFontFamily,fontSize:this.boundaryFontSize,fontWeight:this.boundaryFontWeight}},"boundaryFont"),messageFont:d(function(){return{fontFamily:this.messageFontFamily,fontSize:this.messageFontSize,fontWeight:this.messageFontWeight}},"messageFont")},pie:{...Re.pie,useWidth:984},xyChart:{...Re.xyChart,useWidth:void 0},requirement:{...Re.requirement,useWidth:void 0},packet:{...Re.packet}},sc=d((e,t="")=>Object.keys(e).reduce((r,i)=>Array.isArray(e[i])?r:typeof e[i]=="object"&&e[i]!==null?[...r,t+i,...sc(e[i],"")]:[...r,t+i],[]),"keyify"),Yg=new Set(sc(nc,"")),oc=nc,la=d(e=>{if(O.debug("sanitizeDirective called with",e),!(typeof e!="object"||e==null)){if(Array.isArray(e)){e.forEach(t=>la(t));return}for(const t of Object.keys(e)){if(O.debug("Checking key",t),t.startsWith("__")||t.includes("proto")||t.includes("constr")||!Yg.has(t)||e[t]==null){O.debug("sanitize deleting key: ",t),delete e[t];continue}if(typeof e[t]=="object"){O.debug("sanitizing object",t),la(e[t]);continue}const r=["themeCSS","fontFamily","altFontFamily"];for(const i of r)t.includes(i)&&(O.debug("sanitizing css option",t),e[t]=Gg(e[t]))}if(e.themeVariables)for(const t of Object.keys(e.themeVariables)){const r=e.themeVariables[t];r!=null&&r.match&&!r.match(/^[\d "#%(),.;A-Za-z]+$/)&&(e.themeVariables[t]="")}O.debug("After sanitization",e)}},"sanitizeDirective"),Gg=d(e=>{let t=0,r=0;for(const i of e){if(t<r)return"{ /* ERROR: Unbalanced CSS */ }";i==="{"?t++:i==="}"&&r++}return t!==r?"{ /* ERROR: Unbalanced CSS */ }":e},"sanitizeCss"),vr=Object.freeze(oc),Gt=Et({},vr),lc,Sr=[],ni=Et({},vr),Ia=d((e,t)=>{let r=Et({},e),i={};for(const a of t)uc(a),i=Et(i,a);if(r=Et(r,i),i.theme&&i.theme in Le){const a=Et({},lc),n=Et(a.themeVariables||{},i.themeVariables);r.theme&&r.theme in Le&&(r.themeVariables=Le[r.theme].getThemeVariables(n))}return ni=r,fc(ni),ni},"updateCurrentConfig"),Vg=d(e=>(Gt=Et({},vr),Gt=Et(Gt,e),e.theme&&Le[e.theme]&&(Gt.themeVariables=Le[e.theme].getThemeVariables(e.themeVariables)),Ia(Gt,Sr),Gt),"setSiteConfig"),Xg=d(e=>{lc=Et({},e)},"saveConfigFromInitialize"),Zg=d(e=>(Gt=Et(Gt,e),Ia(Gt,Sr),Gt),"updateSiteConfig"),cc=d(()=>Et({},Gt),"getSiteConfig"),hc=d(e=>(fc(e),Et(ni,e),Xt()),"setConfig"),Xt=d(()=>Et({},ni),"getConfig"),uc=d(e=>{e&&(["secure",...Gt.secure??[]].forEach(t=>{Object.hasOwn(e,t)&&(O.debug(`Denied attempt to modify a secure key ${t}`,e[t]),delete e[t])}),Object.keys(e).forEach(t=>{t.startsWith("__")&&delete e[t]}),Object.keys(e).forEach(t=>{typeof e[t]=="string"&&(e[t].includes("<")||e[t].includes(">")||e[t].includes("url(data:"))&&delete e[t],typeof e[t]=="object"&&uc(e[t])}))},"sanitize"),Kg=d(e=>{var t;la(e),e.fontFamily&&!((t=e.themeVariables)!=null&&t.fontFamily)&&(e.themeVariables={...e.themeVariables,fontFamily:e.fontFamily}),Sr.push(e),Ia(Gt,Sr)},"addDirective"),ca=d((e=Gt)=>{Sr=[],Ia(e,Sr)},"reset"),Qg={LAZY_LOAD_DEPRECATED:"The configuration options lazyLoadedDiagrams and loadExternalDiagramsAtStartup are deprecated. Please use registerExternalDiagrams instead."},zo={},Jg=d(e=>{zo[e]||(O.warn(Qg[e]),zo[e]=!0)},"issueWarning"),fc=d(e=>{e&&(e.lazyLoadedDiagrams||e.loadExternalDiagramsAtStartup)&&Jg("LAZY_LOAD_DEPRECATED")},"checkConfig"),Bi=/<br\s*\/?>/gi,tm=d(e=>e?gc(e).replace(/\\n/g,"#br#").split("#br#"):[""],"getRows"),em=(()=>{let e=!1;return()=>{e||(pc(),e=!0)}})();function pc(){const e="data-temp-href-target";kr.addHook("beforeSanitizeAttributes",t=>{t instanceof Element&&t.tagName==="A"&&t.hasAttribute("target")&&t.setAttribute(e,t.getAttribute("target")??"")}),kr.addHook("afterSanitizeAttributes",t=>{t instanceof Element&&t.tagName==="A"&&t.hasAttribute(e)&&(t.setAttribute("target",t.getAttribute(e)??""),t.removeAttribute(e),t.getAttribute("target")==="_blank"&&t.setAttribute("rel","noopener"))})}d(pc,"setupDompurifyHooks");var dc=d(e=>(em(),kr.sanitize(e)),"removeScript"),Wo=d((e,t)=>{var r;if(((r=t.flowchart)==null?void 0:r.htmlLabels)!==!1){const i=t.securityLevel;i==="antiscript"||i==="strict"?e=dc(e):i!=="loose"&&(e=gc(e),e=e.replace(/</g,"&lt;").replace(/>/g,"&gt;"),e=e.replace(/=/g,"&equals;"),e=nm(e))}return e},"sanitizeMore"),tr=d((e,t)=>e&&(t.dompurifyConfig?e=kr.sanitize(Wo(e,t),t.dompurifyConfig).toString():e=kr.sanitize(Wo(e,t),{FORBID_TAGS:["style"]}).toString(),e),"sanitizeText"),rm=d((e,t)=>typeof e=="string"?tr(e,t):e.flat().map(r=>tr(r,t)),"sanitizeTextOrArray"),im=d(e=>Bi.test(e),"hasBreaks"),am=d(e=>e.split(Bi),"splitBreaks"),nm=d(e=>e.replace(/#br#/g,"<br/>"),"placeholderToBreak"),gc=d(e=>e.replace(Bi,"#br#"),"breakToPlaceholder"),sm=d(e=>{let t="";return e&&(t=window.location.protocol+"//"+window.location.host+window.location.pathname+window.location.search,t=t.replaceAll(/\(/g,"\\("),t=t.replaceAll(/\)/g,"\\)")),t},"getUrl"),vt=d(e=>!(e===!1||["false","null","0"].includes(String(e).trim().toLowerCase())),"evaluate"),om=d(function(...e){const t=e.filter(r=>!isNaN(r));return Math.max(...t)},"getMax"),lm=d(function(...e){const t=e.filter(r=>!isNaN(r));return Math.min(...t)},"getMin"),qo=d(function(e){const t=e.split(/(,)/),r=[];for(let i=0;i<t.length;i++){let a=t[i];if(a===","&&i>0&&i+1<t.length){const n=t[i-1],o=t[i+1];cm(n,o)&&(a=n+","+o,i++,r.pop())}r.push(hm(a))}return r.join("")},"parseGenericTypes"),$n=d((e,t)=>Math.max(0,e.split(t).length-1),"countOccurrence"),cm=d((e,t)=>{const r=$n(e,"~"),i=$n(t,"~");return r===1&&i===1},"shouldCombineSets"),hm=d(e=>{const t=$n(e,"~");let r=!1;if(t<=1)return e;t%2!==0&&e.startsWith("~")&&(e=e.substring(1),r=!0);const i=[...e];let a=i.indexOf("~"),n=i.lastIndexOf("~");for(;a!==-1&&n!==-1&&a!==n;)i[a]="<",i[n]=">",a=i.indexOf("~"),n=i.lastIndexOf("~");return r&&i.unshift("~"),i.join("")},"processSet"),Ho=d(()=>window.MathMLElement!==void 0,"isMathMLSupported"),On=/\$\$(.*)\$\$/g,Tr=d(e=>{var t;return(((t=e.match(On))==null?void 0:t.length)??0)>0},"hasKatex"),_S=d(async(e,t)=>{e=await bs(e,t);const r=document.createElement("div");r.innerHTML=e,r.id="katex-temp",r.style.visibility="hidden",r.style.position="absolute",r.style.top="0";const i=document.querySelector("body");i==null||i.insertAdjacentElement("beforeend",r);const a={width:r.clientWidth,height:r.clientHeight};return r.remove(),a},"calculateMathMLDimensions"),bs=d(async(e,t)=>{if(!Tr(e))return e;if(!(Ho()||t.legacyMathML||t.forceLegacyMathML))return e.replace(On,"MathML is unsupported in this environment.");const{default:r}=await gt(()=>import("./katex.rPiVaalG.js"),[],import.meta.url),i=t.forceLegacyMathML||!Ho()&&t.legacyMathML?"htmlAndMathml":"mathml";return e.split(Bi).map(a=>Tr(a)?`<div style="display: flex; align-items: center; justify-content: center; white-space: nowrap;">${a}</div>`:`<div>${a}</div>`).join("").replace(On,(a,n)=>r.renderToString(n,{throwOnError:!0,displayMode:!0,output:i}).replace(/\n/g," ").replace(/<annotation.*<\/annotation>/g,""))},"renderKatex"),Er={getRows:tm,sanitizeText:tr,sanitizeTextOrArray:rm,hasBreaks:im,splitBreaks:am,lineBreakRegex:Bi,removeScript:dc,getUrl:sm,evaluate:vt,getMax:om,getMin:lm},um=d(function(e,t){for(let r of t)e.attr(r[0],r[1])},"d3Attrs"),fm=d(function(e,t,r){let i=new Map;return r?(i.set("width","100%"),i.set("style",`max-width: ${t}px;`)):(i.set("height",e),i.set("width",t)),i},"calculateSvgSizeAttrs"),mc=d(function(e,t,r,i){const a=fm(t,r,i);um(e,a)},"configureSvgSize"),pm=d(function(e,t,r,i){const a=t.node().getBBox(),n=a.width,o=a.height;O.info(`SVG bounds: ${n}x${o}`,a);let s=0,l=0;O.info(`Graph bounds: ${s}x${l}`,e),s=n+r*2,l=o+r*2,O.info(`Calculated bounds: ${s}x${l}`),mc(t,l,s,i);const c=`${a.x-r} ${a.y-r} ${a.width+2*r} ${a.height+2*r}`;t.attr("viewBox",c)},"setupGraphViewbox"),Qi={},dm=d((e,t,r)=>{let i="";return e in Qi&&Qi[e]?i=Qi[e](r):O.warn(`No theme found for ${e}`),` & {
    font-family: ${r.fontFamily};
    font-size: ${r.fontSize};
    fill: ${r.textColor}
  }
  @keyframes edge-animation-frame {
    from {
      stroke-dashoffset: 0;
    }
  }
  @keyframes dash {
    to {
      stroke-dashoffset: 0;
    }
  }
  & .edge-animation-slow {
    stroke-dasharray: 9,5 !important;
    stroke-dashoffset: 900;
    animation: dash 50s linear infinite;
    stroke-linecap: round;
  }
  & .edge-animation-fast {
    stroke-dasharray: 9,5 !important;
    stroke-dashoffset: 900;
    animation: dash 20s linear infinite;
    stroke-linecap: round;
  }
  /* Classes common for multiple diagrams */

  & .error-icon {
    fill: ${r.errorBkgColor};
  }
  & .error-text {
    fill: ${r.errorTextColor};
    stroke: ${r.errorTextColor};
  }

  & .edge-thickness-normal {
    stroke-width: 1px;
  }
  & .edge-thickness-thick {
    stroke-width: 3.5px
  }
  & .edge-pattern-solid {
    stroke-dasharray: 0;
  }
  & .edge-thickness-invisible {
    stroke-width: 0;
    fill: none;
  }
  & .edge-pattern-dashed{
    stroke-dasharray: 3;
  }
  .edge-pattern-dotted {
    stroke-dasharray: 2;
  }

  & .marker {
    fill: ${r.lineColor};
    stroke: ${r.lineColor};
  }
  & .marker.cross {
    stroke: ${r.lineColor};
  }

  & svg {
    font-family: ${r.fontFamily};
    font-size: ${r.fontSize};
  }
   & p {
    margin: 0
   }

  ${i}

  ${t}
`},"getStyles"),gm=d((e,t)=>{t!==void 0&&(Qi[e]=t)},"addStylesForDiagram"),mm=dm,yc={};$g(yc,{clear:()=>ym,getAccDescription:()=>km,getAccTitle:()=>bm,getDiagramTitle:()=>vm,setAccDescription:()=>Cm,setAccTitle:()=>xm,setDiagramTitle:()=>wm});var Cs="",ks="",ws="",vs=d(e=>tr(e,Xt()),"sanitizeText"),ym=d(()=>{Cs="",ws="",ks=""},"clear"),xm=d(e=>{Cs=vs(e).replace(/^\s+/g,"")},"setAccTitle"),bm=d(()=>Cs,"getAccTitle"),Cm=d(e=>{ws=vs(e).replace(/\n\s+/g,`
`)},"setAccDescription"),km=d(()=>ws,"getAccDescription"),wm=d(e=>{ks=vs(e)},"setDiagramTitle"),vm=d(()=>ks,"getDiagramTitle"),jo=O,Sm=ys,ut=Xt,BS=hc,LS=vr,Ss=d(e=>tr(e,ut()),"sanitizeText"),Tm=pm,_m=d(()=>yc,"getCommonDb"),ha={},ua=d((e,t,r)=>{var i;ha[e]&&jo.warn(`Diagram with id ${e} already registered. Overwriting.`),ha[e]=t,r&&ac(e,r),gm(e,t.styles),(i=t.injectUtils)==null||i.call(t,jo,Sm,ut,Ss,Tm,_m(),()=>{})},"registerDiagram"),Dn=d(e=>{if(e in ha)return ha[e];throw new Bm(e)},"getDiagram"),Si,Bm=(Si=class extends Error{constructor(t){super(`Diagram ${t} not found.`)}},d(Si,"DiagramNotFoundError"),Si);function Ts(e){return typeof e>"u"||e===null}d(Ts,"isNothing");function xc(e){return typeof e=="object"&&e!==null}d(xc,"isObject");function bc(e){return Array.isArray(e)?e:Ts(e)?[]:[e]}d(bc,"toArray");function Cc(e,t){var r,i,a,n;if(t)for(n=Object.keys(t),r=0,i=n.length;r<i;r+=1)a=n[r],e[a]=t[a];return e}d(Cc,"extend");function kc(e,t){var r="",i;for(i=0;i<t;i+=1)r+=e;return r}d(kc,"repeat");function wc(e){return e===0&&Number.NEGATIVE_INFINITY===1/e}d(wc,"isNegativeZero");var Lm=Ts,Am=xc,Mm=bc,Em=kc,Fm=wc,$m=Cc,wt={isNothing:Lm,isObject:Am,toArray:Mm,repeat:Em,isNegativeZero:Fm,extend:$m};function _s(e,t){var r="",i=e.reason||"(unknown reason)";return e.mark?(e.mark.name&&(r+='in "'+e.mark.name+'" '),r+="("+(e.mark.line+1)+":"+(e.mark.column+1)+")",!t&&e.mark.snippet&&(r+=`

`+e.mark.snippet),i+" "+r):i}d(_s,"formatError");function _r(e,t){Error.call(this),this.name="YAMLException",this.reason=e,this.mark=t,this.message=_s(this,!1),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack||""}d(_r,"YAMLException$1");_r.prototype=Object.create(Error.prototype);_r.prototype.constructor=_r;_r.prototype.toString=d(function(t){return this.name+": "+_s(this,t)},"toString");var Vt=_r;function Ji(e,t,r,i,a){var n="",o="",s=Math.floor(a/2)-1;return i-t>s&&(n=" ... ",t=i-s+n.length),r-i>s&&(o=" ...",r=i+s-o.length),{str:n+e.slice(t,r).replace(/\t/g,"→")+o,pos:i-t+n.length}}d(Ji,"getLine");function ta(e,t){return wt.repeat(" ",t-e.length)+e}d(ta,"padStart");function vc(e,t){if(t=Object.create(t||null),!e.buffer)return null;t.maxLength||(t.maxLength=79),typeof t.indent!="number"&&(t.indent=1),typeof t.linesBefore!="number"&&(t.linesBefore=3),typeof t.linesAfter!="number"&&(t.linesAfter=2);for(var r=/\r?\n|\r|\0/g,i=[0],a=[],n,o=-1;n=r.exec(e.buffer);)a.push(n.index),i.push(n.index+n[0].length),e.position<=n.index&&o<0&&(o=i.length-2);o<0&&(o=i.length-1);var s="",l,c,h=Math.min(e.line+t.linesAfter,a.length).toString().length,u=t.maxLength-(t.indent+h+3);for(l=1;l<=t.linesBefore&&!(o-l<0);l++)c=Ji(e.buffer,i[o-l],a[o-l],e.position-(i[o]-i[o-l]),u),s=wt.repeat(" ",t.indent)+ta((e.line-l+1).toString(),h)+" | "+c.str+`
`+s;for(c=Ji(e.buffer,i[o],a[o],e.position,u),s+=wt.repeat(" ",t.indent)+ta((e.line+1).toString(),h)+" | "+c.str+`
`,s+=wt.repeat("-",t.indent+h+3+c.pos)+`^
`,l=1;l<=t.linesAfter&&!(o+l>=a.length);l++)c=Ji(e.buffer,i[o+l],a[o+l],e.position-(i[o]-i[o+l]),u),s+=wt.repeat(" ",t.indent)+ta((e.line+l+1).toString(),h)+" | "+c.str+`
`;return s.replace(/\n$/,"")}d(vc,"makeSnippet");var Om=vc,Dm=["kind","multi","resolve","construct","instanceOf","predicate","represent","representName","defaultStyle","styleAliases"],Rm=["scalar","sequence","mapping"];function Sc(e){var t={};return e!==null&&Object.keys(e).forEach(function(r){e[r].forEach(function(i){t[String(i)]=r})}),t}d(Sc,"compileStyleAliases");function Tc(e,t){if(t=t||{},Object.keys(t).forEach(function(r){if(Dm.indexOf(r)===-1)throw new Vt('Unknown option "'+r+'" is met in definition of "'+e+'" YAML type.')}),this.options=t,this.tag=e,this.kind=t.kind||null,this.resolve=t.resolve||function(){return!0},this.construct=t.construct||function(r){return r},this.instanceOf=t.instanceOf||null,this.predicate=t.predicate||null,this.represent=t.represent||null,this.representName=t.representName||null,this.defaultStyle=t.defaultStyle||null,this.multi=t.multi||!1,this.styleAliases=Sc(t.styleAliases||null),Rm.indexOf(this.kind)===-1)throw new Vt('Unknown kind "'+this.kind+'" is specified for "'+e+'" YAML type.')}d(Tc,"Type$1");var Dt=Tc;function Rn(e,t){var r=[];return e[t].forEach(function(i){var a=r.length;r.forEach(function(n,o){n.tag===i.tag&&n.kind===i.kind&&n.multi===i.multi&&(a=o)}),r[a]=i}),r}d(Rn,"compileList");function _c(){var e={scalar:{},sequence:{},mapping:{},fallback:{},multi:{scalar:[],sequence:[],mapping:[],fallback:[]}},t,r;function i(a){a.multi?(e.multi[a.kind].push(a),e.multi.fallback.push(a)):e[a.kind][a.tag]=e.fallback[a.tag]=a}for(d(i,"collectType"),t=0,r=arguments.length;t<r;t+=1)arguments[t].forEach(i);return e}d(_c,"compileMap");function fa(e){return this.extend(e)}d(fa,"Schema$1");fa.prototype.extend=d(function(t){var r=[],i=[];if(t instanceof Dt)i.push(t);else if(Array.isArray(t))i=i.concat(t);else if(t&&(Array.isArray(t.implicit)||Array.isArray(t.explicit)))t.implicit&&(r=r.concat(t.implicit)),t.explicit&&(i=i.concat(t.explicit));else throw new Vt("Schema.extend argument should be a Type, [ Type ], or a schema definition ({ implicit: [...], explicit: [...] })");r.forEach(function(n){if(!(n instanceof Dt))throw new Vt("Specified list of YAML types (or a single Type object) contains a non-Type object.");if(n.loadKind&&n.loadKind!=="scalar")throw new Vt("There is a non-scalar type in the implicit list of a schema. Implicit resolving of such types is not supported.");if(n.multi)throw new Vt("There is a multi type in the implicit list of a schema. Multi tags can only be listed as explicit.")}),i.forEach(function(n){if(!(n instanceof Dt))throw new Vt("Specified list of YAML types (or a single Type object) contains a non-Type object.")});var a=Object.create(fa.prototype);return a.implicit=(this.implicit||[]).concat(r),a.explicit=(this.explicit||[]).concat(i),a.compiledImplicit=Rn(a,"implicit"),a.compiledExplicit=Rn(a,"explicit"),a.compiledTypeMap=_c(a.compiledImplicit,a.compiledExplicit),a},"extend");var Im=fa,Pm=new Dt("tag:yaml.org,2002:str",{kind:"scalar",construct:d(function(e){return e!==null?e:""},"construct")}),Nm=new Dt("tag:yaml.org,2002:seq",{kind:"sequence",construct:d(function(e){return e!==null?e:[]},"construct")}),zm=new Dt("tag:yaml.org,2002:map",{kind:"mapping",construct:d(function(e){return e!==null?e:{}},"construct")}),Wm=new Im({explicit:[Pm,Nm,zm]});function Bc(e){if(e===null)return!0;var t=e.length;return t===1&&e==="~"||t===4&&(e==="null"||e==="Null"||e==="NULL")}d(Bc,"resolveYamlNull");function Lc(){return null}d(Lc,"constructYamlNull");function Ac(e){return e===null}d(Ac,"isNull");var qm=new Dt("tag:yaml.org,2002:null",{kind:"scalar",resolve:Bc,construct:Lc,predicate:Ac,represent:{canonical:d(function(){return"~"},"canonical"),lowercase:d(function(){return"null"},"lowercase"),uppercase:d(function(){return"NULL"},"uppercase"),camelcase:d(function(){return"Null"},"camelcase"),empty:d(function(){return""},"empty")},defaultStyle:"lowercase"});function Mc(e){if(e===null)return!1;var t=e.length;return t===4&&(e==="true"||e==="True"||e==="TRUE")||t===5&&(e==="false"||e==="False"||e==="FALSE")}d(Mc,"resolveYamlBoolean");function Ec(e){return e==="true"||e==="True"||e==="TRUE"}d(Ec,"constructYamlBoolean");function Fc(e){return Object.prototype.toString.call(e)==="[object Boolean]"}d(Fc,"isBoolean");var Hm=new Dt("tag:yaml.org,2002:bool",{kind:"scalar",resolve:Mc,construct:Ec,predicate:Fc,represent:{lowercase:d(function(e){return e?"true":"false"},"lowercase"),uppercase:d(function(e){return e?"TRUE":"FALSE"},"uppercase"),camelcase:d(function(e){return e?"True":"False"},"camelcase")},defaultStyle:"lowercase"});function $c(e){return 48<=e&&e<=57||65<=e&&e<=70||97<=e&&e<=102}d($c,"isHexCode");function Oc(e){return 48<=e&&e<=55}d(Oc,"isOctCode");function Dc(e){return 48<=e&&e<=57}d(Dc,"isDecCode");function Rc(e){if(e===null)return!1;var t=e.length,r=0,i=!1,a;if(!t)return!1;if(a=e[r],(a==="-"||a==="+")&&(a=e[++r]),a==="0"){if(r+1===t)return!0;if(a=e[++r],a==="b"){for(r++;r<t;r++)if(a=e[r],a!=="_"){if(a!=="0"&&a!=="1")return!1;i=!0}return i&&a!=="_"}if(a==="x"){for(r++;r<t;r++)if(a=e[r],a!=="_"){if(!$c(e.charCodeAt(r)))return!1;i=!0}return i&&a!=="_"}if(a==="o"){for(r++;r<t;r++)if(a=e[r],a!=="_"){if(!Oc(e.charCodeAt(r)))return!1;i=!0}return i&&a!=="_"}}if(a==="_")return!1;for(;r<t;r++)if(a=e[r],a!=="_"){if(!Dc(e.charCodeAt(r)))return!1;i=!0}return!(!i||a==="_")}d(Rc,"resolveYamlInteger");function Ic(e){var t=e,r=1,i;if(t.indexOf("_")!==-1&&(t=t.replace(/_/g,"")),i=t[0],(i==="-"||i==="+")&&(i==="-"&&(r=-1),t=t.slice(1),i=t[0]),t==="0")return 0;if(i==="0"){if(t[1]==="b")return r*parseInt(t.slice(2),2);if(t[1]==="x")return r*parseInt(t.slice(2),16);if(t[1]==="o")return r*parseInt(t.slice(2),8)}return r*parseInt(t,10)}d(Ic,"constructYamlInteger");function Pc(e){return Object.prototype.toString.call(e)==="[object Number]"&&e%1===0&&!wt.isNegativeZero(e)}d(Pc,"isInteger");var jm=new Dt("tag:yaml.org,2002:int",{kind:"scalar",resolve:Rc,construct:Ic,predicate:Pc,represent:{binary:d(function(e){return e>=0?"0b"+e.toString(2):"-0b"+e.toString(2).slice(1)},"binary"),octal:d(function(e){return e>=0?"0o"+e.toString(8):"-0o"+e.toString(8).slice(1)},"octal"),decimal:d(function(e){return e.toString(10)},"decimal"),hexadecimal:d(function(e){return e>=0?"0x"+e.toString(16).toUpperCase():"-0x"+e.toString(16).toUpperCase().slice(1)},"hexadecimal")},defaultStyle:"decimal",styleAliases:{binary:[2,"bin"],octal:[8,"oct"],decimal:[10,"dec"],hexadecimal:[16,"hex"]}}),Um=new RegExp("^(?:[-+]?(?:[0-9][0-9_]*)(?:\\.[0-9_]*)?(?:[eE][-+]?[0-9]+)?|\\.[0-9_]+(?:[eE][-+]?[0-9]+)?|[-+]?\\.(?:inf|Inf|INF)|\\.(?:nan|NaN|NAN))$");function Nc(e){return!(e===null||!Um.test(e)||e[e.length-1]==="_")}d(Nc,"resolveYamlFloat");function zc(e){var t,r;return t=e.replace(/_/g,"").toLowerCase(),r=t[0]==="-"?-1:1,"+-".indexOf(t[0])>=0&&(t=t.slice(1)),t===".inf"?r===1?Number.POSITIVE_INFINITY:Number.NEGATIVE_INFINITY:t===".nan"?NaN:r*parseFloat(t,10)}d(zc,"constructYamlFloat");var Ym=/^[-+]?[0-9]+e/;function Wc(e,t){var r;if(isNaN(e))switch(t){case"lowercase":return".nan";case"uppercase":return".NAN";case"camelcase":return".NaN"}else if(Number.POSITIVE_INFINITY===e)switch(t){case"lowercase":return".inf";case"uppercase":return".INF";case"camelcase":return".Inf"}else if(Number.NEGATIVE_INFINITY===e)switch(t){case"lowercase":return"-.inf";case"uppercase":return"-.INF";case"camelcase":return"-.Inf"}else if(wt.isNegativeZero(e))return"-0.0";return r=e.toString(10),Ym.test(r)?r.replace("e",".e"):r}d(Wc,"representYamlFloat");function qc(e){return Object.prototype.toString.call(e)==="[object Number]"&&(e%1!==0||wt.isNegativeZero(e))}d(qc,"isFloat");var Gm=new Dt("tag:yaml.org,2002:float",{kind:"scalar",resolve:Nc,construct:zc,predicate:qc,represent:Wc,defaultStyle:"lowercase"}),Hc=Wm.extend({implicit:[qm,Hm,jm,Gm]}),Vm=Hc,jc=new RegExp("^([0-9][0-9][0-9][0-9])-([0-9][0-9])-([0-9][0-9])$"),Uc=new RegExp("^([0-9][0-9][0-9][0-9])-([0-9][0-9]?)-([0-9][0-9]?)(?:[Tt]|[ \\t]+)([0-9][0-9]?):([0-9][0-9]):([0-9][0-9])(?:\\.([0-9]*))?(?:[ \\t]*(Z|([-+])([0-9][0-9]?)(?::([0-9][0-9]))?))?$");function Yc(e){return e===null?!1:jc.exec(e)!==null||Uc.exec(e)!==null}d(Yc,"resolveYamlTimestamp");function Gc(e){var t,r,i,a,n,o,s,l=0,c=null,h,u,f;if(t=jc.exec(e),t===null&&(t=Uc.exec(e)),t===null)throw new Error("Date resolve error");if(r=+t[1],i=+t[2]-1,a=+t[3],!t[4])return new Date(Date.UTC(r,i,a));if(n=+t[4],o=+t[5],s=+t[6],t[7]){for(l=t[7].slice(0,3);l.length<3;)l+="0";l=+l}return t[9]&&(h=+t[10],u=+(t[11]||0),c=(h*60+u)*6e4,t[9]==="-"&&(c=-c)),f=new Date(Date.UTC(r,i,a,n,o,s,l)),c&&f.setTime(f.getTime()-c),f}d(Gc,"constructYamlTimestamp");function Vc(e){return e.toISOString()}d(Vc,"representYamlTimestamp");var Xm=new Dt("tag:yaml.org,2002:timestamp",{kind:"scalar",resolve:Yc,construct:Gc,instanceOf:Date,represent:Vc});function Xc(e){return e==="<<"||e===null}d(Xc,"resolveYamlMerge");var Zm=new Dt("tag:yaml.org,2002:merge",{kind:"scalar",resolve:Xc}),Bs=`ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=
\r`;function Zc(e){if(e===null)return!1;var t,r,i=0,a=e.length,n=Bs;for(r=0;r<a;r++)if(t=n.indexOf(e.charAt(r)),!(t>64)){if(t<0)return!1;i+=6}return i%8===0}d(Zc,"resolveYamlBinary");function Kc(e){var t,r,i=e.replace(/[\r\n=]/g,""),a=i.length,n=Bs,o=0,s=[];for(t=0;t<a;t++)t%4===0&&t&&(s.push(o>>16&255),s.push(o>>8&255),s.push(o&255)),o=o<<6|n.indexOf(i.charAt(t));return r=a%4*6,r===0?(s.push(o>>16&255),s.push(o>>8&255),s.push(o&255)):r===18?(s.push(o>>10&255),s.push(o>>2&255)):r===12&&s.push(o>>4&255),new Uint8Array(s)}d(Kc,"constructYamlBinary");function Qc(e){var t="",r=0,i,a,n=e.length,o=Bs;for(i=0;i<n;i++)i%3===0&&i&&(t+=o[r>>18&63],t+=o[r>>12&63],t+=o[r>>6&63],t+=o[r&63]),r=(r<<8)+e[i];return a=n%3,a===0?(t+=o[r>>18&63],t+=o[r>>12&63],t+=o[r>>6&63],t+=o[r&63]):a===2?(t+=o[r>>10&63],t+=o[r>>4&63],t+=o[r<<2&63],t+=o[64]):a===1&&(t+=o[r>>2&63],t+=o[r<<4&63],t+=o[64],t+=o[64]),t}d(Qc,"representYamlBinary");function Jc(e){return Object.prototype.toString.call(e)==="[object Uint8Array]"}d(Jc,"isBinary");var Km=new Dt("tag:yaml.org,2002:binary",{kind:"scalar",resolve:Zc,construct:Kc,predicate:Jc,represent:Qc}),Qm=Object.prototype.hasOwnProperty,Jm=Object.prototype.toString;function th(e){if(e===null)return!0;var t=[],r,i,a,n,o,s=e;for(r=0,i=s.length;r<i;r+=1){if(a=s[r],o=!1,Jm.call(a)!=="[object Object]")return!1;for(n in a)if(Qm.call(a,n))if(!o)o=!0;else return!1;if(!o)return!1;if(t.indexOf(n)===-1)t.push(n);else return!1}return!0}d(th,"resolveYamlOmap");function eh(e){return e!==null?e:[]}d(eh,"constructYamlOmap");var ty=new Dt("tag:yaml.org,2002:omap",{kind:"sequence",resolve:th,construct:eh}),ey=Object.prototype.toString;function rh(e){if(e===null)return!0;var t,r,i,a,n,o=e;for(n=new Array(o.length),t=0,r=o.length;t<r;t+=1){if(i=o[t],ey.call(i)!=="[object Object]"||(a=Object.keys(i),a.length!==1))return!1;n[t]=[a[0],i[a[0]]]}return!0}d(rh,"resolveYamlPairs");function ih(e){if(e===null)return[];var t,r,i,a,n,o=e;for(n=new Array(o.length),t=0,r=o.length;t<r;t+=1)i=o[t],a=Object.keys(i),n[t]=[a[0],i[a[0]]];return n}d(ih,"constructYamlPairs");var ry=new Dt("tag:yaml.org,2002:pairs",{kind:"sequence",resolve:rh,construct:ih}),iy=Object.prototype.hasOwnProperty;function ah(e){if(e===null)return!0;var t,r=e;for(t in r)if(iy.call(r,t)&&r[t]!==null)return!1;return!0}d(ah,"resolveYamlSet");function nh(e){return e!==null?e:{}}d(nh,"constructYamlSet");var ay=new Dt("tag:yaml.org,2002:set",{kind:"mapping",resolve:ah,construct:nh}),sh=Vm.extend({implicit:[Xm,Zm],explicit:[Km,ty,ry,ay]}),ze=Object.prototype.hasOwnProperty,pa=1,oh=2,lh=3,da=4,yn=1,ny=2,Uo=3,sy=/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F-\x84\x86-\x9F\uFFFE\uFFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF]/,oy=/[\x85\u2028\u2029]/,ly=/[,\[\]\{\}]/,ch=/^(?:!|!!|![a-z\-]+!)$/i,hh=/^(?:!|[^,\[\]\{\}])(?:%[0-9a-f]{2}|[0-9a-z\-#;\/\?:@&=\+\$,_\.!~\*'\(\)\[\]])*$/i;function In(e){return Object.prototype.toString.call(e)}d(In,"_class");function ce(e){return e===10||e===13}d(ce,"is_EOL");function Ne(e){return e===9||e===32}d(Ne,"is_WHITE_SPACE");function zt(e){return e===9||e===32||e===10||e===13}d(zt,"is_WS_OR_EOL");function Ke(e){return e===44||e===91||e===93||e===123||e===125}d(Ke,"is_FLOW_INDICATOR");function uh(e){var t;return 48<=e&&e<=57?e-48:(t=e|32,97<=t&&t<=102?t-97+10:-1)}d(uh,"fromHexCode");function fh(e){return e===120?2:e===117?4:e===85?8:0}d(fh,"escapedHexLen");function ph(e){return 48<=e&&e<=57?e-48:-1}d(ph,"fromDecimalCode");function Pn(e){return e===48?"\0":e===97?"\x07":e===98?"\b":e===116||e===9?"	":e===110?`
`:e===118?"\v":e===102?"\f":e===114?"\r":e===101?"\x1B":e===32?" ":e===34?'"':e===47?"/":e===92?"\\":e===78?"":e===95?" ":e===76?"\u2028":e===80?"\u2029":""}d(Pn,"simpleEscapeSequence");function dh(e){return e<=65535?String.fromCharCode(e):String.fromCharCode((e-65536>>10)+55296,(e-65536&1023)+56320)}d(dh,"charFromCodepoint");var gh=new Array(256),mh=new Array(256);for(Ye=0;Ye<256;Ye++)gh[Ye]=Pn(Ye)?1:0,mh[Ye]=Pn(Ye);var Ye;function yh(e,t){this.input=e,this.filename=t.filename||null,this.schema=t.schema||sh,this.onWarning=t.onWarning||null,this.legacy=t.legacy||!1,this.json=t.json||!1,this.listener=t.listener||null,this.implicitTypes=this.schema.compiledImplicit,this.typeMap=this.schema.compiledTypeMap,this.length=e.length,this.position=0,this.line=0,this.lineStart=0,this.lineIndent=0,this.firstTabInLine=-1,this.documents=[]}d(yh,"State$1");function Ls(e,t){var r={name:e.filename,buffer:e.input.slice(0,-1),position:e.position,line:e.line,column:e.position-e.lineStart};return r.snippet=Om(r),new Vt(t,r)}d(Ls,"generateError");function Z(e,t){throw Ls(e,t)}d(Z,"throwError");function hi(e,t){e.onWarning&&e.onWarning.call(null,Ls(e,t))}d(hi,"throwWarning");var Yo={YAML:d(function(t,r,i){var a,n,o;t.version!==null&&Z(t,"duplication of %YAML directive"),i.length!==1&&Z(t,"YAML directive accepts exactly one argument"),a=/^([0-9]+)\.([0-9]+)$/.exec(i[0]),a===null&&Z(t,"ill-formed argument of the YAML directive"),n=parseInt(a[1],10),o=parseInt(a[2],10),n!==1&&Z(t,"unacceptable YAML version of the document"),t.version=i[0],t.checkLineBreaks=o<2,o!==1&&o!==2&&hi(t,"unsupported YAML version of the document")},"handleYamlDirective"),TAG:d(function(t,r,i){var a,n;i.length!==2&&Z(t,"TAG directive accepts exactly two arguments"),a=i[0],n=i[1],ch.test(a)||Z(t,"ill-formed tag handle (first argument) of the TAG directive"),ze.call(t.tagMap,a)&&Z(t,'there is a previously declared suffix for "'+a+'" tag handle'),hh.test(n)||Z(t,"ill-formed tag prefix (second argument) of the TAG directive");try{n=decodeURIComponent(n)}catch{Z(t,"tag prefix is malformed: "+n)}t.tagMap[a]=n},"handleTagDirective")};function Ae(e,t,r,i){var a,n,o,s;if(t<r){if(s=e.input.slice(t,r),i)for(a=0,n=s.length;a<n;a+=1)o=s.charCodeAt(a),o===9||32<=o&&o<=1114111||Z(e,"expected valid JSON character");else sy.test(s)&&Z(e,"the stream contains non-printable characters");e.result+=s}}d(Ae,"captureSegment");function Nn(e,t,r,i){var a,n,o,s;for(wt.isObject(r)||Z(e,"cannot merge mappings; the provided source object is unacceptable"),a=Object.keys(r),o=0,s=a.length;o<s;o+=1)n=a[o],ze.call(t,n)||(t[n]=r[n],i[n]=!0)}d(Nn,"mergeMappings");function Qe(e,t,r,i,a,n,o,s,l){var c,h;if(Array.isArray(a))for(a=Array.prototype.slice.call(a),c=0,h=a.length;c<h;c+=1)Array.isArray(a[c])&&Z(e,"nested arrays are not supported inside keys"),typeof a=="object"&&In(a[c])==="[object Object]"&&(a[c]="[object Object]");if(typeof a=="object"&&In(a)==="[object Object]"&&(a="[object Object]"),a=String(a),t===null&&(t={}),i==="tag:yaml.org,2002:merge")if(Array.isArray(n))for(c=0,h=n.length;c<h;c+=1)Nn(e,t,n[c],r);else Nn(e,t,n,r);else!e.json&&!ze.call(r,a)&&ze.call(t,a)&&(e.line=o||e.line,e.lineStart=s||e.lineStart,e.position=l||e.position,Z(e,"duplicated mapping key")),a==="__proto__"?Object.defineProperty(t,a,{configurable:!0,enumerable:!0,writable:!0,value:n}):t[a]=n,delete r[a];return t}d(Qe,"storeMappingPair");function Pa(e){var t;t=e.input.charCodeAt(e.position),t===10?e.position++:t===13?(e.position++,e.input.charCodeAt(e.position)===10&&e.position++):Z(e,"a line break is expected"),e.line+=1,e.lineStart=e.position,e.firstTabInLine=-1}d(Pa,"readLineBreak");function Ct(e,t,r){for(var i=0,a=e.input.charCodeAt(e.position);a!==0;){for(;Ne(a);)a===9&&e.firstTabInLine===-1&&(e.firstTabInLine=e.position),a=e.input.charCodeAt(++e.position);if(t&&a===35)do a=e.input.charCodeAt(++e.position);while(a!==10&&a!==13&&a!==0);if(ce(a))for(Pa(e),a=e.input.charCodeAt(e.position),i++,e.lineIndent=0;a===32;)e.lineIndent++,a=e.input.charCodeAt(++e.position);else break}return r!==-1&&i!==0&&e.lineIndent<r&&hi(e,"deficient indentation"),i}d(Ct,"skipSeparationSpace");function Li(e){var t=e.position,r;return r=e.input.charCodeAt(t),!!((r===45||r===46)&&r===e.input.charCodeAt(t+1)&&r===e.input.charCodeAt(t+2)&&(t+=3,r=e.input.charCodeAt(t),r===0||zt(r)))}d(Li,"testDocumentSeparator");function Na(e,t){t===1?e.result+=" ":t>1&&(e.result+=wt.repeat(`
`,t-1))}d(Na,"writeFoldedLines");function xh(e,t,r){var i,a,n,o,s,l,c,h,u=e.kind,f=e.result,p;if(p=e.input.charCodeAt(e.position),zt(p)||Ke(p)||p===35||p===38||p===42||p===33||p===124||p===62||p===39||p===34||p===37||p===64||p===96||(p===63||p===45)&&(a=e.input.charCodeAt(e.position+1),zt(a)||r&&Ke(a)))return!1;for(e.kind="scalar",e.result="",n=o=e.position,s=!1;p!==0;){if(p===58){if(a=e.input.charCodeAt(e.position+1),zt(a)||r&&Ke(a))break}else if(p===35){if(i=e.input.charCodeAt(e.position-1),zt(i))break}else{if(e.position===e.lineStart&&Li(e)||r&&Ke(p))break;if(ce(p))if(l=e.line,c=e.lineStart,h=e.lineIndent,Ct(e,!1,-1),e.lineIndent>=t){s=!0,p=e.input.charCodeAt(e.position);continue}else{e.position=o,e.line=l,e.lineStart=c,e.lineIndent=h;break}}s&&(Ae(e,n,o,!1),Na(e,e.line-l),n=o=e.position,s=!1),Ne(p)||(o=e.position+1),p=e.input.charCodeAt(++e.position)}return Ae(e,n,o,!1),e.result?!0:(e.kind=u,e.result=f,!1)}d(xh,"readPlainScalar");function bh(e,t){var r,i,a;if(r=e.input.charCodeAt(e.position),r!==39)return!1;for(e.kind="scalar",e.result="",e.position++,i=a=e.position;(r=e.input.charCodeAt(e.position))!==0;)if(r===39)if(Ae(e,i,e.position,!0),r=e.input.charCodeAt(++e.position),r===39)i=e.position,e.position++,a=e.position;else return!0;else ce(r)?(Ae(e,i,a,!0),Na(e,Ct(e,!1,t)),i=a=e.position):e.position===e.lineStart&&Li(e)?Z(e,"unexpected end of the document within a single quoted scalar"):(e.position++,a=e.position);Z(e,"unexpected end of the stream within a single quoted scalar")}d(bh,"readSingleQuotedScalar");function Ch(e,t){var r,i,a,n,o,s;if(s=e.input.charCodeAt(e.position),s!==34)return!1;for(e.kind="scalar",e.result="",e.position++,r=i=e.position;(s=e.input.charCodeAt(e.position))!==0;){if(s===34)return Ae(e,r,e.position,!0),e.position++,!0;if(s===92){if(Ae(e,r,e.position,!0),s=e.input.charCodeAt(++e.position),ce(s))Ct(e,!1,t);else if(s<256&&gh[s])e.result+=mh[s],e.position++;else if((o=fh(s))>0){for(a=o,n=0;a>0;a--)s=e.input.charCodeAt(++e.position),(o=uh(s))>=0?n=(n<<4)+o:Z(e,"expected hexadecimal character");e.result+=dh(n),e.position++}else Z(e,"unknown escape sequence");r=i=e.position}else ce(s)?(Ae(e,r,i,!0),Na(e,Ct(e,!1,t)),r=i=e.position):e.position===e.lineStart&&Li(e)?Z(e,"unexpected end of the document within a double quoted scalar"):(e.position++,i=e.position)}Z(e,"unexpected end of the stream within a double quoted scalar")}d(Ch,"readDoubleQuotedScalar");function kh(e,t){var r=!0,i,a,n,o=e.tag,s,l=e.anchor,c,h,u,f,p,g=Object.create(null),m,y,x,b;if(b=e.input.charCodeAt(e.position),b===91)h=93,p=!1,s=[];else if(b===123)h=125,p=!0,s={};else return!1;for(e.anchor!==null&&(e.anchorMap[e.anchor]=s),b=e.input.charCodeAt(++e.position);b!==0;){if(Ct(e,!0,t),b=e.input.charCodeAt(e.position),b===h)return e.position++,e.tag=o,e.anchor=l,e.kind=p?"mapping":"sequence",e.result=s,!0;r?b===44&&Z(e,"expected the node content, but found ','"):Z(e,"missed comma between flow collection entries"),y=m=x=null,u=f=!1,b===63&&(c=e.input.charCodeAt(e.position+1),zt(c)&&(u=f=!0,e.position++,Ct(e,!0,t))),i=e.line,a=e.lineStart,n=e.position,er(e,t,pa,!1,!0),y=e.tag,m=e.result,Ct(e,!0,t),b=e.input.charCodeAt(e.position),(f||e.line===i)&&b===58&&(u=!0,b=e.input.charCodeAt(++e.position),Ct(e,!0,t),er(e,t,pa,!1,!0),x=e.result),p?Qe(e,s,g,y,m,x,i,a,n):u?s.push(Qe(e,null,g,y,m,x,i,a,n)):s.push(m),Ct(e,!0,t),b=e.input.charCodeAt(e.position),b===44?(r=!0,b=e.input.charCodeAt(++e.position)):r=!1}Z(e,"unexpected end of the stream within a flow collection")}d(kh,"readFlowCollection");function wh(e,t){var r,i,a=yn,n=!1,o=!1,s=t,l=0,c=!1,h,u;if(u=e.input.charCodeAt(e.position),u===124)i=!1;else if(u===62)i=!0;else return!1;for(e.kind="scalar",e.result="";u!==0;)if(u=e.input.charCodeAt(++e.position),u===43||u===45)yn===a?a=u===43?Uo:ny:Z(e,"repeat of a chomping mode identifier");else if((h=ph(u))>=0)h===0?Z(e,"bad explicit indentation width of a block scalar; it cannot be less than one"):o?Z(e,"repeat of an indentation width identifier"):(s=t+h-1,o=!0);else break;if(Ne(u)){do u=e.input.charCodeAt(++e.position);while(Ne(u));if(u===35)do u=e.input.charCodeAt(++e.position);while(!ce(u)&&u!==0)}for(;u!==0;){for(Pa(e),e.lineIndent=0,u=e.input.charCodeAt(e.position);(!o||e.lineIndent<s)&&u===32;)e.lineIndent++,u=e.input.charCodeAt(++e.position);if(!o&&e.lineIndent>s&&(s=e.lineIndent),ce(u)){l++;continue}if(e.lineIndent<s){a===Uo?e.result+=wt.repeat(`
`,n?1+l:l):a===yn&&n&&(e.result+=`
`);break}for(i?Ne(u)?(c=!0,e.result+=wt.repeat(`
`,n?1+l:l)):c?(c=!1,e.result+=wt.repeat(`
`,l+1)):l===0?n&&(e.result+=" "):e.result+=wt.repeat(`
`,l):e.result+=wt.repeat(`
`,n?1+l:l),n=!0,o=!0,l=0,r=e.position;!ce(u)&&u!==0;)u=e.input.charCodeAt(++e.position);Ae(e,r,e.position,!1)}return!0}d(wh,"readBlockScalar");function zn(e,t){var r,i=e.tag,a=e.anchor,n=[],o,s=!1,l;if(e.firstTabInLine!==-1)return!1;for(e.anchor!==null&&(e.anchorMap[e.anchor]=n),l=e.input.charCodeAt(e.position);l!==0&&(e.firstTabInLine!==-1&&(e.position=e.firstTabInLine,Z(e,"tab characters must not be used in indentation")),!(l!==45||(o=e.input.charCodeAt(e.position+1),!zt(o))));){if(s=!0,e.position++,Ct(e,!0,-1)&&e.lineIndent<=t){n.push(null),l=e.input.charCodeAt(e.position);continue}if(r=e.line,er(e,t,lh,!1,!0),n.push(e.result),Ct(e,!0,-1),l=e.input.charCodeAt(e.position),(e.line===r||e.lineIndent>t)&&l!==0)Z(e,"bad indentation of a sequence entry");else if(e.lineIndent<t)break}return s?(e.tag=i,e.anchor=a,e.kind="sequence",e.result=n,!0):!1}d(zn,"readBlockSequence");function vh(e,t,r){var i,a,n,o,s,l,c=e.tag,h=e.anchor,u={},f=Object.create(null),p=null,g=null,m=null,y=!1,x=!1,b;if(e.firstTabInLine!==-1)return!1;for(e.anchor!==null&&(e.anchorMap[e.anchor]=u),b=e.input.charCodeAt(e.position);b!==0;){if(!y&&e.firstTabInLine!==-1&&(e.position=e.firstTabInLine,Z(e,"tab characters must not be used in indentation")),i=e.input.charCodeAt(e.position+1),n=e.line,(b===63||b===58)&&zt(i))b===63?(y&&(Qe(e,u,f,p,g,null,o,s,l),p=g=m=null),x=!0,y=!0,a=!0):y?(y=!1,a=!0):Z(e,"incomplete explicit mapping pair; a key node is missed; or followed by a non-tabulated empty line"),e.position+=1,b=i;else{if(o=e.line,s=e.lineStart,l=e.position,!er(e,r,oh,!1,!0))break;if(e.line===n){for(b=e.input.charCodeAt(e.position);Ne(b);)b=e.input.charCodeAt(++e.position);if(b===58)b=e.input.charCodeAt(++e.position),zt(b)||Z(e,"a whitespace character is expected after the key-value separator within a block mapping"),y&&(Qe(e,u,f,p,g,null,o,s,l),p=g=m=null),x=!0,y=!1,a=!1,p=e.tag,g=e.result;else if(x)Z(e,"can not read an implicit mapping pair; a colon is missed");else return e.tag=c,e.anchor=h,!0}else if(x)Z(e,"can not read a block mapping entry; a multiline key may not be an implicit key");else return e.tag=c,e.anchor=h,!0}if((e.line===n||e.lineIndent>t)&&(y&&(o=e.line,s=e.lineStart,l=e.position),er(e,t,da,!0,a)&&(y?g=e.result:m=e.result),y||(Qe(e,u,f,p,g,m,o,s,l),p=g=m=null),Ct(e,!0,-1),b=e.input.charCodeAt(e.position)),(e.line===n||e.lineIndent>t)&&b!==0)Z(e,"bad indentation of a mapping entry");else if(e.lineIndent<t)break}return y&&Qe(e,u,f,p,g,null,o,s,l),x&&(e.tag=c,e.anchor=h,e.kind="mapping",e.result=u),x}d(vh,"readBlockMapping");function Sh(e){var t,r=!1,i=!1,a,n,o;if(o=e.input.charCodeAt(e.position),o!==33)return!1;if(e.tag!==null&&Z(e,"duplication of a tag property"),o=e.input.charCodeAt(++e.position),o===60?(r=!0,o=e.input.charCodeAt(++e.position)):o===33?(i=!0,a="!!",o=e.input.charCodeAt(++e.position)):a="!",t=e.position,r){do o=e.input.charCodeAt(++e.position);while(o!==0&&o!==62);e.position<e.length?(n=e.input.slice(t,e.position),o=e.input.charCodeAt(++e.position)):Z(e,"unexpected end of the stream within a verbatim tag")}else{for(;o!==0&&!zt(o);)o===33&&(i?Z(e,"tag suffix cannot contain exclamation marks"):(a=e.input.slice(t-1,e.position+1),ch.test(a)||Z(e,"named tag handle cannot contain such characters"),i=!0,t=e.position+1)),o=e.input.charCodeAt(++e.position);n=e.input.slice(t,e.position),ly.test(n)&&Z(e,"tag suffix cannot contain flow indicator characters")}n&&!hh.test(n)&&Z(e,"tag name cannot contain such characters: "+n);try{n=decodeURIComponent(n)}catch{Z(e,"tag name is malformed: "+n)}return r?e.tag=n:ze.call(e.tagMap,a)?e.tag=e.tagMap[a]+n:a==="!"?e.tag="!"+n:a==="!!"?e.tag="tag:yaml.org,2002:"+n:Z(e,'undeclared tag handle "'+a+'"'),!0}d(Sh,"readTagProperty");function Th(e){var t,r;if(r=e.input.charCodeAt(e.position),r!==38)return!1;for(e.anchor!==null&&Z(e,"duplication of an anchor property"),r=e.input.charCodeAt(++e.position),t=e.position;r!==0&&!zt(r)&&!Ke(r);)r=e.input.charCodeAt(++e.position);return e.position===t&&Z(e,"name of an anchor node must contain at least one character"),e.anchor=e.input.slice(t,e.position),!0}d(Th,"readAnchorProperty");function _h(e){var t,r,i;if(i=e.input.charCodeAt(e.position),i!==42)return!1;for(i=e.input.charCodeAt(++e.position),t=e.position;i!==0&&!zt(i)&&!Ke(i);)i=e.input.charCodeAt(++e.position);return e.position===t&&Z(e,"name of an alias node must contain at least one character"),r=e.input.slice(t,e.position),ze.call(e.anchorMap,r)||Z(e,'unidentified alias "'+r+'"'),e.result=e.anchorMap[r],Ct(e,!0,-1),!0}d(_h,"readAlias");function er(e,t,r,i,a){var n,o,s,l=1,c=!1,h=!1,u,f,p,g,m,y;if(e.listener!==null&&e.listener("open",e),e.tag=null,e.anchor=null,e.kind=null,e.result=null,n=o=s=da===r||lh===r,i&&Ct(e,!0,-1)&&(c=!0,e.lineIndent>t?l=1:e.lineIndent===t?l=0:e.lineIndent<t&&(l=-1)),l===1)for(;Sh(e)||Th(e);)Ct(e,!0,-1)?(c=!0,s=n,e.lineIndent>t?l=1:e.lineIndent===t?l=0:e.lineIndent<t&&(l=-1)):s=!1;if(s&&(s=c||a),(l===1||da===r)&&(pa===r||oh===r?m=t:m=t+1,y=e.position-e.lineStart,l===1?s&&(zn(e,y)||vh(e,y,m))||kh(e,m)?h=!0:(o&&wh(e,m)||bh(e,m)||Ch(e,m)?h=!0:_h(e)?(h=!0,(e.tag!==null||e.anchor!==null)&&Z(e,"alias node should not have any properties")):xh(e,m,pa===r)&&(h=!0,e.tag===null&&(e.tag="?")),e.anchor!==null&&(e.anchorMap[e.anchor]=e.result)):l===0&&(h=s&&zn(e,y))),e.tag===null)e.anchor!==null&&(e.anchorMap[e.anchor]=e.result);else if(e.tag==="?"){for(e.result!==null&&e.kind!=="scalar"&&Z(e,'unacceptable node kind for !<?> tag; it should be "scalar", not "'+e.kind+'"'),u=0,f=e.implicitTypes.length;u<f;u+=1)if(g=e.implicitTypes[u],g.resolve(e.result)){e.result=g.construct(e.result),e.tag=g.tag,e.anchor!==null&&(e.anchorMap[e.anchor]=e.result);break}}else if(e.tag!=="!"){if(ze.call(e.typeMap[e.kind||"fallback"],e.tag))g=e.typeMap[e.kind||"fallback"][e.tag];else for(g=null,p=e.typeMap.multi[e.kind||"fallback"],u=0,f=p.length;u<f;u+=1)if(e.tag.slice(0,p[u].tag.length)===p[u].tag){g=p[u];break}g||Z(e,"unknown tag !<"+e.tag+">"),e.result!==null&&g.kind!==e.kind&&Z(e,"unacceptable node kind for !<"+e.tag+'> tag; it should be "'+g.kind+'", not "'+e.kind+'"'),g.resolve(e.result,e.tag)?(e.result=g.construct(e.result,e.tag),e.anchor!==null&&(e.anchorMap[e.anchor]=e.result)):Z(e,"cannot resolve a node with !<"+e.tag+"> explicit tag")}return e.listener!==null&&e.listener("close",e),e.tag!==null||e.anchor!==null||h}d(er,"composeNode");function Bh(e){var t=e.position,r,i,a,n=!1,o;for(e.version=null,e.checkLineBreaks=e.legacy,e.tagMap=Object.create(null),e.anchorMap=Object.create(null);(o=e.input.charCodeAt(e.position))!==0&&(Ct(e,!0,-1),o=e.input.charCodeAt(e.position),!(e.lineIndent>0||o!==37));){for(n=!0,o=e.input.charCodeAt(++e.position),r=e.position;o!==0&&!zt(o);)o=e.input.charCodeAt(++e.position);for(i=e.input.slice(r,e.position),a=[],i.length<1&&Z(e,"directive name must not be less than one character in length");o!==0;){for(;Ne(o);)o=e.input.charCodeAt(++e.position);if(o===35){do o=e.input.charCodeAt(++e.position);while(o!==0&&!ce(o));break}if(ce(o))break;for(r=e.position;o!==0&&!zt(o);)o=e.input.charCodeAt(++e.position);a.push(e.input.slice(r,e.position))}o!==0&&Pa(e),ze.call(Yo,i)?Yo[i](e,i,a):hi(e,'unknown document directive "'+i+'"')}if(Ct(e,!0,-1),e.lineIndent===0&&e.input.charCodeAt(e.position)===45&&e.input.charCodeAt(e.position+1)===45&&e.input.charCodeAt(e.position+2)===45?(e.position+=3,Ct(e,!0,-1)):n&&Z(e,"directives end mark is expected"),er(e,e.lineIndent-1,da,!1,!0),Ct(e,!0,-1),e.checkLineBreaks&&oy.test(e.input.slice(t,e.position))&&hi(e,"non-ASCII line breaks are interpreted as content"),e.documents.push(e.result),e.position===e.lineStart&&Li(e)){e.input.charCodeAt(e.position)===46&&(e.position+=3,Ct(e,!0,-1));return}if(e.position<e.length-1)Z(e,"end of the stream or a document separator is expected");else return}d(Bh,"readDocument");function As(e,t){e=String(e),t=t||{},e.length!==0&&(e.charCodeAt(e.length-1)!==10&&e.charCodeAt(e.length-1)!==13&&(e+=`
`),e.charCodeAt(0)===65279&&(e=e.slice(1)));var r=new yh(e,t),i=e.indexOf("\0");for(i!==-1&&(r.position=i,Z(r,"null byte is not allowed in input")),r.input+="\0";r.input.charCodeAt(r.position)===32;)r.lineIndent+=1,r.position+=1;for(;r.position<r.length-1;)Bh(r);return r.documents}d(As,"loadDocuments");function Lh(e,t,r){t!==null&&typeof t=="object"&&typeof r>"u"&&(r=t,t=null);var i=As(e,r);if(typeof t!="function")return i;for(var a=0,n=i.length;a<n;a+=1)t(i[a])}d(Lh,"loadAll$1");function Ah(e,t){var r=As(e,t);if(r.length!==0){if(r.length===1)return r[0];throw new Vt("expected a single document in the stream, but found more")}}d(Ah,"load$1");var cy=Lh,hy=Ah,uy={loadAll:cy,load:hy},Mh=Object.prototype.toString,Eh=Object.prototype.hasOwnProperty,Ms=65279,fy=9,ui=10,py=13,dy=32,gy=33,my=34,Wn=35,yy=37,xy=38,by=39,Cy=42,Fh=44,ky=45,ga=58,wy=61,vy=62,Sy=63,Ty=64,$h=91,Oh=93,_y=96,Dh=123,By=124,Rh=125,Rt={};Rt[0]="\\0";Rt[7]="\\a";Rt[8]="\\b";Rt[9]="\\t";Rt[10]="\\n";Rt[11]="\\v";Rt[12]="\\f";Rt[13]="\\r";Rt[27]="\\e";Rt[34]='\\"';Rt[92]="\\\\";Rt[133]="\\N";Rt[160]="\\_";Rt[8232]="\\L";Rt[8233]="\\P";var Ly=["y","Y","yes","Yes","YES","on","On","ON","n","N","no","No","NO","off","Off","OFF"],Ay=/^[-+]?[0-9_]+(?::[0-9_]+)+(?:\.[0-9_]*)?$/;function Ih(e,t){var r,i,a,n,o,s,l;if(t===null)return{};for(r={},i=Object.keys(t),a=0,n=i.length;a<n;a+=1)o=i[a],s=String(t[o]),o.slice(0,2)==="!!"&&(o="tag:yaml.org,2002:"+o.slice(2)),l=e.compiledTypeMap.fallback[o],l&&Eh.call(l.styleAliases,s)&&(s=l.styleAliases[s]),r[o]=s;return r}d(Ih,"compileStyleMap");function Ph(e){var t,r,i;if(t=e.toString(16).toUpperCase(),e<=255)r="x",i=2;else if(e<=65535)r="u",i=4;else if(e<=4294967295)r="U",i=8;else throw new Vt("code point within a string may not be greater than 0xFFFFFFFF");return"\\"+r+wt.repeat("0",i-t.length)+t}d(Ph,"encodeHex");var My=1,fi=2;function Nh(e){this.schema=e.schema||sh,this.indent=Math.max(1,e.indent||2),this.noArrayIndent=e.noArrayIndent||!1,this.skipInvalid=e.skipInvalid||!1,this.flowLevel=wt.isNothing(e.flowLevel)?-1:e.flowLevel,this.styleMap=Ih(this.schema,e.styles||null),this.sortKeys=e.sortKeys||!1,this.lineWidth=e.lineWidth||80,this.noRefs=e.noRefs||!1,this.noCompatMode=e.noCompatMode||!1,this.condenseFlow=e.condenseFlow||!1,this.quotingType=e.quotingType==='"'?fi:My,this.forceQuotes=e.forceQuotes||!1,this.replacer=typeof e.replacer=="function"?e.replacer:null,this.implicitTypes=this.schema.compiledImplicit,this.explicitTypes=this.schema.compiledExplicit,this.tag=null,this.result="",this.duplicates=[],this.usedDuplicates=null}d(Nh,"State");function qn(e,t){for(var r=wt.repeat(" ",t),i=0,a=-1,n="",o,s=e.length;i<s;)a=e.indexOf(`
`,i),a===-1?(o=e.slice(i),i=s):(o=e.slice(i,a+1),i=a+1),o.length&&o!==`
`&&(n+=r),n+=o;return n}d(qn,"indentString");function ma(e,t){return`
`+wt.repeat(" ",e.indent*t)}d(ma,"generateNextLine");function zh(e,t){var r,i,a;for(r=0,i=e.implicitTypes.length;r<i;r+=1)if(a=e.implicitTypes[r],a.resolve(t))return!0;return!1}d(zh,"testImplicitResolving");function pi(e){return e===dy||e===fy}d(pi,"isWhitespace");function Br(e){return 32<=e&&e<=126||161<=e&&e<=55295&&e!==8232&&e!==8233||57344<=e&&e<=65533&&e!==Ms||65536<=e&&e<=1114111}d(Br,"isPrintable");function Hn(e){return Br(e)&&e!==Ms&&e!==py&&e!==ui}d(Hn,"isNsCharOrWhitespace");function jn(e,t,r){var i=Hn(e),a=i&&!pi(e);return(r?i:i&&e!==Fh&&e!==$h&&e!==Oh&&e!==Dh&&e!==Rh)&&e!==Wn&&!(t===ga&&!a)||Hn(t)&&!pi(t)&&e===Wn||t===ga&&a}d(jn,"isPlainSafe");function Wh(e){return Br(e)&&e!==Ms&&!pi(e)&&e!==ky&&e!==Sy&&e!==ga&&e!==Fh&&e!==$h&&e!==Oh&&e!==Dh&&e!==Rh&&e!==Wn&&e!==xy&&e!==Cy&&e!==gy&&e!==By&&e!==wy&&e!==vy&&e!==by&&e!==my&&e!==yy&&e!==Ty&&e!==_y}d(Wh,"isPlainSafeFirst");function qh(e){return!pi(e)&&e!==ga}d(qh,"isPlainSafeLast");function xr(e,t){var r=e.charCodeAt(t),i;return r>=55296&&r<=56319&&t+1<e.length&&(i=e.charCodeAt(t+1),i>=56320&&i<=57343)?(r-55296)*1024+i-56320+65536:r}d(xr,"codePointAt");function Es(e){var t=/^\n* /;return t.test(e)}d(Es,"needIndentIndicator");var Hh=1,Un=2,jh=3,Uh=4,mr=5;function Yh(e,t,r,i,a,n,o,s){var l,c=0,h=null,u=!1,f=!1,p=i!==-1,g=-1,m=Wh(xr(e,0))&&qh(xr(e,e.length-1));if(t||o)for(l=0;l<e.length;c>=65536?l+=2:l++){if(c=xr(e,l),!Br(c))return mr;m=m&&jn(c,h,s),h=c}else{for(l=0;l<e.length;c>=65536?l+=2:l++){if(c=xr(e,l),c===ui)u=!0,p&&(f=f||l-g-1>i&&e[g+1]!==" ",g=l);else if(!Br(c))return mr;m=m&&jn(c,h,s),h=c}f=f||p&&l-g-1>i&&e[g+1]!==" "}return!u&&!f?m&&!o&&!a(e)?Hh:n===fi?mr:Un:r>9&&Es(e)?mr:o?n===fi?mr:Un:f?Uh:jh}d(Yh,"chooseScalarStyle");function Gh(e,t,r,i,a){e.dump=function(){if(t.length===0)return e.quotingType===fi?'""':"''";if(!e.noCompatMode&&(Ly.indexOf(t)!==-1||Ay.test(t)))return e.quotingType===fi?'"'+t+'"':"'"+t+"'";var n=e.indent*Math.max(1,r),o=e.lineWidth===-1?-1:Math.max(Math.min(e.lineWidth,40),e.lineWidth-n),s=i||e.flowLevel>-1&&r>=e.flowLevel;function l(c){return zh(e,c)}switch(d(l,"testAmbiguity"),Yh(t,s,e.indent,o,l,e.quotingType,e.forceQuotes&&!i,a)){case Hh:return t;case Un:return"'"+t.replace(/'/g,"''")+"'";case jh:return"|"+Yn(t,e.indent)+Gn(qn(t,n));case Uh:return">"+Yn(t,e.indent)+Gn(qn(Vh(t,o),n));case mr:return'"'+Xh(t)+'"';default:throw new Vt("impossible error: invalid scalar style")}}()}d(Gh,"writeScalar");function Yn(e,t){var r=Es(e)?String(t):"",i=e[e.length-1]===`
`,a=i&&(e[e.length-2]===`
`||e===`
`),n=a?"+":i?"":"-";return r+n+`
`}d(Yn,"blockHeader");function Gn(e){return e[e.length-1]===`
`?e.slice(0,-1):e}d(Gn,"dropEndingNewline");function Vh(e,t){for(var r=/(\n+)([^\n]*)/g,i=function(){var c=e.indexOf(`
`);return c=c!==-1?c:e.length,r.lastIndex=c,Vn(e.slice(0,c),t)}(),a=e[0]===`
`||e[0]===" ",n,o;o=r.exec(e);){var s=o[1],l=o[2];n=l[0]===" ",i+=s+(!a&&!n&&l!==""?`
`:"")+Vn(l,t),a=n}return i}d(Vh,"foldString");function Vn(e,t){if(e===""||e[0]===" ")return e;for(var r=/ [^ ]/g,i,a=0,n,o=0,s=0,l="";i=r.exec(e);)s=i.index,s-a>t&&(n=o>a?o:s,l+=`
`+e.slice(a,n),a=n+1),o=s;return l+=`
`,e.length-a>t&&o>a?l+=e.slice(a,o)+`
`+e.slice(o+1):l+=e.slice(a),l.slice(1)}d(Vn,"foldLine");function Xh(e){for(var t="",r=0,i,a=0;a<e.length;r>=65536?a+=2:a++)r=xr(e,a),i=Rt[r],!i&&Br(r)?(t+=e[a],r>=65536&&(t+=e[a+1])):t+=i||Ph(r);return t}d(Xh,"escapeString");function Zh(e,t,r){var i="",a=e.tag,n,o,s;for(n=0,o=r.length;n<o;n+=1)s=r[n],e.replacer&&(s=e.replacer.call(r,String(n),s)),(be(e,t,s,!1,!1)||typeof s>"u"&&be(e,t,null,!1,!1))&&(i!==""&&(i+=","+(e.condenseFlow?"":" ")),i+=e.dump);e.tag=a,e.dump="["+i+"]"}d(Zh,"writeFlowSequence");function Xn(e,t,r,i){var a="",n=e.tag,o,s,l;for(o=0,s=r.length;o<s;o+=1)l=r[o],e.replacer&&(l=e.replacer.call(r,String(o),l)),(be(e,t+1,l,!0,!0,!1,!0)||typeof l>"u"&&be(e,t+1,null,!0,!0,!1,!0))&&((!i||a!=="")&&(a+=ma(e,t)),e.dump&&ui===e.dump.charCodeAt(0)?a+="-":a+="- ",a+=e.dump);e.tag=n,e.dump=a||"[]"}d(Xn,"writeBlockSequence");function Kh(e,t,r){var i="",a=e.tag,n=Object.keys(r),o,s,l,c,h;for(o=0,s=n.length;o<s;o+=1)h="",i!==""&&(h+=", "),e.condenseFlow&&(h+='"'),l=n[o],c=r[l],e.replacer&&(c=e.replacer.call(r,l,c)),be(e,t,l,!1,!1)&&(e.dump.length>1024&&(h+="? "),h+=e.dump+(e.condenseFlow?'"':"")+":"+(e.condenseFlow?"":" "),be(e,t,c,!1,!1)&&(h+=e.dump,i+=h));e.tag=a,e.dump="{"+i+"}"}d(Kh,"writeFlowMapping");function Qh(e,t,r,i){var a="",n=e.tag,o=Object.keys(r),s,l,c,h,u,f;if(e.sortKeys===!0)o.sort();else if(typeof e.sortKeys=="function")o.sort(e.sortKeys);else if(e.sortKeys)throw new Vt("sortKeys must be a boolean or a function");for(s=0,l=o.length;s<l;s+=1)f="",(!i||a!=="")&&(f+=ma(e,t)),c=o[s],h=r[c],e.replacer&&(h=e.replacer.call(r,c,h)),be(e,t+1,c,!0,!0,!0)&&(u=e.tag!==null&&e.tag!=="?"||e.dump&&e.dump.length>1024,u&&(e.dump&&ui===e.dump.charCodeAt(0)?f+="?":f+="? "),f+=e.dump,u&&(f+=ma(e,t)),be(e,t+1,h,!0,u)&&(e.dump&&ui===e.dump.charCodeAt(0)?f+=":":f+=": ",f+=e.dump,a+=f));e.tag=n,e.dump=a||"{}"}d(Qh,"writeBlockMapping");function Zn(e,t,r){var i,a,n,o,s,l;for(a=r?e.explicitTypes:e.implicitTypes,n=0,o=a.length;n<o;n+=1)if(s=a[n],(s.instanceOf||s.predicate)&&(!s.instanceOf||typeof t=="object"&&t instanceof s.instanceOf)&&(!s.predicate||s.predicate(t))){if(r?s.multi&&s.representName?e.tag=s.representName(t):e.tag=s.tag:e.tag="?",s.represent){if(l=e.styleMap[s.tag]||s.defaultStyle,Mh.call(s.represent)==="[object Function]")i=s.represent(t,l);else if(Eh.call(s.represent,l))i=s.represent[l](t,l);else throw new Vt("!<"+s.tag+'> tag resolver accepts not "'+l+'" style');e.dump=i}return!0}return!1}d(Zn,"detectType");function be(e,t,r,i,a,n,o){e.tag=null,e.dump=r,Zn(e,r,!1)||Zn(e,r,!0);var s=Mh.call(e.dump),l=i,c;i&&(i=e.flowLevel<0||e.flowLevel>t);var h=s==="[object Object]"||s==="[object Array]",u,f;if(h&&(u=e.duplicates.indexOf(r),f=u!==-1),(e.tag!==null&&e.tag!=="?"||f||e.indent!==2&&t>0)&&(a=!1),f&&e.usedDuplicates[u])e.dump="*ref_"+u;else{if(h&&f&&!e.usedDuplicates[u]&&(e.usedDuplicates[u]=!0),s==="[object Object]")i&&Object.keys(e.dump).length!==0?(Qh(e,t,e.dump,a),f&&(e.dump="&ref_"+u+e.dump)):(Kh(e,t,e.dump),f&&(e.dump="&ref_"+u+" "+e.dump));else if(s==="[object Array]")i&&e.dump.length!==0?(e.noArrayIndent&&!o&&t>0?Xn(e,t-1,e.dump,a):Xn(e,t,e.dump,a),f&&(e.dump="&ref_"+u+e.dump)):(Zh(e,t,e.dump),f&&(e.dump="&ref_"+u+" "+e.dump));else if(s==="[object String]")e.tag!=="?"&&Gh(e,e.dump,t,n,l);else{if(s==="[object Undefined]")return!1;if(e.skipInvalid)return!1;throw new Vt("unacceptable kind of an object to dump "+s)}e.tag!==null&&e.tag!=="?"&&(c=encodeURI(e.tag[0]==="!"?e.tag.slice(1):e.tag).replace(/!/g,"%21"),e.tag[0]==="!"?c="!"+c:c.slice(0,18)==="tag:yaml.org,2002:"?c="!!"+c.slice(18):c="!<"+c+">",e.dump=c+" "+e.dump)}return!0}d(be,"writeNode");function Jh(e,t){var r=[],i=[],a,n;for(ya(e,r,i),a=0,n=i.length;a<n;a+=1)t.duplicates.push(r[i[a]]);t.usedDuplicates=new Array(n)}d(Jh,"getDuplicateReferences");function ya(e,t,r){var i,a,n;if(e!==null&&typeof e=="object")if(a=t.indexOf(e),a!==-1)r.indexOf(a)===-1&&r.push(a);else if(t.push(e),Array.isArray(e))for(a=0,n=e.length;a<n;a+=1)ya(e[a],t,r);else for(i=Object.keys(e),a=0,n=i.length;a<n;a+=1)ya(e[i[a]],t,r)}d(ya,"inspectNode");function Ey(e,t){t=t||{};var r=new Nh(t);r.noRefs||Jh(e,r);var i=e;return r.replacer&&(i=r.replacer.call({"":i},"",i)),be(r,0,i,!0,!0)?r.dump+`
`:""}d(Ey,"dump$1");function Fy(e,t){return function(){throw new Error("Function yaml."+e+" is removed in js-yaml 4. Use yaml."+t+" instead, which is now safe by default.")}}d(Fy,"renamed");var $y=Hc,Oy=uy.load;/*! Bundled license information:

js-yaml/dist/js-yaml.mjs:
  (*! js-yaml 4.1.0 https://github.com/nodeca/js-yaml @license MIT *)
*/const Dy=Object.freeze({left:0,top:0,width:16,height:16}),xa=Object.freeze({rotate:0,vFlip:!1,hFlip:!1}),tu=Object.freeze({...Dy,...xa}),Ry=Object.freeze({...tu,body:"",hidden:!1}),Iy=Object.freeze({width:null,height:null}),Py=Object.freeze({...Iy,...xa}),Ny=(e,t,r,i="")=>{const a=e.split(":");if(e.slice(0,1)==="@"){if(a.length<2||a.length>3)return null;i=a.shift().slice(1)}if(a.length>3||!a.length)return null;if(a.length>1){const s=a.pop(),l=a.pop(),c={provider:a.length>0?a[0]:i,prefix:l,name:s};return xn(c)?c:null}const n=a[0],o=n.split("-");if(o.length>1){const s={provider:i,prefix:o.shift(),name:o.join("-")};return xn(s)?s:null}if(r&&i===""){const s={provider:i,prefix:"",name:n};return xn(s,r)?s:null}return null},xn=(e,t)=>e?!!((t&&e.prefix===""||e.prefix)&&e.name):!1;function zy(e,t){const r={};!e.hFlip!=!t.hFlip&&(r.hFlip=!0),!e.vFlip!=!t.vFlip&&(r.vFlip=!0);const i=((e.rotate||0)+(t.rotate||0))%4;return i&&(r.rotate=i),r}function Go(e,t){const r=zy(e,t);for(const i in Ry)i in xa?i in e&&!(i in r)&&(r[i]=xa[i]):i in t?r[i]=t[i]:i in e&&(r[i]=e[i]);return r}function Wy(e,t){const r=e.icons,i=e.aliases||Object.create(null),a=Object.create(null);function n(o){if(r[o])return a[o]=[];if(!(o in a)){a[o]=null;const s=i[o]&&i[o].parent,l=s&&n(s);l&&(a[o]=[s].concat(l))}return a[o]}return(t||Object.keys(r).concat(Object.keys(i))).forEach(n),a}function Vo(e,t,r){const i=e.icons,a=e.aliases||Object.create(null);let n={};function o(s){n=Go(i[s]||a[s],n)}return o(t),r.forEach(o),Go(e,n)}function qy(e,t){if(e.icons[t])return Vo(e,t,[]);const r=Wy(e,[t])[t];return r?Vo(e,t,r):null}const Hy=/(-?[0-9.]*[0-9]+[0-9.]*)/g,jy=/^-?[0-9.]*[0-9]+[0-9.]*$/g;function Xo(e,t,r){if(t===1)return e;if(r=r||100,typeof e=="number")return Math.ceil(e*t*r)/r;if(typeof e!="string")return e;const i=e.split(Hy);if(i===null||!i.length)return e;const a=[];let n=i.shift(),o=jy.test(n);for(;;){if(o){const s=parseFloat(n);isNaN(s)?a.push(n):a.push(Math.ceil(s*t*r)/r)}else a.push(n);if(n=i.shift(),n===void 0)return a.join("");o=!o}}function Uy(e,t="defs"){let r="";const i=e.indexOf("<"+t);for(;i>=0;){const a=e.indexOf(">",i),n=e.indexOf("</"+t);if(a===-1||n===-1)break;const o=e.indexOf(">",n);if(o===-1)break;r+=e.slice(a+1,n).trim(),e=e.slice(0,i).trim()+e.slice(o+1)}return{defs:r,content:e}}function Yy(e,t){return e?"<defs>"+e+"</defs>"+t:t}function Gy(e,t,r){const i=Uy(e);return Yy(i.defs,t+i.content+r)}const Vy=e=>e==="unset"||e==="undefined"||e==="none";function Xy(e,t){const r={...tu,...e},i={...Py,...t},a={left:r.left,top:r.top,width:r.width,height:r.height};let n=r.body;[r,i].forEach(m=>{const y=[],x=m.hFlip,b=m.vFlip;let C=m.rotate;x?b?C+=2:(y.push("translate("+(a.width+a.left).toString()+" "+(0-a.top).toString()+")"),y.push("scale(-1 1)"),a.top=a.left=0):b&&(y.push("translate("+(0-a.left).toString()+" "+(a.height+a.top).toString()+")"),y.push("scale(1 -1)"),a.top=a.left=0);let S;switch(C<0&&(C-=Math.floor(C/4)*4),C=C%4,C){case 1:S=a.height/2+a.top,y.unshift("rotate(90 "+S.toString()+" "+S.toString()+")");break;case 2:y.unshift("rotate(180 "+(a.width/2+a.left).toString()+" "+(a.height/2+a.top).toString()+")");break;case 3:S=a.width/2+a.left,y.unshift("rotate(-90 "+S.toString()+" "+S.toString()+")");break}C%2===1&&(a.left!==a.top&&(S=a.left,a.left=a.top,a.top=S),a.width!==a.height&&(S=a.width,a.width=a.height,a.height=S)),y.length&&(n=Gy(n,'<g transform="'+y.join(" ")+'">',"</g>"))});const o=i.width,s=i.height,l=a.width,c=a.height;let h,u;o===null?(u=s===null?"1em":s==="auto"?c:s,h=Xo(u,l/c)):(h=o==="auto"?l:o,u=s===null?Xo(h,c/l):s==="auto"?c:s);const f={},p=(m,y)=>{Vy(y)||(f[m]=y.toString())};p("width",h),p("height",u);const g=[a.left,a.top,l,c];return f.viewBox=g.join(" "),{attributes:f,viewBox:g,body:n}}const Zy=/\sid="(\S+)"/g,Ky="IconifyId"+Date.now().toString(16)+(Math.random()*16777216|0).toString(16);let Qy=0;function Jy(e,t=Ky){const r=[];let i;for(;i=Zy.exec(e);)r.push(i[1]);if(!r.length)return e;const a="suffix"+(Math.random()*16777216|Date.now()).toString(16);return r.forEach(n=>{const o=typeof t=="function"?t(n):t+(Qy++).toString(),s=n.replace(/[.*+?^${}()|[\]\\]/g,"\\$&");e=e.replace(new RegExp('([#;"])('+s+')([")]|\\.[a-z])',"g"),"$1"+o+a+"$3")}),e=e.replace(new RegExp(a,"g"),""),e}function t0(e,t){let r=e.indexOf("xlink:")===-1?"":' xmlns:xlink="http://www.w3.org/1999/xlink"';for(const i in t)r+=" "+i+'="'+t[i]+'"';return'<svg xmlns="http://www.w3.org/2000/svg"'+r+">"+e+"</svg>"}var e0={body:'<g><rect width="80" height="80" style="fill: #087ebf; stroke-width: 0px;"/><text transform="translate(21.16 64.67)" style="fill: #fff; font-family: ArialMT, Arial; font-size: 67.75px;"><tspan x="0" y="0">?</tspan></text></g>',height:80,width:80},Kn=new Map,eu=new Map,r0=d(e=>{for(const t of e){if(!t.name)throw new Error('Invalid icon loader. Must have a "name" property with non-empty string value.');if(O.debug("Registering icon pack:",t.name),"loader"in t)eu.set(t.name,t.loader);else if("icons"in t)Kn.set(t.name,t.icons);else throw O.error("Invalid icon loader:",t),new Error('Invalid icon loader. Must have either "icons" or "loader" property.')}},"registerIconPacks"),i0=d(async(e,t)=>{const r=Ny(e,!0,t!==void 0);if(!r)throw new Error(`Invalid icon name: ${e}`);const i=r.prefix||t;if(!i)throw new Error(`Icon name must contain a prefix: ${e}`);let a=Kn.get(i);if(!a){const o=eu.get(i);if(!o)throw new Error(`Icon set not found: ${r.prefix}`);try{a={...await o(),prefix:i},Kn.set(i,a)}catch(s){throw O.error(s),new Error(`Failed to load icon set: ${r.prefix}`)}}const n=qy(a,r.name);if(!n)throw new Error(`Icon not found: ${e}`);return n},"getRegisteredIconData"),za=d(async(e,t)=>{let r;try{r=await i0(e,t==null?void 0:t.fallbackPrefix)}catch(n){O.error(n),r=e0}const i=Xy(r,t);return t0(Jy(i.body),i.attributes)},"getIconSVG"),Fs=d(({flowchart:e})=>{var a,n;const t=((a=e==null?void 0:e.subGraphTitleMargin)==null?void 0:a.top)??0,r=((n=e==null?void 0:e.subGraphTitleMargin)==null?void 0:n.bottom)??0,i=t+r;return{subGraphTitleTopMargin:t,subGraphTitleBottomMargin:r,subGraphTitleTotalMargin:i}},"getSubGraphTitleMargins"),$s={},_t={};Object.defineProperty(_t,"__esModule",{value:!0});_t.BLANK_URL=_t.relativeFirstCharacters=_t.whitespaceEscapeCharsRegex=_t.urlSchemeRegex=_t.ctrlCharactersRegex=_t.htmlCtrlEntityRegex=_t.htmlEntitiesRegex=_t.invalidProtocolRegex=void 0;_t.invalidProtocolRegex=/^([^\w]*)(javascript|data|vbscript)/im;_t.htmlEntitiesRegex=/&#(\w+)(^\w|;)?/g;_t.htmlCtrlEntityRegex=/&(newline|tab);/gi;_t.ctrlCharactersRegex=/[\u0000-\u001F\u007F-\u009F\u2000-\u200D\uFEFF]/gim;_t.urlSchemeRegex=/^.+(:|&colon;)/gim;_t.whitespaceEscapeCharsRegex=/(\\|%5[cC])((%(6[eE]|72|74))|[nrt])/g;_t.relativeFirstCharacters=[".","/"];_t.BLANK_URL="about:blank";Object.defineProperty($s,"__esModule",{value:!0});var ru=$s.sanitizeUrl=void 0,Ot=_t;function a0(e){return Ot.relativeFirstCharacters.indexOf(e[0])>-1}function n0(e){var t=e.replace(Ot.ctrlCharactersRegex,"");return t.replace(Ot.htmlEntitiesRegex,function(r,i){return String.fromCharCode(i)})}function s0(e){return URL.canParse(e)}function Zo(e){try{return decodeURIComponent(e)}catch{return e}}function o0(e){if(!e)return Ot.BLANK_URL;var t,r=Zo(e.trim());do r=n0(r).replace(Ot.htmlCtrlEntityRegex,"").replace(Ot.ctrlCharactersRegex,"").replace(Ot.whitespaceEscapeCharsRegex,"").trim(),r=Zo(r),t=r.match(Ot.ctrlCharactersRegex)||r.match(Ot.htmlEntitiesRegex)||r.match(Ot.htmlCtrlEntityRegex)||r.match(Ot.whitespaceEscapeCharsRegex);while(t&&t.length>0);var i=r;if(!i)return Ot.BLANK_URL;if(a0(i))return i;var a=i.trimStart(),n=a.match(Ot.urlSchemeRegex);if(!n)return i;var o=n[0].toLowerCase().trim();if(Ot.invalidProtocolRegex.test(o))return Ot.BLANK_URL;var s=a.replace(/\\/g,"/");if(o==="mailto:"||o.includes("://"))return s;if(o==="http:"||o==="https:"){if(!s0(s))return Ot.BLANK_URL;var l=new URL(s);return l.protocol=l.protocol.toLowerCase(),l.hostname=l.hostname.toLowerCase(),l.toString()}return s}ru=$s.sanitizeUrl=o0;function Ko(e,t,r){var i=new Ed;return t=t==null?0:+t,i.restart(a=>{i.stop(),e(a+t)},t,r),i}var l0=Md("start","end","cancel","interrupt"),c0=[],iu=0,Qo=1,Qn=2,ea=3,Jo=4,Jn=5,ra=6;function Wa(e,t,r,i,a,n){var o=e.__transition;if(!o)e.__transition={};else if(r in o)return;h0(e,r,{name:t,index:i,group:a,on:l0,tween:c0,time:n.time,delay:n.delay,duration:n.duration,ease:n.ease,timer:null,state:iu})}function Os(e,t){var r=ue(e,t);if(r.state>iu)throw new Error("too late; already scheduled");return r}function Ce(e,t){var r=ue(e,t);if(r.state>ea)throw new Error("too late; already running");return r}function ue(e,t){var r=e.__transition;if(!r||!(r=r[t]))throw new Error("transition not found");return r}function h0(e,t,r){var i=e.__transition,a;i[t]=r,r.timer=Fd(n,0,r.time);function n(c){r.state=Qo,r.timer.restart(o,r.delay,r.time),r.delay<=c&&o(c-r.delay)}function o(c){var h,u,f,p;if(r.state!==Qo)return l();for(h in i)if(p=i[h],p.name===r.name){if(p.state===ea)return Ko(o);p.state===Jo?(p.state=ra,p.timer.stop(),p.on.call("interrupt",e,e.__data__,p.index,p.group),delete i[h]):+h<t&&(p.state=ra,p.timer.stop(),p.on.call("cancel",e,e.__data__,p.index,p.group),delete i[h])}if(Ko(function(){r.state===ea&&(r.state=Jo,r.timer.restart(s,r.delay,r.time),s(c))}),r.state=Qn,r.on.call("start",e,e.__data__,r.index,r.group),r.state===Qn){for(r.state=ea,a=new Array(f=r.tween.length),h=0,u=-1;h<f;++h)(p=r.tween[h].value.call(e,e.__data__,r.index,r.group))&&(a[++u]=p);a.length=u+1}}function s(c){for(var h=c<r.duration?r.ease.call(null,c/r.duration):(r.timer.restart(l),r.state=Jn,1),u=-1,f=a.length;++u<f;)a[u].call(e,h);r.state===Jn&&(r.on.call("end",e,e.__data__,r.index,r.group),l())}function l(){r.state=ra,r.timer.stop(),delete i[t];for(var c in i)return;delete e.__transition}}function u0(e,t){var r=e.__transition,i,a,n=!0,o;if(r){t=t==null?null:t+"";for(o in r){if((i=r[o]).name!==t){n=!1;continue}a=i.state>Qn&&i.state<Jn,i.state=ra,i.timer.stop(),i.on.call(a?"interrupt":"cancel",e,e.__data__,i.index,i.group),delete r[o]}n&&delete e.__transition}}function f0(e){return this.each(function(){u0(this,e)})}function p0(e,t){var r,i;return function(){var a=Ce(this,e),n=a.tween;if(n!==r){i=r=n;for(var o=0,s=i.length;o<s;++o)if(i[o].name===t){i=i.slice(),i.splice(o,1);break}}a.tween=i}}function d0(e,t,r){var i,a;if(typeof r!="function")throw new Error;return function(){var n=Ce(this,e),o=n.tween;if(o!==i){a=(i=o).slice();for(var s={name:t,value:r},l=0,c=a.length;l<c;++l)if(a[l].name===t){a[l]=s;break}l===c&&a.push(s)}n.tween=a}}function g0(e,t){var r=this._id;if(e+="",arguments.length<2){for(var i=ue(this.node(),r).tween,a=0,n=i.length,o;a<n;++a)if((o=i[a]).name===e)return o.value;return null}return this.each((t==null?p0:d0)(r,e,t))}function Ds(e,t,r){var i=e._id;return e.each(function(){var a=Ce(this,i);(a.value||(a.value={}))[t]=r.apply(this,arguments)}),function(a){return ue(a,i).value[t]}}function au(e,t){var r;return(typeof t=="number"?$d:t instanceof Ao?Mo:(r=Ao(t))?(t=r,Mo):Od)(e,t)}function m0(e){return function(){this.removeAttribute(e)}}function y0(e){return function(){this.removeAttributeNS(e.space,e.local)}}function x0(e,t,r){var i,a=r+"",n;return function(){var o=this.getAttribute(e);return o===a?null:o===i?n:n=t(i=o,r)}}function b0(e,t,r){var i,a=r+"",n;return function(){var o=this.getAttributeNS(e.space,e.local);return o===a?null:o===i?n:n=t(i=o,r)}}function C0(e,t,r){var i,a,n;return function(){var o,s=r(this),l;return s==null?void this.removeAttribute(e):(o=this.getAttribute(e),l=s+"",o===l?null:o===i&&l===a?n:(a=l,n=t(i=o,s)))}}function k0(e,t,r){var i,a,n;return function(){var o,s=r(this),l;return s==null?void this.removeAttributeNS(e.space,e.local):(o=this.getAttributeNS(e.space,e.local),l=s+"",o===l?null:o===i&&l===a?n:(a=l,n=t(i=o,s)))}}function w0(e,t){var r=Ul(e),i=r==="transform"?Dd:au;return this.attrTween(e,typeof t=="function"?(r.local?k0:C0)(r,i,Ds(this,"attr."+e,t)):t==null?(r.local?y0:m0)(r):(r.local?b0:x0)(r,i,t))}function v0(e,t){return function(r){this.setAttribute(e,t.call(this,r))}}function S0(e,t){return function(r){this.setAttributeNS(e.space,e.local,t.call(this,r))}}function T0(e,t){var r,i;function a(){var n=t.apply(this,arguments);return n!==i&&(r=(i=n)&&S0(e,n)),r}return a._value=t,a}function _0(e,t){var r,i;function a(){var n=t.apply(this,arguments);return n!==i&&(r=(i=n)&&v0(e,n)),r}return a._value=t,a}function B0(e,t){var r="attr."+e;if(arguments.length<2)return(r=this.tween(r))&&r._value;if(t==null)return this.tween(r,null);if(typeof t!="function")throw new Error;var i=Ul(e);return this.tween(r,(i.local?T0:_0)(i,t))}function L0(e,t){return function(){Os(this,e).delay=+t.apply(this,arguments)}}function A0(e,t){return t=+t,function(){Os(this,e).delay=t}}function M0(e){var t=this._id;return arguments.length?this.each((typeof e=="function"?L0:A0)(t,e)):ue(this.node(),t).delay}function E0(e,t){return function(){Ce(this,e).duration=+t.apply(this,arguments)}}function F0(e,t){return t=+t,function(){Ce(this,e).duration=t}}function $0(e){var t=this._id;return arguments.length?this.each((typeof e=="function"?E0:F0)(t,e)):ue(this.node(),t).duration}function O0(e,t){if(typeof t!="function")throw new Error;return function(){Ce(this,e).ease=t}}function D0(e){var t=this._id;return arguments.length?this.each(O0(t,e)):ue(this.node(),t).ease}function R0(e,t){return function(){var r=t.apply(this,arguments);if(typeof r!="function")throw new Error;Ce(this,e).ease=r}}function I0(e){if(typeof e!="function")throw new Error;return this.each(R0(this._id,e))}function P0(e){typeof e!="function"&&(e=tg(e));for(var t=this._groups,r=t.length,i=new Array(r),a=0;a<r;++a)for(var n=t[a],o=n.length,s=i[a]=[],l,c=0;c<o;++c)(l=n[c])&&e.call(l,l.__data__,c,n)&&s.push(l);return new Me(i,this._parents,this._name,this._id)}function N0(e){if(e._id!==this._id)throw new Error;for(var t=this._groups,r=e._groups,i=t.length,a=r.length,n=Math.min(i,a),o=new Array(i),s=0;s<n;++s)for(var l=t[s],c=r[s],h=l.length,u=o[s]=new Array(h),f,p=0;p<h;++p)(f=l[p]||c[p])&&(u[p]=f);for(;s<i;++s)o[s]=t[s];return new Me(o,this._parents,this._name,this._id)}function z0(e){return(e+"").trim().split(/^|\s+/).every(function(t){var r=t.indexOf(".");return r>=0&&(t=t.slice(0,r)),!t||t==="start"})}function W0(e,t,r){var i,a,n=z0(t)?Os:Ce;return function(){var o=n(this,e),s=o.on;s!==i&&(a=(i=s).copy()).on(t,r),o.on=a}}function q0(e,t){var r=this._id;return arguments.length<2?ue(this.node(),r).on.on(e):this.each(W0(r,e,t))}function H0(e){return function(){var t=this.parentNode;for(var r in this.__transition)if(+r!==e)return;t&&t.removeChild(this)}}function j0(){return this.on("end.remove",H0(this._id))}function U0(e){var t=this._name,r=this._id;typeof e!="function"&&(e=eg(e));for(var i=this._groups,a=i.length,n=new Array(a),o=0;o<a;++o)for(var s=i[o],l=s.length,c=n[o]=new Array(l),h,u,f=0;f<l;++f)(h=s[f])&&(u=e.call(h,h.__data__,f,s))&&("__data__"in h&&(u.__data__=h.__data__),c[f]=u,Wa(c[f],t,r,f,c,ue(h,r)));return new Me(n,this._parents,t,r)}function Y0(e){var t=this._name,r=this._id;typeof e!="function"&&(e=rg(e));for(var i=this._groups,a=i.length,n=[],o=[],s=0;s<a;++s)for(var l=i[s],c=l.length,h,u=0;u<c;++u)if(h=l[u]){for(var f=e.call(h,h.__data__,u,l),p,g=ue(h,r),m=0,y=f.length;m<y;++m)(p=f[m])&&Wa(p,t,r,m,f,g);n.push(f),o.push(h)}return new Me(n,o,t,r)}var G0=$a.prototype.constructor;function V0(){return new G0(this._groups,this._parents)}function X0(e,t){var r,i,a;return function(){var n=ci(this,e),o=(this.style.removeProperty(e),ci(this,e));return n===o?null:n===r&&o===i?a:a=t(r=n,i=o)}}function nu(e){return function(){this.style.removeProperty(e)}}function Z0(e,t,r){var i,a=r+"",n;return function(){var o=ci(this,e);return o===a?null:o===i?n:n=t(i=o,r)}}function K0(e,t,r){var i,a,n;return function(){var o=ci(this,e),s=r(this),l=s+"";return s==null&&(l=s=(this.style.removeProperty(e),ci(this,e))),o===l?null:o===i&&l===a?n:(a=l,n=t(i=o,s))}}function Q0(e,t){var r,i,a,n="style."+t,o="end."+n,s;return function(){var l=Ce(this,e),c=l.on,h=l.value[n]==null?s||(s=nu(t)):void 0;(c!==r||a!==h)&&(i=(r=c).copy()).on(o,a=h),l.on=i}}function J0(e,t,r){var i=(e+="")=="transform"?Rd:au;return t==null?this.styleTween(e,X0(e,i)).on("end.style."+e,nu(e)):typeof t=="function"?this.styleTween(e,K0(e,i,Ds(this,"style."+e,t))).each(Q0(this._id,e)):this.styleTween(e,Z0(e,i,t),r).on("end.style."+e,null)}function tx(e,t,r){return function(i){this.style.setProperty(e,t.call(this,i),r)}}function ex(e,t,r){var i,a;function n(){var o=t.apply(this,arguments);return o!==a&&(i=(a=o)&&tx(e,o,r)),i}return n._value=t,n}function rx(e,t,r){var i="style."+(e+="");if(arguments.length<2)return(i=this.tween(i))&&i._value;if(t==null)return this.tween(i,null);if(typeof t!="function")throw new Error;return this.tween(i,ex(e,t,r??""))}function ix(e){return function(){this.textContent=e}}function ax(e){return function(){var t=e(this);this.textContent=t??""}}function nx(e){return this.tween("text",typeof e=="function"?ax(Ds(this,"text",e)):ix(e==null?"":e+""))}function sx(e){return function(t){this.textContent=e.call(this,t)}}function ox(e){var t,r;function i(){var a=e.apply(this,arguments);return a!==r&&(t=(r=a)&&sx(a)),t}return i._value=e,i}function lx(e){var t="text";if(arguments.length<1)return(t=this.tween(t))&&t._value;if(e==null)return this.tween(t,null);if(typeof e!="function")throw new Error;return this.tween(t,ox(e))}function cx(){for(var e=this._name,t=this._id,r=su(),i=this._groups,a=i.length,n=0;n<a;++n)for(var o=i[n],s=o.length,l,c=0;c<s;++c)if(l=o[c]){var h=ue(l,t);Wa(l,e,r,c,o,{time:h.time+h.delay+h.duration,delay:0,duration:h.duration,ease:h.ease})}return new Me(i,this._parents,e,r)}function hx(){var e,t,r=this,i=r._id,a=r.size();return new Promise(function(n,o){var s={value:o},l={value:function(){--a===0&&n()}};r.each(function(){var c=Ce(this,i),h=c.on;h!==e&&(t=(e=h).copy(),t._.cancel.push(s),t._.interrupt.push(s),t._.end.push(l)),c.on=t}),a===0&&n()})}var ux=0;function Me(e,t,r,i){this._groups=e,this._parents=t,this._name=r,this._id=i}function su(){return++ux}var Te=$a.prototype;Me.prototype={constructor:Me,select:U0,selectAll:Y0,selectChild:Te.selectChild,selectChildren:Te.selectChildren,filter:P0,merge:N0,selection:V0,transition:cx,call:Te.call,nodes:Te.nodes,node:Te.node,size:Te.size,empty:Te.empty,each:Te.each,on:q0,attr:w0,attrTween:B0,style:J0,styleTween:rx,text:nx,textTween:lx,remove:j0,tween:g0,delay:M0,duration:$0,ease:D0,easeVarying:I0,end:hx,[Symbol.iterator]:Te[Symbol.iterator]};function fx(e){return((e*=2)<=1?e*e*e:(e-=2)*e*e+2)/2}var px={time:null,delay:0,duration:250,ease:fx};function dx(e,t){for(var r;!(r=e.__transition)||!(r=r[t]);)if(!(e=e.parentNode))throw new Error(`transition ${t} not found`);return r}function gx(e){var t,r;e instanceof Me?(t=e._id,e=e._name):(t=su(),(r=px).time=Id(),e=e==null?null:e+"");for(var i=this._groups,a=i.length,n=0;n<a;++n)for(var o=i[n],s=o.length,l,c=0;c<s;++c)(l=o[c])&&Wa(l,e,t,c,o,r||dx(l,t));return new Me(i,this._parents,e,t)}$a.prototype.interrupt=f0;$a.prototype.transition=gx;class ou{constructor(t,r){this._context=t,this._x=r}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line}point(t,r){switch(t=+t,r=+r,this._point){case 0:{this._point=1,this._line?this._context.lineTo(t,r):this._context.moveTo(t,r);break}case 1:this._point=2;default:{this._x?this._context.bezierCurveTo(this._x0=(this._x0+t)/2,this._y0,this._x0,r,t,r):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+r)/2,t,this._y0,t,r);break}}this._x0=t,this._y0=r}}function mx(e){return new ou(e,!0)}function yx(e){return new ou(e,!1)}function Qr(e,t,r){this.k=e,this.x=t,this.y=r}Qr.prototype={constructor:Qr,scale:function(e){return e===1?this:new Qr(this.k*e,this.x,this.y)},translate:function(e,t){return e===0&t===0?this:new Qr(this.k,this.x+this.k*e,this.y+this.k*t)},apply:function(e){return[e[0]*this.k+this.x,e[1]*this.k+this.y]},applyX:function(e){return e*this.k+this.x},applyY:function(e){return e*this.k+this.y},invert:function(e){return[(e[0]-this.x)/this.k,(e[1]-this.y)/this.k]},invertX:function(e){return(e-this.x)/this.k},invertY:function(e){return(e-this.y)/this.k},rescaleX:function(e){return e.copy().domain(e.range().map(this.invertX,this).map(e.invert,e))},rescaleY:function(e){return e.copy().domain(e.range().map(this.invertY,this).map(e.invert,e))},toString:function(){return"translate("+this.x+","+this.y+") scale("+this.k+")"}};Qr.prototype;var lu=typeof global=="object"&&global&&global.Object===Object&&global,xx=typeof self=="object"&&self&&self.Object===Object&&self,ke=lu||xx||Function("return this")(),ba=ke.Symbol,cu=Object.prototype,bx=cu.hasOwnProperty,Cx=cu.toString,Ur=ba?ba.toStringTag:void 0;function kx(e){var t=bx.call(e,Ur),r=e[Ur];try{e[Ur]=void 0;var i=!0}catch{}var a=Cx.call(e);return i&&(t?e[Ur]=r:delete e[Ur]),a}var wx=Object.prototype,vx=wx.toString;function Sx(e){return vx.call(e)}var Tx="[object Null]",_x="[object Undefined]",tl=ba?ba.toStringTag:void 0;function Fr(e){return e==null?e===void 0?_x:Tx:tl&&tl in Object(e)?kx(e):Sx(e)}function nr(e){var t=typeof e;return e!=null&&(t=="object"||t=="function")}var Bx="[object AsyncFunction]",Lx="[object Function]",Ax="[object GeneratorFunction]",Mx="[object Proxy]";function Rs(e){if(!nr(e))return!1;var t=Fr(e);return t==Lx||t==Ax||t==Bx||t==Mx}var bn=ke["__core-js_shared__"],el=function(){var e=/[^.]+$/.exec(bn&&bn.keys&&bn.keys.IE_PROTO||"");return e?"Symbol(src)_1."+e:""}();function Ex(e){return!!el&&el in e}var Fx=Function.prototype,$x=Fx.toString;function sr(e){if(e!=null){try{return $x.call(e)}catch{}try{return e+""}catch{}}return""}var Ox=/[\\^$.*+?()[\]{}|]/g,Dx=/^\[object .+?Constructor\]$/,Rx=Function.prototype,Ix=Object.prototype,Px=Rx.toString,Nx=Ix.hasOwnProperty,zx=RegExp("^"+Px.call(Nx).replace(Ox,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");function Wx(e){if(!nr(e)||Ex(e))return!1;var t=Rs(e)?zx:Dx;return t.test(sr(e))}function qx(e,t){return e==null?void 0:e[t]}function or(e,t){var r=qx(e,t);return Wx(r)?r:void 0}var di=or(Object,"create");function Hx(){this.__data__=di?di(null):{},this.size=0}function jx(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t}var Ux="__lodash_hash_undefined__",Yx=Object.prototype,Gx=Yx.hasOwnProperty;function Vx(e){var t=this.__data__;if(di){var r=t[e];return r===Ux?void 0:r}return Gx.call(t,e)?t[e]:void 0}var Xx=Object.prototype,Zx=Xx.hasOwnProperty;function Kx(e){var t=this.__data__;return di?t[e]!==void 0:Zx.call(t,e)}var Qx="__lodash_hash_undefined__";function Jx(e,t){var r=this.__data__;return this.size+=this.has(e)?0:1,r[e]=di&&t===void 0?Qx:t,this}function rr(e){var t=-1,r=e==null?0:e.length;for(this.clear();++t<r;){var i=e[t];this.set(i[0],i[1])}}rr.prototype.clear=Hx;rr.prototype.delete=jx;rr.prototype.get=Vx;rr.prototype.has=Kx;rr.prototype.set=Jx;function tb(){this.__data__=[],this.size=0}function qa(e,t){return e===t||e!==e&&t!==t}function Ha(e,t){for(var r=e.length;r--;)if(qa(e[r][0],t))return r;return-1}var eb=Array.prototype,rb=eb.splice;function ib(e){var t=this.__data__,r=Ha(t,e);if(r<0)return!1;var i=t.length-1;return r==i?t.pop():rb.call(t,r,1),--this.size,!0}function ab(e){var t=this.__data__,r=Ha(t,e);return r<0?void 0:t[r][1]}function nb(e){return Ha(this.__data__,e)>-1}function sb(e,t){var r=this.__data__,i=Ha(r,e);return i<0?(++this.size,r.push([e,t])):r[i][1]=t,this}function Fe(e){var t=-1,r=e==null?0:e.length;for(this.clear();++t<r;){var i=e[t];this.set(i[0],i[1])}}Fe.prototype.clear=tb;Fe.prototype.delete=ib;Fe.prototype.get=ab;Fe.prototype.has=nb;Fe.prototype.set=sb;var gi=or(ke,"Map");function ob(){this.size=0,this.__data__={hash:new rr,map:new(gi||Fe),string:new rr}}function lb(e){var t=typeof e;return t=="string"||t=="number"||t=="symbol"||t=="boolean"?e!=="__proto__":e===null}function ja(e,t){var r=e.__data__;return lb(t)?r[typeof t=="string"?"string":"hash"]:r.map}function cb(e){var t=ja(this,e).delete(e);return this.size-=t?1:0,t}function hb(e){return ja(this,e).get(e)}function ub(e){return ja(this,e).has(e)}function fb(e,t){var r=ja(this,e),i=r.size;return r.set(e,t),this.size+=r.size==i?0:1,this}function He(e){var t=-1,r=e==null?0:e.length;for(this.clear();++t<r;){var i=e[t];this.set(i[0],i[1])}}He.prototype.clear=ob;He.prototype.delete=cb;He.prototype.get=hb;He.prototype.has=ub;He.prototype.set=fb;var pb="Expected a function";function Ai(e,t){if(typeof e!="function"||t!=null&&typeof t!="function")throw new TypeError(pb);var r=function(){var i=arguments,a=t?t.apply(this,i):i[0],n=r.cache;if(n.has(a))return n.get(a);var o=e.apply(this,i);return r.cache=n.set(a,o)||n,o};return r.cache=new(Ai.Cache||He),r}Ai.Cache=He;function db(){this.__data__=new Fe,this.size=0}function gb(e){var t=this.__data__,r=t.delete(e);return this.size=t.size,r}function mb(e){return this.__data__.get(e)}function yb(e){return this.__data__.has(e)}var xb=200;function bb(e,t){var r=this.__data__;if(r instanceof Fe){var i=r.__data__;if(!gi||i.length<xb-1)return i.push([e,t]),this.size=++r.size,this;r=this.__data__=new He(i)}return r.set(e,t),this.size=r.size,this}function $r(e){var t=this.__data__=new Fe(e);this.size=t.size}$r.prototype.clear=db;$r.prototype.delete=gb;$r.prototype.get=mb;$r.prototype.has=yb;$r.prototype.set=bb;var Ca=function(){try{var e=or(Object,"defineProperty");return e({},"",{}),e}catch{}}();function Is(e,t,r){t=="__proto__"&&Ca?Ca(e,t,{configurable:!0,enumerable:!0,value:r,writable:!0}):e[t]=r}function ts(e,t,r){(r!==void 0&&!qa(e[t],r)||r===void 0&&!(t in e))&&Is(e,t,r)}function Cb(e){return function(t,r,i){for(var a=-1,n=Object(t),o=i(t),s=o.length;s--;){var l=o[++a];if(r(n[l],l,n)===!1)break}return t}}var kb=Cb(),hu=typeof exports=="object"&&exports&&!exports.nodeType&&exports,rl=hu&&typeof module=="object"&&module&&!module.nodeType&&module,wb=rl&&rl.exports===hu,il=wb?ke.Buffer:void 0,al=il?il.allocUnsafe:void 0;function vb(e,t){if(t)return e.slice();var r=e.length,i=al?al(r):new e.constructor(r);return e.copy(i),i}var nl=ke.Uint8Array;function Sb(e){var t=new e.constructor(e.byteLength);return new nl(t).set(new nl(e)),t}function Tb(e,t){var r=t?Sb(e.buffer):e.buffer;return new e.constructor(r,e.byteOffset,e.length)}function _b(e,t){var r=-1,i=e.length;for(t||(t=Array(i));++r<i;)t[r]=e[r];return t}var sl=Object.create,Bb=function(){function e(){}return function(t){if(!nr(t))return{};if(sl)return sl(t);e.prototype=t;var r=new e;return e.prototype=void 0,r}}();function uu(e,t){return function(r){return e(t(r))}}var fu=uu(Object.getPrototypeOf,Object),Lb=Object.prototype;function Ua(e){var t=e&&e.constructor,r=typeof t=="function"&&t.prototype||Lb;return e===r}function Ab(e){return typeof e.constructor=="function"&&!Ua(e)?Bb(fu(e)):{}}function Mi(e){return e!=null&&typeof e=="object"}var Mb="[object Arguments]";function ol(e){return Mi(e)&&Fr(e)==Mb}var pu=Object.prototype,Eb=pu.hasOwnProperty,Fb=pu.propertyIsEnumerable,ka=ol(function(){return arguments}())?ol:function(e){return Mi(e)&&Eb.call(e,"callee")&&!Fb.call(e,"callee")},wa=Array.isArray,$b=9007199254740991;function du(e){return typeof e=="number"&&e>-1&&e%1==0&&e<=$b}function Ya(e){return e!=null&&du(e.length)&&!Rs(e)}function Ob(e){return Mi(e)&&Ya(e)}function Db(){return!1}var gu=typeof exports=="object"&&exports&&!exports.nodeType&&exports,ll=gu&&typeof module=="object"&&module&&!module.nodeType&&module,Rb=ll&&ll.exports===gu,cl=Rb?ke.Buffer:void 0,Ib=cl?cl.isBuffer:void 0,Ps=Ib||Db,Pb="[object Object]",Nb=Function.prototype,zb=Object.prototype,mu=Nb.toString,Wb=zb.hasOwnProperty,qb=mu.call(Object);function Hb(e){if(!Mi(e)||Fr(e)!=Pb)return!1;var t=fu(e);if(t===null)return!0;var r=Wb.call(t,"constructor")&&t.constructor;return typeof r=="function"&&r instanceof r&&mu.call(r)==qb}var jb="[object Arguments]",Ub="[object Array]",Yb="[object Boolean]",Gb="[object Date]",Vb="[object Error]",Xb="[object Function]",Zb="[object Map]",Kb="[object Number]",Qb="[object Object]",Jb="[object RegExp]",tC="[object Set]",eC="[object String]",rC="[object WeakMap]",iC="[object ArrayBuffer]",aC="[object DataView]",nC="[object Float32Array]",sC="[object Float64Array]",oC="[object Int8Array]",lC="[object Int16Array]",cC="[object Int32Array]",hC="[object Uint8Array]",uC="[object Uint8ClampedArray]",fC="[object Uint16Array]",pC="[object Uint32Array]",mt={};mt[nC]=mt[sC]=mt[oC]=mt[lC]=mt[cC]=mt[hC]=mt[uC]=mt[fC]=mt[pC]=!0;mt[jb]=mt[Ub]=mt[iC]=mt[Yb]=mt[aC]=mt[Gb]=mt[Vb]=mt[Xb]=mt[Zb]=mt[Kb]=mt[Qb]=mt[Jb]=mt[tC]=mt[eC]=mt[rC]=!1;function dC(e){return Mi(e)&&du(e.length)&&!!mt[Fr(e)]}function gC(e){return function(t){return e(t)}}var yu=typeof exports=="object"&&exports&&!exports.nodeType&&exports,si=yu&&typeof module=="object"&&module&&!module.nodeType&&module,mC=si&&si.exports===yu,Cn=mC&&lu.process,hl=function(){try{var e=si&&si.require&&si.require("util").types;return e||Cn&&Cn.binding&&Cn.binding("util")}catch{}}(),ul=hl&&hl.isTypedArray,Ns=ul?gC(ul):dC;function es(e,t){if(!(t==="constructor"&&typeof e[t]=="function")&&t!="__proto__")return e[t]}var yC=Object.prototype,xC=yC.hasOwnProperty;function bC(e,t,r){var i=e[t];(!(xC.call(e,t)&&qa(i,r))||r===void 0&&!(t in e))&&Is(e,t,r)}function CC(e,t,r,i){var a=!r;r||(r={});for(var n=-1,o=t.length;++n<o;){var s=t[n],l=void 0;l===void 0&&(l=e[s]),a?Is(r,s,l):bC(r,s,l)}return r}function kC(e,t){for(var r=-1,i=Array(e);++r<e;)i[r]=t(r);return i}var wC=9007199254740991,vC=/^(?:0|[1-9]\d*)$/;function xu(e,t){var r=typeof e;return t=t??wC,!!t&&(r=="number"||r!="symbol"&&vC.test(e))&&e>-1&&e%1==0&&e<t}var SC=Object.prototype,TC=SC.hasOwnProperty;function _C(e,t){var r=wa(e),i=!r&&ka(e),a=!r&&!i&&Ps(e),n=!r&&!i&&!a&&Ns(e),o=r||i||a||n,s=o?kC(e.length,String):[],l=s.length;for(var c in e)(t||TC.call(e,c))&&!(o&&(c=="length"||a&&(c=="offset"||c=="parent")||n&&(c=="buffer"||c=="byteLength"||c=="byteOffset")||xu(c,l)))&&s.push(c);return s}function BC(e){var t=[];if(e!=null)for(var r in Object(e))t.push(r);return t}var LC=Object.prototype,AC=LC.hasOwnProperty;function MC(e){if(!nr(e))return BC(e);var t=Ua(e),r=[];for(var i in e)i=="constructor"&&(t||!AC.call(e,i))||r.push(i);return r}function bu(e){return Ya(e)?_C(e,!0):MC(e)}function EC(e){return CC(e,bu(e))}function FC(e,t,r,i,a,n,o){var s=es(e,r),l=es(t,r),c=o.get(l);if(c){ts(e,r,c);return}var h=n?n(s,l,r+"",e,t,o):void 0,u=h===void 0;if(u){var f=wa(l),p=!f&&Ps(l),g=!f&&!p&&Ns(l);h=l,f||p||g?wa(s)?h=s:Ob(s)?h=_b(s):p?(u=!1,h=vb(l,!0)):g?(u=!1,h=Tb(l,!0)):h=[]:Hb(l)||ka(l)?(h=s,ka(s)?h=EC(s):(!nr(s)||Rs(s))&&(h=Ab(l))):u=!1}u&&(o.set(l,h),a(h,l,i,n,o),o.delete(l)),ts(e,r,h)}function Cu(e,t,r,i,a){e!==t&&kb(t,function(n,o){if(a||(a=new $r),nr(n))FC(e,t,o,r,Cu,i,a);else{var s=i?i(es(e,o),n,o+"",e,t,a):void 0;s===void 0&&(s=n),ts(e,o,s)}},bu)}function ku(e){return e}function $C(e,t,r){switch(r.length){case 0:return e.call(t);case 1:return e.call(t,r[0]);case 2:return e.call(t,r[0],r[1]);case 3:return e.call(t,r[0],r[1],r[2])}return e.apply(t,r)}var fl=Math.max;function OC(e,t,r){return t=fl(t===void 0?e.length-1:t,0),function(){for(var i=arguments,a=-1,n=fl(i.length-t,0),o=Array(n);++a<n;)o[a]=i[t+a];a=-1;for(var s=Array(t+1);++a<t;)s[a]=i[a];return s[t]=r(o),$C(e,this,s)}}function DC(e){return function(){return e}}var RC=Ca?function(e,t){return Ca(e,"toString",{configurable:!0,enumerable:!1,value:DC(t),writable:!0})}:ku,IC=800,PC=16,NC=Date.now;function zC(e){var t=0,r=0;return function(){var i=NC(),a=PC-(i-r);if(r=i,a>0){if(++t>=IC)return arguments[0]}else t=0;return e.apply(void 0,arguments)}}var WC=zC(RC);function qC(e,t){return WC(OC(e,t,ku),e+"")}function HC(e,t,r){if(!nr(r))return!1;var i=typeof t;return(i=="number"?Ya(r)&&xu(t,r.length):i=="string"&&t in r)?qa(r[t],e):!1}function jC(e){return qC(function(t,r){var i=-1,a=r.length,n=a>1?r[a-1]:void 0,o=a>2?r[2]:void 0;for(n=e.length>3&&typeof n=="function"?(a--,n):void 0,o&&HC(r[0],r[1],o)&&(n=a<3?void 0:n,a=1),t=Object(t);++i<a;){var s=r[i];s&&e(t,s,i,n)}return t})}var UC=jC(function(e,t,r){Cu(e,t,r)}),YC="​",GC={curveBasis:Xi,curveBasisClosed:Pd,curveBasisOpen:Nd,curveBumpX:mx,curveBumpY:yx,curveBundle:zd,curveCardinalClosed:Wd,curveCardinalOpen:qd,curveCardinal:jl,curveCatmullRomClosed:Hd,curveCatmullRomOpen:jd,curveCatmullRom:Ud,curveLinear:An,curveLinearClosed:Yd,curveMonotoneX:Gd,curveMonotoneY:Vd,curveNatural:Xd,curveStep:Zd,curveStepAfter:Kd,curveStepBefore:Qd},VC=/\s*(?:(\w+)(?=:):|(\w+))\s*(?:(\w+)|((?:(?!}%{2}).|\r?\n)*))?\s*(?:}%{2})?/gi,XC=d(function(e,t){const r=wu(e,/(?:init\b)|(?:initialize\b)/);let i={};if(Array.isArray(r)){const o=r.map(s=>s.args);la(o),i=Et(i,[...o])}else i=r.args;if(!i)return;let a=xs(e,t);const n="config";return i[n]!==void 0&&(a==="flowchart-v2"&&(a="flowchart"),i[a]=i[n],delete i[n]),i},"detectInit"),wu=d(function(e,t=null){var r,i;try{const a=new RegExp(`[%]{2}(?![{]${VC.source})(?=[}][%]{2}).*
`,"ig");e=e.trim().replace(a,"").replace(/'/gm,'"'),O.debug(`Detecting diagram directive${t!==null?" type:"+t:""} based on the text:${e}`);let n;const o=[];for(;(n=ai.exec(e))!==null;)if(n.index===ai.lastIndex&&ai.lastIndex++,n&&!t||t&&((r=n[1])!=null&&r.match(t))||t&&((i=n[2])!=null&&i.match(t))){const s=n[1]?n[1]:n[2],l=n[3]?n[3].trim():n[4]?JSON.parse(n[4].trim()):null;o.push({type:s,args:l})}return o.length===0?{type:e,args:null}:o.length===1?o[0]:o}catch(a){return O.error(`ERROR: ${a.message} - Unable to parse directive type: '${t}' based on the text: '${e}'`),{type:void 0,args:null}}},"detectDirective"),ZC=d(function(e){return e.replace(ai,"")},"removeDirectives"),KC=d(function(e,t){for(const[r,i]of t.entries())if(i.match(e))return r;return-1},"isSubstringInArray");function zs(e,t){if(!e)return t;const r=`curve${e.charAt(0).toUpperCase()+e.slice(1)}`;return GC[r]??t}d(zs,"interpolateToCurve");function vu(e,t){const r=e.trim();if(r)return t.securityLevel!=="loose"?ru(r):r}d(vu,"formatUrl");var QC=d((e,...t)=>{const r=e.split("."),i=r.length-1,a=r[i];let n=window;for(let o=0;o<i;o++)if(n=n[r[o]],!n){O.error(`Function name: ${e} not found in window`);return}n[a](...t)},"runFunc");function Ws(e,t){return!e||!t?0:Math.sqrt(Math.pow(t.x-e.x,2)+Math.pow(t.y-e.y,2))}d(Ws,"distance");function Su(e){let t,r=0;e.forEach(a=>{r+=Ws(a,t),t=a});const i=r/2;return qs(e,i)}d(Su,"traverseEdge");function Tu(e){return e.length===1?e[0]:Su(e)}d(Tu,"calcLabelPosition");var pl=d((e,t=2)=>{const r=Math.pow(10,t);return Math.round(e*r)/r},"roundNumber"),qs=d((e,t)=>{let r,i=t;for(const a of e){if(r){const n=Ws(a,r);if(n===0)return r;if(n<i)i-=n;else{const o=i/n;if(o<=0)return r;if(o>=1)return{x:a.x,y:a.y};if(o>0&&o<1)return{x:pl((1-o)*r.x+o*a.x,5),y:pl((1-o)*r.y+o*a.y,5)}}}r=a}throw new Error("Could not find a suitable point for the given distance")},"calculatePoint"),JC=d((e,t,r)=>{O.info(`our points ${JSON.stringify(t)}`),t[0]!==r&&(t=t.reverse());const a=qs(t,25),n=e?10:5,o=Math.atan2(t[0].y-a.y,t[0].x-a.x),s={x:0,y:0};return s.x=Math.sin(o)*n+(t[0].x+a.x)/2,s.y=-Math.cos(o)*n+(t[0].y+a.y)/2,s},"calcCardinalityPosition");function _u(e,t,r){const i=structuredClone(r);O.info("our points",i),t!=="start_left"&&t!=="start_right"&&i.reverse();const a=25+e,n=qs(i,a),o=10+e*.5,s=Math.atan2(i[0].y-n.y,i[0].x-n.x),l={x:0,y:0};return t==="start_left"?(l.x=Math.sin(s+Math.PI)*o+(i[0].x+n.x)/2,l.y=-Math.cos(s+Math.PI)*o+(i[0].y+n.y)/2):t==="end_right"?(l.x=Math.sin(s-Math.PI)*o+(i[0].x+n.x)/2-5,l.y=-Math.cos(s-Math.PI)*o+(i[0].y+n.y)/2-5):t==="end_left"?(l.x=Math.sin(s)*o+(i[0].x+n.x)/2-5,l.y=-Math.cos(s)*o+(i[0].y+n.y)/2-5):(l.x=Math.sin(s)*o+(i[0].x+n.x)/2,l.y=-Math.cos(s)*o+(i[0].y+n.y)/2),l}d(_u,"calcTerminalLabelPosition");function Bu(e){let t="",r="";for(const i of e)i!==void 0&&(i.startsWith("color:")||i.startsWith("text-align:")?r=r+i+";":t=t+i+";");return{style:t,labelStyle:r}}d(Bu,"getStylesFromArray");var dl=0,t1=d(()=>(dl++,"id-"+Math.random().toString(36).substr(2,12)+"-"+dl),"generateId");function Lu(e){let t="";const r="0123456789abcdef",i=r.length;for(let a=0;a<e;a++)t+=r.charAt(Math.floor(Math.random()*i));return t}d(Lu,"makeRandomHex");var e1=d(e=>Lu(e.length),"random"),r1=d(function(){return{x:0,y:0,fill:void 0,anchor:"start",style:"#666",width:100,height:100,textMargin:0,rx:0,ry:0,valign:void 0,text:""}},"getTextObj"),i1=d(function(e,t){const r=t.text.replace(Er.lineBreakRegex," "),[,i]=Ga(t.fontSize),a=e.append("text");a.attr("x",t.x),a.attr("y",t.y),a.style("text-anchor",t.anchor),a.style("font-family",t.fontFamily),a.style("font-size",i),a.style("font-weight",t.fontWeight),a.attr("fill",t.fill),t.class!==void 0&&a.attr("class",t.class);const n=a.append("tspan");return n.attr("x",t.x+t.textMargin*2),n.attr("fill",t.fill),n.text(r),a},"drawSimpleText"),a1=Ai((e,t,r)=>{if(!e||(r=Object.assign({fontSize:12,fontWeight:400,fontFamily:"Arial",joinWith:"<br/>"},r),Er.lineBreakRegex.test(e)))return e;const i=e.split(" ").filter(Boolean),a=[];let n="";return i.forEach((o,s)=>{const l=Ee(`${o} `,r),c=Ee(n,r);if(l>t){const{hyphenatedStrings:f,remainingWord:p}=n1(o,t,"-",r);a.push(n,...f),n=p}else c+l>=t?(a.push(n),n=o):n=[n,o].filter(Boolean).join(" ");s+1===i.length&&a.push(n)}),a.filter(o=>o!=="").join(r.joinWith)},(e,t,r)=>`${e}${t}${r.fontSize}${r.fontWeight}${r.fontFamily}${r.joinWith}`),n1=Ai((e,t,r="-",i)=>{i=Object.assign({fontSize:12,fontWeight:400,fontFamily:"Arial",margin:0},i);const a=[...e],n=[];let o="";return a.forEach((s,l)=>{const c=`${o}${s}`;if(Ee(c,i)>=t){const u=l+1,f=a.length===u,p=`${c}${r}`;n.push(f?c:p),o=""}else o=c}),{hyphenatedStrings:n,remainingWord:o}},(e,t,r="-",i)=>`${e}${t}${r}${i.fontSize}${i.fontWeight}${i.fontFamily}`);function Au(e,t){return Hs(e,t).height}d(Au,"calculateTextHeight");function Ee(e,t){return Hs(e,t).width}d(Ee,"calculateTextWidth");var Hs=Ai((e,t)=>{const{fontSize:r=12,fontFamily:i="Arial",fontWeight:a=400}=t;if(!e)return{width:0,height:0};const[,n]=Ga(r),o=["sans-serif",i],s=e.split(Er.lineBreakRegex),l=[],c=lt("body");if(!c.remove)return{width:0,height:0,lineHeight:0};const h=c.append("svg");for(const f of o){let p=0;const g={width:0,height:0,lineHeight:0};for(const m of s){const y=r1();y.text=m||YC;const x=i1(h,y).style("font-size",n).style("font-weight",a).style("font-family",f),b=(x._groups||x)[0][0].getBBox();if(b.width===0&&b.height===0)throw new Error("svg element not in render tree");g.width=Math.round(Math.max(g.width,b.width)),p=Math.round(b.height),g.height+=p,g.lineHeight=Math.round(Math.max(g.lineHeight,p))}l.push(g)}h.remove();const u=isNaN(l[1].height)||isNaN(l[1].width)||isNaN(l[1].lineHeight)||l[0].height>l[1].height&&l[0].width>l[1].width&&l[0].lineHeight>l[1].lineHeight?0:1;return l[u]},(e,t)=>`${e}${t.fontSize}${t.fontWeight}${t.fontFamily}`),Ti,s1=(Ti=class{constructor(t=!1,r){this.count=0,this.count=r?r.length:0,this.next=t?()=>this.count++:()=>Date.now()}},d(Ti,"InitIDGenerator"),Ti),Hi,o1=d(function(e){return Hi=Hi||document.createElement("div"),e=escape(e).replace(/%26/g,"&").replace(/%23/g,"#").replace(/%3B/g,";"),Hi.innerHTML=e,unescape(Hi.textContent)},"entityDecode");function js(e){return"str"in e}d(js,"isDetailedError");var l1=d((e,t,r,i)=>{var n;if(!i)return;const a=(n=e.node())==null?void 0:n.getBBox();a&&e.append("text").text(i).attr("text-anchor","middle").attr("x",a.x+a.width/2).attr("y",-r).attr("class",t)},"insertTitle"),Ga=d(e=>{if(typeof e=="number")return[e,e+"px"];const t=parseInt(e??"",10);return Number.isNaN(t)?[void 0,void 0]:e===String(t)?[t,e+"px"]:[t,e]},"parseFontSize");function Us(e,t){return UC({},e,t)}d(Us,"cleanAndMerge");var ye={assignWithDepth:Et,wrapLabel:a1,calculateTextHeight:Au,calculateTextWidth:Ee,calculateTextDimensions:Hs,cleanAndMerge:Us,detectInit:XC,detectDirective:wu,isSubstringInArray:KC,interpolateToCurve:zs,calcLabelPosition:Tu,calcCardinalityPosition:JC,calcTerminalLabelPosition:_u,formatUrl:vu,getStylesFromArray:Bu,generateId:t1,random:e1,runFunc:QC,entityDecode:o1,insertTitle:l1,parseFontSize:Ga,InitIDGenerator:s1},c1=d(function(e){let t=e;return t=t.replace(/style.*:\S*#.*;/g,function(r){return r.substring(0,r.length-1)}),t=t.replace(/classDef.*:\S*#.*;/g,function(r){return r.substring(0,r.length-1)}),t=t.replace(/#\w+;/g,function(r){const i=r.substring(1,r.length-1);return/^\+?\d+$/.test(i)?"ﬂ°°"+i+"¶ß":"ﬂ°"+i+"¶ß"}),t},"encodeEntities"),lr=d(function(e){return e.replace(/ﬂ°°/g,"&#").replace(/ﬂ°/g,"&").replace(/¶ß/g,";")},"decodeEntities"),AS=d((e,t,{counter:r=0,prefix:i,suffix:a},n)=>n||`${i?`${i}_`:""}${e}_${t}_${r}${a?`_${a}`:""}`,"getEdgeId");function qt(e){return e??null}d(qt,"handleUndefinedAttr");function Ys(){return{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null}}let cr=Ys();function Mu(e){cr=e}const oi={exec:()=>null};function pt(e,t=""){let r=typeof e=="string"?e:e.source;const i={replace:(a,n)=>{let o=typeof n=="string"?n:n.source;return o=o.replace(Wt.caret,"$1"),r=r.replace(a,o),i},getRegex:()=>new RegExp(r,t)};return i}const Wt={codeRemoveIndent:/^(?: {1,4}| {0,3}\t)/gm,outputLinkReplace:/\\([\[\]])/g,indentCodeCompensation:/^(\s+)(?:```)/,beginningSpace:/^\s+/,endingHash:/#$/,startingSpaceChar:/^ /,endingSpaceChar:/ $/,nonSpaceChar:/[^ ]/,newLineCharGlobal:/\n/g,tabCharGlobal:/\t/g,multipleSpaceGlobal:/\s+/g,blankLine:/^[ \t]*$/,doubleBlankLine:/\n[ \t]*\n[ \t]*$/,blockquoteStart:/^ {0,3}>/,blockquoteSetextReplace:/\n {0,3}((?:=+|-+) *)(?=\n|$)/g,blockquoteSetextReplace2:/^ {0,3}>[ \t]?/gm,listReplaceTabs:/^\t+/,listReplaceNesting:/^ {1,4}(?=( {4})*[^ ])/g,listIsTask:/^\[[ xX]\] /,listReplaceTask:/^\[[ xX]\] +/,anyLine:/\n.*\n/,hrefBrackets:/^<(.*)>$/,tableDelimiter:/[:|]/,tableAlignChars:/^\||\| *$/g,tableRowBlankLine:/\n[ \t]*$/,tableAlignRight:/^ *-+: *$/,tableAlignCenter:/^ *:-+: *$/,tableAlignLeft:/^ *:-+ *$/,startATag:/^<a /i,endATag:/^<\/a>/i,startPreScriptTag:/^<(pre|code|kbd|script)(\s|>)/i,endPreScriptTag:/^<\/(pre|code|kbd|script)(\s|>)/i,startAngleBracket:/^</,endAngleBracket:/>$/,pedanticHrefTitle:/^([^'"]*[^\s])\s+(['"])(.*)\2/,unicodeAlphaNumeric:/[\p{L}\p{N}]/u,escapeTest:/[&<>"']/,escapeReplace:/[&<>"']/g,escapeTestNoEncode:/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,escapeReplaceNoEncode:/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/g,unescapeTest:/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/ig,caret:/(^|[^\[])\^/g,percentDecode:/%25/g,findPipe:/\|/g,splitPipe:/ \|/,slashPipe:/\\\|/g,carriageReturn:/\r\n|\r/g,spaceLine:/^ +$/gm,notSpaceStart:/^\S*/,endingNewline:/\n$/,listItemRegex:e=>new RegExp(`^( {0,3}${e})((?:[	 ][^\\n]*)?(?:\\n|$))`),nextBulletRegex:e=>new RegExp(`^ {0,${Math.min(3,e-1)}}(?:[*+-]|\\d{1,9}[.)])((?:[ 	][^\\n]*)?(?:\\n|$))`),hrRegex:e=>new RegExp(`^ {0,${Math.min(3,e-1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)`),fencesBeginRegex:e=>new RegExp(`^ {0,${Math.min(3,e-1)}}(?:\`\`\`|~~~)`),headingBeginRegex:e=>new RegExp(`^ {0,${Math.min(3,e-1)}}#`),htmlBeginRegex:e=>new RegExp(`^ {0,${Math.min(3,e-1)}}<(?:[a-z].*>|!--)`,"i")},h1=/^(?:[ \t]*(?:\n|$))+/,u1=/^((?: {4}| {0,3}\t)[^\n]+(?:\n(?:[ \t]*(?:\n|$))*)?)+/,f1=/^ {0,3}(`{3,}(?=[^`\n]*(?:\n|$))|~{3,})([^\n]*)(?:\n|$)(?:|([\s\S]*?)(?:\n|$))(?: {0,3}\1[~`]* *(?=\n|$)|$)/,Ei=/^ {0,3}((?:-[\t ]*){3,}|(?:_[ \t]*){3,}|(?:\*[ \t]*){3,})(?:\n+|$)/,p1=/^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,Gs=/(?:[*+-]|\d{1,9}[.)])/,Eu=/^(?!bull |blockCode|fences|blockquote|heading|html|table)((?:.|\n(?!\s*?\n|bull |blockCode|fences|blockquote|heading|html|table))+?)\n {0,3}(=+|-+) *(?:\n+|$)/,Fu=pt(Eu).replace(/bull/g,Gs).replace(/blockCode/g,/(?: {4}| {0,3}\t)/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\n>]+>\n/).replace(/\|table/g,"").getRegex(),d1=pt(Eu).replace(/bull/g,Gs).replace(/blockCode/g,/(?: {4}| {0,3}\t)/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\n>]+>\n/).replace(/table/g,/ {0,3}\|?(?:[:\- ]*\|)+[\:\- ]*\n/).getRegex(),Vs=/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/,g1=/^[^\n]+/,Xs=/(?!\s*\])(?:\\.|[^\[\]\\])+/,m1=pt(/^ {0,3}\[(label)\]: *(?:\n[ \t]*)?([^<\s][^\s]*|<.*?>)(?:(?: +(?:\n[ \t]*)?| *\n[ \t]*)(title))? *(?:\n+|$)/).replace("label",Xs).replace("title",/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/).getRegex(),y1=pt(/^( {0,3}bull)([ \t][^\n]+?)?(?:\n|$)/).replace(/bull/g,Gs).getRegex(),Va="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|search|section|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul",Zs=/<!--(?:-?>|[\s\S]*?(?:-->|$))/,x1=pt("^ {0,3}(?:<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$)|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$)|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$))","i").replace("comment",Zs).replace("tag",Va).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex(),$u=pt(Vs).replace("hr",Ei).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("|table","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Va).getRegex(),b1=pt(/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/).replace("paragraph",$u).getRegex(),Ks={blockquote:b1,code:u1,def:m1,fences:f1,heading:p1,hr:Ei,html:x1,lheading:Fu,list:y1,newline:h1,paragraph:$u,table:oi,text:g1},gl=pt("^ *([^\\n ].*)\\n {0,3}((?:\\| *)?:?-+:? *(?:\\| *:?-+:? *)*(?:\\| *)?)(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)").replace("hr",Ei).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("blockquote"," {0,3}>").replace("code","(?: {4}| {0,3}	)[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Va).getRegex(),C1={...Ks,lheading:d1,table:gl,paragraph:pt(Vs).replace("hr",Ei).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("table",gl).replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Va).getRegex()},k1={...Ks,html:pt(`^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:"[^"]*"|'[^']*'|\\s[^'"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))`).replace("comment",Zs).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^(#{1,6})(.*)(?:\n+|$)/,fences:oi,lheading:/^(.+?)\n {0,3}(=+|-+) *(?:\n+|$)/,paragraph:pt(Vs).replace("hr",Ei).replace("heading",` *#{1,6} *[^
]`).replace("lheading",Fu).replace("|table","").replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").replace("|tag","").getRegex()},w1=/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,v1=/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,Ou=/^( {2,}|\\)\n(?!\s*$)/,S1=/^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/,Xa=/[\p{P}\p{S}]/u,Qs=/[\s\p{P}\p{S}]/u,Du=/[^\s\p{P}\p{S}]/u,T1=pt(/^((?![*_])punctSpace)/,"u").replace(/punctSpace/g,Qs).getRegex(),Ru=/(?!~)[\p{P}\p{S}]/u,_1=/(?!~)[\s\p{P}\p{S}]/u,B1=/(?:[^\s\p{P}\p{S}]|~)/u,L1=/\[[^[\]]*?\]\((?:\\.|[^\\\(\)]|\((?:\\.|[^\\\(\)])*\))*\)|`[^`]*?`|<[^<>]*?>/g,Iu=/^(?:\*+(?:((?!\*)punct)|[^\s*]))|^_+(?:((?!_)punct)|([^\s_]))/,A1=pt(Iu,"u").replace(/punct/g,Xa).getRegex(),M1=pt(Iu,"u").replace(/punct/g,Ru).getRegex(),Pu="^[^_*]*?__[^_*]*?\\*[^_*]*?(?=__)|[^*]+(?=[^*])|(?!\\*)punct(\\*+)(?=[\\s]|$)|notPunctSpace(\\*+)(?!\\*)(?=punctSpace|$)|(?!\\*)punctSpace(\\*+)(?=notPunctSpace)|[\\s](\\*+)(?!\\*)(?=punct)|(?!\\*)punct(\\*+)(?!\\*)(?=punct)|notPunctSpace(\\*+)(?=notPunctSpace)",E1=pt(Pu,"gu").replace(/notPunctSpace/g,Du).replace(/punctSpace/g,Qs).replace(/punct/g,Xa).getRegex(),F1=pt(Pu,"gu").replace(/notPunctSpace/g,B1).replace(/punctSpace/g,_1).replace(/punct/g,Ru).getRegex(),$1=pt("^[^_*]*?\\*\\*[^_*]*?_[^_*]*?(?=\\*\\*)|[^_]+(?=[^_])|(?!_)punct(_+)(?=[\\s]|$)|notPunctSpace(_+)(?!_)(?=punctSpace|$)|(?!_)punctSpace(_+)(?=notPunctSpace)|[\\s](_+)(?!_)(?=punct)|(?!_)punct(_+)(?!_)(?=punct)","gu").replace(/notPunctSpace/g,Du).replace(/punctSpace/g,Qs).replace(/punct/g,Xa).getRegex(),O1=pt(/\\(punct)/,"gu").replace(/punct/g,Xa).getRegex(),D1=pt(/^<(scheme:[^\s\x00-\x1f<>]*|email)>/).replace("scheme",/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/).replace("email",/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/).getRegex(),R1=pt(Zs).replace("(?:-->|$)","-->").getRegex(),I1=pt("^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>").replace("comment",R1).replace("attribute",/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/).getRegex(),va=/(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/,P1=pt(/^!?\[(label)\]\(\s*(href)(?:\s+(title))?\s*\)/).replace("label",va).replace("href",/<(?:\\.|[^\n<>\\])+>|[^\s\x00-\x1f]*/).replace("title",/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/).getRegex(),Nu=pt(/^!?\[(label)\]\[(ref)\]/).replace("label",va).replace("ref",Xs).getRegex(),zu=pt(/^!?\[(ref)\](?:\[\])?/).replace("ref",Xs).getRegex(),N1=pt("reflink|nolink(?!\\()","g").replace("reflink",Nu).replace("nolink",zu).getRegex(),Js={_backpedal:oi,anyPunctuation:O1,autolink:D1,blockSkip:L1,br:Ou,code:v1,del:oi,emStrongLDelim:A1,emStrongRDelimAst:E1,emStrongRDelimUnd:$1,escape:w1,link:P1,nolink:zu,punctuation:T1,reflink:Nu,reflinkSearch:N1,tag:I1,text:S1,url:oi},z1={...Js,link:pt(/^!?\[(label)\]\((.*?)\)/).replace("label",va).getRegex(),reflink:pt(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",va).getRegex()},rs={...Js,emStrongRDelimAst:F1,emStrongLDelim:M1,url:pt(/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,"i").replace("email",/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/).getRegex(),_backpedal:/(?:[^?!.,:;*_'"~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'"~)]+(?!$))+/,del:/^(~~?)(?=[^\s~])((?:\\.|[^\\])*?(?:\\.|[^\s~\\]))\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/},W1={...rs,br:pt(Ou).replace("{2,}","*").getRegex(),text:pt(rs.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()},ji={normal:Ks,gfm:C1,pedantic:k1},Yr={normal:Js,gfm:rs,breaks:W1,pedantic:z1},q1={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},ml=e=>q1[e];function de(e,t){if(t){if(Wt.escapeTest.test(e))return e.replace(Wt.escapeReplace,ml)}else if(Wt.escapeTestNoEncode.test(e))return e.replace(Wt.escapeReplaceNoEncode,ml);return e}function yl(e){try{e=encodeURI(e).replace(Wt.percentDecode,"%")}catch{return null}return e}function xl(e,t){var n;const r=e.replace(Wt.findPipe,(o,s,l)=>{let c=!1,h=s;for(;--h>=0&&l[h]==="\\";)c=!c;return c?"|":" |"}),i=r.split(Wt.splitPipe);let a=0;if(i[0].trim()||i.shift(),i.length>0&&!((n=i.at(-1))!=null&&n.trim())&&i.pop(),t)if(i.length>t)i.splice(t);else for(;i.length<t;)i.push("");for(;a<i.length;a++)i[a]=i[a].trim().replace(Wt.slashPipe,"|");return i}function Gr(e,t,r){const i=e.length;if(i===0)return"";let a=0;for(;a<i&&e.charAt(i-a-1)===t;)a++;return e.slice(0,i-a)}function H1(e,t){if(e.indexOf(t[1])===-1)return-1;let r=0;for(let i=0;i<e.length;i++)if(e[i]==="\\")i++;else if(e[i]===t[0])r++;else if(e[i]===t[1]&&(r--,r<0))return i;return-1}function bl(e,t,r,i,a){const n=t.href,o=t.title||null,s=e[1].replace(a.other.outputLinkReplace,"$1");if(e[0].charAt(0)!=="!"){i.state.inLink=!0;const l={type:"link",raw:r,href:n,title:o,text:s,tokens:i.inlineTokens(s)};return i.state.inLink=!1,l}return{type:"image",raw:r,href:n,title:o,text:s}}function j1(e,t,r){const i=e.match(r.other.indentCodeCompensation);if(i===null)return t;const a=i[1];return t.split(`
`).map(n=>{const o=n.match(r.other.beginningSpace);if(o===null)return n;const[s]=o;return s.length>=a.length?n.slice(a.length):n}).join(`
`)}class Sa{constructor(t){dt(this,"options");dt(this,"rules");dt(this,"lexer");this.options=t||cr}space(t){const r=this.rules.block.newline.exec(t);if(r&&r[0].length>0)return{type:"space",raw:r[0]}}code(t){const r=this.rules.block.code.exec(t);if(r){const i=r[0].replace(this.rules.other.codeRemoveIndent,"");return{type:"code",raw:r[0],codeBlockStyle:"indented",text:this.options.pedantic?i:Gr(i,`
`)}}}fences(t){const r=this.rules.block.fences.exec(t);if(r){const i=r[0],a=j1(i,r[3]||"",this.rules);return{type:"code",raw:i,lang:r[2]?r[2].trim().replace(this.rules.inline.anyPunctuation,"$1"):r[2],text:a}}}heading(t){const r=this.rules.block.heading.exec(t);if(r){let i=r[2].trim();if(this.rules.other.endingHash.test(i)){const a=Gr(i,"#");(this.options.pedantic||!a||this.rules.other.endingSpaceChar.test(a))&&(i=a.trim())}return{type:"heading",raw:r[0],depth:r[1].length,text:i,tokens:this.lexer.inline(i)}}}hr(t){const r=this.rules.block.hr.exec(t);if(r)return{type:"hr",raw:Gr(r[0],`
`)}}blockquote(t){const r=this.rules.block.blockquote.exec(t);if(r){let i=Gr(r[0],`
`).split(`
`),a="",n="";const o=[];for(;i.length>0;){let s=!1;const l=[];let c;for(c=0;c<i.length;c++)if(this.rules.other.blockquoteStart.test(i[c]))l.push(i[c]),s=!0;else if(!s)l.push(i[c]);else break;i=i.slice(c);const h=l.join(`
`),u=h.replace(this.rules.other.blockquoteSetextReplace,`
    $1`).replace(this.rules.other.blockquoteSetextReplace2,"");a=a?`${a}
${h}`:h,n=n?`${n}
${u}`:u;const f=this.lexer.state.top;if(this.lexer.state.top=!0,this.lexer.blockTokens(u,o,!0),this.lexer.state.top=f,i.length===0)break;const p=o.at(-1);if((p==null?void 0:p.type)==="code")break;if((p==null?void 0:p.type)==="blockquote"){const g=p,m=g.raw+`
`+i.join(`
`),y=this.blockquote(m);o[o.length-1]=y,a=a.substring(0,a.length-g.raw.length)+y.raw,n=n.substring(0,n.length-g.text.length)+y.text;break}else if((p==null?void 0:p.type)==="list"){const g=p,m=g.raw+`
`+i.join(`
`),y=this.list(m);o[o.length-1]=y,a=a.substring(0,a.length-p.raw.length)+y.raw,n=n.substring(0,n.length-g.raw.length)+y.raw,i=m.substring(o.at(-1).raw.length).split(`
`);continue}}return{type:"blockquote",raw:a,tokens:o,text:n}}}list(t){let r=this.rules.block.list.exec(t);if(r){let i=r[1].trim();const a=i.length>1,n={type:"list",raw:"",ordered:a,start:a?+i.slice(0,-1):"",loose:!1,items:[]};i=a?`\\d{1,9}\\${i.slice(-1)}`:`\\${i}`,this.options.pedantic&&(i=a?i:"[*+-]");const o=this.rules.other.listItemRegex(i);let s=!1;for(;t;){let c=!1,h="",u="";if(!(r=o.exec(t))||this.rules.block.hr.test(t))break;h=r[0],t=t.substring(h.length);let f=r[2].split(`
`,1)[0].replace(this.rules.other.listReplaceTabs,b=>" ".repeat(3*b.length)),p=t.split(`
`,1)[0],g=!f.trim(),m=0;if(this.options.pedantic?(m=2,u=f.trimStart()):g?m=r[1].length+1:(m=r[2].search(this.rules.other.nonSpaceChar),m=m>4?1:m,u=f.slice(m),m+=r[1].length),g&&this.rules.other.blankLine.test(p)&&(h+=p+`
`,t=t.substring(p.length+1),c=!0),!c){const b=this.rules.other.nextBulletRegex(m),C=this.rules.other.hrRegex(m),S=this.rules.other.fencesBeginRegex(m),v=this.rules.other.headingBeginRegex(m),M=this.rules.other.htmlBeginRegex(m);for(;t;){const T=t.split(`
`,1)[0];let D;if(p=T,this.options.pedantic?(p=p.replace(this.rules.other.listReplaceNesting,"  "),D=p):D=p.replace(this.rules.other.tabCharGlobal,"    "),S.test(p)||v.test(p)||M.test(p)||b.test(p)||C.test(p))break;if(D.search(this.rules.other.nonSpaceChar)>=m||!p.trim())u+=`
`+D.slice(m);else{if(g||f.replace(this.rules.other.tabCharGlobal,"    ").search(this.rules.other.nonSpaceChar)>=4||S.test(f)||v.test(f)||C.test(f))break;u+=`
`+p}!g&&!p.trim()&&(g=!0),h+=T+`
`,t=t.substring(T.length+1),f=D.slice(m)}}n.loose||(s?n.loose=!0:this.rules.other.doubleBlankLine.test(h)&&(s=!0));let y=null,x;this.options.gfm&&(y=this.rules.other.listIsTask.exec(u),y&&(x=y[0]!=="[ ] ",u=u.replace(this.rules.other.listReplaceTask,""))),n.items.push({type:"list_item",raw:h,task:!!y,checked:x,loose:!1,text:u,tokens:[]}),n.raw+=h}const l=n.items.at(-1);if(l)l.raw=l.raw.trimEnd(),l.text=l.text.trimEnd();else return;n.raw=n.raw.trimEnd();for(let c=0;c<n.items.length;c++)if(this.lexer.state.top=!1,n.items[c].tokens=this.lexer.blockTokens(n.items[c].text,[]),!n.loose){const h=n.items[c].tokens.filter(f=>f.type==="space"),u=h.length>0&&h.some(f=>this.rules.other.anyLine.test(f.raw));n.loose=u}if(n.loose)for(let c=0;c<n.items.length;c++)n.items[c].loose=!0;return n}}html(t){const r=this.rules.block.html.exec(t);if(r)return{type:"html",block:!0,raw:r[0],pre:r[1]==="pre"||r[1]==="script"||r[1]==="style",text:r[0]}}def(t){const r=this.rules.block.def.exec(t);if(r){const i=r[1].toLowerCase().replace(this.rules.other.multipleSpaceGlobal," "),a=r[2]?r[2].replace(this.rules.other.hrefBrackets,"$1").replace(this.rules.inline.anyPunctuation,"$1"):"",n=r[3]?r[3].substring(1,r[3].length-1).replace(this.rules.inline.anyPunctuation,"$1"):r[3];return{type:"def",tag:i,raw:r[0],href:a,title:n}}}table(t){var s;const r=this.rules.block.table.exec(t);if(!r||!this.rules.other.tableDelimiter.test(r[2]))return;const i=xl(r[1]),a=r[2].replace(this.rules.other.tableAlignChars,"").split("|"),n=(s=r[3])!=null&&s.trim()?r[3].replace(this.rules.other.tableRowBlankLine,"").split(`
`):[],o={type:"table",raw:r[0],header:[],align:[],rows:[]};if(i.length===a.length){for(const l of a)this.rules.other.tableAlignRight.test(l)?o.align.push("right"):this.rules.other.tableAlignCenter.test(l)?o.align.push("center"):this.rules.other.tableAlignLeft.test(l)?o.align.push("left"):o.align.push(null);for(let l=0;l<i.length;l++)o.header.push({text:i[l],tokens:this.lexer.inline(i[l]),header:!0,align:o.align[l]});for(const l of n)o.rows.push(xl(l,o.header.length).map((c,h)=>({text:c,tokens:this.lexer.inline(c),header:!1,align:o.align[h]})));return o}}lheading(t){const r=this.rules.block.lheading.exec(t);if(r)return{type:"heading",raw:r[0],depth:r[2].charAt(0)==="="?1:2,text:r[1],tokens:this.lexer.inline(r[1])}}paragraph(t){const r=this.rules.block.paragraph.exec(t);if(r){const i=r[1].charAt(r[1].length-1)===`
`?r[1].slice(0,-1):r[1];return{type:"paragraph",raw:r[0],text:i,tokens:this.lexer.inline(i)}}}text(t){const r=this.rules.block.text.exec(t);if(r)return{type:"text",raw:r[0],text:r[0],tokens:this.lexer.inline(r[0])}}escape(t){const r=this.rules.inline.escape.exec(t);if(r)return{type:"escape",raw:r[0],text:r[1]}}tag(t){const r=this.rules.inline.tag.exec(t);if(r)return!this.lexer.state.inLink&&this.rules.other.startATag.test(r[0])?this.lexer.state.inLink=!0:this.lexer.state.inLink&&this.rules.other.endATag.test(r[0])&&(this.lexer.state.inLink=!1),!this.lexer.state.inRawBlock&&this.rules.other.startPreScriptTag.test(r[0])?this.lexer.state.inRawBlock=!0:this.lexer.state.inRawBlock&&this.rules.other.endPreScriptTag.test(r[0])&&(this.lexer.state.inRawBlock=!1),{type:"html",raw:r[0],inLink:this.lexer.state.inLink,inRawBlock:this.lexer.state.inRawBlock,block:!1,text:r[0]}}link(t){const r=this.rules.inline.link.exec(t);if(r){const i=r[2].trim();if(!this.options.pedantic&&this.rules.other.startAngleBracket.test(i)){if(!this.rules.other.endAngleBracket.test(i))return;const o=Gr(i.slice(0,-1),"\\");if((i.length-o.length)%2===0)return}else{const o=H1(r[2],"()");if(o>-1){const l=(r[0].indexOf("!")===0?5:4)+r[1].length+o;r[2]=r[2].substring(0,o),r[0]=r[0].substring(0,l).trim(),r[3]=""}}let a=r[2],n="";if(this.options.pedantic){const o=this.rules.other.pedanticHrefTitle.exec(a);o&&(a=o[1],n=o[3])}else n=r[3]?r[3].slice(1,-1):"";return a=a.trim(),this.rules.other.startAngleBracket.test(a)&&(this.options.pedantic&&!this.rules.other.endAngleBracket.test(i)?a=a.slice(1):a=a.slice(1,-1)),bl(r,{href:a&&a.replace(this.rules.inline.anyPunctuation,"$1"),title:n&&n.replace(this.rules.inline.anyPunctuation,"$1")},r[0],this.lexer,this.rules)}}reflink(t,r){let i;if((i=this.rules.inline.reflink.exec(t))||(i=this.rules.inline.nolink.exec(t))){const a=(i[2]||i[1]).replace(this.rules.other.multipleSpaceGlobal," "),n=r[a.toLowerCase()];if(!n){const o=i[0].charAt(0);return{type:"text",raw:o,text:o}}return bl(i,n,i[0],this.lexer,this.rules)}}emStrong(t,r,i=""){let a=this.rules.inline.emStrongLDelim.exec(t);if(!a||a[3]&&i.match(this.rules.other.unicodeAlphaNumeric))return;if(!(a[1]||a[2]||"")||!i||this.rules.inline.punctuation.exec(i)){const o=[...a[0]].length-1;let s,l,c=o,h=0;const u=a[0][0]==="*"?this.rules.inline.emStrongRDelimAst:this.rules.inline.emStrongRDelimUnd;for(u.lastIndex=0,r=r.slice(-1*t.length+o);(a=u.exec(r))!=null;){if(s=a[1]||a[2]||a[3]||a[4]||a[5]||a[6],!s)continue;if(l=[...s].length,a[3]||a[4]){c+=l;continue}else if((a[5]||a[6])&&o%3&&!((o+l)%3)){h+=l;continue}if(c-=l,c>0)continue;l=Math.min(l,l+c+h);const f=[...a[0]][0].length,p=t.slice(0,o+a.index+f+l);if(Math.min(o,l)%2){const m=p.slice(1,-1);return{type:"em",raw:p,text:m,tokens:this.lexer.inlineTokens(m)}}const g=p.slice(2,-2);return{type:"strong",raw:p,text:g,tokens:this.lexer.inlineTokens(g)}}}}codespan(t){const r=this.rules.inline.code.exec(t);if(r){let i=r[2].replace(this.rules.other.newLineCharGlobal," ");const a=this.rules.other.nonSpaceChar.test(i),n=this.rules.other.startingSpaceChar.test(i)&&this.rules.other.endingSpaceChar.test(i);return a&&n&&(i=i.substring(1,i.length-1)),{type:"codespan",raw:r[0],text:i}}}br(t){const r=this.rules.inline.br.exec(t);if(r)return{type:"br",raw:r[0]}}del(t){const r=this.rules.inline.del.exec(t);if(r)return{type:"del",raw:r[0],text:r[2],tokens:this.lexer.inlineTokens(r[2])}}autolink(t){const r=this.rules.inline.autolink.exec(t);if(r){let i,a;return r[2]==="@"?(i=r[1],a="mailto:"+i):(i=r[1],a=i),{type:"link",raw:r[0],text:i,href:a,tokens:[{type:"text",raw:i,text:i}]}}}url(t){var i;let r;if(r=this.rules.inline.url.exec(t)){let a,n;if(r[2]==="@")a=r[0],n="mailto:"+a;else{let o;do o=r[0],r[0]=((i=this.rules.inline._backpedal.exec(r[0]))==null?void 0:i[0])??"";while(o!==r[0]);a=r[0],r[1]==="www."?n="http://"+r[0]:n=r[0]}return{type:"link",raw:r[0],text:a,href:n,tokens:[{type:"text",raw:a,text:a}]}}}inlineText(t){const r=this.rules.inline.text.exec(t);if(r){const i=this.lexer.state.inRawBlock;return{type:"text",raw:r[0],text:r[0],escaped:i}}}}class re{constructor(t){dt(this,"tokens");dt(this,"options");dt(this,"state");dt(this,"tokenizer");dt(this,"inlineQueue");this.tokens=[],this.tokens.links=Object.create(null),this.options=t||cr,this.options.tokenizer=this.options.tokenizer||new Sa,this.tokenizer=this.options.tokenizer,this.tokenizer.options=this.options,this.tokenizer.lexer=this,this.inlineQueue=[],this.state={inLink:!1,inRawBlock:!1,top:!0};const r={other:Wt,block:ji.normal,inline:Yr.normal};this.options.pedantic?(r.block=ji.pedantic,r.inline=Yr.pedantic):this.options.gfm&&(r.block=ji.gfm,this.options.breaks?r.inline=Yr.breaks:r.inline=Yr.gfm),this.tokenizer.rules=r}static get rules(){return{block:ji,inline:Yr}}static lex(t,r){return new re(r).lex(t)}static lexInline(t,r){return new re(r).inlineTokens(t)}lex(t){t=t.replace(Wt.carriageReturn,`
`),this.blockTokens(t,this.tokens);for(let r=0;r<this.inlineQueue.length;r++){const i=this.inlineQueue[r];this.inlineTokens(i.src,i.tokens)}return this.inlineQueue=[],this.tokens}blockTokens(t,r=[],i=!1){var a,n,o;for(this.options.pedantic&&(t=t.replace(Wt.tabCharGlobal,"    ").replace(Wt.spaceLine,""));t;){let s;if((n=(a=this.options.extensions)==null?void 0:a.block)!=null&&n.some(c=>(s=c.call({lexer:this},t,r))?(t=t.substring(s.raw.length),r.push(s),!0):!1))continue;if(s=this.tokenizer.space(t)){t=t.substring(s.raw.length);const c=r.at(-1);s.raw.length===1&&c!==void 0?c.raw+=`
`:r.push(s);continue}if(s=this.tokenizer.code(t)){t=t.substring(s.raw.length);const c=r.at(-1);(c==null?void 0:c.type)==="paragraph"||(c==null?void 0:c.type)==="text"?(c.raw+=`
`+s.raw,c.text+=`
`+s.text,this.inlineQueue.at(-1).src=c.text):r.push(s);continue}if(s=this.tokenizer.fences(t)){t=t.substring(s.raw.length),r.push(s);continue}if(s=this.tokenizer.heading(t)){t=t.substring(s.raw.length),r.push(s);continue}if(s=this.tokenizer.hr(t)){t=t.substring(s.raw.length),r.push(s);continue}if(s=this.tokenizer.blockquote(t)){t=t.substring(s.raw.length),r.push(s);continue}if(s=this.tokenizer.list(t)){t=t.substring(s.raw.length),r.push(s);continue}if(s=this.tokenizer.html(t)){t=t.substring(s.raw.length),r.push(s);continue}if(s=this.tokenizer.def(t)){t=t.substring(s.raw.length);const c=r.at(-1);(c==null?void 0:c.type)==="paragraph"||(c==null?void 0:c.type)==="text"?(c.raw+=`
`+s.raw,c.text+=`
`+s.raw,this.inlineQueue.at(-1).src=c.text):this.tokens.links[s.tag]||(this.tokens.links[s.tag]={href:s.href,title:s.title});continue}if(s=this.tokenizer.table(t)){t=t.substring(s.raw.length),r.push(s);continue}if(s=this.tokenizer.lheading(t)){t=t.substring(s.raw.length),r.push(s);continue}let l=t;if((o=this.options.extensions)!=null&&o.startBlock){let c=1/0;const h=t.slice(1);let u;this.options.extensions.startBlock.forEach(f=>{u=f.call({lexer:this},h),typeof u=="number"&&u>=0&&(c=Math.min(c,u))}),c<1/0&&c>=0&&(l=t.substring(0,c+1))}if(this.state.top&&(s=this.tokenizer.paragraph(l))){const c=r.at(-1);i&&(c==null?void 0:c.type)==="paragraph"?(c.raw+=`
`+s.raw,c.text+=`
`+s.text,this.inlineQueue.pop(),this.inlineQueue.at(-1).src=c.text):r.push(s),i=l.length!==t.length,t=t.substring(s.raw.length);continue}if(s=this.tokenizer.text(t)){t=t.substring(s.raw.length);const c=r.at(-1);(c==null?void 0:c.type)==="text"?(c.raw+=`
`+s.raw,c.text+=`
`+s.text,this.inlineQueue.pop(),this.inlineQueue.at(-1).src=c.text):r.push(s);continue}if(t){const c="Infinite loop on byte: "+t.charCodeAt(0);if(this.options.silent){console.error(c);break}else throw new Error(c)}}return this.state.top=!0,r}inline(t,r=[]){return this.inlineQueue.push({src:t,tokens:r}),r}inlineTokens(t,r=[]){var s,l,c;let i=t,a=null;if(this.tokens.links){const h=Object.keys(this.tokens.links);if(h.length>0)for(;(a=this.tokenizer.rules.inline.reflinkSearch.exec(i))!=null;)h.includes(a[0].slice(a[0].lastIndexOf("[")+1,-1))&&(i=i.slice(0,a.index)+"["+"a".repeat(a[0].length-2)+"]"+i.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))}for(;(a=this.tokenizer.rules.inline.blockSkip.exec(i))!=null;)i=i.slice(0,a.index)+"["+"a".repeat(a[0].length-2)+"]"+i.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);for(;(a=this.tokenizer.rules.inline.anyPunctuation.exec(i))!=null;)i=i.slice(0,a.index)+"++"+i.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);let n=!1,o="";for(;t;){n||(o=""),n=!1;let h;if((l=(s=this.options.extensions)==null?void 0:s.inline)!=null&&l.some(f=>(h=f.call({lexer:this},t,r))?(t=t.substring(h.raw.length),r.push(h),!0):!1))continue;if(h=this.tokenizer.escape(t)){t=t.substring(h.raw.length),r.push(h);continue}if(h=this.tokenizer.tag(t)){t=t.substring(h.raw.length),r.push(h);continue}if(h=this.tokenizer.link(t)){t=t.substring(h.raw.length),r.push(h);continue}if(h=this.tokenizer.reflink(t,this.tokens.links)){t=t.substring(h.raw.length);const f=r.at(-1);h.type==="text"&&(f==null?void 0:f.type)==="text"?(f.raw+=h.raw,f.text+=h.text):r.push(h);continue}if(h=this.tokenizer.emStrong(t,i,o)){t=t.substring(h.raw.length),r.push(h);continue}if(h=this.tokenizer.codespan(t)){t=t.substring(h.raw.length),r.push(h);continue}if(h=this.tokenizer.br(t)){t=t.substring(h.raw.length),r.push(h);continue}if(h=this.tokenizer.del(t)){t=t.substring(h.raw.length),r.push(h);continue}if(h=this.tokenizer.autolink(t)){t=t.substring(h.raw.length),r.push(h);continue}if(!this.state.inLink&&(h=this.tokenizer.url(t))){t=t.substring(h.raw.length),r.push(h);continue}let u=t;if((c=this.options.extensions)!=null&&c.startInline){let f=1/0;const p=t.slice(1);let g;this.options.extensions.startInline.forEach(m=>{g=m.call({lexer:this},p),typeof g=="number"&&g>=0&&(f=Math.min(f,g))}),f<1/0&&f>=0&&(u=t.substring(0,f+1))}if(h=this.tokenizer.inlineText(u)){t=t.substring(h.raw.length),h.raw.slice(-1)!=="_"&&(o=h.raw.slice(-1)),n=!0;const f=r.at(-1);(f==null?void 0:f.type)==="text"?(f.raw+=h.raw,f.text+=h.text):r.push(h);continue}if(t){const f="Infinite loop on byte: "+t.charCodeAt(0);if(this.options.silent){console.error(f);break}else throw new Error(f)}}return r}}class Ta{constructor(t){dt(this,"options");dt(this,"parser");this.options=t||cr}space(t){return""}code({text:t,lang:r,escaped:i}){var o;const a=(o=(r||"").match(Wt.notSpaceStart))==null?void 0:o[0],n=t.replace(Wt.endingNewline,"")+`
`;return a?'<pre><code class="language-'+de(a)+'">'+(i?n:de(n,!0))+`</code></pre>
`:"<pre><code>"+(i?n:de(n,!0))+`</code></pre>
`}blockquote({tokens:t}){return`<blockquote>
${this.parser.parse(t)}</blockquote>
`}html({text:t}){return t}heading({tokens:t,depth:r}){return`<h${r}>${this.parser.parseInline(t)}</h${r}>
`}hr(t){return`<hr>
`}list(t){const r=t.ordered,i=t.start;let a="";for(let s=0;s<t.items.length;s++){const l=t.items[s];a+=this.listitem(l)}const n=r?"ol":"ul",o=r&&i!==1?' start="'+i+'"':"";return"<"+n+o+`>
`+a+"</"+n+`>
`}listitem(t){var i;let r="";if(t.task){const a=this.checkbox({checked:!!t.checked});t.loose?((i=t.tokens[0])==null?void 0:i.type)==="paragraph"?(t.tokens[0].text=a+" "+t.tokens[0].text,t.tokens[0].tokens&&t.tokens[0].tokens.length>0&&t.tokens[0].tokens[0].type==="text"&&(t.tokens[0].tokens[0].text=a+" "+de(t.tokens[0].tokens[0].text),t.tokens[0].tokens[0].escaped=!0)):t.tokens.unshift({type:"text",raw:a+" ",text:a+" ",escaped:!0}):r+=a+" "}return r+=this.parser.parse(t.tokens,!!t.loose),`<li>${r}</li>
`}checkbox({checked:t}){return"<input "+(t?'checked="" ':"")+'disabled="" type="checkbox">'}paragraph({tokens:t}){return`<p>${this.parser.parseInline(t)}</p>
`}table(t){let r="",i="";for(let n=0;n<t.header.length;n++)i+=this.tablecell(t.header[n]);r+=this.tablerow({text:i});let a="";for(let n=0;n<t.rows.length;n++){const o=t.rows[n];i="";for(let s=0;s<o.length;s++)i+=this.tablecell(o[s]);a+=this.tablerow({text:i})}return a&&(a=`<tbody>${a}</tbody>`),`<table>
<thead>
`+r+`</thead>
`+a+`</table>
`}tablerow({text:t}){return`<tr>
${t}</tr>
`}tablecell(t){const r=this.parser.parseInline(t.tokens),i=t.header?"th":"td";return(t.align?`<${i} align="${t.align}">`:`<${i}>`)+r+`</${i}>
`}strong({tokens:t}){return`<strong>${this.parser.parseInline(t)}</strong>`}em({tokens:t}){return`<em>${this.parser.parseInline(t)}</em>`}codespan({text:t}){return`<code>${de(t,!0)}</code>`}br(t){return"<br>"}del({tokens:t}){return`<del>${this.parser.parseInline(t)}</del>`}link({href:t,title:r,tokens:i}){const a=this.parser.parseInline(i),n=yl(t);if(n===null)return a;t=n;let o='<a href="'+t+'"';return r&&(o+=' title="'+de(r)+'"'),o+=">"+a+"</a>",o}image({href:t,title:r,text:i}){const a=yl(t);if(a===null)return de(i);t=a;let n=`<img src="${t}" alt="${i}"`;return r&&(n+=` title="${de(r)}"`),n+=">",n}text(t){return"tokens"in t&&t.tokens?this.parser.parseInline(t.tokens):"escaped"in t&&t.escaped?t.text:de(t.text)}}class to{strong({text:t}){return t}em({text:t}){return t}codespan({text:t}){return t}del({text:t}){return t}html({text:t}){return t}text({text:t}){return t}link({text:t}){return""+t}image({text:t}){return""+t}br(){return""}}class ie{constructor(t){dt(this,"options");dt(this,"renderer");dt(this,"textRenderer");this.options=t||cr,this.options.renderer=this.options.renderer||new Ta,this.renderer=this.options.renderer,this.renderer.options=this.options,this.renderer.parser=this,this.textRenderer=new to}static parse(t,r){return new ie(r).parse(t)}static parseInline(t,r){return new ie(r).parseInline(t)}parse(t,r=!0){var a,n;let i="";for(let o=0;o<t.length;o++){const s=t[o];if((n=(a=this.options.extensions)==null?void 0:a.renderers)!=null&&n[s.type]){const c=s,h=this.options.extensions.renderers[c.type].call({parser:this},c);if(h!==!1||!["space","hr","heading","code","table","blockquote","list","html","paragraph","text"].includes(c.type)){i+=h||"";continue}}const l=s;switch(l.type){case"space":{i+=this.renderer.space(l);continue}case"hr":{i+=this.renderer.hr(l);continue}case"heading":{i+=this.renderer.heading(l);continue}case"code":{i+=this.renderer.code(l);continue}case"table":{i+=this.renderer.table(l);continue}case"blockquote":{i+=this.renderer.blockquote(l);continue}case"list":{i+=this.renderer.list(l);continue}case"html":{i+=this.renderer.html(l);continue}case"paragraph":{i+=this.renderer.paragraph(l);continue}case"text":{let c=l,h=this.renderer.text(c);for(;o+1<t.length&&t[o+1].type==="text";)c=t[++o],h+=`
`+this.renderer.text(c);r?i+=this.renderer.paragraph({type:"paragraph",raw:h,text:h,tokens:[{type:"text",raw:h,text:h,escaped:!0}]}):i+=h;continue}default:{const c='Token with "'+l.type+'" type was not found.';if(this.options.silent)return console.error(c),"";throw new Error(c)}}}return i}parseInline(t,r=this.renderer){var a,n;let i="";for(let o=0;o<t.length;o++){const s=t[o];if((n=(a=this.options.extensions)==null?void 0:a.renderers)!=null&&n[s.type]){const c=this.options.extensions.renderers[s.type].call({parser:this},s);if(c!==!1||!["escape","html","link","image","strong","em","codespan","br","del","text"].includes(s.type)){i+=c||"";continue}}const l=s;switch(l.type){case"escape":{i+=r.text(l);break}case"html":{i+=r.html(l);break}case"link":{i+=r.link(l);break}case"image":{i+=r.image(l);break}case"strong":{i+=r.strong(l);break}case"em":{i+=r.em(l);break}case"codespan":{i+=r.codespan(l);break}case"br":{i+=r.br(l);break}case"del":{i+=r.del(l);break}case"text":{i+=r.text(l);break}default:{const c='Token with "'+l.type+'" type was not found.';if(this.options.silent)return console.error(c),"";throw new Error(c)}}}return i}}class li{constructor(t){dt(this,"options");dt(this,"block");this.options=t||cr}preprocess(t){return t}postprocess(t){return t}processAllTokens(t){return t}provideLexer(){return this.block?re.lex:re.lexInline}provideParser(){return this.block?ie.parse:ie.parseInline}}dt(li,"passThroughHooks",new Set(["preprocess","postprocess","processAllTokens"]));class U1{constructor(...t){dt(this,"defaults",Ys());dt(this,"options",this.setOptions);dt(this,"parse",this.parseMarkdown(!0));dt(this,"parseInline",this.parseMarkdown(!1));dt(this,"Parser",ie);dt(this,"Renderer",Ta);dt(this,"TextRenderer",to);dt(this,"Lexer",re);dt(this,"Tokenizer",Sa);dt(this,"Hooks",li);this.use(...t)}walkTokens(t,r){var a,n;let i=[];for(const o of t)switch(i=i.concat(r.call(this,o)),o.type){case"table":{const s=o;for(const l of s.header)i=i.concat(this.walkTokens(l.tokens,r));for(const l of s.rows)for(const c of l)i=i.concat(this.walkTokens(c.tokens,r));break}case"list":{const s=o;i=i.concat(this.walkTokens(s.items,r));break}default:{const s=o;(n=(a=this.defaults.extensions)==null?void 0:a.childTokens)!=null&&n[s.type]?this.defaults.extensions.childTokens[s.type].forEach(l=>{const c=s[l].flat(1/0);i=i.concat(this.walkTokens(c,r))}):s.tokens&&(i=i.concat(this.walkTokens(s.tokens,r)))}}return i}use(...t){const r=this.defaults.extensions||{renderers:{},childTokens:{}};return t.forEach(i=>{const a={...i};if(a.async=this.defaults.async||a.async||!1,i.extensions&&(i.extensions.forEach(n=>{if(!n.name)throw new Error("extension name required");if("renderer"in n){const o=r.renderers[n.name];o?r.renderers[n.name]=function(...s){let l=n.renderer.apply(this,s);return l===!1&&(l=o.apply(this,s)),l}:r.renderers[n.name]=n.renderer}if("tokenizer"in n){if(!n.level||n.level!=="block"&&n.level!=="inline")throw new Error("extension level must be 'block' or 'inline'");const o=r[n.level];o?o.unshift(n.tokenizer):r[n.level]=[n.tokenizer],n.start&&(n.level==="block"?r.startBlock?r.startBlock.push(n.start):r.startBlock=[n.start]:n.level==="inline"&&(r.startInline?r.startInline.push(n.start):r.startInline=[n.start]))}"childTokens"in n&&n.childTokens&&(r.childTokens[n.name]=n.childTokens)}),a.extensions=r),i.renderer){const n=this.defaults.renderer||new Ta(this.defaults);for(const o in i.renderer){if(!(o in n))throw new Error(`renderer '${o}' does not exist`);if(["options","parser"].includes(o))continue;const s=o,l=i.renderer[s],c=n[s];n[s]=(...h)=>{let u=l.apply(n,h);return u===!1&&(u=c.apply(n,h)),u||""}}a.renderer=n}if(i.tokenizer){const n=this.defaults.tokenizer||new Sa(this.defaults);for(const o in i.tokenizer){if(!(o in n))throw new Error(`tokenizer '${o}' does not exist`);if(["options","rules","lexer"].includes(o))continue;const s=o,l=i.tokenizer[s],c=n[s];n[s]=(...h)=>{let u=l.apply(n,h);return u===!1&&(u=c.apply(n,h)),u}}a.tokenizer=n}if(i.hooks){const n=this.defaults.hooks||new li;for(const o in i.hooks){if(!(o in n))throw new Error(`hook '${o}' does not exist`);if(["options","block"].includes(o))continue;const s=o,l=i.hooks[s],c=n[s];li.passThroughHooks.has(o)?n[s]=h=>{if(this.defaults.async)return Promise.resolve(l.call(n,h)).then(f=>c.call(n,f));const u=l.call(n,h);return c.call(n,u)}:n[s]=(...h)=>{let u=l.apply(n,h);return u===!1&&(u=c.apply(n,h)),u}}a.hooks=n}if(i.walkTokens){const n=this.defaults.walkTokens,o=i.walkTokens;a.walkTokens=function(s){let l=[];return l.push(o.call(this,s)),n&&(l=l.concat(n.call(this,s))),l}}this.defaults={...this.defaults,...a}}),this}setOptions(t){return this.defaults={...this.defaults,...t},this}lexer(t,r){return re.lex(t,r??this.defaults)}parser(t,r){return ie.parse(t,r??this.defaults)}parseMarkdown(t){return(i,a)=>{const n={...a},o={...this.defaults,...n},s=this.onError(!!o.silent,!!o.async);if(this.defaults.async===!0&&n.async===!1)return s(new Error("marked(): The async option was set to true by an extension. Remove async: false from the parse options object to return a Promise."));if(typeof i>"u"||i===null)return s(new Error("marked(): input parameter is undefined or null"));if(typeof i!="string")return s(new Error("marked(): input parameter is of type "+Object.prototype.toString.call(i)+", string expected"));o.hooks&&(o.hooks.options=o,o.hooks.block=t);const l=o.hooks?o.hooks.provideLexer():t?re.lex:re.lexInline,c=o.hooks?o.hooks.provideParser():t?ie.parse:ie.parseInline;if(o.async)return Promise.resolve(o.hooks?o.hooks.preprocess(i):i).then(h=>l(h,o)).then(h=>o.hooks?o.hooks.processAllTokens(h):h).then(h=>o.walkTokens?Promise.all(this.walkTokens(h,o.walkTokens)).then(()=>h):h).then(h=>c(h,o)).then(h=>o.hooks?o.hooks.postprocess(h):h).catch(s);try{o.hooks&&(i=o.hooks.preprocess(i));let h=l(i,o);o.hooks&&(h=o.hooks.processAllTokens(h)),o.walkTokens&&this.walkTokens(h,o.walkTokens);let u=c(h,o);return o.hooks&&(u=o.hooks.postprocess(u)),u}catch(h){return s(h)}}}onError(t,r){return i=>{if(i.message+=`
Please report this to https://github.com/markedjs/marked.`,t){const a="<p>An error occurred:</p><pre>"+de(i.message+"",!0)+"</pre>";return r?Promise.resolve(a):a}if(r)return Promise.reject(i);throw i}}}const ir=new U1;function ft(e,t){return ir.parse(e,t)}ft.options=ft.setOptions=function(e){return ir.setOptions(e),ft.defaults=ir.defaults,Mu(ft.defaults),ft};ft.getDefaults=Ys;ft.defaults=cr;ft.use=function(...e){return ir.use(...e),ft.defaults=ir.defaults,Mu(ft.defaults),ft};ft.walkTokens=function(e,t){return ir.walkTokens(e,t)};ft.parseInline=ir.parseInline;ft.Parser=ie;ft.parser=ie.parse;ft.Renderer=Ta;ft.TextRenderer=to;ft.Lexer=re;ft.lexer=re.lex;ft.Tokenizer=Sa;ft.Hooks=li;ft.parse=ft;ft.options;ft.setOptions;ft.use;ft.walkTokens;ft.parseInline;ie.parse;re.lex;function Wu(e){for(var t=[],r=1;r<arguments.length;r++)t[r-1]=arguments[r];var i=Array.from(typeof e=="string"?[e]:e);i[i.length-1]=i[i.length-1].replace(/\r?\n([\t ]*)$/,"");var a=i.reduce(function(s,l){var c=l.match(/\n([\t ]+|(?!\s).)/g);return c?s.concat(c.map(function(h){var u,f;return(f=(u=h.match(/[\t ]/g))===null||u===void 0?void 0:u.length)!==null&&f!==void 0?f:0})):s},[]);if(a.length){var n=new RegExp(`
[	 ]{`+Math.min.apply(Math,a)+"}","g");i=i.map(function(s){return s.replace(n,`
`)})}i[0]=i[0].replace(/^\r?\n/,"");var o=i[0];return t.forEach(function(s,l){var c=o.match(/(?:^|\n)( *)$/),h=c?c[1]:"",u=s;typeof s=="string"&&s.includes(`
`)&&(u=String(s).split(`
`).map(function(f,p){return p===0?f:""+h+f}).join(`
`)),o+=u+i[l+1]}),o}function qu(e,{markdownAutoWrap:t}){const i=e.replace(/<br\/>/g,`
`).replace(/\n{2,}/g,`
`),a=Wu(i);return t===!1?a.replace(/ /g,"&nbsp;"):a}d(qu,"preprocessMarkdown");function Hu(e,t={}){const r=qu(e,t),i=ft.lexer(r),a=[[]];let n=0;function o(s,l="normal"){s.type==="text"?s.text.split(`
`).forEach((h,u)=>{u!==0&&(n++,a.push([])),h.split(" ").forEach(f=>{f=f.replace(/&#39;/g,"'"),f&&a[n].push({content:f,type:l})})}):s.type==="strong"||s.type==="em"?s.tokens.forEach(c=>{o(c,s.type)}):s.type==="html"&&a[n].push({content:s.text,type:"normal"})}return d(o,"processNode"),i.forEach(s=>{var l;s.type==="paragraph"?(l=s.tokens)==null||l.forEach(c=>{o(c)}):s.type==="html"&&a[n].push({content:s.text,type:"normal"})}),a}d(Hu,"markdownToLines");function ju(e,{markdownAutoWrap:t}={}){const r=ft.lexer(e);function i(a){var n,o,s;return a.type==="text"?t===!1?a.text.replace(/\n */g,"<br/>").replace(/ /g,"&nbsp;"):a.text.replace(/\n */g,"<br/>"):a.type==="strong"?`<strong>${(n=a.tokens)==null?void 0:n.map(i).join("")}</strong>`:a.type==="em"?`<em>${(o=a.tokens)==null?void 0:o.map(i).join("")}</em>`:a.type==="paragraph"?`<p>${(s=a.tokens)==null?void 0:s.map(i).join("")}</p>`:a.type==="space"?"":a.type==="html"?`${a.text}`:a.type==="escape"?a.text:`Unsupported markdown: ${a.type}`}return d(i,"output"),r.map(i).join("")}d(ju,"markdownToHTML");function Uu(e){return Intl.Segmenter?[...new Intl.Segmenter().segment(e)].map(t=>t.segment):[...e]}d(Uu,"splitTextToChars");function Yu(e,t){const r=Uu(t.content);return eo(e,[],r,t.type)}d(Yu,"splitWordToFitWidth");function eo(e,t,r,i){if(r.length===0)return[{content:t.join(""),type:i},{content:"",type:i}];const[a,...n]=r,o=[...t,a];return e([{content:o.join(""),type:i}])?eo(e,o,n,i):(t.length===0&&a&&(t.push(a),r.shift()),[{content:t.join(""),type:i},{content:r.join(""),type:i}])}d(eo,"splitWordToFitWidthRecursion");function Gu(e,t){if(e.some(({content:r})=>r.includes(`
`)))throw new Error("splitLineToFitWidth does not support newlines in the line");return _a(e,t)}d(Gu,"splitLineToFitWidth");function _a(e,t,r=[],i=[]){if(e.length===0)return i.length>0&&r.push(i),r.length>0?r:[];let a="";e[0].content===" "&&(a=" ",e.shift());const n=e.shift()??{content:" ",type:"normal"},o=[...i];if(a!==""&&o.push({content:a,type:"normal"}),o.push(n),t(o))return _a(e,t,r,o);if(i.length>0)r.push(i),e.unshift(n);else if(n.content){const[s,l]=Yu(t,n);r.push([s]),l.content&&e.unshift(l)}return _a(e,t,r)}d(_a,"splitLineToFitWidthRecursion");function is(e,t){t&&e.attr("style",t)}d(is,"applyStyle");async function Vu(e,t,r,i,a=!1){const n=e.append("foreignObject");n.attr("width",`${10*r}px`),n.attr("height",`${10*r}px`);const o=n.append("xhtml:div");let s=t.label;t.label&&Tr(t.label)&&(s=await bs(t.label.replace(Er.lineBreakRegex,`
`),ut()));const l=t.isNode?"nodeLabel":"edgeLabel",c=o.append("span");c.html(s),is(c,t.labelStyle),c.attr("class",`${l} ${i}`),is(o,t.labelStyle),o.style("display","table-cell"),o.style("white-space","nowrap"),o.style("line-height","1.5"),o.style("max-width",r+"px"),o.style("text-align","center"),o.attr("xmlns","http://www.w3.org/1999/xhtml"),a&&o.attr("class","labelBkg");let h=o.node().getBoundingClientRect();return h.width===r&&(o.style("display","table"),o.style("white-space","break-spaces"),o.style("width",r+"px"),h=o.node().getBoundingClientRect()),n.node()}d(Vu,"addHtmlSpan");function Za(e,t,r){return e.append("tspan").attr("class","text-outer-tspan").attr("x",0).attr("y",t*r-.1+"em").attr("dy",r+"em")}d(Za,"createTspan");function Xu(e,t,r){const i=e.append("text"),a=Za(i,1,t);Ka(a,r);const n=a.node().getComputedTextLength();return i.remove(),n}d(Xu,"computeWidthOfText");function Y1(e,t,r){var o;const i=e.append("text"),a=Za(i,1,t);Ka(a,[{content:r,type:"normal"}]);const n=(o=a.node())==null?void 0:o.getBoundingClientRect();return n&&i.remove(),n}d(Y1,"computeDimensionOfText");function Zu(e,t,r,i=!1){const n=t.append("g"),o=n.insert("rect").attr("class","background").attr("style","stroke: none"),s=n.append("text").attr("y","-10.1");let l=0;for(const c of r){const h=d(f=>Xu(n,1.1,f)<=e,"checkWidth"),u=h(c)?[c]:Gu(c,h);for(const f of u){const p=Za(s,l,1.1);Ka(p,f),l++}}if(i){const c=s.node().getBBox(),h=2;return o.attr("x",c.x-h).attr("y",c.y-h).attr("width",c.width+2*h).attr("height",c.height+2*h),n.node()}else return s.node()}d(Zu,"createFormattedText");function Ka(e,t){e.text(""),t.forEach((r,i)=>{const a=e.append("tspan").attr("font-style",r.type==="em"?"italic":"normal").attr("class","text-inner-tspan").attr("font-weight",r.type==="strong"?"bold":"normal");i===0?a.text(r.content):a.text(" "+r.content)})}d(Ka,"updateTextContentAndStyles");function Ku(e){return e.replace(/fa[bklrs]?:fa-[\w-]+/g,t=>`<i class='${t.replace(":"," ")}'></i>`)}d(Ku,"replaceIconSubstring");var je=d(async(e,t="",{style:r="",isTitle:i=!1,classes:a="",useHtmlLabels:n=!0,isNode:o=!0,width:s=200,addSvgBackground:l=!1}={},c)=>{if(O.debug("XYZ createText",t,r,i,a,n,o,"addSvgBackground: ",l),n){const h=ju(t,c),u=Ku(lr(h)),f=t.replace(/\\\\/g,"\\"),p={isNode:o,label:Tr(t)?f:u,labelStyle:r.replace("fill:","color:")};return await Vu(e,p,s,a,l)}else{const h=t.replace(/<br\s*\/?>/g,"<br/>"),u=Hu(h.replace("<br>","<br/>"),c),f=Zu(s,e,u,t?l:!1);if(o){/stroke:/.exec(r)&&(r=r.replace("stroke:","lineColor:"));const p=r.replace(/stroke:[^;]+;?/g,"").replace(/stroke-width:[^;]+;?/g,"").replace(/fill:[^;]+;?/g,"").replace(/color:/g,"fill:");lt(f).attr("style",p)}else{const p=r.replace(/stroke:[^;]+;?/g,"").replace(/stroke-width:[^;]+;?/g,"").replace(/fill:[^;]+;?/g,"").replace(/background:/g,"fill:");lt(f).select("rect").attr("style",p.replace(/background:/g,"fill:"));const g=r.replace(/stroke:[^;]+;?/g,"").replace(/stroke-width:[^;]+;?/g,"").replace(/fill:[^;]+;?/g,"").replace(/color:/g,"fill:");lt(f).select("text").attr("style",g)}return f}},"createText");function kn(e,t,r){if(e&&e.length){const[i,a]=t,n=Math.PI/180*r,o=Math.cos(n),s=Math.sin(n);for(const l of e){const[c,h]=l;l[0]=(c-i)*o-(h-a)*s+i,l[1]=(c-i)*s+(h-a)*o+a}}}function G1(e,t){return e[0]===t[0]&&e[1]===t[1]}function V1(e,t,r,i=1){const a=r,n=Math.max(t,.1),o=e[0]&&e[0][0]&&typeof e[0][0]=="number"?[e]:e,s=[0,0];if(a)for(const c of o)kn(c,s,a);const l=function(c,h,u){const f=[];for(const b of c){const C=[...b];G1(C[0],C[C.length-1])||C.push([C[0][0],C[0][1]]),C.length>2&&f.push(C)}const p=[];h=Math.max(h,.1);const g=[];for(const b of f)for(let C=0;C<b.length-1;C++){const S=b[C],v=b[C+1];if(S[1]!==v[1]){const M=Math.min(S[1],v[1]);g.push({ymin:M,ymax:Math.max(S[1],v[1]),x:M===S[1]?S[0]:v[0],islope:(v[0]-S[0])/(v[1]-S[1])})}}if(g.sort((b,C)=>b.ymin<C.ymin?-1:b.ymin>C.ymin?1:b.x<C.x?-1:b.x>C.x?1:b.ymax===C.ymax?0:(b.ymax-C.ymax)/Math.abs(b.ymax-C.ymax)),!g.length)return p;let m=[],y=g[0].ymin,x=0;for(;m.length||g.length;){if(g.length){let b=-1;for(let C=0;C<g.length&&!(g[C].ymin>y);C++)b=C;g.splice(0,b+1).forEach(C=>{m.push({s:y,edge:C})})}if(m=m.filter(b=>!(b.edge.ymax<=y)),m.sort((b,C)=>b.edge.x===C.edge.x?0:(b.edge.x-C.edge.x)/Math.abs(b.edge.x-C.edge.x)),(u!==1||x%h==0)&&m.length>1)for(let b=0;b<m.length;b+=2){const C=b+1;if(C>=m.length)break;const S=m[b].edge,v=m[C].edge;p.push([[Math.round(S.x),y],[Math.round(v.x),y]])}y+=u,m.forEach(b=>{b.edge.x=b.edge.x+u*b.edge.islope}),x++}return p}(o,n,i);if(a){for(const c of o)kn(c,s,-a);(function(c,h,u){const f=[];c.forEach(p=>f.push(...p)),kn(f,h,u)})(l,s,-a)}return l}function Fi(e,t){var r;const i=t.hachureAngle+90;let a=t.hachureGap;a<0&&(a=4*t.strokeWidth),a=Math.round(Math.max(a,.1));let n=1;return t.roughness>=1&&(((r=t.randomizer)===null||r===void 0?void 0:r.next())||Math.random())>.7&&(n=a),V1(e,a,i,n||1)}class ro{constructor(t){this.helper=t}fillPolygons(t,r){return this._fillPolygons(t,r)}_fillPolygons(t,r){const i=Fi(t,r);return{type:"fillSketch",ops:this.renderLines(i,r)}}renderLines(t,r){const i=[];for(const a of t)i.push(...this.helper.doubleLineOps(a[0][0],a[0][1],a[1][0],a[1][1],r));return i}}function Qa(e){const t=e[0],r=e[1];return Math.sqrt(Math.pow(t[0]-r[0],2)+Math.pow(t[1]-r[1],2))}class X1 extends ro{fillPolygons(t,r){let i=r.hachureGap;i<0&&(i=4*r.strokeWidth),i=Math.max(i,.1);const a=Fi(t,Object.assign({},r,{hachureGap:i})),n=Math.PI/180*r.hachureAngle,o=[],s=.5*i*Math.cos(n),l=.5*i*Math.sin(n);for(const[c,h]of a)Qa([c,h])&&o.push([[c[0]-s,c[1]+l],[...h]],[[c[0]+s,c[1]-l],[...h]]);return{type:"fillSketch",ops:this.renderLines(o,r)}}}class Z1 extends ro{fillPolygons(t,r){const i=this._fillPolygons(t,r),a=Object.assign({},r,{hachureAngle:r.hachureAngle+90}),n=this._fillPolygons(t,a);return i.ops=i.ops.concat(n.ops),i}}class K1{constructor(t){this.helper=t}fillPolygons(t,r){const i=Fi(t,r=Object.assign({},r,{hachureAngle:0}));return this.dotsOnLines(i,r)}dotsOnLines(t,r){const i=[];let a=r.hachureGap;a<0&&(a=4*r.strokeWidth),a=Math.max(a,.1);let n=r.fillWeight;n<0&&(n=r.strokeWidth/2);const o=a/4;for(const s of t){const l=Qa(s),c=l/a,h=Math.ceil(c)-1,u=l-h*a,f=(s[0][0]+s[1][0])/2-a/4,p=Math.min(s[0][1],s[1][1]);for(let g=0;g<h;g++){const m=p+u+g*a,y=f-o+2*Math.random()*o,x=m-o+2*Math.random()*o,b=this.helper.ellipse(y,x,n,n,r);i.push(...b.ops)}}return{type:"fillSketch",ops:i}}}class Q1{constructor(t){this.helper=t}fillPolygons(t,r){const i=Fi(t,r);return{type:"fillSketch",ops:this.dashedLine(i,r)}}dashedLine(t,r){const i=r.dashOffset<0?r.hachureGap<0?4*r.strokeWidth:r.hachureGap:r.dashOffset,a=r.dashGap<0?r.hachureGap<0?4*r.strokeWidth:r.hachureGap:r.dashGap,n=[];return t.forEach(o=>{const s=Qa(o),l=Math.floor(s/(i+a)),c=(s+a-l*(i+a))/2;let h=o[0],u=o[1];h[0]>u[0]&&(h=o[1],u=o[0]);const f=Math.atan((u[1]-h[1])/(u[0]-h[0]));for(let p=0;p<l;p++){const g=p*(i+a),m=g+i,y=[h[0]+g*Math.cos(f)+c*Math.cos(f),h[1]+g*Math.sin(f)+c*Math.sin(f)],x=[h[0]+m*Math.cos(f)+c*Math.cos(f),h[1]+m*Math.sin(f)+c*Math.sin(f)];n.push(...this.helper.doubleLineOps(y[0],y[1],x[0],x[1],r))}}),n}}class J1{constructor(t){this.helper=t}fillPolygons(t,r){const i=r.hachureGap<0?4*r.strokeWidth:r.hachureGap,a=r.zigzagOffset<0?i:r.zigzagOffset,n=Fi(t,r=Object.assign({},r,{hachureGap:i+a}));return{type:"fillSketch",ops:this.zigzagLines(n,a,r)}}zigzagLines(t,r,i){const a=[];return t.forEach(n=>{const o=Qa(n),s=Math.round(o/(2*r));let l=n[0],c=n[1];l[0]>c[0]&&(l=n[1],c=n[0]);const h=Math.atan((c[1]-l[1])/(c[0]-l[0]));for(let u=0;u<s;u++){const f=2*u*r,p=2*(u+1)*r,g=Math.sqrt(2*Math.pow(r,2)),m=[l[0]+f*Math.cos(h),l[1]+f*Math.sin(h)],y=[l[0]+p*Math.cos(h),l[1]+p*Math.sin(h)],x=[m[0]+g*Math.cos(h+Math.PI/4),m[1]+g*Math.sin(h+Math.PI/4)];a.push(...this.helper.doubleLineOps(m[0],m[1],x[0],x[1],i),...this.helper.doubleLineOps(x[0],x[1],y[0],y[1],i))}}),a}}const Yt={};class t2{constructor(t){this.seed=t}next(){return this.seed?(2**31-1&(this.seed=Math.imul(48271,this.seed)))/2**31:Math.random()}}const e2=0,wn=1,Cl=2,Ui={A:7,a:7,C:6,c:6,H:1,h:1,L:2,l:2,M:2,m:2,Q:4,q:4,S:4,s:4,T:2,t:2,V:1,v:1,Z:0,z:0};function vn(e,t){return e.type===t}function io(e){const t=[],r=function(o){const s=new Array;for(;o!=="";)if(o.match(/^([ \t\r\n,]+)/))o=o.substr(RegExp.$1.length);else if(o.match(/^([aAcChHlLmMqQsStTvVzZ])/))s[s.length]={type:e2,text:RegExp.$1},o=o.substr(RegExp.$1.length);else{if(!o.match(/^(([-+]?[0-9]+(\.[0-9]*)?|[-+]?\.[0-9]+)([eE][-+]?[0-9]+)?)/))return[];s[s.length]={type:wn,text:`${parseFloat(RegExp.$1)}`},o=o.substr(RegExp.$1.length)}return s[s.length]={type:Cl,text:""},s}(e);let i="BOD",a=0,n=r[a];for(;!vn(n,Cl);){let o=0;const s=[];if(i==="BOD"){if(n.text!=="M"&&n.text!=="m")return io("M0,0"+e);a++,o=Ui[n.text],i=n.text}else vn(n,wn)?o=Ui[i]:(a++,o=Ui[n.text],i=n.text);if(!(a+o<r.length))throw new Error("Path data ended short");for(let l=a;l<a+o;l++){const c=r[l];if(!vn(c,wn))throw new Error("Param not a number: "+i+","+c.text);s[s.length]=+c.text}if(typeof Ui[i]!="number")throw new Error("Bad segment: "+i);{const l={key:i,data:s};t.push(l),a+=o,n=r[a],i==="M"&&(i="L"),i==="m"&&(i="l")}}return t}function Qu(e){let t=0,r=0,i=0,a=0;const n=[];for(const{key:o,data:s}of e)switch(o){case"M":n.push({key:"M",data:[...s]}),[t,r]=s,[i,a]=s;break;case"m":t+=s[0],r+=s[1],n.push({key:"M",data:[t,r]}),i=t,a=r;break;case"L":n.push({key:"L",data:[...s]}),[t,r]=s;break;case"l":t+=s[0],r+=s[1],n.push({key:"L",data:[t,r]});break;case"C":n.push({key:"C",data:[...s]}),t=s[4],r=s[5];break;case"c":{const l=s.map((c,h)=>h%2?c+r:c+t);n.push({key:"C",data:l}),t=l[4],r=l[5];break}case"Q":n.push({key:"Q",data:[...s]}),t=s[2],r=s[3];break;case"q":{const l=s.map((c,h)=>h%2?c+r:c+t);n.push({key:"Q",data:l}),t=l[2],r=l[3];break}case"A":n.push({key:"A",data:[...s]}),t=s[5],r=s[6];break;case"a":t+=s[5],r+=s[6],n.push({key:"A",data:[s[0],s[1],s[2],s[3],s[4],t,r]});break;case"H":n.push({key:"H",data:[...s]}),t=s[0];break;case"h":t+=s[0],n.push({key:"H",data:[t]});break;case"V":n.push({key:"V",data:[...s]}),r=s[0];break;case"v":r+=s[0],n.push({key:"V",data:[r]});break;case"S":n.push({key:"S",data:[...s]}),t=s[2],r=s[3];break;case"s":{const l=s.map((c,h)=>h%2?c+r:c+t);n.push({key:"S",data:l}),t=l[2],r=l[3];break}case"T":n.push({key:"T",data:[...s]}),t=s[0],r=s[1];break;case"t":t+=s[0],r+=s[1],n.push({key:"T",data:[t,r]});break;case"Z":case"z":n.push({key:"Z",data:[]}),t=i,r=a}return n}function Ju(e){const t=[];let r="",i=0,a=0,n=0,o=0,s=0,l=0;for(const{key:c,data:h}of e){switch(c){case"M":t.push({key:"M",data:[...h]}),[i,a]=h,[n,o]=h;break;case"C":t.push({key:"C",data:[...h]}),i=h[4],a=h[5],s=h[2],l=h[3];break;case"L":t.push({key:"L",data:[...h]}),[i,a]=h;break;case"H":i=h[0],t.push({key:"L",data:[i,a]});break;case"V":a=h[0],t.push({key:"L",data:[i,a]});break;case"S":{let u=0,f=0;r==="C"||r==="S"?(u=i+(i-s),f=a+(a-l)):(u=i,f=a),t.push({key:"C",data:[u,f,...h]}),s=h[0],l=h[1],i=h[2],a=h[3];break}case"T":{const[u,f]=h;let p=0,g=0;r==="Q"||r==="T"?(p=i+(i-s),g=a+(a-l)):(p=i,g=a);const m=i+2*(p-i)/3,y=a+2*(g-a)/3,x=u+2*(p-u)/3,b=f+2*(g-f)/3;t.push({key:"C",data:[m,y,x,b,u,f]}),s=p,l=g,i=u,a=f;break}case"Q":{const[u,f,p,g]=h,m=i+2*(u-i)/3,y=a+2*(f-a)/3,x=p+2*(u-p)/3,b=g+2*(f-g)/3;t.push({key:"C",data:[m,y,x,b,p,g]}),s=u,l=f,i=p,a=g;break}case"A":{const u=Math.abs(h[0]),f=Math.abs(h[1]),p=h[2],g=h[3],m=h[4],y=h[5],x=h[6];u===0||f===0?(t.push({key:"C",data:[i,a,y,x,y,x]}),i=y,a=x):(i!==y||a!==x)&&(tf(i,a,y,x,u,f,p,g,m).forEach(function(b){t.push({key:"C",data:b})}),i=y,a=x);break}case"Z":t.push({key:"Z",data:[]}),i=n,a=o}r=c}return t}function Vr(e,t,r){return[e*Math.cos(r)-t*Math.sin(r),e*Math.sin(r)+t*Math.cos(r)]}function tf(e,t,r,i,a,n,o,s,l,c){const h=(u=o,Math.PI*u/180);var u;let f=[],p=0,g=0,m=0,y=0;if(c)[p,g,m,y]=c;else{[e,t]=Vr(e,t,-h),[r,i]=Vr(r,i,-h);const R=(e-r)/2,E=(t-i)/2;let A=R*R/(a*a)+E*E/(n*n);A>1&&(A=Math.sqrt(A),a*=A,n*=A);const _=a*a,F=n*n,B=_*F-_*E*E-F*R*R,W=_*E*E+F*R*R,U=(s===l?-1:1)*Math.sqrt(Math.abs(B/W));m=U*a*E/n+(e+r)/2,y=U*-n*R/a+(t+i)/2,p=Math.asin(parseFloat(((t-y)/n).toFixed(9))),g=Math.asin(parseFloat(((i-y)/n).toFixed(9))),e<m&&(p=Math.PI-p),r<m&&(g=Math.PI-g),p<0&&(p=2*Math.PI+p),g<0&&(g=2*Math.PI+g),l&&p>g&&(p-=2*Math.PI),!l&&g>p&&(g-=2*Math.PI)}let x=g-p;if(Math.abs(x)>120*Math.PI/180){const R=g,E=r,A=i;g=l&&g>p?p+120*Math.PI/180*1:p+120*Math.PI/180*-1,f=tf(r=m+a*Math.cos(g),i=y+n*Math.sin(g),E,A,a,n,o,0,l,[g,R,m,y])}x=g-p;const b=Math.cos(p),C=Math.sin(p),S=Math.cos(g),v=Math.sin(g),M=Math.tan(x/4),T=4/3*a*M,D=4/3*n*M,P=[e,t],$=[e+T*C,t-D*b],L=[r+T*v,i-D*S],z=[r,i];if($[0]=2*P[0]-$[0],$[1]=2*P[1]-$[1],c)return[$,L,z].concat(f);{f=[$,L,z].concat(f);const R=[];for(let E=0;E<f.length;E+=3){const A=Vr(f[E][0],f[E][1],h),_=Vr(f[E+1][0],f[E+1][1],h),F=Vr(f[E+2][0],f[E+2][1],h);R.push([A[0],A[1],_[0],_[1],F[0],F[1]])}return R}}const r2={randOffset:function(e,t){return rt(e,t)},randOffsetWithRange:function(e,t,r){return Ba(e,t,r)},ellipse:function(e,t,r,i,a){const n=rf(r,i,a);return as(e,t,a,n).opset},doubleLineOps:function(e,t,r,i,a){return We(e,t,r,i,a,!0)}};function ef(e,t,r,i,a){return{type:"path",ops:We(e,t,r,i,a)}}function ia(e,t,r){const i=(e||[]).length;if(i>2){const a=[];for(let n=0;n<i-1;n++)a.push(...We(e[n][0],e[n][1],e[n+1][0],e[n+1][1],r));return t&&a.push(...We(e[i-1][0],e[i-1][1],e[0][0],e[0][1],r)),{type:"path",ops:a}}return i===2?ef(e[0][0],e[0][1],e[1][0],e[1][1],r):{type:"path",ops:[]}}function i2(e,t,r,i,a){return function(n,o){return ia(n,!0,o)}([[e,t],[e+r,t],[e+r,t+i],[e,t+i]],a)}function kl(e,t){if(e.length){const r=typeof e[0][0]=="number"?[e]:e,i=Yi(r[0],1*(1+.2*t.roughness),t),a=t.disableMultiStroke?[]:Yi(r[0],1.5*(1+.22*t.roughness),Sl(t));for(let n=1;n<r.length;n++){const o=r[n];if(o.length){const s=Yi(o,1*(1+.2*t.roughness),t),l=t.disableMultiStroke?[]:Yi(o,1.5*(1+.22*t.roughness),Sl(t));for(const c of s)c.op!=="move"&&i.push(c);for(const c of l)c.op!=="move"&&a.push(c)}}return{type:"path",ops:i.concat(a)}}return{type:"path",ops:[]}}function rf(e,t,r){const i=Math.sqrt(2*Math.PI*Math.sqrt((Math.pow(e/2,2)+Math.pow(t/2,2))/2)),a=Math.ceil(Math.max(r.curveStepCount,r.curveStepCount/Math.sqrt(200)*i)),n=2*Math.PI/a;let o=Math.abs(e/2),s=Math.abs(t/2);const l=1-r.curveFitting;return o+=rt(o*l,r),s+=rt(s*l,r),{increment:n,rx:o,ry:s}}function as(e,t,r,i){const[a,n]=Tl(i.increment,e,t,i.rx,i.ry,1,i.increment*Ba(.1,Ba(.4,1,r),r),r);let o=La(a,null,r);if(!r.disableMultiStroke&&r.roughness!==0){const[s]=Tl(i.increment,e,t,i.rx,i.ry,1.5,0,r),l=La(s,null,r);o=o.concat(l)}return{estimatedPoints:n,opset:{type:"path",ops:o}}}function wl(e,t,r,i,a,n,o,s,l){const c=e,h=t;let u=Math.abs(r/2),f=Math.abs(i/2);u+=rt(.01*u,l),f+=rt(.01*f,l);let p=a,g=n;for(;p<0;)p+=2*Math.PI,g+=2*Math.PI;g-p>2*Math.PI&&(p=0,g=2*Math.PI);const m=2*Math.PI/l.curveStepCount,y=Math.min(m/2,(g-p)/2),x=_l(y,c,h,u,f,p,g,1,l);if(!l.disableMultiStroke){const b=_l(y,c,h,u,f,p,g,1.5,l);x.push(...b)}return o&&(s?x.push(...We(c,h,c+u*Math.cos(p),h+f*Math.sin(p),l),...We(c,h,c+u*Math.cos(g),h+f*Math.sin(g),l)):x.push({op:"lineTo",data:[c,h]},{op:"lineTo",data:[c+u*Math.cos(p),h+f*Math.sin(p)]})),{type:"path",ops:x}}function vl(e,t){const r=Ju(Qu(io(e))),i=[];let a=[0,0],n=[0,0];for(const{key:o,data:s}of r)switch(o){case"M":n=[s[0],s[1]],a=[s[0],s[1]];break;case"L":i.push(...We(n[0],n[1],s[0],s[1],t)),n=[s[0],s[1]];break;case"C":{const[l,c,h,u,f,p]=s;i.push(...a2(l,c,h,u,f,p,n,t)),n=[f,p];break}case"Z":i.push(...We(n[0],n[1],a[0],a[1],t)),n=[a[0],a[1]]}return{type:"path",ops:i}}function Sn(e,t){const r=[];for(const i of e)if(i.length){const a=t.maxRandomnessOffset||0,n=i.length;if(n>2){r.push({op:"move",data:[i[0][0]+rt(a,t),i[0][1]+rt(a,t)]});for(let o=1;o<n;o++)r.push({op:"lineTo",data:[i[o][0]+rt(a,t),i[o][1]+rt(a,t)]})}}return{type:"fillPath",ops:r}}function gr(e,t){return function(r,i){let a=r.fillStyle||"hachure";if(!Yt[a])switch(a){case"zigzag":Yt[a]||(Yt[a]=new X1(i));break;case"cross-hatch":Yt[a]||(Yt[a]=new Z1(i));break;case"dots":Yt[a]||(Yt[a]=new K1(i));break;case"dashed":Yt[a]||(Yt[a]=new Q1(i));break;case"zigzag-line":Yt[a]||(Yt[a]=new J1(i));break;default:a="hachure",Yt[a]||(Yt[a]=new ro(i))}return Yt[a]}(t,r2).fillPolygons(e,t)}function Sl(e){const t=Object.assign({},e);return t.randomizer=void 0,e.seed&&(t.seed=e.seed+1),t}function af(e){return e.randomizer||(e.randomizer=new t2(e.seed||0)),e.randomizer.next()}function Ba(e,t,r,i=1){return r.roughness*i*(af(r)*(t-e)+e)}function rt(e,t,r=1){return Ba(-e,e,t,r)}function We(e,t,r,i,a,n=!1){const o=n?a.disableMultiStrokeFill:a.disableMultiStroke,s=ns(e,t,r,i,a,!0,!1);if(o)return s;const l=ns(e,t,r,i,a,!0,!0);return s.concat(l)}function ns(e,t,r,i,a,n,o){const s=Math.pow(e-r,2)+Math.pow(t-i,2),l=Math.sqrt(s);let c=1;c=l<200?1:l>500?.4:-.0016668*l+1.233334;let h=a.maxRandomnessOffset||0;h*h*100>s&&(h=l/10);const u=h/2,f=.2+.2*af(a);let p=a.bowing*a.maxRandomnessOffset*(i-t)/200,g=a.bowing*a.maxRandomnessOffset*(e-r)/200;p=rt(p,a,c),g=rt(g,a,c);const m=[],y=()=>rt(u,a,c),x=()=>rt(h,a,c),b=a.preserveVertices;return o?m.push({op:"move",data:[e+(b?0:y()),t+(b?0:y())]}):m.push({op:"move",data:[e+(b?0:rt(h,a,c)),t+(b?0:rt(h,a,c))]}),o?m.push({op:"bcurveTo",data:[p+e+(r-e)*f+y(),g+t+(i-t)*f+y(),p+e+2*(r-e)*f+y(),g+t+2*(i-t)*f+y(),r+(b?0:y()),i+(b?0:y())]}):m.push({op:"bcurveTo",data:[p+e+(r-e)*f+x(),g+t+(i-t)*f+x(),p+e+2*(r-e)*f+x(),g+t+2*(i-t)*f+x(),r+(b?0:x()),i+(b?0:x())]}),m}function Yi(e,t,r){if(!e.length)return[];const i=[];i.push([e[0][0]+rt(t,r),e[0][1]+rt(t,r)]),i.push([e[0][0]+rt(t,r),e[0][1]+rt(t,r)]);for(let a=1;a<e.length;a++)i.push([e[a][0]+rt(t,r),e[a][1]+rt(t,r)]),a===e.length-1&&i.push([e[a][0]+rt(t,r),e[a][1]+rt(t,r)]);return La(i,null,r)}function La(e,t,r){const i=e.length,a=[];if(i>3){const n=[],o=1-r.curveTightness;a.push({op:"move",data:[e[1][0],e[1][1]]});for(let s=1;s+2<i;s++){const l=e[s];n[0]=[l[0],l[1]],n[1]=[l[0]+(o*e[s+1][0]-o*e[s-1][0])/6,l[1]+(o*e[s+1][1]-o*e[s-1][1])/6],n[2]=[e[s+1][0]+(o*e[s][0]-o*e[s+2][0])/6,e[s+1][1]+(o*e[s][1]-o*e[s+2][1])/6],n[3]=[e[s+1][0],e[s+1][1]],a.push({op:"bcurveTo",data:[n[1][0],n[1][1],n[2][0],n[2][1],n[3][0],n[3][1]]})}}else i===3?(a.push({op:"move",data:[e[1][0],e[1][1]]}),a.push({op:"bcurveTo",data:[e[1][0],e[1][1],e[2][0],e[2][1],e[2][0],e[2][1]]})):i===2&&a.push(...ns(e[0][0],e[0][1],e[1][0],e[1][1],r,!0,!0));return a}function Tl(e,t,r,i,a,n,o,s){const l=[],c=[];if(s.roughness===0){e/=4,c.push([t+i*Math.cos(-e),r+a*Math.sin(-e)]);for(let h=0;h<=2*Math.PI;h+=e){const u=[t+i*Math.cos(h),r+a*Math.sin(h)];l.push(u),c.push(u)}c.push([t+i*Math.cos(0),r+a*Math.sin(0)]),c.push([t+i*Math.cos(e),r+a*Math.sin(e)])}else{const h=rt(.5,s)-Math.PI/2;c.push([rt(n,s)+t+.9*i*Math.cos(h-e),rt(n,s)+r+.9*a*Math.sin(h-e)]);const u=2*Math.PI+h-.01;for(let f=h;f<u;f+=e){const p=[rt(n,s)+t+i*Math.cos(f),rt(n,s)+r+a*Math.sin(f)];l.push(p),c.push(p)}c.push([rt(n,s)+t+i*Math.cos(h+2*Math.PI+.5*o),rt(n,s)+r+a*Math.sin(h+2*Math.PI+.5*o)]),c.push([rt(n,s)+t+.98*i*Math.cos(h+o),rt(n,s)+r+.98*a*Math.sin(h+o)]),c.push([rt(n,s)+t+.9*i*Math.cos(h+.5*o),rt(n,s)+r+.9*a*Math.sin(h+.5*o)])}return[c,l]}function _l(e,t,r,i,a,n,o,s,l){const c=n+rt(.1,l),h=[];h.push([rt(s,l)+t+.9*i*Math.cos(c-e),rt(s,l)+r+.9*a*Math.sin(c-e)]);for(let u=c;u<=o;u+=e)h.push([rt(s,l)+t+i*Math.cos(u),rt(s,l)+r+a*Math.sin(u)]);return h.push([t+i*Math.cos(o),r+a*Math.sin(o)]),h.push([t+i*Math.cos(o),r+a*Math.sin(o)]),La(h,null,l)}function a2(e,t,r,i,a,n,o,s){const l=[],c=[s.maxRandomnessOffset||1,(s.maxRandomnessOffset||1)+.3];let h=[0,0];const u=s.disableMultiStroke?1:2,f=s.preserveVertices;for(let p=0;p<u;p++)p===0?l.push({op:"move",data:[o[0],o[1]]}):l.push({op:"move",data:[o[0]+(f?0:rt(c[0],s)),o[1]+(f?0:rt(c[0],s))]}),h=f?[a,n]:[a+rt(c[p],s),n+rt(c[p],s)],l.push({op:"bcurveTo",data:[e+rt(c[p],s),t+rt(c[p],s),r+rt(c[p],s),i+rt(c[p],s),h[0],h[1]]});return l}function Xr(e){return[...e]}function Bl(e,t=0){const r=e.length;if(r<3)throw new Error("A curve must have at least three points.");const i=[];if(r===3)i.push(Xr(e[0]),Xr(e[1]),Xr(e[2]),Xr(e[2]));else{const a=[];a.push(e[0],e[0]);for(let s=1;s<e.length;s++)a.push(e[s]),s===e.length-1&&a.push(e[s]);const n=[],o=1-t;i.push(Xr(a[0]));for(let s=1;s+2<a.length;s++){const l=a[s];n[0]=[l[0],l[1]],n[1]=[l[0]+(o*a[s+1][0]-o*a[s-1][0])/6,l[1]+(o*a[s+1][1]-o*a[s-1][1])/6],n[2]=[a[s+1][0]+(o*a[s][0]-o*a[s+2][0])/6,a[s+1][1]+(o*a[s][1]-o*a[s+2][1])/6],n[3]=[a[s+1][0],a[s+1][1]],i.push(n[1],n[2],n[3])}}return i}function aa(e,t){return Math.pow(e[0]-t[0],2)+Math.pow(e[1]-t[1],2)}function n2(e,t,r){const i=aa(t,r);if(i===0)return aa(e,t);let a=((e[0]-t[0])*(r[0]-t[0])+(e[1]-t[1])*(r[1]-t[1]))/i;return a=Math.max(0,Math.min(1,a)),aa(e,Ve(t,r,a))}function Ve(e,t,r){return[e[0]+(t[0]-e[0])*r,e[1]+(t[1]-e[1])*r]}function ss(e,t,r,i){const a=i||[];if(function(s,l){const c=s[l+0],h=s[l+1],u=s[l+2],f=s[l+3];let p=3*h[0]-2*c[0]-f[0];p*=p;let g=3*h[1]-2*c[1]-f[1];g*=g;let m=3*u[0]-2*f[0]-c[0];m*=m;let y=3*u[1]-2*f[1]-c[1];return y*=y,p<m&&(p=m),g<y&&(g=y),p+g}(e,t)<r){const s=e[t+0];a.length?(n=a[a.length-1],o=s,Math.sqrt(aa(n,o))>1&&a.push(s)):a.push(s),a.push(e[t+3])}else{const l=e[t+0],c=e[t+1],h=e[t+2],u=e[t+3],f=Ve(l,c,.5),p=Ve(c,h,.5),g=Ve(h,u,.5),m=Ve(f,p,.5),y=Ve(p,g,.5),x=Ve(m,y,.5);ss([l,f,m,x],0,r,a),ss([x,y,g,u],0,r,a)}var n,o;return a}function s2(e,t){return Aa(e,0,e.length,t)}function Aa(e,t,r,i,a){const n=a||[],o=e[t],s=e[r-1];let l=0,c=1;for(let h=t+1;h<r-1;++h){const u=n2(e[h],o,s);u>l&&(l=u,c=h)}return Math.sqrt(l)>i?(Aa(e,t,c+1,i,n),Aa(e,c,r,i,n)):(n.length||n.push(o),n.push(s)),n}function Tn(e,t=.15,r){const i=[],a=(e.length-1)/3;for(let n=0;n<a;n++)ss(e,3*n,t,i);return r&&r>0?Aa(i,0,i.length,r):i}const Zt="none";class Ma{constructor(t){this.defaultOptions={maxRandomnessOffset:2,roughness:1,bowing:1,stroke:"#000",strokeWidth:1,curveTightness:0,curveFitting:.95,curveStepCount:9,fillStyle:"hachure",fillWeight:-1,hachureAngle:-41,hachureGap:-1,dashOffset:-1,dashGap:-1,zigzagOffset:-1,seed:0,disableMultiStroke:!1,disableMultiStrokeFill:!1,preserveVertices:!1,fillShapeRoughnessGain:.8},this.config=t||{},this.config.options&&(this.defaultOptions=this._o(this.config.options))}static newSeed(){return Math.floor(Math.random()*2**31)}_o(t){return t?Object.assign({},this.defaultOptions,t):this.defaultOptions}_d(t,r,i){return{shape:t,sets:r||[],options:i||this.defaultOptions}}line(t,r,i,a,n){const o=this._o(n);return this._d("line",[ef(t,r,i,a,o)],o)}rectangle(t,r,i,a,n){const o=this._o(n),s=[],l=i2(t,r,i,a,o);if(o.fill){const c=[[t,r],[t+i,r],[t+i,r+a],[t,r+a]];o.fillStyle==="solid"?s.push(Sn([c],o)):s.push(gr([c],o))}return o.stroke!==Zt&&s.push(l),this._d("rectangle",s,o)}ellipse(t,r,i,a,n){const o=this._o(n),s=[],l=rf(i,a,o),c=as(t,r,o,l);if(o.fill)if(o.fillStyle==="solid"){const h=as(t,r,o,l).opset;h.type="fillPath",s.push(h)}else s.push(gr([c.estimatedPoints],o));return o.stroke!==Zt&&s.push(c.opset),this._d("ellipse",s,o)}circle(t,r,i,a){const n=this.ellipse(t,r,i,i,a);return n.shape="circle",n}linearPath(t,r){const i=this._o(r);return this._d("linearPath",[ia(t,!1,i)],i)}arc(t,r,i,a,n,o,s=!1,l){const c=this._o(l),h=[],u=wl(t,r,i,a,n,o,s,!0,c);if(s&&c.fill)if(c.fillStyle==="solid"){const f=Object.assign({},c);f.disableMultiStroke=!0;const p=wl(t,r,i,a,n,o,!0,!1,f);p.type="fillPath",h.push(p)}else h.push(function(f,p,g,m,y,x,b){const C=f,S=p;let v=Math.abs(g/2),M=Math.abs(m/2);v+=rt(.01*v,b),M+=rt(.01*M,b);let T=y,D=x;for(;T<0;)T+=2*Math.PI,D+=2*Math.PI;D-T>2*Math.PI&&(T=0,D=2*Math.PI);const P=(D-T)/b.curveStepCount,$=[];for(let L=T;L<=D;L+=P)$.push([C+v*Math.cos(L),S+M*Math.sin(L)]);return $.push([C+v*Math.cos(D),S+M*Math.sin(D)]),$.push([C,S]),gr([$],b)}(t,r,i,a,n,o,c));return c.stroke!==Zt&&h.push(u),this._d("arc",h,c)}curve(t,r){const i=this._o(r),a=[],n=kl(t,i);if(i.fill&&i.fill!==Zt)if(i.fillStyle==="solid"){const o=kl(t,Object.assign(Object.assign({},i),{disableMultiStroke:!0,roughness:i.roughness?i.roughness+i.fillShapeRoughnessGain:0}));a.push({type:"fillPath",ops:this._mergedShape(o.ops)})}else{const o=[],s=t;if(s.length){const l=typeof s[0][0]=="number"?[s]:s;for(const c of l)c.length<3?o.push(...c):c.length===3?o.push(...Tn(Bl([c[0],c[0],c[1],c[2]]),10,(1+i.roughness)/2)):o.push(...Tn(Bl(c),10,(1+i.roughness)/2))}o.length&&a.push(gr([o],i))}return i.stroke!==Zt&&a.push(n),this._d("curve",a,i)}polygon(t,r){const i=this._o(r),a=[],n=ia(t,!0,i);return i.fill&&(i.fillStyle==="solid"?a.push(Sn([t],i)):a.push(gr([t],i))),i.stroke!==Zt&&a.push(n),this._d("polygon",a,i)}path(t,r){const i=this._o(r),a=[];if(!t)return this._d("path",a,i);t=(t||"").replace(/\n/g," ").replace(/(-\s)/g,"-").replace("/(ss)/g"," ");const n=i.fill&&i.fill!=="transparent"&&i.fill!==Zt,o=i.stroke!==Zt,s=!!(i.simplification&&i.simplification<1),l=function(h,u,f){const p=Ju(Qu(io(h))),g=[];let m=[],y=[0,0],x=[];const b=()=>{x.length>=4&&m.push(...Tn(x,u)),x=[]},C=()=>{b(),m.length&&(g.push(m),m=[])};for(const{key:v,data:M}of p)switch(v){case"M":C(),y=[M[0],M[1]],m.push(y);break;case"L":b(),m.push([M[0],M[1]]);break;case"C":if(!x.length){const T=m.length?m[m.length-1]:y;x.push([T[0],T[1]])}x.push([M[0],M[1]]),x.push([M[2],M[3]]),x.push([M[4],M[5]]);break;case"Z":b(),m.push([y[0],y[1]])}if(C(),!f)return g;const S=[];for(const v of g){const M=s2(v,f);M.length&&S.push(M)}return S}(t,1,s?4-4*(i.simplification||1):(1+i.roughness)/2),c=vl(t,i);if(n)if(i.fillStyle==="solid")if(l.length===1){const h=vl(t,Object.assign(Object.assign({},i),{disableMultiStroke:!0,roughness:i.roughness?i.roughness+i.fillShapeRoughnessGain:0}));a.push({type:"fillPath",ops:this._mergedShape(h.ops)})}else a.push(Sn(l,i));else a.push(gr(l,i));return o&&(s?l.forEach(h=>{a.push(ia(h,!1,i))}):a.push(c)),this._d("path",a,i)}opsToPath(t,r){let i="";for(const a of t.ops){const n=typeof r=="number"&&r>=0?a.data.map(o=>+o.toFixed(r)):a.data;switch(a.op){case"move":i+=`M${n[0]} ${n[1]} `;break;case"bcurveTo":i+=`C${n[0]} ${n[1]}, ${n[2]} ${n[3]}, ${n[4]} ${n[5]} `;break;case"lineTo":i+=`L${n[0]} ${n[1]} `}}return i.trim()}toPaths(t){const r=t.sets||[],i=t.options||this.defaultOptions,a=[];for(const n of r){let o=null;switch(n.type){case"path":o={d:this.opsToPath(n),stroke:i.stroke,strokeWidth:i.strokeWidth,fill:Zt};break;case"fillPath":o={d:this.opsToPath(n),stroke:Zt,strokeWidth:0,fill:i.fill||Zt};break;case"fillSketch":o=this.fillSketch(n,i)}o&&a.push(o)}return a}fillSketch(t,r){let i=r.fillWeight;return i<0&&(i=r.strokeWidth/2),{d:this.opsToPath(t),stroke:r.fill||Zt,strokeWidth:i,fill:Zt}}_mergedShape(t){return t.filter((r,i)=>i===0||r.op!=="move")}}class o2{constructor(t,r){this.canvas=t,this.ctx=this.canvas.getContext("2d"),this.gen=new Ma(r)}draw(t){const r=t.sets||[],i=t.options||this.getDefaultOptions(),a=this.ctx,n=t.options.fixedDecimalPlaceDigits;for(const o of r)switch(o.type){case"path":a.save(),a.strokeStyle=i.stroke==="none"?"transparent":i.stroke,a.lineWidth=i.strokeWidth,i.strokeLineDash&&a.setLineDash(i.strokeLineDash),i.strokeLineDashOffset&&(a.lineDashOffset=i.strokeLineDashOffset),this._drawToContext(a,o,n),a.restore();break;case"fillPath":{a.save(),a.fillStyle=i.fill||"";const s=t.shape==="curve"||t.shape==="polygon"||t.shape==="path"?"evenodd":"nonzero";this._drawToContext(a,o,n,s),a.restore();break}case"fillSketch":this.fillSketch(a,o,i)}}fillSketch(t,r,i){let a=i.fillWeight;a<0&&(a=i.strokeWidth/2),t.save(),i.fillLineDash&&t.setLineDash(i.fillLineDash),i.fillLineDashOffset&&(t.lineDashOffset=i.fillLineDashOffset),t.strokeStyle=i.fill||"",t.lineWidth=a,this._drawToContext(t,r,i.fixedDecimalPlaceDigits),t.restore()}_drawToContext(t,r,i,a="nonzero"){t.beginPath();for(const n of r.ops){const o=typeof i=="number"&&i>=0?n.data.map(s=>+s.toFixed(i)):n.data;switch(n.op){case"move":t.moveTo(o[0],o[1]);break;case"bcurveTo":t.bezierCurveTo(o[0],o[1],o[2],o[3],o[4],o[5]);break;case"lineTo":t.lineTo(o[0],o[1])}}r.type==="fillPath"?t.fill(a):t.stroke()}get generator(){return this.gen}getDefaultOptions(){return this.gen.defaultOptions}line(t,r,i,a,n){const o=this.gen.line(t,r,i,a,n);return this.draw(o),o}rectangle(t,r,i,a,n){const o=this.gen.rectangle(t,r,i,a,n);return this.draw(o),o}ellipse(t,r,i,a,n){const o=this.gen.ellipse(t,r,i,a,n);return this.draw(o),o}circle(t,r,i,a){const n=this.gen.circle(t,r,i,a);return this.draw(n),n}linearPath(t,r){const i=this.gen.linearPath(t,r);return this.draw(i),i}polygon(t,r){const i=this.gen.polygon(t,r);return this.draw(i),i}arc(t,r,i,a,n,o,s=!1,l){const c=this.gen.arc(t,r,i,a,n,o,s,l);return this.draw(c),c}curve(t,r){const i=this.gen.curve(t,r);return this.draw(i),i}path(t,r){const i=this.gen.path(t,r);return this.draw(i),i}}const Gi="http://www.w3.org/2000/svg";class l2{constructor(t,r){this.svg=t,this.gen=new Ma(r)}draw(t){const r=t.sets||[],i=t.options||this.getDefaultOptions(),a=this.svg.ownerDocument||window.document,n=a.createElementNS(Gi,"g"),o=t.options.fixedDecimalPlaceDigits;for(const s of r){let l=null;switch(s.type){case"path":l=a.createElementNS(Gi,"path"),l.setAttribute("d",this.opsToPath(s,o)),l.setAttribute("stroke",i.stroke),l.setAttribute("stroke-width",i.strokeWidth+""),l.setAttribute("fill","none"),i.strokeLineDash&&l.setAttribute("stroke-dasharray",i.strokeLineDash.join(" ").trim()),i.strokeLineDashOffset&&l.setAttribute("stroke-dashoffset",`${i.strokeLineDashOffset}`);break;case"fillPath":l=a.createElementNS(Gi,"path"),l.setAttribute("d",this.opsToPath(s,o)),l.setAttribute("stroke","none"),l.setAttribute("stroke-width","0"),l.setAttribute("fill",i.fill||""),t.shape!=="curve"&&t.shape!=="polygon"||l.setAttribute("fill-rule","evenodd");break;case"fillSketch":l=this.fillSketch(a,s,i)}l&&n.appendChild(l)}return n}fillSketch(t,r,i){let a=i.fillWeight;a<0&&(a=i.strokeWidth/2);const n=t.createElementNS(Gi,"path");return n.setAttribute("d",this.opsToPath(r,i.fixedDecimalPlaceDigits)),n.setAttribute("stroke",i.fill||""),n.setAttribute("stroke-width",a+""),n.setAttribute("fill","none"),i.fillLineDash&&n.setAttribute("stroke-dasharray",i.fillLineDash.join(" ").trim()),i.fillLineDashOffset&&n.setAttribute("stroke-dashoffset",`${i.fillLineDashOffset}`),n}get generator(){return this.gen}getDefaultOptions(){return this.gen.defaultOptions}opsToPath(t,r){return this.gen.opsToPath(t,r)}line(t,r,i,a,n){const o=this.gen.line(t,r,i,a,n);return this.draw(o)}rectangle(t,r,i,a,n){const o=this.gen.rectangle(t,r,i,a,n);return this.draw(o)}ellipse(t,r,i,a,n){const o=this.gen.ellipse(t,r,i,a,n);return this.draw(o)}circle(t,r,i,a){const n=this.gen.circle(t,r,i,a);return this.draw(n)}linearPath(t,r){const i=this.gen.linearPath(t,r);return this.draw(i)}polygon(t,r){const i=this.gen.polygon(t,r);return this.draw(i)}arc(t,r,i,a,n,o,s=!1,l){const c=this.gen.arc(t,r,i,a,n,o,s,l);return this.draw(c)}curve(t,r){const i=this.gen.curve(t,r);return this.draw(i)}path(t,r){const i=this.gen.path(t,r);return this.draw(i)}}var j={canvas:(e,t)=>new o2(e,t),svg:(e,t)=>new l2(e,t),generator:e=>new Ma(e),newSeed:()=>Ma.newSeed()},st=d(async(e,t,r)=>{var u,f;let i;const a=t.useHtmlLabels||vt((u=ut())==null?void 0:u.htmlLabels);r?i=r:i="node default";const n=e.insert("g").attr("class",i).attr("id",t.domId||t.id),o=n.insert("g").attr("class","label").attr("style",qt(t.labelStyle));let s;t.label===void 0?s="":s=typeof t.label=="string"?t.label:t.label[0];const l=await je(o,tr(lr(s),ut()),{useHtmlLabels:a,width:t.width||((f=ut().flowchart)==null?void 0:f.wrappingWidth),cssClasses:"markdown-node-label",style:t.labelStyle,addSvgBackground:!!t.icon||!!t.img});let c=l.getBBox();const h=((t==null?void 0:t.padding)??0)/2;if(a){const p=l.children[0],g=lt(l),m=p.getElementsByTagName("img");if(m){const y=s.replace(/<img[^>]*>/g,"").trim()==="";await Promise.all([...m].map(x=>new Promise(b=>{function C(){if(x.style.display="flex",x.style.flexDirection="column",y){const S=ut().fontSize?ut().fontSize:window.getComputedStyle(document.body).fontSize,v=5,[M=oc.fontSize]=Ga(S),T=M*v+"px";x.style.minWidth=T,x.style.maxWidth=T}else x.style.width="100%";b(x)}d(C,"setupImage"),setTimeout(()=>{x.complete&&C()}),x.addEventListener("error",C),x.addEventListener("load",C)})))}c=p.getBoundingClientRect(),g.attr("width",c.width),g.attr("height",c.height)}return a?o.attr("transform","translate("+-c.width/2+", "+-c.height/2+")"):o.attr("transform","translate(0, "+-c.height/2+")"),t.centerLabel&&o.attr("transform","translate("+-c.width/2+", "+-c.height/2+")"),o.insert("rect",":first-child"),{shapeSvg:n,bbox:c,halfPadding:h,label:o}},"labelHelper"),_n=d(async(e,t,r)=>{var l,c,h,u,f,p;const i=r.useHtmlLabels||vt((c=(l=ut())==null?void 0:l.flowchart)==null?void 0:c.htmlLabels),a=e.insert("g").attr("class","label").attr("style",r.labelStyle||""),n=await je(a,tr(lr(t),ut()),{useHtmlLabels:i,width:r.width||((u=(h=ut())==null?void 0:h.flowchart)==null?void 0:u.wrappingWidth),style:r.labelStyle,addSvgBackground:!!r.icon||!!r.img});let o=n.getBBox();const s=r.padding/2;if(vt((p=(f=ut())==null?void 0:f.flowchart)==null?void 0:p.htmlLabels)){const g=n.children[0],m=lt(n);o=g.getBoundingClientRect(),m.attr("width",o.width),m.attr("height",o.height)}return i?a.attr("transform","translate("+-o.width/2+", "+-o.height/2+")"):a.attr("transform","translate(0, "+-o.height/2+")"),r.centerLabel&&a.attr("transform","translate("+-o.width/2+", "+-o.height/2+")"),a.insert("rect",":first-child"),{shapeSvg:e,bbox:o,halfPadding:s,label:a}},"insertLabel"),V=d((e,t)=>{const r=t.node().getBBox();e.width=r.width,e.height=r.height},"updateNodeBounds"),it=d((e,t)=>(e.look==="handDrawn"?"rough-node":"node")+" "+e.cssClasses+" "+(t||""),"getNodeClasses");function ct(e){const t=e.map((r,i)=>`${i===0?"M":"L"}${r.x},${r.y}`);return t.push("Z"),t.join(" ")}d(ct,"createPathFromPoints");function qe(e,t,r,i,a,n){const o=[],l=r-e,c=i-t,h=l/n,u=2*Math.PI/h,f=t+c/2;for(let p=0;p<=50;p++){const g=p/50,m=e+g*l,y=f+a*Math.sin(u*(m-e));o.push({x:m,y})}return o}d(qe,"generateFullSineWavePoints");function ao(e,t,r,i,a,n){const o=[],s=a*Math.PI/180,h=(n*Math.PI/180-s)/(i-1);for(let u=0;u<i;u++){const f=s+u*h,p=e+r*Math.cos(f),g=t+r*Math.sin(f);o.push({x:-p,y:-g})}return o}d(ao,"generateCirclePoints");var c2=d((e,t)=>{var r=e.x,i=e.y,a=t.x-r,n=t.y-i,o=e.width/2,s=e.height/2,l,c;return Math.abs(n)*o>Math.abs(a)*s?(n<0&&(s=-s),l=n===0?0:s*a/n,c=s):(a<0&&(o=-o),l=o,c=a===0?0:o*n/a),{x:r+l,y:i+c}},"intersectRect"),Or=c2;function nf(e,t){t&&e.attr("style",t)}d(nf,"applyStyle");async function sf(e){const t=lt(document.createElementNS("http://www.w3.org/2000/svg","foreignObject")),r=t.append("xhtml:div");let i=e.label;e.label&&Tr(e.label)&&(i=await bs(e.label.replace(Er.lineBreakRegex,`
`),ut()));const a=e.isNode?"nodeLabel":"edgeLabel";return r.html('<span class="'+a+'" '+(e.labelStyle?'style="'+e.labelStyle+'"':"")+">"+i+"</span>"),nf(r,e.labelStyle),r.style("display","inline-block"),r.style("padding-right","1px"),r.style("white-space","nowrap"),r.attr("xmlns","http://www.w3.org/1999/xhtml"),t.node()}d(sf,"addHtmlLabel");var h2=d(async(e,t,r,i)=>{let a=e||"";if(typeof a=="object"&&(a=a[0]),vt(ut().flowchart.htmlLabels)){a=a.replace(/\\n|\n/g,"<br />"),O.info("vertexText"+a);const n={isNode:i,label:lr(a).replace(/fa[blrs]?:fa-[\w-]+/g,s=>`<i class='${s.replace(":"," ")}'></i>`),labelStyle:t&&t.replace("fill:","color:")};return await sf(n)}else{const n=document.createElementNS("http://www.w3.org/2000/svg","text");n.setAttribute("style",t.replace("color:","fill:"));let o=[];typeof a=="string"?o=a.split(/\\n|\n|<br\s*\/?>/gi):Array.isArray(a)?o=a:o=[];for(const s of o){const l=document.createElementNS("http://www.w3.org/2000/svg","tspan");l.setAttributeNS("http://www.w3.org/XML/1998/namespace","xml:space","preserve"),l.setAttribute("dy","1em"),l.setAttribute("x","0"),r?l.setAttribute("class","title-row"):l.setAttribute("class","row"),l.textContent=s.trim(),n.appendChild(l)}return n}},"createLabel"),Je=h2,$e=d((e,t,r,i,a)=>["M",e+a,t,"H",e+r-a,"A",a,a,0,0,1,e+r,t+a,"V",t+i-a,"A",a,a,0,0,1,e+r-a,t+i,"H",e+a,"A",a,a,0,0,1,e,t+i-a,"V",t+a,"A",a,a,0,0,1,e+a,t,"Z"].join(" "),"createRoundedRectPathD"),u2=d(e=>{const{handDrawnSeed:t}=ut();return{fill:e,hachureAngle:120,hachureGap:4,fillWeight:2,roughness:.7,stroke:e,seed:t}},"solidStateFill"),Dr=d(e=>{const t=f2([...e.cssCompiledStyles||[],...e.cssStyles||[]]);return{stylesMap:t,stylesArray:[...t]}},"compileStyles"),f2=d(e=>{const t=new Map;return e.forEach(r=>{const[i,a]=r.split(":");t.set(i.trim(),a==null?void 0:a.trim())}),t},"styles2Map"),of=d(e=>e==="color"||e==="font-size"||e==="font-family"||e==="font-weight"||e==="font-style"||e==="text-decoration"||e==="text-align"||e==="text-transform"||e==="line-height"||e==="letter-spacing"||e==="word-spacing"||e==="text-shadow"||e==="text-overflow"||e==="white-space"||e==="word-wrap"||e==="word-break"||e==="overflow-wrap"||e==="hyphens","isLabelStyle"),X=d(e=>{const{stylesArray:t}=Dr(e),r=[],i=[],a=[],n=[];return t.forEach(o=>{const s=o[0];of(s)?r.push(o.join(":")+" !important"):(i.push(o.join(":")+" !important"),s.includes("stroke")&&a.push(o.join(":")+" !important"),s==="fill"&&n.push(o.join(":")+" !important"))}),{labelStyles:r.join(";"),nodeStyles:i.join(";"),stylesArray:t,borderStyles:a,backgroundStyles:n}},"styles2String"),G=d((e,t)=>{var l;const{themeVariables:r,handDrawnSeed:i}=ut(),{nodeBorder:a,mainBkg:n}=r,{stylesMap:o}=Dr(e);return Object.assign({roughness:.7,fill:o.get("fill")||n,fillStyle:"hachure",fillWeight:4,hachureGap:5.2,stroke:o.get("stroke")||a,seed:i,strokeWidth:((l=o.get("stroke-width"))==null?void 0:l.replace("px",""))||1.3,fillLineDash:[0,0]},t)},"userNodeOverrides"),lf=d(async(e,t)=>{O.info("Creating subgraph rect for ",t.id,t);const r=ut(),{themeVariables:i,handDrawnSeed:a}=r,{clusterBkg:n,clusterBorder:o}=i,{labelStyles:s,nodeStyles:l,borderStyles:c,backgroundStyles:h}=X(t),u=e.insert("g").attr("class","cluster "+t.cssClasses).attr("id",t.id).attr("data-look",t.look),f=vt(r.flowchart.htmlLabels),p=u.insert("g").attr("class","cluster-label "),g=await je(p,t.label,{style:t.labelStyle,useHtmlLabels:f,isNode:!0});let m=g.getBBox();if(vt(r.flowchart.htmlLabels)){const T=g.children[0],D=lt(g);m=T.getBoundingClientRect(),D.attr("width",m.width),D.attr("height",m.height)}const y=t.width<=m.width+t.padding?m.width+t.padding:t.width;t.width<=m.width+t.padding?t.diff=(y-t.width)/2-t.padding:t.diff=-t.padding;const x=t.height,b=t.x-y/2,C=t.y-x/2;O.trace("Data ",t,JSON.stringify(t));let S;if(t.look==="handDrawn"){const T=j.svg(u),D=G(t,{roughness:.7,fill:n,stroke:o,fillWeight:3,seed:a}),P=T.path($e(b,C,y,x,0),D);S=u.insert(()=>(O.debug("Rough node insert CXC",P),P),":first-child"),S.select("path:nth-child(2)").attr("style",c.join(";")),S.select("path").attr("style",h.join(";").replace("fill","stroke"))}else S=u.insert("rect",":first-child"),S.attr("style",l).attr("rx",t.rx).attr("ry",t.ry).attr("x",b).attr("y",C).attr("width",y).attr("height",x);const{subGraphTitleTopMargin:v}=Fs(r);if(p.attr("transform",`translate(${t.x-m.width/2}, ${t.y-t.height/2+v})`),s){const T=p.select("span");T&&T.attr("style",s)}const M=S.node().getBBox();return t.offsetX=0,t.width=M.width,t.height=M.height,t.offsetY=m.height-t.padding/2,t.intersect=function(T){return Or(t,T)},{cluster:u,labelBBox:m}},"rect"),p2=d((e,t)=>{const r=e.insert("g").attr("class","note-cluster").attr("id",t.id),i=r.insert("rect",":first-child"),a=0*t.padding,n=a/2;i.attr("rx",t.rx).attr("ry",t.ry).attr("x",t.x-t.width/2-n).attr("y",t.y-t.height/2-n).attr("width",t.width+a).attr("height",t.height+a).attr("fill","none");const o=i.node().getBBox();return t.width=o.width,t.height=o.height,t.intersect=function(s){return Or(t,s)},{cluster:r,labelBBox:{width:0,height:0}}},"noteGroup"),d2=d(async(e,t)=>{const r=ut(),{themeVariables:i,handDrawnSeed:a}=r,{altBackground:n,compositeBackground:o,compositeTitleBackground:s,nodeBorder:l}=i,c=e.insert("g").attr("class",t.cssClasses).attr("id",t.id).attr("data-id",t.id).attr("data-look",t.look),h=c.insert("g",":first-child"),u=c.insert("g").attr("class","cluster-label");let f=c.append("rect");const p=u.node().appendChild(await Je(t.label,t.labelStyle,void 0,!0));let g=p.getBBox();if(vt(r.flowchart.htmlLabels)){const P=p.children[0],$=lt(p);g=P.getBoundingClientRect(),$.attr("width",g.width),$.attr("height",g.height)}const m=0*t.padding,y=m/2,x=(t.width<=g.width+t.padding?g.width+t.padding:t.width)+m;t.width<=g.width+t.padding?t.diff=(x-t.width)/2-t.padding:t.diff=-t.padding;const b=t.height+m,C=t.height+m-g.height-6,S=t.x-x/2,v=t.y-b/2;t.width=x;const M=t.y-t.height/2-y+g.height+2;let T;if(t.look==="handDrawn"){const P=t.cssClasses.includes("statediagram-cluster-alt"),$=j.svg(c),L=t.rx||t.ry?$.path($e(S,v,x,b,10),{roughness:.7,fill:s,fillStyle:"solid",stroke:l,seed:a}):$.rectangle(S,v,x,b,{seed:a});T=c.insert(()=>L,":first-child");const z=$.rectangle(S,M,x,C,{fill:P?n:o,fillStyle:P?"hachure":"solid",stroke:l,seed:a});T=c.insert(()=>L,":first-child"),f=c.insert(()=>z)}else T=h.insert("rect",":first-child"),T.attr("class","outer").attr("x",S).attr("y",v).attr("width",x).attr("height",b).attr("data-look",t.look),f.attr("class","inner").attr("x",S).attr("y",M).attr("width",x).attr("height",C);u.attr("transform",`translate(${t.x-g.width/2}, ${v+1-(vt(r.flowchart.htmlLabels)?0:3)})`);const D=T.node().getBBox();return t.height=D.height,t.offsetX=0,t.offsetY=g.height-t.padding/2,t.labelBBox=g,t.intersect=function(P){return Or(t,P)},{cluster:c,labelBBox:g}},"roundedWithTitle"),g2=d(async(e,t)=>{O.info("Creating subgraph rect for ",t.id,t);const r=ut(),{themeVariables:i,handDrawnSeed:a}=r,{clusterBkg:n,clusterBorder:o}=i,{labelStyles:s,nodeStyles:l,borderStyles:c,backgroundStyles:h}=X(t),u=e.insert("g").attr("class","cluster "+t.cssClasses).attr("id",t.id).attr("data-look",t.look),f=vt(r.flowchart.htmlLabels),p=u.insert("g").attr("class","cluster-label "),g=await je(p,t.label,{style:t.labelStyle,useHtmlLabels:f,isNode:!0,width:t.width});let m=g.getBBox();if(vt(r.flowchart.htmlLabels)){const T=g.children[0],D=lt(g);m=T.getBoundingClientRect(),D.attr("width",m.width),D.attr("height",m.height)}const y=t.width<=m.width+t.padding?m.width+t.padding:t.width;t.width<=m.width+t.padding?t.diff=(y-t.width)/2-t.padding:t.diff=-t.padding;const x=t.height,b=t.x-y/2,C=t.y-x/2;O.trace("Data ",t,JSON.stringify(t));let S;if(t.look==="handDrawn"){const T=j.svg(u),D=G(t,{roughness:.7,fill:n,stroke:o,fillWeight:4,seed:a}),P=T.path($e(b,C,y,x,t.rx),D);S=u.insert(()=>(O.debug("Rough node insert CXC",P),P),":first-child"),S.select("path:nth-child(2)").attr("style",c.join(";")),S.select("path").attr("style",h.join(";").replace("fill","stroke"))}else S=u.insert("rect",":first-child"),S.attr("style",l).attr("rx",t.rx).attr("ry",t.ry).attr("x",b).attr("y",C).attr("width",y).attr("height",x);const{subGraphTitleTopMargin:v}=Fs(r);if(p.attr("transform",`translate(${t.x-m.width/2}, ${t.y-t.height/2+v})`),s){const T=p.select("span");T&&T.attr("style",s)}const M=S.node().getBBox();return t.offsetX=0,t.width=M.width,t.height=M.height,t.offsetY=m.height-t.padding/2,t.intersect=function(T){return Or(t,T)},{cluster:u,labelBBox:m}},"kanbanSection"),m2=d((e,t)=>{const r=ut(),{themeVariables:i,handDrawnSeed:a}=r,{nodeBorder:n}=i,o=e.insert("g").attr("class",t.cssClasses).attr("id",t.id).attr("data-look",t.look),s=o.insert("g",":first-child"),l=0*t.padding,c=t.width+l;t.diff=-t.padding;const h=t.height+l,u=t.x-c/2,f=t.y-h/2;t.width=c;let p;if(t.look==="handDrawn"){const y=j.svg(o).rectangle(u,f,c,h,{fill:"lightgrey",roughness:.5,strokeLineDash:[5],stroke:n,seed:a});p=o.insert(()=>y,":first-child")}else p=s.insert("rect",":first-child"),p.attr("class","divider").attr("x",u).attr("y",f).attr("width",c).attr("height",h).attr("data-look",t.look);const g=p.node().getBBox();return t.height=g.height,t.offsetX=0,t.offsetY=0,t.intersect=function(m){return Or(t,m)},{cluster:o,labelBBox:{}}},"divider"),y2=lf,x2={rect:lf,squareRect:y2,roundedWithTitle:d2,noteGroup:p2,divider:m2,kanbanSection:g2},cf=new Map,b2=d(async(e,t)=>{const r=t.shape||"rect",i=await x2[r](e,t);return cf.set(t.id,i),i},"insertCluster"),MS=d(()=>{cf=new Map},"clear");function hf(e,t){return e.intersect(t)}d(hf,"intersectNode");var C2=hf;function uf(e,t,r,i){var a=e.x,n=e.y,o=a-i.x,s=n-i.y,l=Math.sqrt(t*t*s*s+r*r*o*o),c=Math.abs(t*r*o/l);i.x<a&&(c=-c);var h=Math.abs(t*r*s/l);return i.y<n&&(h=-h),{x:a+c,y:n+h}}d(uf,"intersectEllipse");var ff=uf;function pf(e,t,r){return ff(e,t,t,r)}d(pf,"intersectCircle");var k2=pf;function df(e,t,r,i){var a,n,o,s,l,c,h,u,f,p,g,m,y,x,b;if(a=t.y-e.y,o=e.x-t.x,l=t.x*e.y-e.x*t.y,f=a*r.x+o*r.y+l,p=a*i.x+o*i.y+l,!(f!==0&&p!==0&&os(f,p))&&(n=i.y-r.y,s=r.x-i.x,c=i.x*r.y-r.x*i.y,h=n*e.x+s*e.y+c,u=n*t.x+s*t.y+c,!(h!==0&&u!==0&&os(h,u))&&(g=a*s-n*o,g!==0)))return m=Math.abs(g/2),y=o*c-s*l,x=y<0?(y-m)/g:(y+m)/g,y=n*l-a*c,b=y<0?(y-m)/g:(y+m)/g,{x,y:b}}d(df,"intersectLine");function os(e,t){return e*t>0}d(os,"sameSign");var w2=df;function gf(e,t,r){let i=e.x,a=e.y,n=[],o=Number.POSITIVE_INFINITY,s=Number.POSITIVE_INFINITY;typeof t.forEach=="function"?t.forEach(function(h){o=Math.min(o,h.x),s=Math.min(s,h.y)}):(o=Math.min(o,t.x),s=Math.min(s,t.y));let l=i-e.width/2-o,c=a-e.height/2-s;for(let h=0;h<t.length;h++){let u=t[h],f=t[h<t.length-1?h+1:0],p=w2(e,r,{x:l+u.x,y:c+u.y},{x:l+f.x,y:c+f.y});p&&n.push(p)}return n.length?(n.length>1&&n.sort(function(h,u){let f=h.x-r.x,p=h.y-r.y,g=Math.sqrt(f*f+p*p),m=u.x-r.x,y=u.y-r.y,x=Math.sqrt(m*m+y*y);return g<x?-1:g===x?0:1}),n[0]):e}d(gf,"intersectPolygon");var v2=gf,H={node:C2,circle:k2,ellipse:ff,polygon:v2,rect:Or};function mf(e,t){const{labelStyles:r}=X(t);t.labelStyle=r;const i=it(t);let a=i;i||(a="anchor");const n=e.insert("g").attr("class",a).attr("id",t.domId||t.id),o=1,{cssStyles:s}=t,l=j.svg(n),c=G(t,{fill:"black",stroke:"none",fillStyle:"solid"});t.look!=="handDrawn"&&(c.roughness=0);const h=l.circle(0,0,o*2,c),u=n.insert(()=>h,":first-child");return u.attr("class","anchor").attr("style",qt(s)),V(t,u),t.intersect=function(f){return O.info("Circle intersect",t,o,f),H.circle(t,o,f)},n}d(mf,"anchor");function ls(e,t,r,i,a,n,o){const l=(e+r)/2,c=(t+i)/2,h=Math.atan2(i-t,r-e),u=(r-e)/2,f=(i-t)/2,p=u/a,g=f/n,m=Math.sqrt(p**2+g**2);if(m>1)throw new Error("The given radii are too small to create an arc between the points.");const y=Math.sqrt(1-m**2),x=l+y*n*Math.sin(h)*(o?-1:1),b=c-y*a*Math.cos(h)*(o?-1:1),C=Math.atan2((t-b)/n,(e-x)/a);let v=Math.atan2((i-b)/n,(r-x)/a)-C;o&&v<0&&(v+=2*Math.PI),!o&&v>0&&(v-=2*Math.PI);const M=[];for(let T=0;T<20;T++){const D=T/19,P=C+D*v,$=x+a*Math.cos(P),L=b+n*Math.sin(P);M.push({x:$,y:L})}return M}d(ls,"generateArcPoints");async function yf(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:a,bbox:n}=await st(e,t,it(t)),o=n.width+t.padding+20,s=n.height+t.padding,l=s/2,c=l/(2.5+s/50),{cssStyles:h}=t,u=[{x:o/2,y:-s/2},{x:-o/2,y:-s/2},...ls(-o/2,-s/2,-o/2,s/2,c,l,!1),{x:o/2,y:s/2},...ls(o/2,s/2,o/2,-s/2,c,l,!0)],f=j.svg(a),p=G(t,{});t.look!=="handDrawn"&&(p.roughness=0,p.fillStyle="solid");const g=ct(u),m=f.path(g,p),y=a.insert(()=>m,":first-child");return y.attr("class","basic label-container"),h&&t.look!=="handDrawn"&&y.selectAll("path").attr("style",h),i&&t.look!=="handDrawn"&&y.selectAll("path").attr("style",i),y.attr("transform",`translate(${c/2}, 0)`),V(t,y),t.intersect=function(x){return H.polygon(t,u,x)},a}d(yf,"bowTieRect");function Oe(e,t,r,i){return e.insert("polygon",":first-child").attr("points",i.map(function(a){return a.x+","+a.y}).join(" ")).attr("class","label-container").attr("transform","translate("+-t/2+","+r/2+")")}d(Oe,"insertPolygonShape");async function xf(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:a,bbox:n}=await st(e,t,it(t)),o=n.height+t.padding,s=12,l=n.width+t.padding+s,c=0,h=l,u=-o,f=0,p=[{x:c+s,y:u},{x:h,y:u},{x:h,y:f},{x:c,y:f},{x:c,y:u+s},{x:c+s,y:u}];let g;const{cssStyles:m}=t;if(t.look==="handDrawn"){const y=j.svg(a),x=G(t,{}),b=ct(p),C=y.path(b,x);g=a.insert(()=>C,":first-child").attr("transform",`translate(${-l/2}, ${o/2})`),m&&g.attr("style",m)}else g=Oe(a,l,o,p);return i&&g.attr("style",i),V(t,g),t.intersect=function(y){return H.polygon(t,p,y)},a}d(xf,"card");function bf(e,t){const{nodeStyles:r}=X(t);t.label="";const i=e.insert("g").attr("class",it(t)).attr("id",t.domId??t.id),{cssStyles:a}=t,n=Math.max(28,t.width??0),o=[{x:0,y:n/2},{x:n/2,y:0},{x:0,y:-n/2},{x:-n/2,y:0}],s=j.svg(i),l=G(t,{});t.look!=="handDrawn"&&(l.roughness=0,l.fillStyle="solid");const c=ct(o),h=s.path(c,l),u=i.insert(()=>h,":first-child");return a&&t.look!=="handDrawn"&&u.selectAll("path").attr("style",a),r&&t.look!=="handDrawn"&&u.selectAll("path").attr("style",r),t.width=28,t.height=28,t.intersect=function(f){return H.polygon(t,o,f)},i}d(bf,"choice");async function Cf(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:a,bbox:n,halfPadding:o}=await st(e,t,it(t)),s=n.width/2+o;let l;const{cssStyles:c}=t;if(t.look==="handDrawn"){const h=j.svg(a),u=G(t,{}),f=h.circle(0,0,s*2,u);l=a.insert(()=>f,":first-child"),l.attr("class","basic label-container").attr("style",qt(c))}else l=a.insert("circle",":first-child").attr("class","basic label-container").attr("style",i).attr("r",s).attr("cx",0).attr("cy",0);return V(t,l),t.intersect=function(h){return O.info("Circle intersect",t,s,h),H.circle(t,s,h)},a}d(Cf,"circle");function kf(e){const t=Math.cos(Math.PI/4),r=Math.sin(Math.PI/4),i=e*2,a={x:i/2*t,y:i/2*r},n={x:-(i/2)*t,y:i/2*r},o={x:-(i/2)*t,y:-(i/2)*r},s={x:i/2*t,y:-(i/2)*r};return`M ${n.x},${n.y} L ${s.x},${s.y}
                   M ${a.x},${a.y} L ${o.x},${o.y}`}d(kf,"createLine");function wf(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r,t.label="";const a=e.insert("g").attr("class",it(t)).attr("id",t.domId??t.id),n=Math.max(30,(t==null?void 0:t.width)??0),{cssStyles:o}=t,s=j.svg(a),l=G(t,{});t.look!=="handDrawn"&&(l.roughness=0,l.fillStyle="solid");const c=s.circle(0,0,n*2,l),h=kf(n),u=s.path(h,l),f=a.insert(()=>c,":first-child");return f.insert(()=>u),o&&t.look!=="handDrawn"&&f.selectAll("path").attr("style",o),i&&t.look!=="handDrawn"&&f.selectAll("path").attr("style",i),V(t,f),t.intersect=function(p){return O.info("crossedCircle intersect",t,{radius:n,point:p}),H.circle(t,n,p)},a}d(wf,"crossedCircle");function _e(e,t,r,i=100,a=0,n=180){const o=[],s=a*Math.PI/180,h=(n*Math.PI/180-s)/(i-1);for(let u=0;u<i;u++){const f=s+u*h,p=e+r*Math.cos(f),g=t+r*Math.sin(f);o.push({x:-p,y:-g})}return o}d(_e,"generateCirclePoints");async function vf(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:a,bbox:n,label:o}=await st(e,t,it(t)),s=n.width+(t.padding??0),l=n.height+(t.padding??0),c=Math.max(5,l*.1),{cssStyles:h}=t,u=[..._e(s/2,-l/2,c,30,-90,0),{x:-s/2-c,y:c},..._e(s/2+c*2,-c,c,20,-180,-270),..._e(s/2+c*2,c,c,20,-90,-180),{x:-s/2-c,y:-l/2},..._e(s/2,l/2,c,20,0,90)],f=[{x:s/2,y:-l/2-c},{x:-s/2,y:-l/2-c},..._e(s/2,-l/2,c,20,-90,0),{x:-s/2-c,y:-c},..._e(s/2+s*.1,-c,c,20,-180,-270),..._e(s/2+s*.1,c,c,20,-90,-180),{x:-s/2-c,y:l/2},..._e(s/2,l/2,c,20,0,90),{x:-s/2,y:l/2+c},{x:s/2,y:l/2+c}],p=j.svg(a),g=G(t,{fill:"none"});t.look!=="handDrawn"&&(g.roughness=0,g.fillStyle="solid");const y=ct(u).replace("Z",""),x=p.path(y,g),b=ct(f),C=p.path(b,{...g}),S=a.insert("g",":first-child");return S.insert(()=>C,":first-child").attr("stroke-opacity",0),S.insert(()=>x,":first-child"),S.attr("class","text"),h&&t.look!=="handDrawn"&&S.selectAll("path").attr("style",h),i&&t.look!=="handDrawn"&&S.selectAll("path").attr("style",i),S.attr("transform",`translate(${c}, 0)`),o.attr("transform",`translate(${-s/2+c-(n.x-(n.left??0))},${-l/2+(t.padding??0)/2-(n.y-(n.top??0))})`),V(t,S),t.intersect=function(v){return H.polygon(t,f,v)},a}d(vf,"curlyBraceLeft");function Be(e,t,r,i=100,a=0,n=180){const o=[],s=a*Math.PI/180,h=(n*Math.PI/180-s)/(i-1);for(let u=0;u<i;u++){const f=s+u*h,p=e+r*Math.cos(f),g=t+r*Math.sin(f);o.push({x:p,y:g})}return o}d(Be,"generateCirclePoints");async function Sf(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:a,bbox:n,label:o}=await st(e,t,it(t)),s=n.width+(t.padding??0),l=n.height+(t.padding??0),c=Math.max(5,l*.1),{cssStyles:h}=t,u=[...Be(s/2,-l/2,c,20,-90,0),{x:s/2+c,y:-c},...Be(s/2+c*2,-c,c,20,-180,-270),...Be(s/2+c*2,c,c,20,-90,-180),{x:s/2+c,y:l/2},...Be(s/2,l/2,c,20,0,90)],f=[{x:-s/2,y:-l/2-c},{x:s/2,y:-l/2-c},...Be(s/2,-l/2,c,20,-90,0),{x:s/2+c,y:-c},...Be(s/2+c*2,-c,c,20,-180,-270),...Be(s/2+c*2,c,c,20,-90,-180),{x:s/2+c,y:l/2},...Be(s/2,l/2,c,20,0,90),{x:s/2,y:l/2+c},{x:-s/2,y:l/2+c}],p=j.svg(a),g=G(t,{fill:"none"});t.look!=="handDrawn"&&(g.roughness=0,g.fillStyle="solid");const y=ct(u).replace("Z",""),x=p.path(y,g),b=ct(f),C=p.path(b,{...g}),S=a.insert("g",":first-child");return S.insert(()=>C,":first-child").attr("stroke-opacity",0),S.insert(()=>x,":first-child"),S.attr("class","text"),h&&t.look!=="handDrawn"&&S.selectAll("path").attr("style",h),i&&t.look!=="handDrawn"&&S.selectAll("path").attr("style",i),S.attr("transform",`translate(${-c}, 0)`),o.attr("transform",`translate(${-s/2+(t.padding??0)/2-(n.x-(n.left??0))},${-l/2+(t.padding??0)/2-(n.y-(n.top??0))})`),V(t,S),t.intersect=function(v){return H.polygon(t,f,v)},a}d(Sf,"curlyBraceRight");function At(e,t,r,i=100,a=0,n=180){const o=[],s=a*Math.PI/180,h=(n*Math.PI/180-s)/(i-1);for(let u=0;u<i;u++){const f=s+u*h,p=e+r*Math.cos(f),g=t+r*Math.sin(f);o.push({x:-p,y:-g})}return o}d(At,"generateCirclePoints");async function Tf(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:a,bbox:n,label:o}=await st(e,t,it(t)),s=n.width+(t.padding??0),l=n.height+(t.padding??0),c=Math.max(5,l*.1),{cssStyles:h}=t,u=[...At(s/2,-l/2,c,30,-90,0),{x:-s/2-c,y:c},...At(s/2+c*2,-c,c,20,-180,-270),...At(s/2+c*2,c,c,20,-90,-180),{x:-s/2-c,y:-l/2},...At(s/2,l/2,c,20,0,90)],f=[...At(-s/2+c+c/2,-l/2,c,20,-90,-180),{x:s/2-c/2,y:c},...At(-s/2-c/2,-c,c,20,0,90),...At(-s/2-c/2,c,c,20,-90,0),{x:s/2-c/2,y:-c},...At(-s/2+c+c/2,l/2,c,30,-180,-270)],p=[{x:s/2,y:-l/2-c},{x:-s/2,y:-l/2-c},...At(s/2,-l/2,c,20,-90,0),{x:-s/2-c,y:-c},...At(s/2+c*2,-c,c,20,-180,-270),...At(s/2+c*2,c,c,20,-90,-180),{x:-s/2-c,y:l/2},...At(s/2,l/2,c,20,0,90),{x:-s/2,y:l/2+c},{x:s/2-c-c/2,y:l/2+c},...At(-s/2+c+c/2,-l/2,c,20,-90,-180),{x:s/2-c/2,y:c},...At(-s/2-c/2,-c,c,20,0,90),...At(-s/2-c/2,c,c,20,-90,0),{x:s/2-c/2,y:-c},...At(-s/2+c+c/2,l/2,c,30,-180,-270)],g=j.svg(a),m=G(t,{fill:"none"});t.look!=="handDrawn"&&(m.roughness=0,m.fillStyle="solid");const x=ct(u).replace("Z",""),b=g.path(x,m),S=ct(f).replace("Z",""),v=g.path(S,m),M=ct(p),T=g.path(M,{...m}),D=a.insert("g",":first-child");return D.insert(()=>T,":first-child").attr("stroke-opacity",0),D.insert(()=>b,":first-child"),D.insert(()=>v,":first-child"),D.attr("class","text"),h&&t.look!=="handDrawn"&&D.selectAll("path").attr("style",h),i&&t.look!=="handDrawn"&&D.selectAll("path").attr("style",i),D.attr("transform",`translate(${c-c/4}, 0)`),o.attr("transform",`translate(${-s/2+(t.padding??0)/2-(n.x-(n.left??0))},${-l/2+(t.padding??0)/2-(n.y-(n.top??0))})`),V(t,D),t.intersect=function(P){return H.polygon(t,p,P)},a}d(Tf,"curlyBraces");async function _f(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:a,bbox:n}=await st(e,t,it(t)),o=80,s=20,l=Math.max(o,(n.width+(t.padding??0)*2)*1.25,(t==null?void 0:t.width)??0),c=Math.max(s,n.height+(t.padding??0)*2,(t==null?void 0:t.height)??0),h=c/2,{cssStyles:u}=t,f=j.svg(a),p=G(t,{});t.look!=="handDrawn"&&(p.roughness=0,p.fillStyle="solid");const g=l,m=c,y=g-h,x=m/4,b=[{x:y,y:0},{x,y:0},{x:0,y:m/2},{x,y:m},{x:y,y:m},...ao(-y,-m/2,h,50,270,90)],C=ct(b),S=f.path(C,p),v=a.insert(()=>S,":first-child");return v.attr("class","basic label-container"),u&&t.look!=="handDrawn"&&v.selectChildren("path").attr("style",u),i&&t.look!=="handDrawn"&&v.selectChildren("path").attr("style",i),v.attr("transform",`translate(${-l/2}, ${-c/2})`),V(t,v),t.intersect=function(M){return H.polygon(t,b,M)},a}d(_f,"curvedTrapezoid");var S2=d((e,t,r,i,a,n)=>[`M${e},${t+n}`,`a${a},${n} 0,0,0 ${r},0`,`a${a},${n} 0,0,0 ${-r},0`,`l0,${i}`,`a${a},${n} 0,0,0 ${r},0`,`l0,${-i}`].join(" "),"createCylinderPathD"),T2=d((e,t,r,i,a,n)=>[`M${e},${t+n}`,`M${e+r},${t+n}`,`a${a},${n} 0,0,0 ${-r},0`,`l0,${i}`,`a${a},${n} 0,0,0 ${r},0`,`l0,${-i}`].join(" "),"createOuterCylinderPathD"),_2=d((e,t,r,i,a,n)=>[`M${e-r/2},${-i/2}`,`a${a},${n} 0,0,0 ${r},0`].join(" "),"createInnerCylinderPathD");async function Bf(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:a,bbox:n,label:o}=await st(e,t,it(t)),s=Math.max(n.width+t.padding,t.width??0),l=s/2,c=l/(2.5+s/50),h=Math.max(n.height+c+t.padding,t.height??0);let u;const{cssStyles:f}=t;if(t.look==="handDrawn"){const p=j.svg(a),g=T2(0,0,s,h,l,c),m=_2(0,c,s,h,l,c),y=p.path(g,G(t,{})),x=p.path(m,G(t,{fill:"none"}));u=a.insert(()=>x,":first-child"),u=a.insert(()=>y,":first-child"),u.attr("class","basic label-container"),f&&u.attr("style",f)}else{const p=S2(0,0,s,h,l,c);u=a.insert("path",":first-child").attr("d",p).attr("class","basic label-container").attr("style",qt(f)).attr("style",i)}return u.attr("label-offset-y",c),u.attr("transform",`translate(${-s/2}, ${-(h/2+c)})`),V(t,u),o.attr("transform",`translate(${-(n.width/2)-(n.x-(n.left??0))}, ${-(n.height/2)+(t.padding??0)/1.5-(n.y-(n.top??0))})`),t.intersect=function(p){const g=H.rect(t,p),m=g.x-(t.x??0);if(l!=0&&(Math.abs(m)<(t.width??0)/2||Math.abs(m)==(t.width??0)/2&&Math.abs(g.y-(t.y??0))>(t.height??0)/2-c)){let y=c*c*(1-m*m/(l*l));y>0&&(y=Math.sqrt(y)),y=c-y,p.y-(t.y??0)>0&&(y=-y),g.y+=y}return g},a}d(Bf,"cylinder");async function Lf(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:a,bbox:n,label:o}=await st(e,t,it(t)),s=n.width+t.padding,l=n.height+t.padding,c=l*.2,h=-s/2,u=-l/2-c/2,{cssStyles:f}=t,p=j.svg(a),g=G(t,{});t.look!=="handDrawn"&&(g.roughness=0,g.fillStyle="solid");const m=[{x:h,y:u+c},{x:-h,y:u+c},{x:-h,y:-u},{x:h,y:-u},{x:h,y:u},{x:-h,y:u},{x:-h,y:u+c}],y=p.polygon(m.map(b=>[b.x,b.y]),g),x=a.insert(()=>y,":first-child");return x.attr("class","basic label-container"),f&&t.look!=="handDrawn"&&x.selectAll("path").attr("style",f),i&&t.look!=="handDrawn"&&x.selectAll("path").attr("style",i),o.attr("transform",`translate(${h+(t.padding??0)/2-(n.x-(n.left??0))}, ${u+c+(t.padding??0)/2-(n.y-(n.top??0))})`),V(t,x),t.intersect=function(b){return H.rect(t,b)},a}d(Lf,"dividedRectangle");async function Af(e,t){var f,p;const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:a,bbox:n,halfPadding:o}=await st(e,t,it(t)),l=n.width/2+o+5,c=n.width/2+o;let h;const{cssStyles:u}=t;if(t.look==="handDrawn"){const g=j.svg(a),m=G(t,{roughness:.2,strokeWidth:2.5}),y=G(t,{roughness:.2,strokeWidth:1.5}),x=g.circle(0,0,l*2,m),b=g.circle(0,0,c*2,y);h=a.insert("g",":first-child"),h.attr("class",qt(t.cssClasses)).attr("style",qt(u)),(f=h.node())==null||f.appendChild(x),(p=h.node())==null||p.appendChild(b)}else{h=a.insert("g",":first-child");const g=h.insert("circle",":first-child"),m=h.insert("circle");h.attr("class","basic label-container").attr("style",i),g.attr("class","outer-circle").attr("style",i).attr("r",l).attr("cx",0).attr("cy",0),m.attr("class","inner-circle").attr("style",i).attr("r",c).attr("cx",0).attr("cy",0)}return V(t,h),t.intersect=function(g){return O.info("DoubleCircle intersect",t,l,g),H.circle(t,l,g)},a}d(Af,"doublecircle");function Mf(e,t,{config:{themeVariables:r}}){const{labelStyles:i,nodeStyles:a}=X(t);t.label="",t.labelStyle=i;const n=e.insert("g").attr("class",it(t)).attr("id",t.domId??t.id),o=7,{cssStyles:s}=t,l=j.svg(n),{nodeBorder:c}=r,h=G(t,{fillStyle:"solid"});t.look!=="handDrawn"&&(h.roughness=0);const u=l.circle(0,0,o*2,h),f=n.insert(()=>u,":first-child");return f.selectAll("path").attr("style",`fill: ${c} !important;`),s&&s.length>0&&t.look!=="handDrawn"&&f.selectAll("path").attr("style",s),a&&t.look!=="handDrawn"&&f.selectAll("path").attr("style",a),V(t,f),t.intersect=function(p){return O.info("filledCircle intersect",t,{radius:o,point:p}),H.circle(t,o,p)},n}d(Mf,"filledCircle");async function Ef(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:a,bbox:n,label:o}=await st(e,t,it(t)),s=n.width+(t.padding??0),l=s+n.height,c=s+n.height,h=[{x:0,y:-l},{x:c,y:-l},{x:c/2,y:0}],{cssStyles:u}=t,f=j.svg(a),p=G(t,{});t.look!=="handDrawn"&&(p.roughness=0,p.fillStyle="solid");const g=ct(h),m=f.path(g,p),y=a.insert(()=>m,":first-child").attr("transform",`translate(${-l/2}, ${l/2})`);return u&&t.look!=="handDrawn"&&y.selectChildren("path").attr("style",u),i&&t.look!=="handDrawn"&&y.selectChildren("path").attr("style",i),t.width=s,t.height=l,V(t,y),o.attr("transform",`translate(${-n.width/2-(n.x-(n.left??0))}, ${-l/2+(t.padding??0)/2+(n.y-(n.top??0))})`),t.intersect=function(x){return O.info("Triangle intersect",t,h,x),H.polygon(t,h,x)},a}d(Ef,"flippedTriangle");function Ff(e,t,{dir:r,config:{state:i,themeVariables:a}}){const{nodeStyles:n}=X(t);t.label="";const o=e.insert("g").attr("class",it(t)).attr("id",t.domId??t.id),{cssStyles:s}=t;let l=Math.max(70,(t==null?void 0:t.width)??0),c=Math.max(10,(t==null?void 0:t.height)??0);r==="LR"&&(l=Math.max(10,(t==null?void 0:t.width)??0),c=Math.max(70,(t==null?void 0:t.height)??0));const h=-1*l/2,u=-1*c/2,f=j.svg(o),p=G(t,{stroke:a.lineColor,fill:a.lineColor});t.look!=="handDrawn"&&(p.roughness=0,p.fillStyle="solid");const g=f.rectangle(h,u,l,c,p),m=o.insert(()=>g,":first-child");s&&t.look!=="handDrawn"&&m.selectAll("path").attr("style",s),n&&t.look!=="handDrawn"&&m.selectAll("path").attr("style",n),V(t,m);const y=(i==null?void 0:i.padding)??0;return t.width&&t.height&&(t.width+=y/2||0,t.height+=y/2||0),t.intersect=function(x){return H.rect(t,x)},o}d(Ff,"forkJoin");async function $f(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const a=80,n=50,{shapeSvg:o,bbox:s}=await st(e,t,it(t)),l=Math.max(a,s.width+(t.padding??0)*2,(t==null?void 0:t.width)??0),c=Math.max(n,s.height+(t.padding??0)*2,(t==null?void 0:t.height)??0),h=c/2,{cssStyles:u}=t,f=j.svg(o),p=G(t,{});t.look!=="handDrawn"&&(p.roughness=0,p.fillStyle="solid");const g=[{x:-l/2,y:-c/2},{x:l/2-h,y:-c/2},...ao(-l/2+h,0,h,50,90,270),{x:l/2-h,y:c/2},{x:-l/2,y:c/2}],m=ct(g),y=f.path(m,p),x=o.insert(()=>y,":first-child");return x.attr("class","basic label-container"),u&&t.look!=="handDrawn"&&x.selectChildren("path").attr("style",u),i&&t.look!=="handDrawn"&&x.selectChildren("path").attr("style",i),V(t,x),t.intersect=function(b){return O.info("Pill intersect",t,{radius:h,point:b}),H.polygon(t,g,b)},o}d($f,"halfRoundedRectangle");var B2=d((e,t,r,i,a)=>[`M${e+a},${t}`,`L${e+r-a},${t}`,`L${e+r},${t-i/2}`,`L${e+r-a},${t-i}`,`L${e+a},${t-i}`,`L${e},${t-i/2}`,"Z"].join(" "),"createHexagonPathD");async function Of(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:a,bbox:n}=await st(e,t,it(t)),o=4,s=n.height+t.padding,l=s/o,c=n.width+2*l+t.padding,h=[{x:l,y:0},{x:c-l,y:0},{x:c,y:-s/2},{x:c-l,y:-s},{x:l,y:-s},{x:0,y:-s/2}];let u;const{cssStyles:f}=t;if(t.look==="handDrawn"){const p=j.svg(a),g=G(t,{}),m=B2(0,0,c,s,l),y=p.path(m,g);u=a.insert(()=>y,":first-child").attr("transform",`translate(${-c/2}, ${s/2})`),f&&u.attr("style",f)}else u=Oe(a,c,s,h);return i&&u.attr("style",i),t.width=c,t.height=s,V(t,u),t.intersect=function(p){return H.polygon(t,h,p)},a}d(Of,"hexagon");async function Df(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.label="",t.labelStyle=r;const{shapeSvg:a}=await st(e,t,it(t)),n=Math.max(30,(t==null?void 0:t.width)??0),o=Math.max(30,(t==null?void 0:t.height)??0),{cssStyles:s}=t,l=j.svg(a),c=G(t,{});t.look!=="handDrawn"&&(c.roughness=0,c.fillStyle="solid");const h=[{x:0,y:0},{x:n,y:0},{x:0,y:o},{x:n,y:o}],u=ct(h),f=l.path(u,c),p=a.insert(()=>f,":first-child");return p.attr("class","basic label-container"),s&&t.look!=="handDrawn"&&p.selectChildren("path").attr("style",s),i&&t.look!=="handDrawn"&&p.selectChildren("path").attr("style",i),p.attr("transform",`translate(${-n/2}, ${-o/2})`),V(t,p),t.intersect=function(g){return O.info("Pill intersect",t,{points:h}),H.polygon(t,h,g)},a}d(Df,"hourglass");async function Rf(e,t,{config:{themeVariables:r,flowchart:i}}){const{labelStyles:a}=X(t);t.labelStyle=a;const n=t.assetHeight??48,o=t.assetWidth??48,s=Math.max(n,o),l=i==null?void 0:i.wrappingWidth;t.width=Math.max(s,l??0);const{shapeSvg:c,bbox:h,label:u}=await st(e,t,"icon-shape default"),f=t.pos==="t",p=s,g=s,{nodeBorder:m}=r,{stylesMap:y}=Dr(t),x=-g/2,b=-p/2,C=t.label?8:0,S=j.svg(c),v=G(t,{stroke:"none",fill:"none"});t.look!=="handDrawn"&&(v.roughness=0,v.fillStyle="solid");const M=S.rectangle(x,b,g,p,v),T=Math.max(g,h.width),D=p+h.height+C,P=S.rectangle(-T/2,-D/2,T,D,{...v,fill:"transparent",stroke:"none"}),$=c.insert(()=>M,":first-child"),L=c.insert(()=>P);if(t.icon){const z=c.append("g");z.html(`<g>${await za(t.icon,{height:s,width:s,fallbackPrefix:""})}</g>`);const R=z.node().getBBox(),E=R.width,A=R.height,_=R.x,F=R.y;z.attr("transform",`translate(${-E/2-_},${f?h.height/2+C/2-A/2-F:-h.height/2-C/2-A/2-F})`),z.attr("style",`color: ${y.get("stroke")??m};`)}return u.attr("transform",`translate(${-h.width/2-(h.x-(h.left??0))},${f?-D/2:D/2-h.height})`),$.attr("transform",`translate(0,${f?h.height/2+C/2:-h.height/2-C/2})`),V(t,L),t.intersect=function(z){if(O.info("iconSquare intersect",t,z),!t.label)return H.rect(t,z);const R=t.x??0,E=t.y??0,A=t.height??0;let _=[];return f?_=[{x:R-h.width/2,y:E-A/2},{x:R+h.width/2,y:E-A/2},{x:R+h.width/2,y:E-A/2+h.height+C},{x:R+g/2,y:E-A/2+h.height+C},{x:R+g/2,y:E+A/2},{x:R-g/2,y:E+A/2},{x:R-g/2,y:E-A/2+h.height+C},{x:R-h.width/2,y:E-A/2+h.height+C}]:_=[{x:R-g/2,y:E-A/2},{x:R+g/2,y:E-A/2},{x:R+g/2,y:E-A/2+p},{x:R+h.width/2,y:E-A/2+p},{x:R+h.width/2/2,y:E+A/2},{x:R-h.width/2,y:E+A/2},{x:R-h.width/2,y:E-A/2+p},{x:R-g/2,y:E-A/2+p}],H.polygon(t,_,z)},c}d(Rf,"icon");async function If(e,t,{config:{themeVariables:r,flowchart:i}}){const{labelStyles:a}=X(t);t.labelStyle=a;const n=t.assetHeight??48,o=t.assetWidth??48,s=Math.max(n,o),l=i==null?void 0:i.wrappingWidth;t.width=Math.max(s,l??0);const{shapeSvg:c,bbox:h,label:u}=await st(e,t,"icon-shape default"),f=20,p=t.label?8:0,g=t.pos==="t",{nodeBorder:m,mainBkg:y}=r,{stylesMap:x}=Dr(t),b=j.svg(c),C=G(t,{});t.look!=="handDrawn"&&(C.roughness=0,C.fillStyle="solid");const S=x.get("fill");C.stroke=S??y;const v=c.append("g");t.icon&&v.html(`<g>${await za(t.icon,{height:s,width:s,fallbackPrefix:""})}</g>`);const M=v.node().getBBox(),T=M.width,D=M.height,P=M.x,$=M.y,L=Math.max(T,D)*Math.SQRT2+f*2,z=b.circle(0,0,L,C),R=Math.max(L,h.width),E=L+h.height+p,A=b.rectangle(-R/2,-E/2,R,E,{...C,fill:"transparent",stroke:"none"}),_=c.insert(()=>z,":first-child"),F=c.insert(()=>A);return v.attr("transform",`translate(${-T/2-P},${g?h.height/2+p/2-D/2-$:-h.height/2-p/2-D/2-$})`),v.attr("style",`color: ${x.get("stroke")??m};`),u.attr("transform",`translate(${-h.width/2-(h.x-(h.left??0))},${g?-E/2:E/2-h.height})`),_.attr("transform",`translate(0,${g?h.height/2+p/2:-h.height/2-p/2})`),V(t,F),t.intersect=function(B){return O.info("iconSquare intersect",t,B),H.rect(t,B)},c}d(If,"iconCircle");async function Pf(e,t,{config:{themeVariables:r,flowchart:i}}){const{labelStyles:a}=X(t);t.labelStyle=a;const n=t.assetHeight??48,o=t.assetWidth??48,s=Math.max(n,o),l=i==null?void 0:i.wrappingWidth;t.width=Math.max(s,l??0);const{shapeSvg:c,bbox:h,halfPadding:u,label:f}=await st(e,t,"icon-shape default"),p=t.pos==="t",g=s+u*2,m=s+u*2,{nodeBorder:y,mainBkg:x}=r,{stylesMap:b}=Dr(t),C=-m/2,S=-g/2,v=t.label?8:0,M=j.svg(c),T=G(t,{});t.look!=="handDrawn"&&(T.roughness=0,T.fillStyle="solid");const D=b.get("fill");T.stroke=D??x;const P=M.path($e(C,S,m,g,5),T),$=Math.max(m,h.width),L=g+h.height+v,z=M.rectangle(-$/2,-L/2,$,L,{...T,fill:"transparent",stroke:"none"}),R=c.insert(()=>P,":first-child").attr("class","icon-shape2"),E=c.insert(()=>z);if(t.icon){const A=c.append("g");A.html(`<g>${await za(t.icon,{height:s,width:s,fallbackPrefix:""})}</g>`);const _=A.node().getBBox(),F=_.width,B=_.height,W=_.x,U=_.y;A.attr("transform",`translate(${-F/2-W},${p?h.height/2+v/2-B/2-U:-h.height/2-v/2-B/2-U})`),A.attr("style",`color: ${b.get("stroke")??y};`)}return f.attr("transform",`translate(${-h.width/2-(h.x-(h.left??0))},${p?-L/2:L/2-h.height})`),R.attr("transform",`translate(0,${p?h.height/2+v/2:-h.height/2-v/2})`),V(t,E),t.intersect=function(A){if(O.info("iconSquare intersect",t,A),!t.label)return H.rect(t,A);const _=t.x??0,F=t.y??0,B=t.height??0;let W=[];return p?W=[{x:_-h.width/2,y:F-B/2},{x:_+h.width/2,y:F-B/2},{x:_+h.width/2,y:F-B/2+h.height+v},{x:_+m/2,y:F-B/2+h.height+v},{x:_+m/2,y:F+B/2},{x:_-m/2,y:F+B/2},{x:_-m/2,y:F-B/2+h.height+v},{x:_-h.width/2,y:F-B/2+h.height+v}]:W=[{x:_-m/2,y:F-B/2},{x:_+m/2,y:F-B/2},{x:_+m/2,y:F-B/2+g},{x:_+h.width/2,y:F-B/2+g},{x:_+h.width/2/2,y:F+B/2},{x:_-h.width/2,y:F+B/2},{x:_-h.width/2,y:F-B/2+g},{x:_-m/2,y:F-B/2+g}],H.polygon(t,W,A)},c}d(Pf,"iconRounded");async function Nf(e,t,{config:{themeVariables:r,flowchart:i}}){const{labelStyles:a}=X(t);t.labelStyle=a;const n=t.assetHeight??48,o=t.assetWidth??48,s=Math.max(n,o),l=i==null?void 0:i.wrappingWidth;t.width=Math.max(s,l??0);const{shapeSvg:c,bbox:h,halfPadding:u,label:f}=await st(e,t,"icon-shape default"),p=t.pos==="t",g=s+u*2,m=s+u*2,{nodeBorder:y,mainBkg:x}=r,{stylesMap:b}=Dr(t),C=-m/2,S=-g/2,v=t.label?8:0,M=j.svg(c),T=G(t,{});t.look!=="handDrawn"&&(T.roughness=0,T.fillStyle="solid");const D=b.get("fill");T.stroke=D??x;const P=M.path($e(C,S,m,g,.1),T),$=Math.max(m,h.width),L=g+h.height+v,z=M.rectangle(-$/2,-L/2,$,L,{...T,fill:"transparent",stroke:"none"}),R=c.insert(()=>P,":first-child"),E=c.insert(()=>z);if(t.icon){const A=c.append("g");A.html(`<g>${await za(t.icon,{height:s,width:s,fallbackPrefix:""})}</g>`);const _=A.node().getBBox(),F=_.width,B=_.height,W=_.x,U=_.y;A.attr("transform",`translate(${-F/2-W},${p?h.height/2+v/2-B/2-U:-h.height/2-v/2-B/2-U})`),A.attr("style",`color: ${b.get("stroke")??y};`)}return f.attr("transform",`translate(${-h.width/2-(h.x-(h.left??0))},${p?-L/2:L/2-h.height})`),R.attr("transform",`translate(0,${p?h.height/2+v/2:-h.height/2-v/2})`),V(t,E),t.intersect=function(A){if(O.info("iconSquare intersect",t,A),!t.label)return H.rect(t,A);const _=t.x??0,F=t.y??0,B=t.height??0;let W=[];return p?W=[{x:_-h.width/2,y:F-B/2},{x:_+h.width/2,y:F-B/2},{x:_+h.width/2,y:F-B/2+h.height+v},{x:_+m/2,y:F-B/2+h.height+v},{x:_+m/2,y:F+B/2},{x:_-m/2,y:F+B/2},{x:_-m/2,y:F-B/2+h.height+v},{x:_-h.width/2,y:F-B/2+h.height+v}]:W=[{x:_-m/2,y:F-B/2},{x:_+m/2,y:F-B/2},{x:_+m/2,y:F-B/2+g},{x:_+h.width/2,y:F-B/2+g},{x:_+h.width/2/2,y:F+B/2},{x:_-h.width/2,y:F+B/2},{x:_-h.width/2,y:F-B/2+g},{x:_-m/2,y:F-B/2+g}],H.polygon(t,W,A)},c}d(Nf,"iconSquare");async function zf(e,t,{config:{flowchart:r}}){const i=new Image;i.src=(t==null?void 0:t.img)??"",await i.decode();const a=Number(i.naturalWidth.toString().replace("px","")),n=Number(i.naturalHeight.toString().replace("px",""));t.imageAspectRatio=a/n;const{labelStyles:o}=X(t);t.labelStyle=o;const s=r==null?void 0:r.wrappingWidth;t.defaultWidth=r==null?void 0:r.wrappingWidth;const l=Math.max(t.label?s??0:0,(t==null?void 0:t.assetWidth)??a),c=t.constraint==="on"&&t!=null&&t.assetHeight?t.assetHeight*t.imageAspectRatio:l,h=t.constraint==="on"?c/t.imageAspectRatio:(t==null?void 0:t.assetHeight)??n;t.width=Math.max(c,s??0);const{shapeSvg:u,bbox:f,label:p}=await st(e,t,"image-shape default"),g=t.pos==="t",m=-c/2,y=-h/2,x=t.label?8:0,b=j.svg(u),C=G(t,{});t.look!=="handDrawn"&&(C.roughness=0,C.fillStyle="solid");const S=b.rectangle(m,y,c,h,C),v=Math.max(c,f.width),M=h+f.height+x,T=b.rectangle(-v/2,-M/2,v,M,{...C,fill:"none",stroke:"none"}),D=u.insert(()=>S,":first-child"),P=u.insert(()=>T);if(t.img){const $=u.append("image");$.attr("href",t.img),$.attr("width",c),$.attr("height",h),$.attr("preserveAspectRatio","none"),$.attr("transform",`translate(${-c/2},${g?M/2-h:-M/2})`)}return p.attr("transform",`translate(${-f.width/2-(f.x-(f.left??0))},${g?-h/2-f.height/2-x/2:h/2-f.height/2+x/2})`),D.attr("transform",`translate(0,${g?f.height/2+x/2:-f.height/2-x/2})`),V(t,P),t.intersect=function($){if(O.info("iconSquare intersect",t,$),!t.label)return H.rect(t,$);const L=t.x??0,z=t.y??0,R=t.height??0;let E=[];return g?E=[{x:L-f.width/2,y:z-R/2},{x:L+f.width/2,y:z-R/2},{x:L+f.width/2,y:z-R/2+f.height+x},{x:L+c/2,y:z-R/2+f.height+x},{x:L+c/2,y:z+R/2},{x:L-c/2,y:z+R/2},{x:L-c/2,y:z-R/2+f.height+x},{x:L-f.width/2,y:z-R/2+f.height+x}]:E=[{x:L-c/2,y:z-R/2},{x:L+c/2,y:z-R/2},{x:L+c/2,y:z-R/2+h},{x:L+f.width/2,y:z-R/2+h},{x:L+f.width/2/2,y:z+R/2},{x:L-f.width/2,y:z+R/2},{x:L-f.width/2,y:z-R/2+h},{x:L-c/2,y:z-R/2+h}],H.polygon(t,E,$)},u}d(zf,"imageSquare");async function Wf(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:a,bbox:n}=await st(e,t,it(t)),o=Math.max(n.width+(t.padding??0)*2,(t==null?void 0:t.width)??0),s=Math.max(n.height+(t.padding??0)*2,(t==null?void 0:t.height)??0),l=[{x:0,y:0},{x:o,y:0},{x:o+3*s/6,y:-s},{x:-3*s/6,y:-s}];let c;const{cssStyles:h}=t;if(t.look==="handDrawn"){const u=j.svg(a),f=G(t,{}),p=ct(l),g=u.path(p,f);c=a.insert(()=>g,":first-child").attr("transform",`translate(${-o/2}, ${s/2})`),h&&c.attr("style",h)}else c=Oe(a,o,s,l);return i&&c.attr("style",i),t.width=o,t.height=s,V(t,c),t.intersect=function(u){return H.polygon(t,l,u)},a}d(Wf,"inv_trapezoid");async function $i(e,t,r){const{labelStyles:i,nodeStyles:a}=X(t);t.labelStyle=i;const{shapeSvg:n,bbox:o}=await st(e,t,it(t)),s=Math.max(o.width+r.labelPaddingX*2,(t==null?void 0:t.width)||0),l=Math.max(o.height+r.labelPaddingY*2,(t==null?void 0:t.height)||0),c=-s/2,h=-l/2;let u,{rx:f,ry:p}=t;const{cssStyles:g}=t;if(r!=null&&r.rx&&r.ry&&(f=r.rx,p=r.ry),t.look==="handDrawn"){const m=j.svg(n),y=G(t,{}),x=f||p?m.path($e(c,h,s,l,f||0),y):m.rectangle(c,h,s,l,y);u=n.insert(()=>x,":first-child"),u.attr("class","basic label-container").attr("style",qt(g))}else u=n.insert("rect",":first-child"),u.attr("class","basic label-container").attr("style",a).attr("rx",qt(f)).attr("ry",qt(p)).attr("x",c).attr("y",h).attr("width",s).attr("height",l);return V(t,u),t.intersect=function(m){return H.rect(t,m)},n}d($i,"drawRect");async function qf(e,t){const{shapeSvg:r,bbox:i,label:a}=await st(e,t,"label"),n=r.insert("rect",":first-child");return n.attr("width",.1).attr("height",.1),r.attr("class","label edgeLabel"),a.attr("transform",`translate(${-(i.width/2)-(i.x-(i.left??0))}, ${-(i.height/2)-(i.y-(i.top??0))})`),V(t,n),t.intersect=function(l){return H.rect(t,l)},r}d(qf,"labelRect");async function Hf(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:a,bbox:n}=await st(e,t,it(t)),o=Math.max(n.width+(t.padding??0),(t==null?void 0:t.width)??0),s=Math.max(n.height+(t.padding??0),(t==null?void 0:t.height)??0),l=[{x:0,y:0},{x:o+3*s/6,y:0},{x:o,y:-s},{x:-(3*s)/6,y:-s}];let c;const{cssStyles:h}=t;if(t.look==="handDrawn"){const u=j.svg(a),f=G(t,{}),p=ct(l),g=u.path(p,f);c=a.insert(()=>g,":first-child").attr("transform",`translate(${-o/2}, ${s/2})`),h&&c.attr("style",h)}else c=Oe(a,o,s,l);return i&&c.attr("style",i),t.width=o,t.height=s,V(t,c),t.intersect=function(u){return H.polygon(t,l,u)},a}d(Hf,"lean_left");async function jf(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:a,bbox:n}=await st(e,t,it(t)),o=Math.max(n.width+(t.padding??0),(t==null?void 0:t.width)??0),s=Math.max(n.height+(t.padding??0),(t==null?void 0:t.height)??0),l=[{x:-3*s/6,y:0},{x:o,y:0},{x:o+3*s/6,y:-s},{x:0,y:-s}];let c;const{cssStyles:h}=t;if(t.look==="handDrawn"){const u=j.svg(a),f=G(t,{}),p=ct(l),g=u.path(p,f);c=a.insert(()=>g,":first-child").attr("transform",`translate(${-o/2}, ${s/2})`),h&&c.attr("style",h)}else c=Oe(a,o,s,l);return i&&c.attr("style",i),t.width=o,t.height=s,V(t,c),t.intersect=function(u){return H.polygon(t,l,u)},a}d(jf,"lean_right");function Uf(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.label="",t.labelStyle=r;const a=e.insert("g").attr("class",it(t)).attr("id",t.domId??t.id),{cssStyles:n}=t,o=Math.max(35,(t==null?void 0:t.width)??0),s=Math.max(35,(t==null?void 0:t.height)??0),l=7,c=[{x:o,y:0},{x:0,y:s+l/2},{x:o-2*l,y:s+l/2},{x:0,y:2*s},{x:o,y:s-l/2},{x:2*l,y:s-l/2}],h=j.svg(a),u=G(t,{});t.look!=="handDrawn"&&(u.roughness=0,u.fillStyle="solid");const f=ct(c),p=h.path(f,u),g=a.insert(()=>p,":first-child");return n&&t.look!=="handDrawn"&&g.selectAll("path").attr("style",n),i&&t.look!=="handDrawn"&&g.selectAll("path").attr("style",i),g.attr("transform",`translate(-${o/2},${-s})`),V(t,g),t.intersect=function(m){return O.info("lightningBolt intersect",t,m),H.polygon(t,c,m)},a}d(Uf,"lightningBolt");var L2=d((e,t,r,i,a,n,o)=>[`M${e},${t+n}`,`a${a},${n} 0,0,0 ${r},0`,`a${a},${n} 0,0,0 ${-r},0`,`l0,${i}`,`a${a},${n} 0,0,0 ${r},0`,`l0,${-i}`,`M${e},${t+n+o}`,`a${a},${n} 0,0,0 ${r},0`].join(" "),"createCylinderPathD"),A2=d((e,t,r,i,a,n,o)=>[`M${e},${t+n}`,`M${e+r},${t+n}`,`a${a},${n} 0,0,0 ${-r},0`,`l0,${i}`,`a${a},${n} 0,0,0 ${r},0`,`l0,${-i}`,`M${e},${t+n+o}`,`a${a},${n} 0,0,0 ${r},0`].join(" "),"createOuterCylinderPathD"),M2=d((e,t,r,i,a,n)=>[`M${e-r/2},${-i/2}`,`a${a},${n} 0,0,0 ${r},0`].join(" "),"createInnerCylinderPathD");async function Yf(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:a,bbox:n,label:o}=await st(e,t,it(t)),s=Math.max(n.width+(t.padding??0),t.width??0),l=s/2,c=l/(2.5+s/50),h=Math.max(n.height+c+(t.padding??0),t.height??0),u=h*.1;let f;const{cssStyles:p}=t;if(t.look==="handDrawn"){const g=j.svg(a),m=A2(0,0,s,h,l,c,u),y=M2(0,c,s,h,l,c),x=G(t,{}),b=g.path(m,x),C=g.path(y,x);a.insert(()=>C,":first-child").attr("class","line"),f=a.insert(()=>b,":first-child"),f.attr("class","basic label-container"),p&&f.attr("style",p)}else{const g=L2(0,0,s,h,l,c,u);f=a.insert("path",":first-child").attr("d",g).attr("class","basic label-container").attr("style",qt(p)).attr("style",i)}return f.attr("label-offset-y",c),f.attr("transform",`translate(${-s/2}, ${-(h/2+c)})`),V(t,f),o.attr("transform",`translate(${-(n.width/2)-(n.x-(n.left??0))}, ${-(n.height/2)+c-(n.y-(n.top??0))})`),t.intersect=function(g){const m=H.rect(t,g),y=m.x-(t.x??0);if(l!=0&&(Math.abs(y)<(t.width??0)/2||Math.abs(y)==(t.width??0)/2&&Math.abs(m.y-(t.y??0))>(t.height??0)/2-c)){let x=c*c*(1-y*y/(l*l));x>0&&(x=Math.sqrt(x)),x=c-x,g.y-(t.y??0)>0&&(x=-x),m.y+=x}return m},a}d(Yf,"linedCylinder");async function Gf(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:a,bbox:n,label:o}=await st(e,t,it(t)),s=Math.max(n.width+(t.padding??0)*2,(t==null?void 0:t.width)??0),l=Math.max(n.height+(t.padding??0)*2,(t==null?void 0:t.height)??0),c=l/4,h=l+c,{cssStyles:u}=t,f=j.svg(a),p=G(t,{});t.look!=="handDrawn"&&(p.roughness=0,p.fillStyle="solid");const g=[{x:-s/2-s/2*.1,y:-h/2},{x:-s/2-s/2*.1,y:h/2},...qe(-s/2-s/2*.1,h/2,s/2+s/2*.1,h/2,c,.8),{x:s/2+s/2*.1,y:-h/2},{x:-s/2-s/2*.1,y:-h/2},{x:-s/2,y:-h/2},{x:-s/2,y:h/2*1.1},{x:-s/2,y:-h/2}],m=f.polygon(g.map(x=>[x.x,x.y]),p),y=a.insert(()=>m,":first-child");return y.attr("class","basic label-container"),u&&t.look!=="handDrawn"&&y.selectAll("path").attr("style",u),i&&t.look!=="handDrawn"&&y.selectAll("path").attr("style",i),y.attr("transform",`translate(0,${-c/2})`),o.attr("transform",`translate(${-s/2+(t.padding??0)+s/2*.1/2-(n.x-(n.left??0))},${-l/2+(t.padding??0)-c/2-(n.y-(n.top??0))})`),V(t,y),t.intersect=function(x){return H.polygon(t,g,x)},a}d(Gf,"linedWaveEdgedRect");async function Vf(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:a,bbox:n,label:o}=await st(e,t,it(t)),s=Math.max(n.width+(t.padding??0)*2,(t==null?void 0:t.width)??0),l=Math.max(n.height+(t.padding??0)*2,(t==null?void 0:t.height)??0),c=5,h=-s/2,u=-l/2,{cssStyles:f}=t,p=j.svg(a),g=G(t,{}),m=[{x:h-c,y:u+c},{x:h-c,y:u+l+c},{x:h+s-c,y:u+l+c},{x:h+s-c,y:u+l},{x:h+s,y:u+l},{x:h+s,y:u+l-c},{x:h+s+c,y:u+l-c},{x:h+s+c,y:u-c},{x:h+c,y:u-c},{x:h+c,y:u},{x:h,y:u},{x:h,y:u+c}],y=[{x:h,y:u+c},{x:h+s-c,y:u+c},{x:h+s-c,y:u+l},{x:h+s,y:u+l},{x:h+s,y:u},{x:h,y:u}];t.look!=="handDrawn"&&(g.roughness=0,g.fillStyle="solid");const x=ct(m),b=p.path(x,g),C=ct(y),S=p.path(C,{...g,fill:"none"}),v=a.insert(()=>S,":first-child");return v.insert(()=>b,":first-child"),v.attr("class","basic label-container"),f&&t.look!=="handDrawn"&&v.selectAll("path").attr("style",f),i&&t.look!=="handDrawn"&&v.selectAll("path").attr("style",i),o.attr("transform",`translate(${-(n.width/2)-c-(n.x-(n.left??0))}, ${-(n.height/2)+c-(n.y-(n.top??0))})`),V(t,v),t.intersect=function(M){return H.polygon(t,m,M)},a}d(Vf,"multiRect");async function Xf(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:a,bbox:n,label:o}=await st(e,t,it(t)),s=Math.max(n.width+(t.padding??0)*2,(t==null?void 0:t.width)??0),l=Math.max(n.height+(t.padding??0)*2,(t==null?void 0:t.height)??0),c=l/4,h=l+c,u=-s/2,f=-h/2,p=5,{cssStyles:g}=t,m=qe(u-p,f+h+p,u+s-p,f+h+p,c,.8),y=m==null?void 0:m[m.length-1],x=[{x:u-p,y:f+p},{x:u-p,y:f+h+p},...m,{x:u+s-p,y:y.y-p},{x:u+s,y:y.y-p},{x:u+s,y:y.y-2*p},{x:u+s+p,y:y.y-2*p},{x:u+s+p,y:f-p},{x:u+p,y:f-p},{x:u+p,y:f},{x:u,y:f},{x:u,y:f+p}],b=[{x:u,y:f+p},{x:u+s-p,y:f+p},{x:u+s-p,y:y.y-p},{x:u+s,y:y.y-p},{x:u+s,y:f},{x:u,y:f}],C=j.svg(a),S=G(t,{});t.look!=="handDrawn"&&(S.roughness=0,S.fillStyle="solid");const v=ct(x),M=C.path(v,S),T=ct(b),D=C.path(T,S),P=a.insert(()=>M,":first-child");return P.insert(()=>D),P.attr("class","basic label-container"),g&&t.look!=="handDrawn"&&P.selectAll("path").attr("style",g),i&&t.look!=="handDrawn"&&P.selectAll("path").attr("style",i),P.attr("transform",`translate(0,${-c/2})`),o.attr("transform",`translate(${-(n.width/2)-p-(n.x-(n.left??0))}, ${-(n.height/2)+p-c/2-(n.y-(n.top??0))})`),V(t,P),t.intersect=function($){return H.polygon(t,x,$)},a}d(Xf,"multiWaveEdgedRectangle");async function Zf(e,t,{config:{themeVariables:r}}){var x;const{labelStyles:i,nodeStyles:a}=X(t);t.labelStyle=i,t.useHtmlLabels||((x=Xt().flowchart)==null?void 0:x.htmlLabels)!==!1||(t.centerLabel=!0);const{shapeSvg:o,bbox:s}=await st(e,t,it(t)),l=Math.max(s.width+(t.padding??0)*2,(t==null?void 0:t.width)??0),c=Math.max(s.height+(t.padding??0)*2,(t==null?void 0:t.height)??0),h=-l/2,u=-c/2,{cssStyles:f}=t,p=j.svg(o),g=G(t,{fill:r.noteBkgColor,stroke:r.noteBorderColor});t.look!=="handDrawn"&&(g.roughness=0,g.fillStyle="solid");const m=p.rectangle(h,u,l,c,g),y=o.insert(()=>m,":first-child");return y.attr("class","basic label-container"),f&&t.look!=="handDrawn"&&y.selectAll("path").attr("style",f),a&&t.look!=="handDrawn"&&y.selectAll("path").attr("style",a),V(t,y),t.intersect=function(b){return H.rect(t,b)},o}d(Zf,"note");var E2=d((e,t,r)=>[`M${e+r/2},${t}`,`L${e+r},${t-r/2}`,`L${e+r/2},${t-r}`,`L${e},${t-r/2}`,"Z"].join(" "),"createDecisionBoxPathD");async function Kf(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:a,bbox:n}=await st(e,t,it(t)),o=n.width+t.padding,s=n.height+t.padding,l=o+s,c=[{x:l/2,y:0},{x:l,y:-l/2},{x:l/2,y:-l},{x:0,y:-l/2}];let h;const{cssStyles:u}=t;if(t.look==="handDrawn"){const f=j.svg(a),p=G(t,{}),g=E2(0,0,l),m=f.path(g,p);h=a.insert(()=>m,":first-child").attr("transform",`translate(${-l/2}, ${l/2})`),u&&h.attr("style",u)}else h=Oe(a,l,l,c);return i&&h.attr("style",i),V(t,h),t.intersect=function(f){return O.debug(`APA12 Intersect called SPLIT
point:`,f,`
node:
`,t,`
res:`,H.polygon(t,c,f)),H.polygon(t,c,f)},a}d(Kf,"question");async function Qf(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:a,bbox:n,label:o}=await st(e,t,it(t)),s=Math.max(n.width+(t.padding??0),(t==null?void 0:t.width)??0),l=Math.max(n.height+(t.padding??0),(t==null?void 0:t.height)??0),c=-s/2,h=-l/2,u=h/2,f=[{x:c+u,y:h},{x:c,y:0},{x:c+u,y:-h},{x:-c,y:-h},{x:-c,y:h}],{cssStyles:p}=t,g=j.svg(a),m=G(t,{});t.look!=="handDrawn"&&(m.roughness=0,m.fillStyle="solid");const y=ct(f),x=g.path(y,m),b=a.insert(()=>x,":first-child");return b.attr("class","basic label-container"),p&&t.look!=="handDrawn"&&b.selectAll("path").attr("style",p),i&&t.look!=="handDrawn"&&b.selectAll("path").attr("style",i),b.attr("transform",`translate(${-u/2},0)`),o.attr("transform",`translate(${-u/2-n.width/2-(n.x-(n.left??0))}, ${-(n.height/2)-(n.y-(n.top??0))})`),V(t,b),t.intersect=function(C){return H.polygon(t,f,C)},a}d(Qf,"rect_left_inv_arrow");async function Jf(e,t){var D,P;const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;let a;t.cssClasses?a="node "+t.cssClasses:a="node default";const n=e.insert("g").attr("class",a).attr("id",t.domId||t.id),o=n.insert("g"),s=n.insert("g").attr("class","label").attr("style",i),l=t.description,c=t.label,h=s.node().appendChild(await Je(c,t.labelStyle,!0,!0));let u={width:0,height:0};if(vt((P=(D=ut())==null?void 0:D.flowchart)==null?void 0:P.htmlLabels)){const $=h.children[0],L=lt(h);u=$.getBoundingClientRect(),L.attr("width",u.width),L.attr("height",u.height)}O.info("Text 2",l);const f=l||[],p=h.getBBox(),g=s.node().appendChild(await Je(f.join?f.join("<br/>"):f,t.labelStyle,!0,!0)),m=g.children[0],y=lt(g);u=m.getBoundingClientRect(),y.attr("width",u.width),y.attr("height",u.height);const x=(t.padding||0)/2;lt(g).attr("transform","translate( "+(u.width>p.width?0:(p.width-u.width)/2)+", "+(p.height+x+5)+")"),lt(h).attr("transform","translate( "+(u.width<p.width?0:-(p.width-u.width)/2)+", 0)"),u=s.node().getBBox(),s.attr("transform","translate("+-u.width/2+", "+(-u.height/2-x+3)+")");const b=u.width+(t.padding||0),C=u.height+(t.padding||0),S=-u.width/2-x,v=-u.height/2-x;let M,T;if(t.look==="handDrawn"){const $=j.svg(n),L=G(t,{}),z=$.path($e(S,v,b,C,t.rx||0),L),R=$.line(-u.width/2-x,-u.height/2-x+p.height+x,u.width/2+x,-u.height/2-x+p.height+x,L);T=n.insert(()=>(O.debug("Rough node insert CXC",z),R),":first-child"),M=n.insert(()=>(O.debug("Rough node insert CXC",z),z),":first-child")}else M=o.insert("rect",":first-child"),T=o.insert("line"),M.attr("class","outer title-state").attr("style",i).attr("x",-u.width/2-x).attr("y",-u.height/2-x).attr("width",u.width+(t.padding||0)).attr("height",u.height+(t.padding||0)),T.attr("class","divider").attr("x1",-u.width/2-x).attr("x2",u.width/2+x).attr("y1",-u.height/2-x+p.height+x).attr("y2",-u.height/2-x+p.height+x);return V(t,M),t.intersect=function($){return H.rect(t,$)},n}d(Jf,"rectWithTitle");async function tp(e,t){const r={rx:5,ry:5,classes:"",labelPaddingX:((t==null?void 0:t.padding)||0)*1,labelPaddingY:((t==null?void 0:t.padding)||0)*1};return $i(e,t,r)}d(tp,"roundedRect");async function ep(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:a,bbox:n,label:o}=await st(e,t,it(t)),s=(t==null?void 0:t.padding)??0,l=Math.max(n.width+(t.padding??0)*2,(t==null?void 0:t.width)??0),c=Math.max(n.height+(t.padding??0)*2,(t==null?void 0:t.height)??0),h=-n.width/2-s,u=-n.height/2-s,{cssStyles:f}=t,p=j.svg(a),g=G(t,{});t.look!=="handDrawn"&&(g.roughness=0,g.fillStyle="solid");const m=[{x:h,y:u},{x:h+l+8,y:u},{x:h+l+8,y:u+c},{x:h-8,y:u+c},{x:h-8,y:u},{x:h,y:u},{x:h,y:u+c}],y=p.polygon(m.map(b=>[b.x,b.y]),g),x=a.insert(()=>y,":first-child");return x.attr("class","basic label-container").attr("style",qt(f)),i&&t.look!=="handDrawn"&&x.selectAll("path").attr("style",i),f&&t.look!=="handDrawn"&&x.selectAll("path").attr("style",i),o.attr("transform",`translate(${-l/2+4+(t.padding??0)-(n.x-(n.left??0))},${-c/2+(t.padding??0)-(n.y-(n.top??0))})`),V(t,x),t.intersect=function(b){return H.rect(t,b)},a}d(ep,"shadedProcess");async function rp(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:a,bbox:n,label:o}=await st(e,t,it(t)),s=Math.max(n.width+(t.padding??0)*2,(t==null?void 0:t.width)??0),l=Math.max(n.height+(t.padding??0)*2,(t==null?void 0:t.height)??0),c=-s/2,h=-l/2,{cssStyles:u}=t,f=j.svg(a),p=G(t,{});t.look!=="handDrawn"&&(p.roughness=0,p.fillStyle="solid");const g=[{x:c,y:h},{x:c,y:h+l},{x:c+s,y:h+l},{x:c+s,y:h-l/2}],m=ct(g),y=f.path(m,p),x=a.insert(()=>y,":first-child");return x.attr("class","basic label-container"),u&&t.look!=="handDrawn"&&x.selectChildren("path").attr("style",u),i&&t.look!=="handDrawn"&&x.selectChildren("path").attr("style",i),x.attr("transform",`translate(0, ${l/4})`),o.attr("transform",`translate(${-s/2+(t.padding??0)-(n.x-(n.left??0))}, ${-l/4+(t.padding??0)-(n.y-(n.top??0))})`),V(t,x),t.intersect=function(b){return H.polygon(t,g,b)},a}d(rp,"slopedRect");async function ip(e,t){const r={rx:0,ry:0,classes:"",labelPaddingX:((t==null?void 0:t.padding)||0)*2,labelPaddingY:((t==null?void 0:t.padding)||0)*1};return $i(e,t,r)}d(ip,"squareRect");async function ap(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:a,bbox:n}=await st(e,t,it(t)),o=n.height+t.padding,s=n.width+o/4+t.padding;let l;const{cssStyles:c}=t;if(t.look==="handDrawn"){const h=j.svg(a),u=G(t,{}),f=$e(-s/2,-o/2,s,o,o/2),p=h.path(f,u);l=a.insert(()=>p,":first-child"),l.attr("class","basic label-container").attr("style",qt(c))}else l=a.insert("rect",":first-child"),l.attr("class","basic label-container").attr("style",i).attr("rx",o/2).attr("ry",o/2).attr("x",-s/2).attr("y",-o/2).attr("width",s).attr("height",o);return V(t,l),t.intersect=function(h){return H.rect(t,h)},a}d(ap,"stadium");async function np(e,t){return $i(e,t,{rx:5,ry:5,classes:"flowchart-node"})}d(np,"state");function sp(e,t,{config:{themeVariables:r}}){const{labelStyles:i,nodeStyles:a}=X(t);t.labelStyle=i;const{cssStyles:n}=t,{lineColor:o,stateBorder:s,nodeBorder:l}=r,c=e.insert("g").attr("class","node default").attr("id",t.domId||t.id),h=j.svg(c),u=G(t,{});t.look!=="handDrawn"&&(u.roughness=0,u.fillStyle="solid");const f=h.circle(0,0,14,{...u,stroke:o,strokeWidth:2}),p=s??l,g=h.circle(0,0,5,{...u,fill:p,stroke:p,strokeWidth:2,fillStyle:"solid"}),m=c.insert(()=>f,":first-child");return m.insert(()=>g),n&&m.selectAll("path").attr("style",n),a&&m.selectAll("path").attr("style",a),V(t,m),t.intersect=function(y){return H.circle(t,7,y)},c}d(sp,"stateEnd");function op(e,t,{config:{themeVariables:r}}){const{lineColor:i}=r,a=e.insert("g").attr("class","node default").attr("id",t.domId||t.id);let n;if(t.look==="handDrawn"){const s=j.svg(a).circle(0,0,14,u2(i));n=a.insert(()=>s),n.attr("class","state-start").attr("r",7).attr("width",14).attr("height",14)}else n=a.insert("circle",":first-child"),n.attr("class","state-start").attr("r",7).attr("width",14).attr("height",14);return V(t,n),t.intersect=function(o){return H.circle(t,7,o)},a}d(op,"stateStart");async function lp(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:a,bbox:n}=await st(e,t,it(t)),o=((t==null?void 0:t.padding)||0)/2,s=n.width+t.padding,l=n.height+t.padding,c=-n.width/2-o,h=-n.height/2-o,u=[{x:0,y:0},{x:s,y:0},{x:s,y:-l},{x:0,y:-l},{x:0,y:0},{x:-8,y:0},{x:s+8,y:0},{x:s+8,y:-l},{x:-8,y:-l},{x:-8,y:0}];if(t.look==="handDrawn"){const f=j.svg(a),p=G(t,{}),g=f.rectangle(c-8,h,s+16,l,p),m=f.line(c,h,c,h+l,p),y=f.line(c+s,h,c+s,h+l,p);a.insert(()=>m,":first-child"),a.insert(()=>y,":first-child");const x=a.insert(()=>g,":first-child"),{cssStyles:b}=t;x.attr("class","basic label-container").attr("style",qt(b)),V(t,x)}else{const f=Oe(a,s,l,u);i&&f.attr("style",i),V(t,f)}return t.intersect=function(f){return H.polygon(t,u,f)},a}d(lp,"subroutine");async function cp(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:a,bbox:n}=await st(e,t,it(t)),o=Math.max(n.width+(t.padding??0)*2,(t==null?void 0:t.width)??0),s=Math.max(n.height+(t.padding??0)*2,(t==null?void 0:t.height)??0),l=-o/2,c=-s/2,h=.2*s,u=.2*s,{cssStyles:f}=t,p=j.svg(a),g=G(t,{}),m=[{x:l-h/2,y:c},{x:l+o+h/2,y:c},{x:l+o+h/2,y:c+s},{x:l-h/2,y:c+s}],y=[{x:l+o-h/2,y:c+s},{x:l+o+h/2,y:c+s},{x:l+o+h/2,y:c+s-u}];t.look!=="handDrawn"&&(g.roughness=0,g.fillStyle="solid");const x=ct(m),b=p.path(x,g),C=ct(y),S=p.path(C,{...g,fillStyle:"solid"}),v=a.insert(()=>S,":first-child");return v.insert(()=>b,":first-child"),v.attr("class","basic label-container"),f&&t.look!=="handDrawn"&&v.selectAll("path").attr("style",f),i&&t.look!=="handDrawn"&&v.selectAll("path").attr("style",i),V(t,v),t.intersect=function(M){return H.polygon(t,m,M)},a}d(cp,"taggedRect");async function hp(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:a,bbox:n,label:o}=await st(e,t,it(t)),s=Math.max(n.width+(t.padding??0)*2,(t==null?void 0:t.width)??0),l=Math.max(n.height+(t.padding??0)*2,(t==null?void 0:t.height)??0),c=l/4,h=.2*s,u=.2*l,f=l+c,{cssStyles:p}=t,g=j.svg(a),m=G(t,{});t.look!=="handDrawn"&&(m.roughness=0,m.fillStyle="solid");const y=[{x:-s/2-s/2*.1,y:f/2},...qe(-s/2-s/2*.1,f/2,s/2+s/2*.1,f/2,c,.8),{x:s/2+s/2*.1,y:-f/2},{x:-s/2-s/2*.1,y:-f/2}],x=-s/2+s/2*.1,b=-f/2-u*.4,C=[{x:x+s-h,y:(b+l)*1.4},{x:x+s,y:b+l-u},{x:x+s,y:(b+l)*.9},...qe(x+s,(b+l)*1.3,x+s-h,(b+l)*1.5,-l*.03,.5)],S=ct(y),v=g.path(S,m),M=ct(C),T=g.path(M,{...m,fillStyle:"solid"}),D=a.insert(()=>T,":first-child");return D.insert(()=>v,":first-child"),D.attr("class","basic label-container"),p&&t.look!=="handDrawn"&&D.selectAll("path").attr("style",p),i&&t.look!=="handDrawn"&&D.selectAll("path").attr("style",i),D.attr("transform",`translate(0,${-c/2})`),o.attr("transform",`translate(${-s/2+(t.padding??0)-(n.x-(n.left??0))},${-l/2+(t.padding??0)-c/2-(n.y-(n.top??0))})`),V(t,D),t.intersect=function(P){return H.polygon(t,y,P)},a}d(hp,"taggedWaveEdgedRectangle");async function up(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:a,bbox:n}=await st(e,t,it(t)),o=Math.max(n.width+t.padding,(t==null?void 0:t.width)||0),s=Math.max(n.height+t.padding,(t==null?void 0:t.height)||0),l=-o/2,c=-s/2,h=a.insert("rect",":first-child");return h.attr("class","text").attr("style",i).attr("rx",0).attr("ry",0).attr("x",l).attr("y",c).attr("width",o).attr("height",s),V(t,h),t.intersect=function(u){return H.rect(t,u)},a}d(up,"text");var F2=d((e,t,r,i,a,n)=>`M${e},${t}
    a${a},${n} 0,0,1 0,${-i}
    l${r},0
    a${a},${n} 0,0,1 0,${i}
    M${r},${-i}
    a${a},${n} 0,0,0 0,${i}
    l${-r},0`,"createCylinderPathD"),$2=d((e,t,r,i,a,n)=>[`M${e},${t}`,`M${e+r},${t}`,`a${a},${n} 0,0,0 0,${-i}`,`l${-r},0`,`a${a},${n} 0,0,0 0,${i}`,`l${r},0`].join(" "),"createOuterCylinderPathD"),O2=d((e,t,r,i,a,n)=>[`M${e+r/2},${-i/2}`,`a${a},${n} 0,0,0 0,${i}`].join(" "),"createInnerCylinderPathD");async function fp(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:a,bbox:n,label:o,halfPadding:s}=await st(e,t,it(t)),l=t.look==="neo"?s*2:s,c=n.height+l,h=c/2,u=h/(2.5+c/50),f=n.width+u+l,{cssStyles:p}=t;let g;if(t.look==="handDrawn"){const m=j.svg(a),y=$2(0,0,f,c,u,h),x=O2(0,0,f,c,u,h),b=m.path(y,G(t,{})),C=m.path(x,G(t,{fill:"none"}));g=a.insert(()=>C,":first-child"),g=a.insert(()=>b,":first-child"),g.attr("class","basic label-container"),p&&g.attr("style",p)}else{const m=F2(0,0,f,c,u,h);g=a.insert("path",":first-child").attr("d",m).attr("class","basic label-container").attr("style",qt(p)).attr("style",i),g.attr("class","basic label-container"),p&&g.selectAll("path").attr("style",p),i&&g.selectAll("path").attr("style",i)}return g.attr("label-offset-x",u),g.attr("transform",`translate(${-f/2}, ${c/2} )`),o.attr("transform",`translate(${-(n.width/2)-u-(n.x-(n.left??0))}, ${-(n.height/2)-(n.y-(n.top??0))})`),V(t,g),t.intersect=function(m){const y=H.rect(t,m),x=y.y-(t.y??0);if(h!=0&&(Math.abs(x)<(t.height??0)/2||Math.abs(x)==(t.height??0)/2&&Math.abs(y.x-(t.x??0))>(t.width??0)/2-u)){let b=u*u*(1-x*x/(h*h));b!=0&&(b=Math.sqrt(Math.abs(b))),b=u-b,m.x-(t.x??0)>0&&(b=-b),y.x+=b}return y},a}d(fp,"tiltedCylinder");async function pp(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:a,bbox:n}=await st(e,t,it(t)),o=n.width+t.padding,s=n.height+t.padding,l=[{x:-3*s/6,y:0},{x:o+3*s/6,y:0},{x:o,y:-s},{x:0,y:-s}];let c;const{cssStyles:h}=t;if(t.look==="handDrawn"){const u=j.svg(a),f=G(t,{}),p=ct(l),g=u.path(p,f);c=a.insert(()=>g,":first-child").attr("transform",`translate(${-o/2}, ${s/2})`),h&&c.attr("style",h)}else c=Oe(a,o,s,l);return i&&c.attr("style",i),t.width=o,t.height=s,V(t,c),t.intersect=function(u){return H.polygon(t,l,u)},a}d(pp,"trapezoid");async function dp(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:a,bbox:n}=await st(e,t,it(t)),o=60,s=20,l=Math.max(o,n.width+(t.padding??0)*2,(t==null?void 0:t.width)??0),c=Math.max(s,n.height+(t.padding??0)*2,(t==null?void 0:t.height)??0),{cssStyles:h}=t,u=j.svg(a),f=G(t,{});t.look!=="handDrawn"&&(f.roughness=0,f.fillStyle="solid");const p=[{x:-l/2*.8,y:-c/2},{x:l/2*.8,y:-c/2},{x:l/2,y:-c/2*.6},{x:l/2,y:c/2},{x:-l/2,y:c/2},{x:-l/2,y:-c/2*.6}],g=ct(p),m=u.path(g,f),y=a.insert(()=>m,":first-child");return y.attr("class","basic label-container"),h&&t.look!=="handDrawn"&&y.selectChildren("path").attr("style",h),i&&t.look!=="handDrawn"&&y.selectChildren("path").attr("style",i),V(t,y),t.intersect=function(x){return H.polygon(t,p,x)},a}d(dp,"trapezoidalPentagon");async function gp(e,t){var b;const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:a,bbox:n,label:o}=await st(e,t,it(t)),s=vt((b=ut().flowchart)==null?void 0:b.htmlLabels),l=n.width+(t.padding??0),c=l+n.height,h=l+n.height,u=[{x:0,y:0},{x:h,y:0},{x:h/2,y:-c}],{cssStyles:f}=t,p=j.svg(a),g=G(t,{});t.look!=="handDrawn"&&(g.roughness=0,g.fillStyle="solid");const m=ct(u),y=p.path(m,g),x=a.insert(()=>y,":first-child").attr("transform",`translate(${-c/2}, ${c/2})`);return f&&t.look!=="handDrawn"&&x.selectChildren("path").attr("style",f),i&&t.look!=="handDrawn"&&x.selectChildren("path").attr("style",i),t.width=l,t.height=c,V(t,x),o.attr("transform",`translate(${-n.width/2-(n.x-(n.left??0))}, ${c/2-(n.height+(t.padding??0)/(s?2:1)-(n.y-(n.top??0)))})`),t.intersect=function(C){return O.info("Triangle intersect",t,u,C),H.polygon(t,u,C)},a}d(gp,"triangle");async function mp(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:a,bbox:n,label:o}=await st(e,t,it(t)),s=Math.max(n.width+(t.padding??0)*2,(t==null?void 0:t.width)??0),l=Math.max(n.height+(t.padding??0)*2,(t==null?void 0:t.height)??0),c=l/8,h=l+c,{cssStyles:u}=t,p=70-s,g=p>0?p/2:0,m=j.svg(a),y=G(t,{});t.look!=="handDrawn"&&(y.roughness=0,y.fillStyle="solid");const x=[{x:-s/2-g,y:h/2},...qe(-s/2-g,h/2,s/2+g,h/2,c,.8),{x:s/2+g,y:-h/2},{x:-s/2-g,y:-h/2}],b=ct(x),C=m.path(b,y),S=a.insert(()=>C,":first-child");return S.attr("class","basic label-container"),u&&t.look!=="handDrawn"&&S.selectAll("path").attr("style",u),i&&t.look!=="handDrawn"&&S.selectAll("path").attr("style",i),S.attr("transform",`translate(0,${-c/2})`),o.attr("transform",`translate(${-s/2+(t.padding??0)-(n.x-(n.left??0))},${-l/2+(t.padding??0)-c-(n.y-(n.top??0))})`),V(t,S),t.intersect=function(v){return H.polygon(t,x,v)},a}d(mp,"waveEdgedRectangle");async function yp(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:a,bbox:n}=await st(e,t,it(t)),o=100,s=50,l=Math.max(n.width+(t.padding??0)*2,(t==null?void 0:t.width)??0),c=Math.max(n.height+(t.padding??0)*2,(t==null?void 0:t.height)??0),h=l/c;let u=l,f=c;u>f*h?f=u/h:u=f*h,u=Math.max(u,o),f=Math.max(f,s);const p=Math.min(f*.2,f/4),g=f+p*2,{cssStyles:m}=t,y=j.svg(a),x=G(t,{});t.look!=="handDrawn"&&(x.roughness=0,x.fillStyle="solid");const b=[{x:-u/2,y:g/2},...qe(-u/2,g/2,u/2,g/2,p,1),{x:u/2,y:-g/2},...qe(u/2,-g/2,-u/2,-g/2,p,-1)],C=ct(b),S=y.path(C,x),v=a.insert(()=>S,":first-child");return v.attr("class","basic label-container"),m&&t.look!=="handDrawn"&&v.selectAll("path").attr("style",m),i&&t.look!=="handDrawn"&&v.selectAll("path").attr("style",i),V(t,v),t.intersect=function(M){return H.polygon(t,b,M)},a}d(yp,"waveRectangle");async function xp(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:a,bbox:n,label:o}=await st(e,t,it(t)),s=Math.max(n.width+(t.padding??0)*2,(t==null?void 0:t.width)??0),l=Math.max(n.height+(t.padding??0)*2,(t==null?void 0:t.height)??0),c=5,h=-s/2,u=-l/2,{cssStyles:f}=t,p=j.svg(a),g=G(t,{}),m=[{x:h-c,y:u-c},{x:h-c,y:u+l},{x:h+s,y:u+l},{x:h+s,y:u-c}],y=`M${h-c},${u-c} L${h+s},${u-c} L${h+s},${u+l} L${h-c},${u+l} L${h-c},${u-c}
                M${h-c},${u} L${h+s},${u}
                M${h},${u-c} L${h},${u+l}`;t.look!=="handDrawn"&&(g.roughness=0,g.fillStyle="solid");const x=p.path(y,g),b=a.insert(()=>x,":first-child");return b.attr("transform",`translate(${c/2}, ${c/2})`),b.attr("class","basic label-container"),f&&t.look!=="handDrawn"&&b.selectAll("path").attr("style",f),i&&t.look!=="handDrawn"&&b.selectAll("path").attr("style",i),o.attr("transform",`translate(${-(n.width/2)+c/2-(n.x-(n.left??0))}, ${-(n.height/2)+c/2-(n.y-(n.top??0))})`),V(t,b),t.intersect=function(C){return H.polygon(t,m,C)},a}d(xp,"windowPane");async function no(e,t){var W,U,J;const r=t;if(r.alias&&(t.label=r.alias),t.look==="handDrawn"){const{themeVariables:tt}=Xt(),{background:et}=tt,ht={...t,id:t.id+"-background",look:"default",cssStyles:["stroke: none",`fill: ${et}`]};await no(e,ht)}const i=Xt();t.useHtmlLabels=i.htmlLabels;let a=((W=i.er)==null?void 0:W.diagramPadding)??10,n=((U=i.er)==null?void 0:U.entityPadding)??6;const{cssStyles:o}=t,{labelStyles:s}=X(t);if(r.attributes.length===0&&t.label){const tt={rx:0,ry:0,labelPaddingX:a,labelPaddingY:a*1.5,classes:""};Ee(t.label,i)+tt.labelPaddingX*2<i.er.minEntityWidth&&(t.width=i.er.minEntityWidth);const et=await $i(e,t,tt);if(!vt(i.htmlLabels)){const ht=et.select("text"),at=(J=ht.node())==null?void 0:J.getBBox();ht.attr("transform",`translate(${-at.width/2}, 0)`)}return et}i.htmlLabels||(a*=1.25,n*=1.25);let l=it(t);l||(l="node default");const c=e.insert("g").attr("class",l).attr("id",t.domId||t.id),h=await yr(c,t.label??"",i,0,0,["name"],s);h.height+=n;let u=0;const f=[];let p=0,g=0,m=0,y=0,x=!0,b=!0;for(const tt of r.attributes){const et=await yr(c,tt.type,i,0,u,["attribute-type"],s);p=Math.max(p,et.width+a);const ht=await yr(c,tt.name,i,0,u,["attribute-name"],s);g=Math.max(g,ht.width+a);const at=await yr(c,tt.keys.join(),i,0,u,["attribute-keys"],s);m=Math.max(m,at.width+a);const St=await yr(c,tt.comment,i,0,u,["attribute-comment"],s);y=Math.max(y,St.width+a),u+=Math.max(et.height,ht.height,at.height,St.height)+n,f.push(u)}f.pop();let C=4;m<=a&&(x=!1,m=0,C--),y<=a&&(b=!1,y=0,C--);const S=c.node().getBBox();if(h.width+a*2-(p+g+m+y)>0){const tt=h.width+a*2-(p+g+m+y);p+=tt/C,g+=tt/C,m>0&&(m+=tt/C),y>0&&(y+=tt/C)}const v=p+g+m+y,M=j.svg(c),T=G(t,{});t.look!=="handDrawn"&&(T.roughness=0,T.fillStyle="solid");const D=Math.max(S.width+a*2,(t==null?void 0:t.width)||0,v),P=Math.max(S.height+(f[0]||u)+n,(t==null?void 0:t.height)||0),$=-D/2,L=-P/2;c.selectAll("g:not(:first-child)").each((tt,et,ht)=>{const at=lt(ht[et]),St=at.attr("transform");let Bt=0,se=0;if(St){const bt=RegExp(/translate\(([^,]+),([^)]+)\)/).exec(St);bt&&(Bt=parseFloat(bt[1]),se=parseFloat(bt[2]),at.attr("class").includes("attribute-name")?Bt+=p:at.attr("class").includes("attribute-keys")?Bt+=p+g:at.attr("class").includes("attribute-comment")&&(Bt+=p+g+m))}at.attr("transform",`translate(${$+a/2+Bt}, ${se+L+h.height+n/2})`)}),c.select(".name").attr("transform","translate("+-h.width/2+", "+(L+n/2)+")");const z=M.rectangle($,L,D,P,T),R=c.insert(()=>z,":first-child").attr("style",o.join("")),{themeVariables:E}=Xt(),{rowEven:A,rowOdd:_,nodeBorder:F}=E;f.push(0);for(const[tt,et]of f.entries()){if(tt===0&&f.length>1)continue;const ht=tt%2===0&&et!==0,at=M.rectangle($,h.height+L+et,D,h.height,{...T,fill:ht?A:_,stroke:F});c.insert(()=>at,"g.label").attr("style",o.join("")).attr("class",`row-rect-${tt%2===0?"even":"odd"}`)}let B=M.line($,h.height+L,D+$,h.height+L,T);c.insert(()=>B).attr("class","divider"),B=M.line(p+$,h.height+L,p+$,P+L,T),c.insert(()=>B).attr("class","divider"),x&&(B=M.line(p+g+$,h.height+L,p+g+$,P+L,T),c.insert(()=>B).attr("class","divider")),b&&(B=M.line(p+g+m+$,h.height+L,p+g+m+$,P+L,T),c.insert(()=>B).attr("class","divider"));for(const tt of f)B=M.line($,h.height+L+tt,D+$,h.height+L+tt,T),c.insert(()=>B).attr("class","divider");return V(t,R),t.intersect=function(tt){return H.rect(t,tt)},c}d(no,"erBox");async function yr(e,t,r,i=0,a=0,n=[],o=""){const s=e.insert("g").attr("class",`label ${n.join(" ")}`).attr("transform",`translate(${i}, ${a})`).attr("style",o);t!==qo(t)&&(t=qo(t),t=t.replaceAll("<","&lt;").replaceAll(">","&gt;"));const l=s.node().appendChild(await je(s,t,{width:Ee(t,r)+100,style:o,useHtmlLabels:r.htmlLabels},r));if(t.includes("&lt;")||t.includes("&gt;")){let h=l.children[0];for(h.textContent=h.textContent.replaceAll("&lt;","<").replaceAll("&gt;",">");h.childNodes[0];)h=h.childNodes[0],h.textContent=h.textContent.replaceAll("&lt;","<").replaceAll("&gt;",">")}let c=l.getBBox();if(vt(r.htmlLabels)){const h=l.children[0];h.style.textAlign="start";const u=lt(l);c=h.getBoundingClientRect(),u.attr("width",c.width),u.attr("height",c.height)}return c}d(yr,"addText");async function bp(e,t,r,i,a=r.class.padding??12){const n=i?0:3,o=e.insert("g").attr("class",it(t)).attr("id",t.domId||t.id);let s=null,l=null,c=null,h=null,u=0,f=0,p=0;if(s=o.insert("g").attr("class","annotation-group text"),t.annotations.length>0){const b=t.annotations[0];await Jr(s,{text:`«${b}»`},0),u=s.node().getBBox().height}l=o.insert("g").attr("class","label-group text"),await Jr(l,t,0,["font-weight: bolder"]);const g=l.node().getBBox();f=g.height,c=o.insert("g").attr("class","members-group text");let m=0;for(const b of t.members){const C=await Jr(c,b,m,[b.parseClassifier()]);m+=C+n}p=c.node().getBBox().height,p<=0&&(p=a/2),h=o.insert("g").attr("class","methods-group text");let y=0;for(const b of t.methods){const C=await Jr(h,b,y,[b.parseClassifier()]);y+=C+n}let x=o.node().getBBox();if(s!==null){const b=s.node().getBBox();s.attr("transform",`translate(${-b.width/2})`)}return l.attr("transform",`translate(${-g.width/2}, ${u})`),x=o.node().getBBox(),c.attr("transform",`translate(0, ${u+f+a*2})`),x=o.node().getBBox(),h.attr("transform",`translate(0, ${u+f+(p?p+a*4:a*2)})`),x=o.node().getBBox(),{shapeSvg:o,bbox:x}}d(bp,"textHelper");async function Jr(e,t,r,i=[]){const a=e.insert("g").attr("class","label").attr("style",i.join("; ")),n=Xt();let o="useHtmlLabels"in t?t.useHtmlLabels:vt(n.htmlLabels)??!0,s="";"text"in t?s=t.text:s=t.label,!o&&s.startsWith("\\")&&(s=s.substring(1)),Tr(s)&&(o=!0);const l=await je(a,Ss(lr(s)),{width:Ee(s,n)+50,classes:"markdown-node-label",useHtmlLabels:o},n);let c,h=1;if(o){const u=l.children[0],f=lt(l);h=u.innerHTML.split("<br>").length,u.innerHTML.includes("</math>")&&(h+=u.innerHTML.split("<mrow>").length-1);const p=u.getElementsByTagName("img");if(p){const g=s.replace(/<img[^>]*>/g,"").trim()==="";await Promise.all([...p].map(m=>new Promise(y=>{function x(){var b;if(m.style.display="flex",m.style.flexDirection="column",g){const C=((b=n.fontSize)==null?void 0:b.toString())??window.getComputedStyle(document.body).fontSize,v=parseInt(C,10)*5+"px";m.style.minWidth=v,m.style.maxWidth=v}else m.style.width="100%";y(m)}d(x,"setupImage"),setTimeout(()=>{m.complete&&x()}),m.addEventListener("error",x),m.addEventListener("load",x)})))}c=u.getBoundingClientRect(),f.attr("width",c.width),f.attr("height",c.height)}else{i.includes("font-weight: bolder")&&lt(l).selectAll("tspan").attr("font-weight",""),h=l.children.length;const u=l.children[0];(l.textContent===""||l.textContent.includes("&gt"))&&(u.textContent=s[0]+s.substring(1).replaceAll("&gt;",">").replaceAll("&lt;","<").trim(),s[1]===" "&&(u.textContent=u.textContent[0]+" "+u.textContent.substring(1))),u.textContent==="undefined"&&(u.textContent=""),c=l.getBBox()}return a.attr("transform","translate(0,"+(-c.height/(2*h)+r)+")"),c.height}d(Jr,"addText");async function Cp(e,t){var P,$;const r=ut(),i=r.class.padding??12,a=i,n=t.useHtmlLabels??vt(r.htmlLabels)??!0,o=t;o.annotations=o.annotations??[],o.members=o.members??[],o.methods=o.methods??[];const{shapeSvg:s,bbox:l}=await bp(e,t,r,n,a),{labelStyles:c,nodeStyles:h}=X(t);t.labelStyle=c,t.cssStyles=o.styles||"";const u=((P=o.styles)==null?void 0:P.join(";"))||h||"";t.cssStyles||(t.cssStyles=u.replaceAll("!important","").split(";"));const f=o.members.length===0&&o.methods.length===0&&!(($=r.class)!=null&&$.hideEmptyMembersBox),p=j.svg(s),g=G(t,{});t.look!=="handDrawn"&&(g.roughness=0,g.fillStyle="solid");const m=l.width;let y=l.height;o.members.length===0&&o.methods.length===0?y+=a:o.members.length>0&&o.methods.length===0&&(y+=a*2);const x=-m/2,b=-y/2,C=p.rectangle(x-i,b-i-(f?i:o.members.length===0&&o.methods.length===0?-i/2:0),m+2*i,y+2*i+(f?i*2:o.members.length===0&&o.methods.length===0?-i:0),g),S=s.insert(()=>C,":first-child");S.attr("class","basic label-container");const v=S.node().getBBox();s.selectAll(".text").each((L,z,R)=>{var W;const E=lt(R[z]),A=E.attr("transform");let _=0;if(A){const J=RegExp(/translate\(([^,]+),([^)]+)\)/).exec(A);J&&(_=parseFloat(J[2]))}let F=_+b+i-(f?i:o.members.length===0&&o.methods.length===0?-i/2:0);n||(F-=4);let B=x;(E.attr("class").includes("label-group")||E.attr("class").includes("annotation-group"))&&(B=-((W=E.node())==null?void 0:W.getBBox().width)/2||0,s.selectAll("text").each(function(U,J,tt){window.getComputedStyle(tt[J]).textAnchor==="middle"&&(B=0)})),E.attr("transform",`translate(${B}, ${F})`)});const M=s.select(".annotation-group").node().getBBox().height-(f?i/2:0)||0,T=s.select(".label-group").node().getBBox().height-(f?i/2:0)||0,D=s.select(".members-group").node().getBBox().height-(f?i/2:0)||0;if(o.members.length>0||o.methods.length>0||f){const L=p.line(v.x,M+T+b+i,v.x+v.width,M+T+b+i,g);s.insert(()=>L).attr("class","divider").attr("style",u)}if(f||o.members.length>0||o.methods.length>0){const L=p.line(v.x,M+T+D+b+a*2+i,v.x+v.width,M+T+D+b+i+a*2,g);s.insert(()=>L).attr("class","divider").attr("style",u)}if(o.look!=="handDrawn"&&s.selectAll("path").attr("style",u),S.select(":nth-child(2)").attr("style",u),s.selectAll(".divider").select("path").attr("style",u),t.labelStyle?s.selectAll("span").attr("style",t.labelStyle):s.selectAll("span").attr("style",u),!n){const L=RegExp(/color\s*:\s*([^;]*)/),z=L.exec(u);if(z){const R=z[0].replace("color","fill");s.selectAll("tspan").attr("style",R)}else if(c){const R=L.exec(c);if(R){const E=R[0].replace("color","fill");s.selectAll("tspan").attr("style",E)}}}return V(t,S),t.intersect=function(L){return H.rect(t,L)},s}d(Cp,"classBox");async function kp(e,t){var M,T;const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const a=t,n=t,o=20,s=20,l="verifyMethod"in t,c=it(t),h=e.insert("g").attr("class",c).attr("id",t.domId??t.id);let u;l?u=await ge(h,`&lt;&lt;${a.type}&gt;&gt;`,0,t.labelStyle):u=await ge(h,"&lt;&lt;Element&gt;&gt;",0,t.labelStyle);let f=u;const p=await ge(h,a.name,f,t.labelStyle+"; font-weight: bold;");if(f+=p+s,l){const D=await ge(h,`${a.requirementId?`Id: ${a.requirementId}`:""}`,f,t.labelStyle);f+=D;const P=await ge(h,`${a.text?`Text: ${a.text}`:""}`,f,t.labelStyle);f+=P;const $=await ge(h,`${a.risk?`Risk: ${a.risk}`:""}`,f,t.labelStyle);f+=$,await ge(h,`${a.verifyMethod?`Verification: ${a.verifyMethod}`:""}`,f,t.labelStyle)}else{const D=await ge(h,`${n.type?`Type: ${n.type}`:""}`,f,t.labelStyle);f+=D,await ge(h,`${n.docRef?`Doc Ref: ${n.docRef}`:""}`,f,t.labelStyle)}const g=(((M=h.node())==null?void 0:M.getBBox().width)??200)+o,m=(((T=h.node())==null?void 0:T.getBBox().height)??200)+o,y=-g/2,x=-m/2,b=j.svg(h),C=G(t,{});t.look!=="handDrawn"&&(C.roughness=0,C.fillStyle="solid");const S=b.rectangle(y,x,g,m,C),v=h.insert(()=>S,":first-child");if(v.attr("class","basic label-container").attr("style",i),h.selectAll(".label").each((D,P,$)=>{const L=lt($[P]),z=L.attr("transform");let R=0,E=0;if(z){const B=RegExp(/translate\(([^,]+),([^)]+)\)/).exec(z);B&&(R=parseFloat(B[1]),E=parseFloat(B[2]))}const A=E-m/2;let _=y+o/2;(P===0||P===1)&&(_=R),L.attr("transform",`translate(${_}, ${A+o})`)}),f>u+p+s){const D=b.line(y,x+u+p+s,y+g,x+u+p+s,C);h.insert(()=>D).attr("style",i)}return V(t,v),t.intersect=function(D){return H.rect(t,D)},h}d(kp,"requirementBox");async function ge(e,t,r,i=""){if(t==="")return 0;const a=e.insert("g").attr("class","label").attr("style",i),n=ut(),o=n.htmlLabels??!0,s=await je(a,Ss(lr(t)),{width:Ee(t,n)+50,classes:"markdown-node-label",useHtmlLabels:o,style:i},n);let l;if(o){const c=s.children[0],h=lt(s);l=c.getBoundingClientRect(),h.attr("width",l.width),h.attr("height",l.height)}else{const c=s.children[0];for(const h of c.children)h.textContent=h.textContent.replaceAll("&gt;",">").replaceAll("&lt;","<"),i&&h.setAttribute("style",i);l=s.getBBox(),l.height+=6}return a.attr("transform",`translate(${-l.width/2},${-l.height/2+r})`),l.height}d(ge,"addText");var D2=d(e=>{switch(e){case"Very High":return"red";case"High":return"orange";case"Medium":return null;case"Low":return"blue";case"Very Low":return"lightblue"}},"colorFromPriority");async function wp(e,t,{config:r}){var z,R;const{labelStyles:i,nodeStyles:a}=X(t);t.labelStyle=i||"";const n=10,o=t.width;t.width=(t.width??200)-10;const{shapeSvg:s,bbox:l,label:c}=await st(e,t,it(t)),h=t.padding||10;let u="",f;"ticket"in t&&t.ticket&&((z=r==null?void 0:r.kanban)!=null&&z.ticketBaseUrl)&&(u=(R=r==null?void 0:r.kanban)==null?void 0:R.ticketBaseUrl.replace("#TICKET#",t.ticket),f=s.insert("svg:a",":first-child").attr("class","kanban-ticket-link").attr("xlink:href",u).attr("target","_blank"));const p={useHtmlLabels:t.useHtmlLabels,labelStyle:t.labelStyle||"",width:t.width,img:t.img,padding:t.padding||8,centerLabel:!1};let g,m;f?{label:g,bbox:m}=await _n(f,"ticket"in t&&t.ticket||"",p):{label:g,bbox:m}=await _n(s,"ticket"in t&&t.ticket||"",p);const{label:y,bbox:x}=await _n(s,"assigned"in t&&t.assigned||"",p);t.width=o;const b=10,C=(t==null?void 0:t.width)||0,S=Math.max(m.height,x.height)/2,v=Math.max(l.height+b*2,(t==null?void 0:t.height)||0)+S,M=-C/2,T=-v/2;c.attr("transform","translate("+(h-C/2)+", "+(-S-l.height/2)+")"),g.attr("transform","translate("+(h-C/2)+", "+(-S+l.height/2)+")"),y.attr("transform","translate("+(h+C/2-x.width-2*n)+", "+(-S+l.height/2)+")");let D;const{rx:P,ry:$}=t,{cssStyles:L}=t;if(t.look==="handDrawn"){const E=j.svg(s),A=G(t,{}),_=P||$?E.path($e(M,T,C,v,P||0),A):E.rectangle(M,T,C,v,A);D=s.insert(()=>_,":first-child"),D.attr("class","basic label-container").attr("style",L||null)}else{D=s.insert("rect",":first-child"),D.attr("class","basic label-container __APA__").attr("style",a).attr("rx",P??5).attr("ry",$??5).attr("x",M).attr("y",T).attr("width",C).attr("height",v);const E="priority"in t&&t.priority;if(E){const A=s.append("line"),_=M+2,F=T+Math.floor((P??0)/2),B=T+v-Math.floor((P??0)/2);A.attr("x1",_).attr("y1",F).attr("x2",_).attr("y2",B).attr("stroke-width","4").attr("stroke",D2(E))}}return V(t,D),t.height=v,t.intersect=function(E){return H.rect(t,E)},s}d(wp,"kanbanItem");var R2=[{semanticName:"Process",name:"Rectangle",shortName:"rect",description:"Standard process shape",aliases:["proc","process","rectangle"],internalAliases:["squareRect"],handler:ip},{semanticName:"Event",name:"Rounded Rectangle",shortName:"rounded",description:"Represents an event",aliases:["event"],internalAliases:["roundedRect"],handler:tp},{semanticName:"Terminal Point",name:"Stadium",shortName:"stadium",description:"Terminal point",aliases:["terminal","pill"],handler:ap},{semanticName:"Subprocess",name:"Framed Rectangle",shortName:"fr-rect",description:"Subprocess",aliases:["subprocess","subproc","framed-rectangle","subroutine"],handler:lp},{semanticName:"Database",name:"Cylinder",shortName:"cyl",description:"Database storage",aliases:["db","database","cylinder"],handler:Bf},{semanticName:"Start",name:"Circle",shortName:"circle",description:"Starting point",aliases:["circ"],handler:Cf},{semanticName:"Decision",name:"Diamond",shortName:"diam",description:"Decision-making step",aliases:["decision","diamond","question"],handler:Kf},{semanticName:"Prepare Conditional",name:"Hexagon",shortName:"hex",description:"Preparation or condition step",aliases:["hexagon","prepare"],handler:Of},{semanticName:"Data Input/Output",name:"Lean Right",shortName:"lean-r",description:"Represents input or output",aliases:["lean-right","in-out"],internalAliases:["lean_right"],handler:jf},{semanticName:"Data Input/Output",name:"Lean Left",shortName:"lean-l",description:"Represents output or input",aliases:["lean-left","out-in"],internalAliases:["lean_left"],handler:Hf},{semanticName:"Priority Action",name:"Trapezoid Base Bottom",shortName:"trap-b",description:"Priority action",aliases:["priority","trapezoid-bottom","trapezoid"],handler:pp},{semanticName:"Manual Operation",name:"Trapezoid Base Top",shortName:"trap-t",description:"Represents a manual task",aliases:["manual","trapezoid-top","inv-trapezoid"],internalAliases:["inv_trapezoid"],handler:Wf},{semanticName:"Stop",name:"Double Circle",shortName:"dbl-circ",description:"Represents a stop point",aliases:["double-circle"],internalAliases:["doublecircle"],handler:Af},{semanticName:"Text Block",name:"Text Block",shortName:"text",description:"Text block",handler:up},{semanticName:"Card",name:"Notched Rectangle",shortName:"notch-rect",description:"Represents a card",aliases:["card","notched-rectangle"],handler:xf},{semanticName:"Lined/Shaded Process",name:"Lined Rectangle",shortName:"lin-rect",description:"Lined process shape",aliases:["lined-rectangle","lined-process","lin-proc","shaded-process"],handler:ep},{semanticName:"Start",name:"Small Circle",shortName:"sm-circ",description:"Small starting point",aliases:["start","small-circle"],internalAliases:["stateStart"],handler:op},{semanticName:"Stop",name:"Framed Circle",shortName:"fr-circ",description:"Stop point",aliases:["stop","framed-circle"],internalAliases:["stateEnd"],handler:sp},{semanticName:"Fork/Join",name:"Filled Rectangle",shortName:"fork",description:"Fork or join in process flow",aliases:["join"],internalAliases:["forkJoin"],handler:Ff},{semanticName:"Collate",name:"Hourglass",shortName:"hourglass",description:"Represents a collate operation",aliases:["hourglass","collate"],handler:Df},{semanticName:"Comment",name:"Curly Brace",shortName:"brace",description:"Adds a comment",aliases:["comment","brace-l"],handler:vf},{semanticName:"Comment Right",name:"Curly Brace",shortName:"brace-r",description:"Adds a comment",handler:Sf},{semanticName:"Comment with braces on both sides",name:"Curly Braces",shortName:"braces",description:"Adds a comment",handler:Tf},{semanticName:"Com Link",name:"Lightning Bolt",shortName:"bolt",description:"Communication link",aliases:["com-link","lightning-bolt"],handler:Uf},{semanticName:"Document",name:"Document",shortName:"doc",description:"Represents a document",aliases:["doc","document"],handler:mp},{semanticName:"Delay",name:"Half-Rounded Rectangle",shortName:"delay",description:"Represents a delay",aliases:["half-rounded-rectangle"],handler:$f},{semanticName:"Direct Access Storage",name:"Horizontal Cylinder",shortName:"h-cyl",description:"Direct access storage",aliases:["das","horizontal-cylinder"],handler:fp},{semanticName:"Disk Storage",name:"Lined Cylinder",shortName:"lin-cyl",description:"Disk storage",aliases:["disk","lined-cylinder"],handler:Yf},{semanticName:"Display",name:"Curved Trapezoid",shortName:"curv-trap",description:"Represents a display",aliases:["curved-trapezoid","display"],handler:_f},{semanticName:"Divided Process",name:"Divided Rectangle",shortName:"div-rect",description:"Divided process shape",aliases:["div-proc","divided-rectangle","divided-process"],handler:Lf},{semanticName:"Extract",name:"Triangle",shortName:"tri",description:"Extraction process",aliases:["extract","triangle"],handler:gp},{semanticName:"Internal Storage",name:"Window Pane",shortName:"win-pane",description:"Internal storage",aliases:["internal-storage","window-pane"],handler:xp},{semanticName:"Junction",name:"Filled Circle",shortName:"f-circ",description:"Junction point",aliases:["junction","filled-circle"],handler:Mf},{semanticName:"Loop Limit",name:"Trapezoidal Pentagon",shortName:"notch-pent",description:"Loop limit step",aliases:["loop-limit","notched-pentagon"],handler:dp},{semanticName:"Manual File",name:"Flipped Triangle",shortName:"flip-tri",description:"Manual file operation",aliases:["manual-file","flipped-triangle"],handler:Ef},{semanticName:"Manual Input",name:"Sloped Rectangle",shortName:"sl-rect",description:"Manual input step",aliases:["manual-input","sloped-rectangle"],handler:rp},{semanticName:"Multi-Document",name:"Stacked Document",shortName:"docs",description:"Multiple documents",aliases:["documents","st-doc","stacked-document"],handler:Xf},{semanticName:"Multi-Process",name:"Stacked Rectangle",shortName:"st-rect",description:"Multiple processes",aliases:["procs","processes","stacked-rectangle"],handler:Vf},{semanticName:"Stored Data",name:"Bow Tie Rectangle",shortName:"bow-rect",description:"Stored data",aliases:["stored-data","bow-tie-rectangle"],handler:yf},{semanticName:"Summary",name:"Crossed Circle",shortName:"cross-circ",description:"Summary",aliases:["summary","crossed-circle"],handler:wf},{semanticName:"Tagged Document",name:"Tagged Document",shortName:"tag-doc",description:"Tagged document",aliases:["tag-doc","tagged-document"],handler:hp},{semanticName:"Tagged Process",name:"Tagged Rectangle",shortName:"tag-rect",description:"Tagged process",aliases:["tagged-rectangle","tag-proc","tagged-process"],handler:cp},{semanticName:"Paper Tape",name:"Flag",shortName:"flag",description:"Paper tape",aliases:["paper-tape"],handler:yp},{semanticName:"Odd",name:"Odd",shortName:"odd",description:"Odd shape",internalAliases:["rect_left_inv_arrow"],handler:Qf},{semanticName:"Lined Document",name:"Lined Document",shortName:"lin-doc",description:"Lined document",aliases:["lined-document"],handler:Gf}],I2=d(()=>{const t=[...Object.entries({state:np,choice:bf,note:Zf,rectWithTitle:Jf,labelRect:qf,iconSquare:Nf,iconCircle:If,icon:Rf,iconRounded:Pf,imageSquare:zf,anchor:mf,kanbanItem:wp,classBox:Cp,erBox:no,requirementBox:kp}),...R2.flatMap(r=>[r.shortName,..."aliases"in r?r.aliases:[],..."internalAliases"in r?r.internalAliases:[]].map(a=>[a,r.handler]))];return Object.fromEntries(t)},"generateShapeMap"),vp=I2();function P2(e){return e in vp}d(P2,"isValidShape");var Ja=new Map;async function Sp(e,t,r){let i,a;t.shape==="rect"&&(t.rx&&t.ry?t.shape="roundedRect":t.shape="squareRect");const n=t.shape?vp[t.shape]:void 0;if(!n)throw new Error(`No such shape: ${t.shape}. Please check your syntax.`);if(t.link){let o;r.config.securityLevel==="sandbox"?o="_top":t.linkTarget&&(o=t.linkTarget||"_blank"),i=e.insert("svg:a").attr("xlink:href",t.link).attr("target",o??null),a=await n(i,t,r)}else a=await n(e,t,r),i=a;return t.tooltip&&a.attr("title",t.tooltip),Ja.set(t.id,i),t.haveCallback&&i.attr("class",i.attr("class")+" clickable"),i}d(Sp,"insertNode");var ES=d((e,t)=>{Ja.set(t.id,e)},"setNodeElem"),FS=d(()=>{Ja.clear()},"clear"),$S=d(e=>{const t=Ja.get(e.id);O.trace("Transforming node",e.diff,e,"translate("+(e.x-e.width/2-5)+", "+e.width/2+")");const r=8,i=e.diff||0;return e.clusterNode?t.attr("transform","translate("+(e.x+i-e.width/2)+", "+(e.y-e.height/2-r)+")"):t.attr("transform","translate("+e.x+", "+e.y+")"),i},"positionNode"),ee={aggregation:18,extension:18,composition:18,dependency:6,lollipop:13.5,arrow_point:4};function ti(e,t){if(e===void 0||t===void 0)return{angle:0,deltaX:0,deltaY:0};e=xt(e),t=xt(t);const[r,i]=[e.x,e.y],[a,n]=[t.x,t.y],o=a-r,s=n-i;return{angle:Math.atan(s/o),deltaX:o,deltaY:s}}d(ti,"calculateDeltaAndAngle");var xt=d(e=>Array.isArray(e)?{x:e[0],y:e[1]}:e,"pointTransformer"),N2=d(e=>({x:d(function(t,r,i){let a=0;const n=xt(i[0]).x<xt(i[i.length-1]).x?"left":"right";if(r===0&&Object.hasOwn(ee,e.arrowTypeStart)){const{angle:p,deltaX:g}=ti(i[0],i[1]);a=ee[e.arrowTypeStart]*Math.cos(p)*(g>=0?1:-1)}else if(r===i.length-1&&Object.hasOwn(ee,e.arrowTypeEnd)){const{angle:p,deltaX:g}=ti(i[i.length-1],i[i.length-2]);a=ee[e.arrowTypeEnd]*Math.cos(p)*(g>=0?1:-1)}const o=Math.abs(xt(t).x-xt(i[i.length-1]).x),s=Math.abs(xt(t).y-xt(i[i.length-1]).y),l=Math.abs(xt(t).x-xt(i[0]).x),c=Math.abs(xt(t).y-xt(i[0]).y),h=ee[e.arrowTypeStart],u=ee[e.arrowTypeEnd],f=1;if(o<u&&o>0&&s<u){let p=u+f-o;p*=n==="right"?-1:1,a-=p}if(l<h&&l>0&&c<h){let p=h+f-l;p*=n==="right"?-1:1,a+=p}return xt(t).x+a},"x"),y:d(function(t,r,i){let a=0;const n=xt(i[0]).y<xt(i[i.length-1]).y?"down":"up";if(r===0&&Object.hasOwn(ee,e.arrowTypeStart)){const{angle:p,deltaY:g}=ti(i[0],i[1]);a=ee[e.arrowTypeStart]*Math.abs(Math.sin(p))*(g>=0?1:-1)}else if(r===i.length-1&&Object.hasOwn(ee,e.arrowTypeEnd)){const{angle:p,deltaY:g}=ti(i[i.length-1],i[i.length-2]);a=ee[e.arrowTypeEnd]*Math.abs(Math.sin(p))*(g>=0?1:-1)}const o=Math.abs(xt(t).y-xt(i[i.length-1]).y),s=Math.abs(xt(t).x-xt(i[i.length-1]).x),l=Math.abs(xt(t).y-xt(i[0]).y),c=Math.abs(xt(t).x-xt(i[0]).x),h=ee[e.arrowTypeStart],u=ee[e.arrowTypeEnd],f=1;if(o<u&&o>0&&s<u){let p=u+f-o;p*=n==="up"?-1:1,a-=p}if(l<h&&l>0&&c<h){let p=h+f-l;p*=n==="up"?-1:1,a+=p}return xt(t).y+a},"y")}),"getLineFunctionsWithOffset"),z2=d((e,t,r,i,a,n)=>{t.arrowTypeStart&&Ll(e,"start",t.arrowTypeStart,r,i,a,n),t.arrowTypeEnd&&Ll(e,"end",t.arrowTypeEnd,r,i,a,n)},"addEdgeMarkers"),W2={arrow_cross:{type:"cross",fill:!1},arrow_point:{type:"point",fill:!0},arrow_barb:{type:"barb",fill:!0},arrow_circle:{type:"circle",fill:!1},aggregation:{type:"aggregation",fill:!1},extension:{type:"extension",fill:!1},composition:{type:"composition",fill:!0},dependency:{type:"dependency",fill:!0},lollipop:{type:"lollipop",fill:!1},only_one:{type:"onlyOne",fill:!1},zero_or_one:{type:"zeroOrOne",fill:!1},one_or_more:{type:"oneOrMore",fill:!1},zero_or_more:{type:"zeroOrMore",fill:!1},requirement_arrow:{type:"requirement_arrow",fill:!1},requirement_contains:{type:"requirement_contains",fill:!1}},Ll=d((e,t,r,i,a,n,o)=>{var u;const s=W2[r];if(!s){O.warn(`Unknown arrow type: ${r}`);return}const l=s.type,h=`${a}_${n}-${l}${t==="start"?"Start":"End"}`;if(o&&o.trim()!==""){const f=o.replace(/[^\dA-Za-z]/g,"_"),p=`${h}_${f}`;if(!document.getElementById(p)){const g=document.getElementById(h);if(g){const m=g.cloneNode(!0);m.id=p,m.querySelectorAll("path, circle, line").forEach(x=>{x.setAttribute("stroke",o),s.fill&&x.setAttribute("fill",o)}),(u=g.parentNode)==null||u.appendChild(m)}}e.attr(`marker-${t}`,`url(${i}#${p})`)}else e.attr(`marker-${t}`,`url(${i}#${h})`)},"addEdgeMarker"),Ea=new Map,Mt=new Map,OS=d(()=>{Ea.clear(),Mt.clear()},"clear"),Zr=d(e=>e?e.reduce((r,i)=>r+";"+i,""):"","getLabelStyles"),q2=d(async(e,t)=>{let r=vt(ut().flowchart.htmlLabels);const i=await je(e,t.label,{style:Zr(t.labelStyle),useHtmlLabels:r,addSvgBackground:!0,isNode:!1});O.info("abc82",t,t.labelType);const a=e.insert("g").attr("class","edgeLabel"),n=a.insert("g").attr("class","label");n.node().appendChild(i);let o=i.getBBox();if(r){const l=i.children[0],c=lt(i);o=l.getBoundingClientRect(),c.attr("width",o.width),c.attr("height",o.height)}n.attr("transform","translate("+-o.width/2+", "+-o.height/2+")"),Ea.set(t.id,a),t.width=o.width,t.height=o.height;let s;if(t.startLabelLeft){const l=await Je(t.startLabelLeft,Zr(t.labelStyle)),c=e.insert("g").attr("class","edgeTerminals"),h=c.insert("g").attr("class","inner");s=h.node().appendChild(l);const u=l.getBBox();h.attr("transform","translate("+-u.width/2+", "+-u.height/2+")"),Mt.get(t.id)||Mt.set(t.id,{}),Mt.get(t.id).startLeft=c,ei(s,t.startLabelLeft)}if(t.startLabelRight){const l=await Je(t.startLabelRight,Zr(t.labelStyle)),c=e.insert("g").attr("class","edgeTerminals"),h=c.insert("g").attr("class","inner");s=c.node().appendChild(l),h.node().appendChild(l);const u=l.getBBox();h.attr("transform","translate("+-u.width/2+", "+-u.height/2+")"),Mt.get(t.id)||Mt.set(t.id,{}),Mt.get(t.id).startRight=c,ei(s,t.startLabelRight)}if(t.endLabelLeft){const l=await Je(t.endLabelLeft,Zr(t.labelStyle)),c=e.insert("g").attr("class","edgeTerminals"),h=c.insert("g").attr("class","inner");s=h.node().appendChild(l);const u=l.getBBox();h.attr("transform","translate("+-u.width/2+", "+-u.height/2+")"),c.node().appendChild(l),Mt.get(t.id)||Mt.set(t.id,{}),Mt.get(t.id).endLeft=c,ei(s,t.endLabelLeft)}if(t.endLabelRight){const l=await Je(t.endLabelRight,Zr(t.labelStyle)),c=e.insert("g").attr("class","edgeTerminals"),h=c.insert("g").attr("class","inner");s=h.node().appendChild(l);const u=l.getBBox();h.attr("transform","translate("+-u.width/2+", "+-u.height/2+")"),c.node().appendChild(l),Mt.get(t.id)||Mt.set(t.id,{}),Mt.get(t.id).endRight=c,ei(s,t.endLabelRight)}return i},"insertEdgeLabel");function ei(e,t){ut().flowchart.htmlLabels&&e&&(e.style.width=t.length*9+"px",e.style.height="12px")}d(ei,"setTerminalWidth");var H2=d((e,t)=>{O.debug("Moving label abc88 ",e.id,e.label,Ea.get(e.id),t);let r=t.updatedPath?t.updatedPath:t.originalPath;const i=ut(),{subGraphTitleTotalMargin:a}=Fs(i);if(e.label){const n=Ea.get(e.id);let o=e.x,s=e.y;if(r){const l=ye.calcLabelPosition(r);O.debug("Moving label "+e.label+" from (",o,",",s,") to (",l.x,",",l.y,") abc88"),t.updatedPath&&(o=l.x,s=l.y)}n.attr("transform",`translate(${o}, ${s+a/2})`)}if(e.startLabelLeft){const n=Mt.get(e.id).startLeft;let o=e.x,s=e.y;if(r){const l=ye.calcTerminalLabelPosition(e.arrowTypeStart?10:0,"start_left",r);o=l.x,s=l.y}n.attr("transform",`translate(${o}, ${s})`)}if(e.startLabelRight){const n=Mt.get(e.id).startRight;let o=e.x,s=e.y;if(r){const l=ye.calcTerminalLabelPosition(e.arrowTypeStart?10:0,"start_right",r);o=l.x,s=l.y}n.attr("transform",`translate(${o}, ${s})`)}if(e.endLabelLeft){const n=Mt.get(e.id).endLeft;let o=e.x,s=e.y;if(r){const l=ye.calcTerminalLabelPosition(e.arrowTypeEnd?10:0,"end_left",r);o=l.x,s=l.y}n.attr("transform",`translate(${o}, ${s})`)}if(e.endLabelRight){const n=Mt.get(e.id).endRight;let o=e.x,s=e.y;if(r){const l=ye.calcTerminalLabelPosition(e.arrowTypeEnd?10:0,"end_right",r);o=l.x,s=l.y}n.attr("transform",`translate(${o}, ${s})`)}},"positionEdgeLabel"),j2=d((e,t)=>{const r=e.x,i=e.y,a=Math.abs(t.x-r),n=Math.abs(t.y-i),o=e.width/2,s=e.height/2;return a>=o||n>=s},"outsideNode"),U2=d((e,t,r)=>{O.debug(`intersection calc abc89:
  outsidePoint: ${JSON.stringify(t)}
  insidePoint : ${JSON.stringify(r)}
  node        : x:${e.x} y:${e.y} w:${e.width} h:${e.height}`);const i=e.x,a=e.y,n=Math.abs(i-r.x),o=e.width/2;let s=r.x<t.x?o-n:o+n;const l=e.height/2,c=Math.abs(t.y-r.y),h=Math.abs(t.x-r.x);if(Math.abs(a-t.y)*o>Math.abs(i-t.x)*l){let u=r.y<t.y?t.y-l-a:a-l-t.y;s=h*u/c;const f={x:r.x<t.x?r.x+s:r.x-h+s,y:r.y<t.y?r.y+c-u:r.y-c+u};return s===0&&(f.x=t.x,f.y=t.y),h===0&&(f.x=t.x),c===0&&(f.y=t.y),O.debug(`abc89 top/bottom calc, Q ${c}, q ${u}, R ${h}, r ${s}`,f),f}else{r.x<t.x?s=t.x-o-i:s=i-o-t.x;let u=c*s/h,f=r.x<t.x?r.x+h-s:r.x-h+s,p=r.y<t.y?r.y+u:r.y-u;return O.debug(`sides calc abc89, Q ${c}, q ${u}, R ${h}, r ${s}`,{_x:f,_y:p}),s===0&&(f=t.x,p=t.y),h===0&&(f=t.x),c===0&&(p=t.y),{x:f,y:p}}},"intersection"),Al=d((e,t)=>{O.warn("abc88 cutPathAtIntersect",e,t);let r=[],i=e[0],a=!1;return e.forEach(n=>{if(O.info("abc88 checking point",n,t),!j2(t,n)&&!a){const o=U2(t,i,n);O.debug("abc88 inside",n,i,o),O.debug("abc88 intersection",o,t);let s=!1;r.forEach(l=>{s=s||l.x===o.x&&l.y===o.y}),r.some(l=>l.x===o.x&&l.y===o.y)?O.warn("abc88 no intersect",o,r):r.push(o),a=!0}else O.warn("abc88 outside",n,i),i=n,a||r.push(n)}),O.debug("returning points",r),r},"cutPathAtIntersect");function Tp(e){const t=[],r=[];for(let i=1;i<e.length-1;i++){const a=e[i-1],n=e[i],o=e[i+1];(a.x===n.x&&n.y===o.y&&Math.abs(n.x-o.x)>5&&Math.abs(n.y-a.y)>5||a.y===n.y&&n.x===o.x&&Math.abs(n.x-a.x)>5&&Math.abs(n.y-o.y)>5)&&(t.push(n),r.push(i))}return{cornerPoints:t,cornerPointPositions:r}}d(Tp,"extractCornerPoints");var Ml=d(function(e,t,r){const i=t.x-e.x,a=t.y-e.y,n=Math.sqrt(i*i+a*a),o=r/n;return{x:t.x-o*i,y:t.y-o*a}},"findAdjacentPoint"),Y2=d(function(e){const{cornerPointPositions:t}=Tp(e),r=[];for(let i=0;i<e.length;i++)if(t.includes(i)){const a=e[i-1],n=e[i+1],o=e[i],s=Ml(a,o,5),l=Ml(n,o,5),c=l.x-s.x,h=l.y-s.y;r.push(s);const u=Math.sqrt(2)*2;let f={x:o.x,y:o.y};if(Math.abs(n.x-a.x)>10&&Math.abs(n.y-a.y)>=10){O.debug("Corner point fixing",Math.abs(n.x-a.x),Math.abs(n.y-a.y));const p=5;o.x===s.x?f={x:c<0?s.x-p+u:s.x+p-u,y:h<0?s.y-u:s.y+u}:f={x:c<0?s.x-u:s.x+u,y:h<0?s.y-p+u:s.y+p-u}}else O.debug("Corner point skipping fixing",Math.abs(n.x-a.x),Math.abs(n.y-a.y));r.push(f,l)}else r.push(e[i]);return r},"fixCorners"),G2=d(function(e,t,r,i,a,n,o){var P;const{handDrawnSeed:s}=ut();let l=t.points,c=!1;const h=a;var u=n;const f=[];for(const $ in t.cssCompiledStyles)of($)||f.push(t.cssCompiledStyles[$]);u.intersect&&h.intersect&&(l=l.slice(1,t.points.length-1),l.unshift(h.intersect(l[0])),O.debug("Last point APA12",t.start,"-->",t.end,l[l.length-1],u,u.intersect(l[l.length-1])),l.push(u.intersect(l[l.length-1]))),t.toCluster&&(O.info("to cluster abc88",r.get(t.toCluster)),l=Al(t.points,r.get(t.toCluster).node),c=!0),t.fromCluster&&(O.debug("from cluster abc88",r.get(t.fromCluster),JSON.stringify(l,null,2)),l=Al(l.reverse(),r.get(t.fromCluster).node).reverse(),c=!0);let p=l.filter($=>!Number.isNaN($.y));p=Y2(p);let g=Xi;switch(g=An,t.curve){case"linear":g=An;break;case"basis":g=Xi;break;case"cardinal":g=jl;break;default:g=Xi}const{x:m,y}=N2(t),x=Jd().x(m).y(y).curve(g);let b;switch(t.thickness){case"normal":b="edge-thickness-normal";break;case"thick":b="edge-thickness-thick";break;case"invisible":b="edge-thickness-invisible";break;default:b="edge-thickness-normal"}switch(t.pattern){case"solid":b+=" edge-pattern-solid";break;case"dotted":b+=" edge-pattern-dotted";break;case"dashed":b+=" edge-pattern-dashed";break;default:b+=" edge-pattern-solid"}let C,S=x(p);const v=Array.isArray(t.style)?t.style:[t.style];let M=v.find($=>$==null?void 0:$.startsWith("stroke:"));if(t.look==="handDrawn"){const $=j.svg(e);Object.assign([],p);const L=$.path(S,{roughness:.3,seed:s});b+=" transition",C=lt(L).select("path").attr("id",t.id).attr("class"," "+b+(t.classes?" "+t.classes:"")).attr("style",v?v.reduce((R,E)=>R+";"+E,""):"");let z=C.attr("d");C.attr("d",z),e.node().appendChild(C.node())}else{const $=f.join(";"),L=v?v.reduce((E,A)=>E+A+";",""):"";let z="";t.animate&&(z=" edge-animation-fast"),t.animation&&(z=" edge-animation-"+t.animation);const R=$?$+";"+L+";":L;C=e.append("path").attr("d",S).attr("id",t.id).attr("class"," "+b+(t.classes?" "+t.classes:"")+(z??"")).attr("style",R),M=(P=R.match(/stroke:([^;]+)/))==null?void 0:P[1]}let T="";(ut().flowchart.arrowMarkerAbsolute||ut().state.arrowMarkerAbsolute)&&(T=window.location.protocol+"//"+window.location.host+window.location.pathname+window.location.search,T=T.replace(/\(/g,"\\(").replace(/\)/g,"\\)")),O.info("arrowTypeStart",t.arrowTypeStart),O.info("arrowTypeEnd",t.arrowTypeEnd),z2(C,t,T,o,i,M);let D={};return c&&(D.updatedPath=l),D.originalPath=t.points,D},"insertEdge"),V2=d((e,t,r,i)=>{t.forEach(a=>{hk[a](e,r,i)})},"insertMarkers"),X2=d((e,t,r)=>{O.trace("Making markers for ",r),e.append("defs").append("marker").attr("id",r+"_"+t+"-extensionStart").attr("class","marker extension "+t).attr("refX",18).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("path").attr("d","M 1,7 L18,13 V 1 Z"),e.append("defs").append("marker").attr("id",r+"_"+t+"-extensionEnd").attr("class","marker extension "+t).attr("refX",1).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 1,1 V 13 L18,7 Z")},"extension"),Z2=d((e,t,r)=>{e.append("defs").append("marker").attr("id",r+"_"+t+"-compositionStart").attr("class","marker composition "+t).attr("refX",18).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L1,7 L9,1 Z"),e.append("defs").append("marker").attr("id",r+"_"+t+"-compositionEnd").attr("class","marker composition "+t).attr("refX",1).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L1,7 L9,1 Z")},"composition"),K2=d((e,t,r)=>{e.append("defs").append("marker").attr("id",r+"_"+t+"-aggregationStart").attr("class","marker aggregation "+t).attr("refX",18).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L1,7 L9,1 Z"),e.append("defs").append("marker").attr("id",r+"_"+t+"-aggregationEnd").attr("class","marker aggregation "+t).attr("refX",1).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L1,7 L9,1 Z")},"aggregation"),Q2=d((e,t,r)=>{e.append("defs").append("marker").attr("id",r+"_"+t+"-dependencyStart").attr("class","marker dependency "+t).attr("refX",6).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("path").attr("d","M 5,7 L9,13 L1,7 L9,1 Z"),e.append("defs").append("marker").attr("id",r+"_"+t+"-dependencyEnd").attr("class","marker dependency "+t).attr("refX",13).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L14,7 L9,1 Z")},"dependency"),J2=d((e,t,r)=>{e.append("defs").append("marker").attr("id",r+"_"+t+"-lollipopStart").attr("class","marker lollipop "+t).attr("refX",13).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("circle").attr("stroke","black").attr("fill","transparent").attr("cx",7).attr("cy",7).attr("r",6),e.append("defs").append("marker").attr("id",r+"_"+t+"-lollipopEnd").attr("class","marker lollipop "+t).attr("refX",1).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("circle").attr("stroke","black").attr("fill","transparent").attr("cx",7).attr("cy",7).attr("r",6)},"lollipop"),tk=d((e,t,r)=>{e.append("marker").attr("id",r+"_"+t+"-pointEnd").attr("class","marker "+t).attr("viewBox","0 0 10 10").attr("refX",5).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",8).attr("markerHeight",8).attr("orient","auto").append("path").attr("d","M 0 0 L 10 5 L 0 10 z").attr("class","arrowMarkerPath").style("stroke-width",1).style("stroke-dasharray","1,0"),e.append("marker").attr("id",r+"_"+t+"-pointStart").attr("class","marker "+t).attr("viewBox","0 0 10 10").attr("refX",4.5).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",8).attr("markerHeight",8).attr("orient","auto").append("path").attr("d","M 0 5 L 10 10 L 10 0 z").attr("class","arrowMarkerPath").style("stroke-width",1).style("stroke-dasharray","1,0")},"point"),ek=d((e,t,r)=>{e.append("marker").attr("id",r+"_"+t+"-circleEnd").attr("class","marker "+t).attr("viewBox","0 0 10 10").attr("refX",11).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",11).attr("markerHeight",11).attr("orient","auto").append("circle").attr("cx","5").attr("cy","5").attr("r","5").attr("class","arrowMarkerPath").style("stroke-width",1).style("stroke-dasharray","1,0"),e.append("marker").attr("id",r+"_"+t+"-circleStart").attr("class","marker "+t).attr("viewBox","0 0 10 10").attr("refX",-1).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",11).attr("markerHeight",11).attr("orient","auto").append("circle").attr("cx","5").attr("cy","5").attr("r","5").attr("class","arrowMarkerPath").style("stroke-width",1).style("stroke-dasharray","1,0")},"circle"),rk=d((e,t,r)=>{e.append("marker").attr("id",r+"_"+t+"-crossEnd").attr("class","marker cross "+t).attr("viewBox","0 0 11 11").attr("refX",12).attr("refY",5.2).attr("markerUnits","userSpaceOnUse").attr("markerWidth",11).attr("markerHeight",11).attr("orient","auto").append("path").attr("d","M 1,1 l 9,9 M 10,1 l -9,9").attr("class","arrowMarkerPath").style("stroke-width",2).style("stroke-dasharray","1,0"),e.append("marker").attr("id",r+"_"+t+"-crossStart").attr("class","marker cross "+t).attr("viewBox","0 0 11 11").attr("refX",-1).attr("refY",5.2).attr("markerUnits","userSpaceOnUse").attr("markerWidth",11).attr("markerHeight",11).attr("orient","auto").append("path").attr("d","M 1,1 l 9,9 M 10,1 l -9,9").attr("class","arrowMarkerPath").style("stroke-width",2).style("stroke-dasharray","1,0")},"cross"),ik=d((e,t,r)=>{e.append("defs").append("marker").attr("id",r+"_"+t+"-barbEnd").attr("refX",19).attr("refY",7).attr("markerWidth",20).attr("markerHeight",14).attr("markerUnits","userSpaceOnUse").attr("orient","auto").append("path").attr("d","M 19,7 L9,13 L14,7 L9,1 Z")},"barb"),ak=d((e,t,r)=>{e.append("defs").append("marker").attr("id",r+"_"+t+"-onlyOneStart").attr("class","marker onlyOne "+t).attr("refX",0).attr("refY",9).attr("markerWidth",18).attr("markerHeight",18).attr("orient","auto").append("path").attr("d","M9,0 L9,18 M15,0 L15,18"),e.append("defs").append("marker").attr("id",r+"_"+t+"-onlyOneEnd").attr("class","marker onlyOne "+t).attr("refX",18).attr("refY",9).attr("markerWidth",18).attr("markerHeight",18).attr("orient","auto").append("path").attr("d","M3,0 L3,18 M9,0 L9,18")},"only_one"),nk=d((e,t,r)=>{const i=e.append("defs").append("marker").attr("id",r+"_"+t+"-zeroOrOneStart").attr("class","marker zeroOrOne "+t).attr("refX",0).attr("refY",9).attr("markerWidth",30).attr("markerHeight",18).attr("orient","auto");i.append("circle").attr("fill","white").attr("cx",21).attr("cy",9).attr("r",6),i.append("path").attr("d","M9,0 L9,18");const a=e.append("defs").append("marker").attr("id",r+"_"+t+"-zeroOrOneEnd").attr("class","marker zeroOrOne "+t).attr("refX",30).attr("refY",9).attr("markerWidth",30).attr("markerHeight",18).attr("orient","auto");a.append("circle").attr("fill","white").attr("cx",9).attr("cy",9).attr("r",6),a.append("path").attr("d","M21,0 L21,18")},"zero_or_one"),sk=d((e,t,r)=>{e.append("defs").append("marker").attr("id",r+"_"+t+"-oneOrMoreStart").attr("class","marker oneOrMore "+t).attr("refX",18).attr("refY",18).attr("markerWidth",45).attr("markerHeight",36).attr("orient","auto").append("path").attr("d","M0,18 Q 18,0 36,18 Q 18,36 0,18 M42,9 L42,27"),e.append("defs").append("marker").attr("id",r+"_"+t+"-oneOrMoreEnd").attr("class","marker oneOrMore "+t).attr("refX",27).attr("refY",18).attr("markerWidth",45).attr("markerHeight",36).attr("orient","auto").append("path").attr("d","M3,9 L3,27 M9,18 Q27,0 45,18 Q27,36 9,18")},"one_or_more"),ok=d((e,t,r)=>{const i=e.append("defs").append("marker").attr("id",r+"_"+t+"-zeroOrMoreStart").attr("class","marker zeroOrMore "+t).attr("refX",18).attr("refY",18).attr("markerWidth",57).attr("markerHeight",36).attr("orient","auto");i.append("circle").attr("fill","white").attr("cx",48).attr("cy",18).attr("r",6),i.append("path").attr("d","M0,18 Q18,0 36,18 Q18,36 0,18");const a=e.append("defs").append("marker").attr("id",r+"_"+t+"-zeroOrMoreEnd").attr("class","marker zeroOrMore "+t).attr("refX",39).attr("refY",18).attr("markerWidth",57).attr("markerHeight",36).attr("orient","auto");a.append("circle").attr("fill","white").attr("cx",9).attr("cy",18).attr("r",6),a.append("path").attr("d","M21,18 Q39,0 57,18 Q39,36 21,18")},"zero_or_more"),lk=d((e,t,r)=>{e.append("defs").append("marker").attr("id",r+"_"+t+"-requirement_arrowEnd").attr("refX",20).attr("refY",10).attr("markerWidth",20).attr("markerHeight",20).attr("orient","auto").append("path").attr("d",`M0,0
      L20,10
      M20,10
      L0,20`)},"requirement_arrow"),ck=d((e,t,r)=>{const i=e.append("defs").append("marker").attr("id",r+"_"+t+"-requirement_containsEnd").attr("refX",20).attr("refY",10).attr("markerWidth",20).attr("markerHeight",20).attr("orient","auto").append("g");i.append("circle").attr("cx",10).attr("cy",10).attr("r",10).attr("fill","none"),i.append("line").attr("x1",0).attr("x2",20).attr("y1",10).attr("y2",10),i.append("line").attr("y1",0).attr("y2",20).attr("x1",10).attr("x2",10)},"requirement_contains"),hk={extension:X2,composition:Z2,aggregation:K2,dependency:Q2,lollipop:J2,point:tk,circle:ek,cross:rk,barb:ik,only_one:ak,zero_or_one:nk,one_or_more:sk,zero_or_more:ok,requirement_arrow:lk,requirement_contains:ck},uk=V2,fk={common:Er,getConfig:Xt,insertCluster:b2,insertEdge:G2,insertEdgeLabel:q2,insertMarkers:uk,insertNode:Sp,interpolateToCurve:zs,labelHelper:st,log:O,positionEdgeLabel:H2},mi={},_p=d(e=>{for(const t of e)mi[t.name]=t},"registerLayoutLoaders"),pk=d(()=>{_p([{name:"dagre",loader:d(async()=>await gt(()=>import("./dagre-QXRM2OYR.BQYt6s-8.js"),__vite__mapDeps([0,1,2,3,4,5]),import.meta.url),"loader")}])},"registerDefaultLayoutLoaders");pk();var DS=d(async(e,t)=>{if(!(e.layoutAlgorithm in mi))throw new Error(`Unknown layout algorithm: ${e.layoutAlgorithm}`);const r=mi[e.layoutAlgorithm];return(await r.loader()).render(e,t,fk,{algorithm:r.algorithm})},"render"),RS=d((e="",{fallback:t="dagre"}={})=>{if(e in mi)return e;if(t in mi)return O.warn(`Layout algorithm ${e} is not registered. Using ${t} as fallback.`),t;throw new Error(`Both layout algorithms ${e} and ${t} are not registered.`)},"getRegisteredLayoutAlgorithm"),El={name:"mermaid",version:"11.5.0",description:"Markdown-ish syntax for generating flowcharts, mindmaps, sequence diagrams, class diagrams, gantt charts, git graphs and more.",type:"module",module:"./dist/mermaid.core.mjs",types:"./dist/mermaid.d.ts",exports:{".":{types:"./dist/mermaid.d.ts",import:"./dist/mermaid.core.mjs",default:"./dist/mermaid.core.mjs"},"./*":"./*"},keywords:["diagram","markdown","flowchart","sequence diagram","gantt","class diagram","git graph","mindmap","packet diagram","c4 diagram","er diagram","pie chart","pie diagram","quadrant chart","requirement diagram","graph"],scripts:{clean:"rimraf dist",dev:"pnpm -w dev","docs:code":"typedoc src/defaultConfig.ts src/config.ts src/mermaid.ts && prettier --write ./src/docs/config/setup","docs:build":"rimraf ../../docs && pnpm docs:code && pnpm docs:spellcheck && tsx scripts/docs.cli.mts","docs:verify":"pnpm docs:code && pnpm docs:spellcheck && tsx scripts/docs.cli.mts --verify","docs:pre:vitepress":"pnpm --filter ./src/docs prefetch && rimraf src/vitepress && pnpm docs:code && tsx scripts/docs.cli.mts --vitepress && pnpm --filter ./src/vitepress install --no-frozen-lockfile --ignore-scripts","docs:build:vitepress":"pnpm docs:pre:vitepress && (cd src/vitepress && pnpm run build) && cpy --flat src/docs/landing/ ./src/vitepress/.vitepress/dist/landing","docs:dev":'pnpm docs:pre:vitepress && concurrently "pnpm --filter ./src/vitepress dev" "tsx scripts/docs.cli.mts --watch --vitepress"',"docs:dev:docker":'pnpm docs:pre:vitepress && concurrently "pnpm --filter ./src/vitepress dev:docker" "tsx scripts/docs.cli.mts --watch --vitepress"',"docs:serve":"pnpm docs:build:vitepress && vitepress serve src/vitepress","docs:spellcheck":'cspell "src/docs/**/*.md"',"docs:release-version":"tsx scripts/update-release-version.mts","docs:verify-version":"tsx scripts/update-release-version.mts --verify","types:build-config":"tsx scripts/create-types-from-json-schema.mts","types:verify-config":"tsx scripts/create-types-from-json-schema.mts --verify",checkCircle:"npx madge --circular ./src",prepublishOnly:"pnpm docs:verify-version"},repository:{type:"git",url:"https://github.com/mermaid-js/mermaid"},author:"Knut Sveidqvist",license:"MIT",standard:{ignore:["**/parser/*.js","dist/**/*.js","cypress/**/*.js"],globals:["page"]},dependencies:{"@braintree/sanitize-url":"^7.0.4","@iconify/utils":"^2.1.33","@mermaid-js/parser":"workspace:^","@types/d3":"^7.4.3",cytoscape:"^3.29.3","cytoscape-cose-bilkent":"^4.1.0","cytoscape-fcose":"^2.2.0",d3:"^7.9.0","d3-sankey":"^0.12.3","dagre-d3-es":"7.0.11",dayjs:"^1.11.13",dompurify:"^3.2.4",katex:"^0.16.9",khroma:"^2.1.0","lodash-es":"^4.17.21",marked:"^15.0.7",roughjs:"^4.6.6",stylis:"^4.3.6","ts-dedent":"^2.2.0",uuid:"^11.1.0"},devDependencies:{"@adobe/jsonschema2md":"^8.0.2","@iconify/types":"^2.0.0","@types/cytoscape":"^3.21.9","@types/cytoscape-fcose":"^2.2.4","@types/d3-sankey":"^0.12.4","@types/d3-scale":"^4.0.9","@types/d3-scale-chromatic":"^3.1.0","@types/d3-selection":"^3.0.11","@types/d3-shape":"^3.1.7","@types/jsdom":"^21.1.7","@types/katex":"^0.16.7","@types/lodash-es":"^4.17.12","@types/micromatch":"^4.0.9","@types/stylis":"^4.2.7","@types/uuid":"^10.0.0",ajv:"^8.17.1",chokidar:"^4.0.3",concurrently:"^9.1.2","csstree-validator":"^4.0.1",globby:"^14.0.2",jison:"^0.4.18","js-base64":"^3.7.7",jsdom:"^26.0.0","json-schema-to-typescript":"^15.0.4",micromatch:"^4.0.8","path-browserify":"^1.0.1",prettier:"^3.5.2",remark:"^15.0.1","remark-frontmatter":"^5.0.0","remark-gfm":"^4.0.1",rimraf:"^6.0.1","start-server-and-test":"^2.0.10","type-fest":"^4.35.0",typedoc:"^0.27.8","typedoc-plugin-markdown":"^4.4.2",typescript:"~5.7.3","unist-util-flatmap":"^1.0.0","unist-util-visit":"^5.0.0",vitepress:"^1.0.2","vitepress-plugin-search":"1.0.4-alpha.22"},files:["dist/","README.md"],publishConfig:{access:"public"}},dk=d(e=>{var a;const{securityLevel:t}=ut();let r=lt("body");if(t==="sandbox"){const o=((a=lt(`#i${e}`).node())==null?void 0:a.contentDocument)??document;r=lt(o.body)}return r.select(`#${e}`)},"selectSvgElement"),Bp="comm",Lp="rule",Ap="decl",gk="@import",mk="@namespace",yk="@keyframes",xk="@layer",Mp=Math.abs,so=String.fromCharCode;function Ep(e){return e.trim()}function na(e,t,r){return e.replace(t,r)}function bk(e,t,r){return e.indexOf(t,r)}function Cr(e,t){return e.charCodeAt(t)|0}function Lr(e,t,r){return e.slice(t,r)}function me(e){return e.length}function Ck(e){return e.length}function Vi(e,t){return t.push(e),e}var tn=1,Ar=1,Fp=0,ne=0,kt=0,Rr="";function oo(e,t,r,i,a,n,o,s){return{value:e,root:t,parent:r,type:i,props:a,children:n,line:tn,column:Ar,length:o,return:"",siblings:s}}function kk(){return kt}function wk(){return kt=ne>0?Cr(Rr,--ne):0,Ar--,kt===10&&(Ar=1,tn--),kt}function he(){return kt=ne<Fp?Cr(Rr,ne++):0,Ar++,kt===10&&(Ar=1,tn++),kt}function Pe(){return Cr(Rr,ne)}function sa(){return ne}function en(e,t){return Lr(Rr,e,t)}function yi(e){switch(e){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function vk(e){return tn=Ar=1,Fp=me(Rr=e),ne=0,[]}function Sk(e){return Rr="",e}function Bn(e){return Ep(en(ne-1,cs(e===91?e+2:e===40?e+1:e)))}function Tk(e){for(;(kt=Pe())&&kt<33;)he();return yi(e)>2||yi(kt)>3?"":" "}function _k(e,t){for(;--t&&he()&&!(kt<48||kt>102||kt>57&&kt<65||kt>70&&kt<97););return en(e,sa()+(t<6&&Pe()==32&&he()==32))}function cs(e){for(;he();)switch(kt){case e:return ne;case 34:case 39:e!==34&&e!==39&&cs(kt);break;case 40:e===41&&cs(e);break;case 92:he();break}return ne}function Bk(e,t){for(;he()&&e+kt!==57;)if(e+kt===84&&Pe()===47)break;return"/*"+en(t,ne-1)+"*"+so(e===47?e:he())}function Lk(e){for(;!yi(Pe());)he();return en(e,ne)}function Ak(e){return Sk(oa("",null,null,null,[""],e=vk(e),0,[0],e))}function oa(e,t,r,i,a,n,o,s,l){for(var c=0,h=0,u=o,f=0,p=0,g=0,m=1,y=1,x=1,b=0,C="",S=a,v=n,M=i,T=C;y;)switch(g=b,b=he()){case 40:if(g!=108&&Cr(T,u-1)==58){bk(T+=na(Bn(b),"&","&\f"),"&\f",Mp(c?s[c-1]:0))!=-1&&(x=-1);break}case 34:case 39:case 91:T+=Bn(b);break;case 9:case 10:case 13:case 32:T+=Tk(g);break;case 92:T+=_k(sa()-1,7);continue;case 47:switch(Pe()){case 42:case 47:Vi(Mk(Bk(he(),sa()),t,r,l),l),(yi(g||1)==5||yi(Pe()||1)==5)&&me(T)&&Lr(T,-1,void 0)!==" "&&(T+=" ");break;default:T+="/"}break;case 123*m:s[c++]=me(T)*x;case 125*m:case 59:case 0:switch(b){case 0:case 125:y=0;case 59+h:x==-1&&(T=na(T,/\f/g,"")),p>0&&(me(T)-u||m===0&&g===47)&&Vi(p>32?$l(T+";",i,r,u-1,l):$l(na(T," ","")+";",i,r,u-2,l),l);break;case 59:T+=";";default:if(Vi(M=Fl(T,t,r,c,h,a,s,C,S=[],v=[],u,n),n),b===123)if(h===0)oa(T,t,M,M,S,n,u,s,v);else{switch(f){case 99:if(Cr(T,3)===110)break;case 108:if(Cr(T,2)===97)break;default:h=0;case 100:case 109:case 115:}h?oa(e,M,M,i&&Vi(Fl(e,M,M,0,0,a,s,C,a,S=[],u,v),v),a,v,u,s,i?S:v):oa(T,M,M,M,[""],v,0,s,v)}}c=h=p=0,m=x=1,C=T="",u=o;break;case 58:u=1+me(T),p=g;default:if(m<1){if(b==123)--m;else if(b==125&&m++==0&&wk()==125)continue}switch(T+=so(b),b*m){case 38:x=h>0?1:(T+="\f",-1);break;case 44:s[c++]=(me(T)-1)*x,x=1;break;case 64:Pe()===45&&(T+=Bn(he())),f=Pe(),h=u=me(C=T+=Lk(sa())),b++;break;case 45:g===45&&me(T)==2&&(m=0)}}return n}function Fl(e,t,r,i,a,n,o,s,l,c,h,u){for(var f=a-1,p=a===0?n:[""],g=Ck(p),m=0,y=0,x=0;m<i;++m)for(var b=0,C=Lr(e,f+1,f=Mp(y=o[m])),S=e;b<g;++b)(S=Ep(y>0?p[b]+" "+C:na(C,/&\f/g,p[b])))&&(l[x++]=S);return oo(e,t,r,a===0?Lp:s,l,c,h,u)}function Mk(e,t,r,i){return oo(e,t,r,Bp,so(kk()),Lr(e,2,-2),0,i)}function $l(e,t,r,i,a){return oo(e,t,r,Ap,Lr(e,0,i),Lr(e,i+1,-1),i,a)}function hs(e,t){for(var r="",i=0;i<e.length;i++)r+=t(e[i],i,e,t)||"";return r}function Ek(e,t,r,i){switch(e.type){case xk:if(e.children.length)break;case gk:case mk:case Ap:return e.return=e.return||e.value;case Bp:return"";case yk:return e.return=e.value+"{"+hs(e.children,i)+"}";case Lp:if(!me(e.value=e.props.join(",")))return""}return me(r=hs(e.children,i))?e.return=e.value+"{"+r+"}":""}var Fk=uu(Object.keys,Object),$k=Object.prototype,Ok=$k.hasOwnProperty;function Dk(e){if(!Ua(e))return Fk(e);var t=[];for(var r in Object(e))Ok.call(e,r)&&r!="constructor"&&t.push(r);return t}var us=or(ke,"DataView"),fs=or(ke,"Promise"),ps=or(ke,"Set"),ds=or(ke,"WeakMap"),Ol="[object Map]",Rk="[object Object]",Dl="[object Promise]",Rl="[object Set]",Il="[object WeakMap]",Pl="[object DataView]",Ik=sr(us),Pk=sr(gi),Nk=sr(fs),zk=sr(ps),Wk=sr(ds),Xe=Fr;(us&&Xe(new us(new ArrayBuffer(1)))!=Pl||gi&&Xe(new gi)!=Ol||fs&&Xe(fs.resolve())!=Dl||ps&&Xe(new ps)!=Rl||ds&&Xe(new ds)!=Il)&&(Xe=function(e){var t=Fr(e),r=t==Rk?e.constructor:void 0,i=r?sr(r):"";if(i)switch(i){case Ik:return Pl;case Pk:return Ol;case Nk:return Dl;case zk:return Rl;case Wk:return Il}return t});var qk="[object Map]",Hk="[object Set]",jk=Object.prototype,Uk=jk.hasOwnProperty;function Nl(e){if(e==null)return!0;if(Ya(e)&&(wa(e)||typeof e=="string"||typeof e.splice=="function"||Ps(e)||Ns(e)||ka(e)))return!e.length;var t=Xe(e);if(t==qk||t==Hk)return!e.size;if(Ua(e))return!Dk(e).length;for(var r in e)if(Uk.call(e,r))return!1;return!0}var $p="c4",Yk=d(e=>/^\s*C4Context|C4Container|C4Component|C4Dynamic|C4Deployment/.test(e),"detector"),Gk=d(async()=>{const{diagram:e}=await gt(()=>import("./c4Diagram-7JAJQR3Y.Go8wi4bE.js"),__vite__mapDeps([6,7,8]),import.meta.url);return{id:$p,diagram:e}},"loader"),Vk={id:$p,detector:Yk,loader:Gk},Xk=Vk,Op="flowchart",Zk=d((e,t)=>{var r,i;return((r=t==null?void 0:t.flowchart)==null?void 0:r.defaultRenderer)==="dagre-wrapper"||((i=t==null?void 0:t.flowchart)==null?void 0:i.defaultRenderer)==="elk"?!1:/^\s*graph/.test(e)},"detector"),Kk=d(async()=>{const{diagram:e}=await gt(()=>import("./flowDiagram-27HWSH3H.CIsWH93n.js"),__vite__mapDeps([9,10,8,11]),import.meta.url);return{id:Op,diagram:e}},"loader"),Qk={id:Op,detector:Zk,loader:Kk},Jk=Qk,Dp="flowchart-v2",tw=d((e,t)=>{var r,i,a;return((r=t==null?void 0:t.flowchart)==null?void 0:r.defaultRenderer)==="dagre-d3"?!1:(((i=t==null?void 0:t.flowchart)==null?void 0:i.defaultRenderer)==="elk"&&(t.layout="elk"),/^\s*graph/.test(e)&&((a=t==null?void 0:t.flowchart)==null?void 0:a.defaultRenderer)==="dagre-wrapper"?!0:/^\s*flowchart/.test(e))},"detector"),ew=d(async()=>{const{diagram:e}=await gt(()=>import("./flowDiagram-27HWSH3H.CIsWH93n.js"),__vite__mapDeps([9,10,8,11]),import.meta.url);return{id:Dp,diagram:e}},"loader"),rw={id:Dp,detector:tw,loader:ew},iw=rw,Rp="er",aw=d(e=>/^\s*erDiagram/.test(e),"detector"),nw=d(async()=>{const{diagram:e}=await gt(()=>import("./erDiagram-MVNNDQJ5.JPDLzYK_.js"),__vite__mapDeps([12,10,8,11]),import.meta.url);return{id:Rp,diagram:e}},"loader"),sw={id:Rp,detector:aw,loader:nw},ow=sw,Ip="gitGraph",lw=d(e=>/^\s*gitGraph/.test(e),"detector"),cw=d(async()=>{const{diagram:e}=await gt(()=>import("./gitGraphDiagram-ISGV4O2Y.DFeXFmhq.js"),__vite__mapDeps([13,14,15,16,17,2,4,5,8]),import.meta.url);return{id:Ip,diagram:e}},"loader"),hw={id:Ip,detector:lw,loader:cw},uw=hw,Pp="gantt",fw=d(e=>/^\s*gantt/.test(e),"detector"),pw=d(async()=>{const{diagram:e}=await gt(()=>import("./ganttDiagram-ZCE2YOAT.CtV-LEr3.js"),__vite__mapDeps([18,19,17,20,21,22,23,24,25,26,8]),import.meta.url);return{id:Pp,diagram:e}},"loader"),dw={id:Pp,detector:fw,loader:pw},gw=dw,Np="info",mw=d(e=>/^\s*info/.test(e),"detector"),yw=d(async()=>{const{diagram:e}=await gt(()=>import("./infoDiagram-SDLB2J7W.B-j-JqLF.js"),__vite__mapDeps([27,16,17,2,4,5]),import.meta.url);return{id:Np,diagram:e}},"loader"),xw={id:Np,detector:mw,loader:yw},zp="pie",bw=d(e=>/^\s*pie/.test(e),"detector"),Cw=d(async()=>{const{diagram:e}=await gt(()=>import("./pieDiagram-OC6WZ2SS.C0ROfH8a.js"),__vite__mapDeps([28,14,16,17,2,4,5,29,24,30,26]),import.meta.url);return{id:zp,diagram:e}},"loader"),kw={id:zp,detector:bw,loader:Cw},Wp="quadrantChart",ww=d(e=>/^\s*quadrantChart/.test(e),"detector"),vw=d(async()=>{const{diagram:e}=await gt(()=>import("./quadrantDiagram-OT6RYTWY.BNidm50O.js"),__vite__mapDeps([31,25,24,26,8]),import.meta.url);return{id:Wp,diagram:e}},"loader"),Sw={id:Wp,detector:ww,loader:vw},Tw=Sw,qp="xychart",_w=d(e=>/^\s*xychart-beta/.test(e),"detector"),Bw=d(async()=>{const{diagram:e}=await gt(()=>import("./xychartDiagram-NJOKMNIP.CtYY9Gpc.js"),__vite__mapDeps([32,26,30,33,25,24]),import.meta.url);return{id:qp,diagram:e}},"loader"),Lw={id:qp,detector:_w,loader:Bw},Aw=Lw,Hp="requirement",Mw=d(e=>/^\s*requirement(Diagram)?/.test(e),"detector"),Ew=d(async()=>{const{diagram:e}=await gt(()=>import("./requirementDiagram-BKGUWIPO.QpoQx3Nq.js"),__vite__mapDeps([34,10,8]),import.meta.url);return{id:Hp,diagram:e}},"loader"),Fw={id:Hp,detector:Mw,loader:Ew},$w=Fw,jp="sequence",Ow=d(e=>/^\s*sequenceDiagram/.test(e),"detector"),Dw=d(async()=>{const{diagram:e}=await gt(()=>import("./sequenceDiagram-C4VUPXDP.Cd5azHM2.js"),__vite__mapDeps([35,7,15,8]),import.meta.url);return{id:jp,diagram:e}},"loader"),Rw={id:jp,detector:Ow,loader:Dw},Iw=Rw,Up="class",Pw=d((e,t)=>{var r;return((r=t==null?void 0:t.class)==null?void 0:r.defaultRenderer)==="dagre-wrapper"?!1:/^\s*classDiagram/.test(e)},"detector"),Nw=d(async()=>{const{diagram:e}=await gt(()=>import("./classDiagram-L266QK7U.D3YN8amE.js"),__vite__mapDeps([36,37,10,8]),import.meta.url);return{id:Up,diagram:e}},"loader"),zw={id:Up,detector:Pw,loader:Nw},Ww=zw,Yp="classDiagram",qw=d((e,t)=>{var r;return/^\s*classDiagram/.test(e)&&((r=t==null?void 0:t.class)==null?void 0:r.defaultRenderer)==="dagre-wrapper"?!0:/^\s*classDiagram-v2/.test(e)},"detector"),Hw=d(async()=>{const{diagram:e}=await gt(()=>import("./classDiagram-v2-JRWBCVM4.D3YN8amE.js"),__vite__mapDeps([38,37,10,8]),import.meta.url);return{id:Yp,diagram:e}},"loader"),jw={id:Yp,detector:qw,loader:Hw},Uw=jw,Gp="state",Yw=d((e,t)=>{var r;return((r=t==null?void 0:t.state)==null?void 0:r.defaultRenderer)==="dagre-wrapper"?!1:/^\s*stateDiagram/.test(e)},"detector"),Gw=d(async()=>{const{diagram:e}=await gt(()=>import("./stateDiagram-BVO7J4UH.DRVlTaN2.js"),__vite__mapDeps([39,40,10,8,1,2,3,4,24]),import.meta.url);return{id:Gp,diagram:e}},"loader"),Vw={id:Gp,detector:Yw,loader:Gw},Xw=Vw,Vp="stateDiagram",Zw=d((e,t)=>{var r;return!!(/^\s*stateDiagram-v2/.test(e)||/^\s*stateDiagram/.test(e)&&((r=t==null?void 0:t.state)==null?void 0:r.defaultRenderer)==="dagre-wrapper")},"detector"),Kw=d(async()=>{const{diagram:e}=await gt(()=>import("./stateDiagram-v2-WR7QG3WR.Dl5SqiWB.js"),__vite__mapDeps([41,40,10,8]),import.meta.url);return{id:Vp,diagram:e}},"loader"),Qw={id:Vp,detector:Zw,loader:Kw},Jw=Qw,Xp="journey",tv=d(e=>/^\s*journey/.test(e),"detector"),ev=d(async()=>{const{diagram:e}=await gt(()=>import("./journeyDiagram-D7A75E63.DtPwmuPl.js"),__vite__mapDeps([42,7,8,29,24]),import.meta.url);return{id:Xp,diagram:e}},"loader"),rv={id:Xp,detector:tv,loader:ev},iv=rv,av=d((e,t,r)=>{O.debug(`rendering svg for syntax error
`);const i=dk(t),a=i.append("g");i.attr("viewBox","0 0 2412 512"),mc(i,100,512,!0),a.append("path").attr("class","error-icon").attr("d","m411.313,123.313c6.25-6.25 6.25-16.375 0-22.625s-16.375-6.25-22.625,0l-32,32-9.375,9.375-20.688-20.688c-12.484-12.5-32.766-12.5-45.25,0l-16,16c-1.261,1.261-2.304,2.648-3.31,4.051-21.739-8.561-45.324-13.426-70.065-13.426-105.867,0-192,86.133-192,192s86.133,192 192,192 192-86.133 192-192c0-24.741-4.864-48.327-13.426-70.065 1.402-1.007 2.79-2.049 4.051-3.31l16-16c12.5-12.492 12.5-32.758 0-45.25l-20.688-20.688 9.375-9.375 32.001-31.999zm-219.313,100.687c-52.938,0-96,43.063-96,96 0,8.836-7.164,16-16,16s-16-7.164-16-16c0-70.578 57.***********-128 8.836,0 16,7.164 16,16s-7.164,16-16,16z"),a.append("path").attr("class","error-icon").attr("d","m459.02,148.98c-6.25-6.25-16.375-6.25-22.625,0s-6.25,16.375 0,22.625l16,16c3.125,3.125 7.219,4.688 11.313,4.688 4.094,0 8.188-1.563 11.313-4.688 6.25-6.25 6.25-16.375 0-22.625l-16.001-16z"),a.append("path").attr("class","error-icon").attr("d","m340.395,75.605c3.125,3.125 7.219,4.688 11.313,4.688 4.094,0 8.188-1.563 11.313-4.688 6.25-6.25 6.25-16.375 0-22.625l-16-16c-6.25-6.25-16.375-6.25-22.625,0s-6.25,16.375 0,22.625l15.999,16z"),a.append("path").attr("class","error-icon").attr("d","m400,64c8.844,0 16-7.164 16-16v-32c0-8.836-7.156-16-16-16-8.844,0-16,7.164-16,16v32c0,8.836 7.156,16 16,16z"),a.append("path").attr("class","error-icon").attr("d","m496,96.586h-32c-8.844,0-16,7.164-16,16 0,8.836 7.156,16 16,16h32c8.844,0 16-7.164 16-16 0-8.836-7.156-16-16-16z"),a.append("path").attr("class","error-icon").attr("d","m436.98,75.605c3.125,3.125 7.219,4.688 11.313,4.688 4.094,0 8.188-1.563 11.313-4.688l32-32c6.25-6.25 6.25-16.375 0-22.625s-16.375-6.25-22.625,0l-32,32c-6.251,6.25-6.251,16.375-0.001,22.625z"),a.append("text").attr("class","error-text").attr("x",1440).attr("y",250).attr("font-size","150px").style("text-anchor","middle").text("Syntax error in text"),a.append("text").attr("class","error-text").attr("x",1250).attr("y",400).attr("font-size","100px").style("text-anchor","middle").text(`mermaid version ${r}`)},"draw"),Zp={draw:av},nv=Zp,sv={db:{},renderer:Zp,parser:{parse:d(()=>{},"parse")}},ov=sv,Kp="flowchart-elk",lv=d((e,t={})=>{var r;return/^\s*flowchart-elk/.test(e)||/^\s*flowchart|graph/.test(e)&&((r=t==null?void 0:t.flowchart)==null?void 0:r.defaultRenderer)==="elk"?(t.layout="elk",!0):!1},"detector"),cv=d(async()=>{const{diagram:e}=await gt(()=>import("./flowDiagram-27HWSH3H.CIsWH93n.js"),__vite__mapDeps([9,10,8,11]),import.meta.url);return{id:Kp,diagram:e}},"loader"),hv={id:Kp,detector:lv,loader:cv},uv=hv,Qp="timeline",fv=d(e=>/^\s*timeline/.test(e),"detector"),pv=d(async()=>{const{diagram:e}=await gt(()=>import("./timeline-definition-WOTUTIAU.gyxjTqh7.js"),__vite__mapDeps([43,8,29,24]),import.meta.url);return{id:Qp,diagram:e}},"loader"),dv={id:Qp,detector:fv,loader:pv},gv=dv,Jp="mindmap",mv=d(e=>/^\s*mindmap/.test(e),"detector"),yv=d(async()=>{const{diagram:e}=await gt(()=>import("./mindmap-definition-7EJRZJGK.D4JLXad2.js"),__vite__mapDeps([44,45,19,17,20,21,22,8]),import.meta.url);return{id:Jp,diagram:e}},"loader"),xv={id:Jp,detector:mv,loader:yv},bv=xv,td="kanban",Cv=d(e=>/^\s*kanban/.test(e),"detector"),kv=d(async()=>{const{diagram:e}=await gt(()=>import("./kanban-definition-4GR4SRK3.1mMH4iNq.js"),[],import.meta.url);return{id:td,diagram:e}},"loader"),wv={id:td,detector:Cv,loader:kv},vv=wv,ed="sankey",Sv=d(e=>/^\s*sankey-beta/.test(e),"detector"),Tv=d(async()=>{const{diagram:e}=await gt(()=>import("./sankeyDiagram-3MH5UGAL.Fi0ZOIw0.js"),__vite__mapDeps([46,30,26,8]),import.meta.url);return{id:ed,diagram:e}},"loader"),_v={id:ed,detector:Sv,loader:Tv},Bv=_v,rd="packet",Lv=d(e=>/^\s*packet-beta/.test(e),"detector"),Av=d(async()=>{const{diagram:e}=await gt(()=>import("./diagram-DHSB7DV3.scXJlDpG.js"),__vite__mapDeps([47,14,16,17,2,4,5]),import.meta.url);return{id:rd,diagram:e}},"loader"),Mv={id:rd,detector:Lv,loader:Av},id="block",Ev=d(e=>/^\s*block-beta/.test(e),"detector"),Fv=d(async()=>{const{diagram:e}=await gt(()=>import("./blockDiagram-5JUZGEFE.ByWfu6po.js"),__vite__mapDeps([48,5,2,1,11,8,24]),import.meta.url);return{id,diagram:e}},"loader"),$v={id,detector:Ev,loader:Fv},Ov=$v,ad="architecture",Dv=d(e=>/^\s*architecture/.test(e),"detector"),Rv=d(async()=>{const{diagram:e}=await gt(()=>import("./architectureDiagram-PQUH6ZAG.CSxtIQ3_.js"),__vite__mapDeps([49,14,15,16,17,2,4,5,45,19,20,21,22,8]),import.meta.url);return{id:ad,diagram:e}},"loader"),Iv={id:ad,detector:Dv,loader:Rv},Pv=Iv,zl=!1,rn=d(()=>{zl||(zl=!0,ua("error",ov,e=>e.toLowerCase().trim()==="error"),ua("---",{db:{clear:d(()=>{},"clear")},styles:{},renderer:{draw:d(()=>{},"draw")},parser:{parse:d(()=>{throw new Error("Diagrams beginning with --- are not valid. If you were trying to use a YAML front-matter, please ensure that you've correctly opened and closed the YAML front-matter with un-indented `---` blocks")},"parse")},init:d(()=>null,"init")},e=>e.toLowerCase().trimStart().startsWith("---")),ic(Xk,vv,Uw,Ww,ow,gw,xw,kw,$w,Iw,uv,iw,Jk,bv,gv,uw,Jw,Xw,iv,Tw,Bv,Mv,Aw,Ov,Pv))},"addDiagrams"),Nv=d(async()=>{O.debug("Loading registered diagrams");const t=(await Promise.allSettled(Object.entries(wr).map(async([r,{detector:i,loader:a}])=>{if(a)try{Dn(r)}catch{try{const{diagram:n,id:o}=await a();ua(o,n,i)}catch(n){throw O.error(`Failed to load external diagram with key ${r}. Removing from detectors.`),delete wr[r],n}}}))).filter(r=>r.status==="rejected");if(t.length>0){O.error(`Failed to load ${t.length} external diagrams`);for(const r of t)O.error(r);throw new Error(`Failed to load ${t.length} external diagrams`)}},"loadRegisteredDiagrams"),zv="graphics-document document";function nd(e,t){e.attr("role",zv),t!==""&&e.attr("aria-roledescription",t)}d(nd,"setA11yDiagramInfo");function sd(e,t,r,i){if(e.insert!==void 0){if(r){const a=`chart-desc-${i}`;e.attr("aria-describedby",a),e.insert("desc",":first-child").attr("id",a).text(r)}if(t){const a=`chart-title-${i}`;e.attr("aria-labelledby",a),e.insert("title",":first-child").attr("id",a).text(t)}}}d(sd,"addSVGa11yTitleDescription");var Mr,gs=(Mr=class{constructor(t,r,i,a,n){this.type=t,this.text=r,this.db=i,this.parser=a,this.renderer=n}static async fromText(t,r={}){var c,h;const i=Xt(),a=xs(t,i);t=c1(t)+`
`;try{Dn(a)}catch{const u=Dg(a);if(!u)throw new rc(`Diagram ${a} not found.`);const{id:f,diagram:p}=await u();ua(f,p)}const{db:n,parser:o,renderer:s,init:l}=Dn(a);return o.parser&&(o.parser.yy=n),(c=n.clear)==null||c.call(n),l==null||l(i),r.title&&((h=n.setDiagramTitle)==null||h.call(n,r.title)),await o.parse(t),new Mr(a,t,n,o,s)}async render(t,r){await this.renderer.draw(this.text,t,r,this)}getParser(){return this.parser}getType(){return this.type}},d(Mr,"Diagram"),Mr),Wl=[],Wv=d(()=>{Wl.forEach(e=>{e()}),Wl=[]},"attachFunctions"),qv=d(e=>e.replace(/^\s*%%(?!{)[^\n]+\n?/gm,"").trimStart(),"cleanupComments");function od(e){const t=e.match(ec);if(!t)return{text:e,metadata:{}};let r=Oy(t[1],{schema:$y})??{};r=typeof r=="object"&&!Array.isArray(r)?r:{};const i={};return r.displayMode&&(i.displayMode=r.displayMode.toString()),r.title&&(i.title=r.title.toString()),r.config&&(i.config=r.config),{text:e.slice(t[0].length),metadata:i}}d(od,"extractFrontMatter");var Hv=d(e=>e.replace(/\r\n?/g,`
`).replace(/<(\w+)([^>]*)>/g,(t,r,i)=>"<"+r+i.replace(/="([^"]*)"/g,"='$1'")+">"),"cleanupText"),jv=d(e=>{const{text:t,metadata:r}=od(e),{displayMode:i,title:a,config:n={}}=r;return i&&(n.gantt||(n.gantt={}),n.gantt.displayMode=i),{title:a,config:n,text:t}},"processFrontmatter"),Uv=d(e=>{const t=ye.detectInit(e)??{},r=ye.detectDirective(e,"wrap");return Array.isArray(r)?t.wrap=r.some(({type:i})=>i==="wrap"):(r==null?void 0:r.type)==="wrap"&&(t.wrap=!0),{text:ZC(e),directive:t}},"processDirectives");function lo(e){const t=Hv(e),r=jv(t),i=Uv(r.text),a=Us(r.config,i.directive);return e=qv(i.text),{code:e,title:r.title,config:a}}d(lo,"preprocessDiagram");function ld(e){const t=new TextEncoder().encode(e),r=Array.from(t,i=>String.fromCodePoint(i)).join("");return btoa(r)}d(ld,"toBase64");var Yv=5e4,Gv="graph TB;a[Maximum text size in diagram exceeded];style a fill:#faa",Vv="sandbox",Xv="loose",Zv="http://www.w3.org/2000/svg",Kv="http://www.w3.org/1999/xlink",Qv="http://www.w3.org/1999/xhtml",Jv="100%",tS="100%",eS="border:0;margin:0;",rS="margin:0",iS="allow-top-navigation-by-user-activation allow-popups",aS='The "iframe" tag is not supported by your browser.',nS=["foreignobject"],sS=["dominant-baseline"];function co(e){const t=lo(e);return ca(),Kg(t.config??{}),t}d(co,"processAndSetConfigs");async function cd(e,t){rn();try{const{code:r,config:i}=co(e);return{diagramType:(await ud(r)).type,config:i}}catch(r){if(t!=null&&t.suppressErrors)return!1;throw r}}d(cd,"parse");var ql=d((e,t,r=[])=>`
.${e} ${t} { ${r.join(" !important; ")} !important; }`,"cssImportantStyles"),oS=d((e,t=new Map)=>{var i;let r="";if(e.themeCSS!==void 0&&(r+=`
${e.themeCSS}`),e.fontFamily!==void 0&&(r+=`
:root { --mermaid-font-family: ${e.fontFamily}}`),e.altFontFamily!==void 0&&(r+=`
:root { --mermaid-alt-font-family: ${e.altFontFamily}}`),t instanceof Map){const s=e.htmlLabels??((i=e.flowchart)==null?void 0:i.htmlLabels)?["> *","span"]:["rect","polygon","ellipse","circle","path"];t.forEach(l=>{Nl(l.styles)||s.forEach(c=>{r+=ql(l.id,c,l.styles)}),Nl(l.textStyles)||(r+=ql(l.id,"tspan",((l==null?void 0:l.textStyles)||[]).map(c=>c.replace("color","fill"))))})}return r},"createCssStyles"),lS=d((e,t,r,i)=>{const a=oS(e,r),n=mm(t,a,e.themeVariables);return hs(Ak(`${i}{${n}}`),Ek)},"createUserStyles"),cS=d((e="",t,r)=>{let i=e;return!r&&!t&&(i=i.replace(/marker-end="url\([\d+./:=?A-Za-z-]*?#/g,'marker-end="url(#')),i=lr(i),i=i.replace(/<br>/g,"<br/>"),i},"cleanUpSvgCode"),hS=d((e="",t)=>{var a,n;const r=(n=(a=t==null?void 0:t.viewBox)==null?void 0:a.baseVal)!=null&&n.height?t.viewBox.baseVal.height+"px":tS,i=ld(`<body style="${rS}">${e}</body>`);return`<iframe style="width:${Jv};height:${r};${eS}" src="data:text/html;charset=UTF-8;base64,${i}" sandbox="${iS}">
  ${aS}
</iframe>`},"putIntoIFrame"),Hl=d((e,t,r,i,a)=>{const n=e.append("div");n.attr("id",r),i&&n.attr("style",i);const o=n.append("svg").attr("id",t).attr("width","100%").attr("xmlns",Zv);return a&&o.attr("xmlns:xlink",a),o.append("g"),e},"appendDivSvgG");function ms(e,t){return e.append("iframe").attr("id",t).attr("style","width: 100%; height: 100%;").attr("sandbox","")}d(ms,"sandboxedIframe");var uS=d((e,t,r,i)=>{var a,n,o;(a=e.getElementById(t))==null||a.remove(),(n=e.getElementById(r))==null||n.remove(),(o=e.getElementById(i))==null||o.remove()},"removeExistingElements"),fS=d(async function(e,t,r){var z,R,E,A,_,F;rn();const i=co(t);t=i.code;const a=Xt();O.debug(a),t.length>((a==null?void 0:a.maxTextSize)??Yv)&&(t=Gv);const n="#"+e,o="i"+e,s="#"+o,l="d"+e,c="#"+l,h=d(()=>{const W=lt(f?s:c).node();W&&"remove"in W&&W.remove()},"removeTempElements");let u=lt("body");const f=a.securityLevel===Vv,p=a.securityLevel===Xv,g=a.fontFamily;if(r!==void 0){if(r&&(r.innerHTML=""),f){const B=ms(lt(r),o);u=lt(B.nodes()[0].contentDocument.body),u.node().style.margin=0}else u=lt(r);Hl(u,e,l,`font-family: ${g}`,Kv)}else{if(uS(document,e,l,o),f){const B=ms(lt("body"),o);u=lt(B.nodes()[0].contentDocument.body),u.node().style.margin=0}else u=lt("body");Hl(u,e,l)}let m,y;try{m=await gs.fromText(t,{title:i.title})}catch(B){if(a.suppressErrorRendering)throw h(),B;m=await gs.fromText("error"),y=B}const x=u.select(c).node(),b=m.type,C=x.firstChild,S=C.firstChild,v=(R=(z=m.renderer).getClasses)==null?void 0:R.call(z,t,m),M=lS(a,b,v,n),T=document.createElement("style");T.innerHTML=M,C.insertBefore(T,S);try{await m.renderer.draw(t,e,El.version,m)}catch(B){throw a.suppressErrorRendering?h():nv.draw(t,e,El.version),B}const D=u.select(`${c} svg`),P=(A=(E=m.db).getAccTitle)==null?void 0:A.call(E),$=(F=(_=m.db).getAccDescription)==null?void 0:F.call(_);fd(b,D,P,$),u.select(`[id="${e}"]`).selectAll("foreignobject > *").attr("xmlns",Qv);let L=u.select(c).node().innerHTML;if(O.debug("config.arrowMarkerAbsolute",a.arrowMarkerAbsolute),L=cS(L,f,vt(a.arrowMarkerAbsolute)),f){const B=u.select(c+" svg").node();L=hS(L,B)}else p||(L=kr.sanitize(L,{ADD_TAGS:nS,ADD_ATTR:sS,HTML_INTEGRATION_POINTS:{foreignobject:!0}}));if(Wv(),y)throw y;return h(),{diagramType:b,svg:L,bindFunctions:m.db.bindFunctions}},"render");function hd(e={}){var i;const t=Et({},e);t!=null&&t.fontFamily&&!((i=t.themeVariables)!=null&&i.fontFamily)&&(t.themeVariables||(t.themeVariables={}),t.themeVariables.fontFamily=t.fontFamily),Xg(t),t!=null&&t.theme&&t.theme in Le?t.themeVariables=Le[t.theme].getThemeVariables(t.themeVariables):t&&(t.themeVariables=Le.default.getThemeVariables(t.themeVariables));const r=typeof t=="object"?Vg(t):cc();ys(r.logLevel),rn()}d(hd,"initialize");var ud=d((e,t={})=>{const{code:r}=lo(e);return gs.fromText(r,t)},"getDiagramFromText");function fd(e,t,r,i){nd(t,e),sd(t,r,i,t.attr("id"))}d(fd,"addA11yInfo");var ar=Object.freeze({render:fS,parse:cd,getDiagramFromText:ud,initialize:hd,getConfig:Xt,setConfig:hc,getSiteConfig:cc,updateSiteConfig:Zg,reset:d(()=>{ca()},"reset"),globalReset:d(()=>{ca(vr)},"globalReset"),defaultConfig:vr});ys(Xt().logLevel);ca(Xt());var pS=d((e,t,r)=>{O.warn(e),js(e)?(r&&r(e.str,e.hash),t.push({...e,message:e.str,error:e})):(r&&r(e),e instanceof Error&&t.push({str:e.message,message:e.message,hash:e.name,error:e}))},"handleError"),pd=d(async function(e={querySelector:".mermaid"}){try{await dS(e)}catch(t){if(js(t)&&O.error(t.str),Kt.parseError&&Kt.parseError(t),!e.suppressErrors)throw O.error("Use the suppressErrors option to suppress these errors"),t}},"run"),dS=d(async function({postRenderCallback:e,querySelector:t,nodes:r}={querySelector:".mermaid"}){const i=ar.getConfig();O.debug(`${e?"":"No "}Callback function found`);let a;if(r)a=r;else if(t)a=document.querySelectorAll(t);else throw new Error("Nodes and querySelector are both undefined");O.debug(`Found ${a.length} diagrams`),(i==null?void 0:i.startOnLoad)!==void 0&&(O.debug("Start On Load: "+(i==null?void 0:i.startOnLoad)),ar.updateSiteConfig({startOnLoad:i==null?void 0:i.startOnLoad}));const n=new ye.InitIDGenerator(i.deterministicIds,i.deterministicIDSeed);let o;const s=[];for(const l of Array.from(a)){if(O.info("Rendering diagram: "+l.id),l.getAttribute("data-processed"))continue;l.setAttribute("data-processed","true");const c=`mermaid-${n.next()}`;o=l.innerHTML,o=Wu(ye.entityDecode(o)).trim().replace(/<br\s*\/?>/gi,"<br/>");const h=ye.detectInit(o);h&&O.debug("Detected early reinit: ",h);try{const{svg:u,bindFunctions:f}=await yd(c,o,l);l.innerHTML=u,e&&await e(c),f&&f(l)}catch(u){pS(u,s,Kt.parseError)}}if(s.length>0)throw s[0]},"runThrowsErrors"),dd=d(function(e){ar.initialize(e)},"initialize"),gS=d(async function(e,t,r){O.warn("mermaid.init is deprecated. Please use run instead."),e&&dd(e);const i={postRenderCallback:r,querySelector:".mermaid"};typeof t=="string"?i.querySelector=t:t&&(t instanceof HTMLElement?i.nodes=[t]:i.nodes=t),await pd(i)},"init"),mS=d(async(e,{lazyLoad:t=!0}={})=>{rn(),ic(...e),t===!1&&await Nv()},"registerExternalDiagrams"),gd=d(function(){if(Kt.startOnLoad){const{startOnLoad:e}=ar.getConfig();e&&Kt.run().catch(t=>O.error("Mermaid failed to initialize",t))}},"contentLoaded");typeof document<"u"&&window.addEventListener("load",gd,!1);var yS=d(function(e){Kt.parseError=e},"setParseErrorHandler"),Fa=[],Ln=!1,md=d(async()=>{if(!Ln){for(Ln=!0;Fa.length>0;){const e=Fa.shift();if(e)try{await e()}catch(t){O.error("Error executing queue",t)}}Ln=!1}},"executeQueue"),xS=d(async(e,t)=>new Promise((r,i)=>{const a=d(()=>new Promise((n,o)=>{ar.parse(e,t).then(s=>{n(s),r(s)},s=>{var l;O.error("Error parsing",s),(l=Kt.parseError)==null||l.call(Kt,s),o(s),i(s)})}),"performCall");Fa.push(a),md().catch(i)}),"parse"),yd=d((e,t,r)=>new Promise((i,a)=>{const n=d(()=>new Promise((o,s)=>{ar.render(e,t,r).then(l=>{o(l),i(l)},l=>{var c;O.error("Error parsing",l),(c=Kt.parseError)==null||c.call(Kt,l),s(l),a(l)})}),"performCall");Fa.push(n),md().catch(a)}),"render"),Kt={startOnLoad:!0,mermaidAPI:ar,parse:xS,render:yd,init:gS,run:pd,registerExternalDiagrams:mS,registerLayoutLoaders:_p,initialize:dd,parseError:void 0,contentLoaded:gd,setParseErrorHandler:yS,detectType:xs,registerIconPacks:r0},bS=Kt;/*! Check if previously processed *//*!
 * Wait for document loaded before starting the execution
 */const IS=Object.freeze(Object.defineProperty({__proto__:null,default:bS},Symbol.toStringTag,{value:"Module"}));export{Sp as $,ii as A,$g as B,xe as C,oc as D,Us as E,Xt as F,e1 as G,Tm as H,dk as I,$y as J,El as K,Wg as L,Tr as M,_S as N,bs as O,Ga as P,qo as Q,t1 as R,yc as S,pm as T,nt as U,_i as V,q as W,Q as X,b2 as Y,YC as Z,d as _,Et as a,du as a$,$S as a0,Bu as a1,vt as a2,je as a3,Fs as a4,N2 as a5,Ku as a6,lr as a7,Y1 as a8,qC as a9,WC as aA,OC as aB,kb as aC,Is as aD,gC as aE,UC as aF,Mi as aG,Fr as aH,ba as aI,_C as aJ,Dk as aK,Ai as aL,ka as aM,CC as aN,fu as aO,Sb as aP,Tb as aQ,Xe as aR,hl as aS,_b as aT,Ps as aU,vb as aV,Ab as aW,$r as aX,He as aY,nl as aZ,Ns as a_,Ob as aa,DC as ab,Rs as ac,Nl as ad,uk as ae,FS as af,OS as ag,MS as ah,V as ai,ES as aj,G2 as ak,H2 as al,q2 as am,e0 as an,r0 as ao,za as ap,ag as aq,nr as ar,HC as as,bu as at,qa as au,Ya as av,wa as aw,ku as ax,xu as ay,bC as az,km as b,ps as b0,jC as b1,Ua as b2,IS as b3,Cm as c,ut as d,Er as e,Au as f,bm as g,Ee as h,tr as i,mc as j,ru as k,O as l,Bi as m,wm as n,vm as o,Oy as p,P2 as q,AS as r,xm as s,ym as t,ye as u,LS as v,a1 as w,BS as x,RS as y,DS as z};
//# sourceMappingURL=mermaid.core.DCmZFddM.js.map
