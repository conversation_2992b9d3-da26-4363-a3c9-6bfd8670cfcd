{"version": 3, "file": "Example.B9D3RA0l.js", "sources": ["../../../../../../../markdown/Example.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport { MarkdownCode } from \"@gradio/markdown-code\";\n\n\texport let value: string | null;\n\texport let type: \"gallery\" | \"table\";\n\texport let selected = false;\n\texport let sanitize_html: boolean;\n\texport let line_breaks: boolean;\n\texport let latex_delimiters: {\n\t\tleft: string;\n\t\tright: string;\n\t\tdisplay: boolean;\n\t}[];\n\n\tfunction truncate_text(text: string | null, max_length = 60): string {\n\t\tif (!text) return \"\";\n\t\tconst str = String(text);\n\t\tif (str.length <= max_length) return str;\n\t\treturn str.slice(0, max_length) + \"...\";\n\t}\n</script>\n\n<div\n\tclass:table={type === \"table\"}\n\tclass:gallery={type === \"gallery\"}\n\tclass:selected\n\tclass=\"prose\"\n>\n\t<MarkdownCode\n\t\tmessage={truncate_text(value)}\n\t\t{latex_delimiters}\n\t\t{sanitize_html}\n\t\t{line_breaks}\n\t\tchatbot={false}\n\t/>\n</div>\n\n<style>\n\t.gallery {\n\t\tpadding: var(--size-1) var(--size-2);\n\t}\n</style>\n"], "names": ["truncate_text", "ctx", "toggle_class", "div", "insert_hydration", "target", "anchor", "dirty", "markdowncode_changes", "text", "max_length", "str", "value", "$$props", "type", "selected", "sanitize_html", "line_breaks", "latex_delimiters"], "mappings": "ocA6BW,QAAAA,EAAcC,EAAK,CAAA,CAAA,oEAInB,2KAVGC,EAAAC,EAAA,QAAAF,OAAS,OAAO,EACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,+BAFlCG,EAaKC,EAAAF,EAAAG,CAAA,wCANMC,EAAA,IAAAC,EAAA,QAAAR,EAAcC,EAAK,CAAA,CAAA,+GANhBC,EAAAC,EAAA,QAAAF,OAAS,OAAO,aACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,wHAVxB,SAAAD,EAAcS,EAAqBC,EAAa,GAAA,KACnDD,EAAa,MAAA,GACZ,MAAAE,EAAM,OAAOF,CAAI,EACnB,OAAAE,EAAI,QAAUD,EAAmBC,EAC9BA,EAAI,MAAM,EAAGD,CAAU,EAAI,wBAfxB,GAAA,CAAA,MAAAE,CAAA,EAAAC,EACA,CAAA,KAAAC,CAAA,EAAAD,GACA,SAAAE,EAAW,EAAA,EAAAF,EACX,CAAA,cAAAG,CAAA,EAAAH,EACA,CAAA,YAAAI,CAAA,EAAAJ,EACA,CAAA,iBAAAK,CAAA,EAAAL"}