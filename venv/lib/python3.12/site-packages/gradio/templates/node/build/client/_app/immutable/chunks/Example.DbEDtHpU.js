import{SvelteComponent as q,init as w,safe_not_equal as D,element as u,claim_element as d,children as y,detach as o,attr as N,toggle_class as _,insert_hydration as h,noop as v,ensure_array_like as b,space as O,empty as p,claim_space as S,destroy_each as U,text as k,claim_text as A,append_hydration as m,set_data as j,get_svelte_dataset as z}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";function C(r,e,l){const t=r.slice();return t[3]=e[l],t}function E(r){let e,l=Array.isArray(r[0])&&r[0].length>3,t,f=b(Array.isArray(r[0])?r[0].slice(0,3):[r[0]]),n=[];for(let i=0;i<f.length;i+=1)n[i]=L(C(r,f,i));let a=l&&I();return{c(){for(let i=0;i<n.length;i+=1)n[i].c();e=O(),a&&a.c(),t=p()},l(i){for(let c=0;c<n.length;c+=1)n[c].l(i);e=S(i),a&&a.l(i),t=p()},m(i,c){for(let s=0;s<n.length;s+=1)n[s]&&n[s].m(i,c);h(i,e,c),a&&a.m(i,c),h(i,t,c)},p(i,c){if(c&1){f=b(Array.isArray(i[0])?i[0].slice(0,3):[i[0]]);let s;for(s=0;s<f.length;s+=1){const g=C(i,f,s);n[s]?n[s].p(g,c):(n[s]=L(g),n[s].c(),n[s].m(e.parentNode,e))}for(;s<n.length;s+=1)n[s].d(1);n.length=f.length}c&1&&(l=Array.isArray(i[0])&&i[0].length>3),l?a||(a=I(),a.c(),a.m(t.parentNode,t)):a&&(a.d(1),a=null)},d(i){i&&(o(e),o(t)),U(n,i),a&&a.d(i)}}}function L(r){let e,l,t,f=r[3]+"",n;return{c(){e=u("li"),l=u("code"),t=k("./"),n=k(f)},l(a){e=d(a,"LI",{});var i=y(e);l=d(i,"CODE",{});var c=y(l);t=A(c,"./"),n=A(c,f),c.forEach(o),i.forEach(o)},m(a,i){h(a,e,i),m(e,l),m(l,t),m(l,n)},p(a,i){i&1&&f!==(f=a[3]+"")&&j(n,f)},d(a){a&&o(e)}}}function I(r){let e,l="...";return{c(){e=u("li"),e.textContent=l,this.h()},l(t){e=d(t,"LI",{class:!0,"data-svelte-h":!0}),z(e)!=="svelte-17d9ayl"&&(e.textContent=l),this.h()},h(){N(e,"class","extra svelte-4tf8f")},m(t,f){h(t,e,f)},d(t){t&&o(e)}}}function B(r){let e,l=r[0]&&E(r);return{c(){e=u("ul"),l&&l.c(),this.h()},l(t){e=d(t,"UL",{class:!0});var f=y(e);l&&l.l(f),f.forEach(o),this.h()},h(){N(e,"class","svelte-4tf8f"),_(e,"table",r[1]==="table"),_(e,"gallery",r[1]==="gallery"),_(e,"selected",r[2])},m(t,f){h(t,e,f),l&&l.m(e,null)},p(t,[f]){t[0]?l?l.p(t,f):(l=E(t),l.c(),l.m(e,null)):l&&(l.d(1),l=null),f&2&&_(e,"table",t[1]==="table"),f&2&&_(e,"gallery",t[1]==="gallery"),f&4&&_(e,"selected",t[2])},i:v,o:v,d(t){t&&o(e),l&&l.d()}}}function F(r,e,l){let{value:t}=e,{type:f}=e,{selected:n=!1}=e;return r.$$set=a=>{"value"in a&&l(0,t=a.value),"type"in a&&l(1,f=a.type),"selected"in a&&l(2,n=a.selected)},[t,f,n]}class J extends q{constructor(e){super(),w(this,e,F,B,D,{value:0,type:1,selected:2})}}export{J as default};
//# sourceMappingURL=Example.DbEDtHpU.js.map
