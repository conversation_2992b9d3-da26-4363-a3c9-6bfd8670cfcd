import{SvelteComponent as c,init as d,safe_not_equal as o,element as h,text as m,claim_element as _,children as v,claim_text as y,detach as u,attr as g,toggle_class as f,insert_hydration as b,append_hydration as q,set_data as E,noop as r}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";function k(n){let e,a=(n[0]!==null?n[0]:"")+"",i;return{c(){e=h("div"),i=m(a),this.h()},l(l){e=_(l,"DIV",{class:!0});var t=v(e);i=y(t,a),t.forEach(u),this.h()},h(){g(e,"class","svelte-1ayixqk"),f(e,"table",n[1]==="table"),f(e,"gallery",n[1]==="gallery"),f(e,"selected",n[2])},m(l,t){b(l,e,t),q(e,i)},p(l,[t]){t&1&&a!==(a=(l[0]!==null?l[0]:"")+"")&&E(i,a),t&2&&f(e,"table",l[1]==="table"),t&2&&f(e,"gallery",l[1]==="gallery"),t&4&&f(e,"selected",l[2])},i:r,o:r,d(l){l&&u(e)}}}function C(n,e,a){let{value:i}=e,{type:l}=e,{selected:t=!1}=e;return n.$$set=s=>{"value"in s&&a(0,i=s.value),"type"in s&&a(1,l=s.type),"selected"in s&&a(2,t=s.selected)},[i,l,t]}class S extends c{constructor(e){super(),d(this,e,C,k,o,{value:0,type:1,selected:2})}}export{S as default};
//# sourceMappingURL=Example.Du75eb0D.js.map
