import{SvelteComponent as g,init as f,safe_not_equal as m,svg_element as c,claim_svg_element as h,children as u,detach as s,attr as t,insert_hydration as x,append_hydration as a,noop as p}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";function w(d){let e,r,i,n;return{c(){e=c("svg"),r=c("rect"),i=c("circle"),n=c("polyline"),this.h()},l(l){e=h(l,"svg",{xmlns:!0,width:!0,height:!0,viewBox:!0,fill:!0,stroke:!0,"stroke-width":!0,"stroke-linecap":!0,"stroke-linejoin":!0,class:!0});var o=u(e);r=h(o,"rect",{x:!0,y:!0,width:!0,height:!0,rx:!0,ry:!0}),u(r).forEach(s),i=h(o,"circle",{cx:!0,cy:!0,r:!0}),u(i).forEach(s),n=h(o,"polyline",{points:!0}),u(n).forEach(s),o.forEach(s),this.h()},h(){t(r,"x","3"),t(r,"y","3"),t(r,"width","18"),t(r,"height","18"),t(r,"rx","2"),t(r,"ry","2"),t(i,"cx","8.5"),t(i,"cy","8.5"),t(i,"r","1.5"),t(n,"points","21 15 16 10 5 21"),t(e,"xmlns","http://www.w3.org/2000/svg"),t(e,"width","100%"),t(e,"height","100%"),t(e,"viewBox","0 0 24 24"),t(e,"fill","none"),t(e,"stroke","currentColor"),t(e,"stroke-width","1.5"),t(e,"stroke-linecap","round"),t(e,"stroke-linejoin","round"),t(e,"class","feather feather-image")},m(l,o){x(l,e,o),a(e,r),a(e,i),a(e,n)},p,i:p,o:p,d(l){l&&s(e)}}}class _ extends g{constructor(e){super(),f(this,e,null,w,m,{})}}export{_ as I};
//# sourceMappingURL=Image.CTVzPhL7.js.map
