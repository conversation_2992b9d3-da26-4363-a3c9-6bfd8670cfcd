import{GLTFLoader as h,ArrayItem as m}from"./glTFLoader.BetPWe9U.js";import{b as c,an as _,ao as D}from"./index.BoI39RQH.js";let f=0,d=null;class r{static get Default(){return r._Default||(r._Default=new r),r._Default}constructor(){const e=r.Configuration.decoder;this._decoderModulePromise=c.LoadBabylonScriptAsync(e.url).then(()=>MeshoptDecoder.ready)}dispose(){delete this._decoderModulePromise}decodeGltfBufferAsync(e,s,n,t,o){return this._decoderModulePromise.then(async()=>{f===0&&(MeshoptDecoder.useWorkers(1),f=1);const u=await MeshoptDecoder.decodeGltfBufferAsync(s,n,e,t,o);return d!==null&&clearTimeout(d),d=setTimeout(()=>{MeshoptDecoder.useWorkers(0),f=0,d=null},1e3),u})}}r.Configuration={decoder:{url:`${c._DefaultCdnUrl}/meshopt_decoder.js`}};r._Default=null;const a="EXT_meshopt_compression";class y{constructor(e){this.name=a,this.enabled=e.isExtensionUsed(a),this._loader=e}dispose(){this._loader=null}loadBufferViewAsync(e,s){return h.LoadExtensionAsync(e,s,this.name,(n,t)=>{const o=s;if(o._meshOptData)return o._meshOptData;const u=m.Get(`${e}/buffer`,this._loader.gltf.buffers,t.buffer);return o._meshOptData=this._loader.loadBufferAsync(`/buffers/${u.index}`,u,t.byteOffset||0,t.byteLength).then(i=>r.Default.decodeGltfBufferAsync(i,t.count,t.byteStride,t.mode,t.filter)),o._meshOptData})}}_(a);D(a,!0,l=>new y(l));export{y as EXT_meshopt_compression};
//# sourceMappingURL=EXT_meshopt_compression.CtpJ6PMj.js.map
