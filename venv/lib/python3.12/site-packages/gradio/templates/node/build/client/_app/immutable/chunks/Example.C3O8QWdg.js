import{SvelteComponent as p,init as E,safe_not_equal as I,element as g,space as y,claim_element as h,children as b,claim_space as j,detach as o,src_url_equal as v,attr as c,toggle_class as m,insert_hydration as q,append_hydration as _,noop as z}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";function G(i){let e,s,n,f,r,u,a,d;return{c(){e=g("div"),s=g("img"),f=y(),r=g("img"),a=y(),d=g("span"),this.h()},l(t){e=h(t,"DIV",{class:!0});var l=b(e);s=h(l,"IMG",{src:!0,class:!0}),f=j(l),r=h(l,"IMG",{src:!0,class:!0}),a=j(l),d=h(l,"SPAN",{class:!0}),b(d).forEach(o),l.forEach(o),this.h()},h(){v(s.src,n=i[1]+i[0][0])||c(s,"src",n),c(s,"class","svelte-11djrz8"),v(r.src,u=i[1]+i[0][1])||c(r,"src",u),c(r,"class","svelte-11djrz8"),c(d,"class","svelte-11djrz8"),c(e,"class","wrap svelte-11djrz8"),m(e,"table",i[2]==="table"),m(e,"gallery",i[2]==="gallery"),m(e,"selected",i[3])},m(t,l){q(t,e,l),_(e,s),_(e,f),_(e,r),_(e,a),_(e,d)},p(t,[l]){l&3&&!v(s.src,n=t[1]+t[0][0])&&c(s,"src",n),l&3&&!v(r.src,u=t[1]+t[0][1])&&c(r,"src",u),l&4&&m(e,"table",t[2]==="table"),l&4&&m(e,"gallery",t[2]==="gallery"),l&8&&m(e,"selected",t[3])},i:z,o:z,d(t){t&&o(e)}}}function M(i,e,s){let{value:n}=e,{samples_dir:f}=e,{type:r}=e,{selected:u=!1}=e;return i.$$set=a=>{"value"in a&&s(0,n=a.value),"samples_dir"in a&&s(1,f=a.samples_dir),"type"in a&&s(2,r=a.type),"selected"in a&&s(3,u=a.selected)},[n,f,r,u]}class A extends p{constructor(e){super(),E(this,e,M,G,I,{value:0,samples_dir:1,type:2,selected:3})}}export{A as default};
//# sourceMappingURL=Example.C3O8QWdg.js.map
