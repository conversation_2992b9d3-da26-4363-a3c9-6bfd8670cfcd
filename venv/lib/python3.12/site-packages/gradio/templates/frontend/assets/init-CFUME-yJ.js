import{E as g,U as nt,T as oe,a6 as Q,l as A,d as Se,w as V,I as w,u as M,P as ot,R as J,J as we,M as P,a3 as Z,a4 as Be,c as k,B,$ as U,ac as j,ad as dt,S as E,z as b,ae as ut,af as ee,N as K,ag as z,t as te,x as lt,G as ct,ah as ht,n as Ce,a7 as Pe,s as Re,aa as Me,q as ft,o as pt,p as gt,a8 as mt,a9 as xt,ab as _t,ai as bt,aj as yt,ak as Tt,al as N,am as vt,D as Ue,m as H,V as de,an as F,ao as St,ap as ue,aq as le,e as T,ar as wt}from"./Index-2QduYZJX.js";import{c as I,a as Bt,b as Ct,B as Ge}from"./colorToUniform-KTpA7KSL.js";class Fe{static init(e){Object.defineProperty(this,"resizeTo",{set(t){globalThis.removeEventListener("resize",this.queueResize),this._resizeTo=t,t&&(globalThis.addEventListener("resize",this.queueResize),this.resize())},get(){return this._resizeTo}}),this.queueResize=()=>{this._resizeTo&&(this._cancelResize(),this._resizeId=requestAnimationFrame(()=>this.resize()))},this._cancelResize=()=>{this._resizeId&&(cancelAnimationFrame(this._resizeId),this._resizeId=null)},this.resize=()=>{if(!this._resizeTo)return;this._cancelResize();let t,r;if(this._resizeTo===globalThis.window)t=globalThis.innerWidth,r=globalThis.innerHeight;else{const{clientWidth:i,clientHeight:s}=this._resizeTo;t=i,r=s}this.renderer.resize(t,r),this.render()},this._resizeId=null,this._resizeTo=null,this.resizeTo=e.resizeTo||null}static destroy(){globalThis.removeEventListener("resize",this.queueResize),this._cancelResize(),this._cancelResize=null,this.queueResize=null,this.resizeTo=null,this.resize=null}}Fe.extension=g.Application;class Ae{static init(e){e=Object.assign({autoStart:!0,sharedTicker:!1},e),Object.defineProperty(this,"ticker",{set(t){this._ticker&&this._ticker.remove(this.render,this),this._ticker=t,t&&t.add(this.render,this,nt.LOW)},get(){return this._ticker}}),this.stop=()=>{this._ticker.stop()},this.start=()=>{this._ticker.start()},this._ticker=null,this.ticker=e.sharedTicker?oe.shared:new oe,e.autoStart&&this.start()}static destroy(){if(this._ticker){const e=this._ticker;this.ticker=null,e.destroy()}}}Ae.extension=g.Application;class ke{constructor(e){this._renderer=e}push(e,t,r){this._renderer.renderPipes.batch.break(r),r.add({renderPipeId:"filter",canBundle:!1,action:"pushFilter",container:t,filterEffect:e})}pop(e,t,r){this._renderer.renderPipes.batch.break(r),r.add({renderPipeId:"filter",action:"popFilter",canBundle:!1})}execute(e){e.action==="pushFilter"?this._renderer.filter.push(e):e.action==="popFilter"&&this._renderer.filter.pop()}destroy(){this._renderer=null}}ke.extension={type:[g.WebGLPipes,g.WebGPUPipes,g.CanvasPipes],name:"filter"};function Pt(a,e){e.clear();const t=e.matrix;for(let r=0;r<a.length;r++){const i=a[r];i.globalDisplayStatus<7||(e.matrix=i.worldTransform,e.addBounds(i.bounds))}return e.matrix=t,e}const Rt=new Q({attributes:{aPosition:{buffer:new Float32Array([0,0,1,0,1,1,0,1]),format:"float32x2",stride:2*4,offset:0}},indexBuffer:new Uint32Array([0,1,2,0,2,3])});class He{constructor(e){this._filterStackIndex=0,this._filterStack=[],this._filterGlobalUniforms=new A({uInputSize:{value:new Float32Array(4),type:"vec4<f32>"},uInputPixel:{value:new Float32Array(4),type:"vec4<f32>"},uInputClamp:{value:new Float32Array(4),type:"vec4<f32>"},uOutputFrame:{value:new Float32Array(4),type:"vec4<f32>"},uGlobalFrame:{value:new Float32Array(4),type:"vec4<f32>"},uOutputTexture:{value:new Float32Array(4),type:"vec4<f32>"}}),this._globalFilterBindGroup=new Se({}),this.renderer=e}get activeBackTexture(){return this._activeFilterData?.backTexture}push(e){const t=this.renderer,r=e.filterEffect.filters;this._filterStack[this._filterStackIndex]||(this._filterStack[this._filterStackIndex]=this._getFilterData());const i=this._filterStack[this._filterStackIndex];if(this._filterStackIndex++,r.length===0){i.skip=!0;return}const s=i.bounds;if(e.renderables?Pt(e.renderables,s):e.filterEffect.filterArea?(s.clear(),s.addRect(e.filterEffect.filterArea),s.applyMatrix(e.container.worldTransform)):e.container.getFastGlobalBounds(!0,s),e.container){const h=(e.container.renderGroup||e.container.parentRenderGroup).cacheToLocalTransform;h&&s.applyMatrix(h)}const n=t.renderTarget.renderTarget.colorTexture.source;let o=1/0,d=0,l=!0,c=!1,u=!1,f=!0;for(let p=0;p<r.length;p++){const h=r[p];if(o=Math.min(o,h.resolution==="inherit"?n._resolution:h.resolution),d+=h.padding,h.antialias==="off"?l=!1:h.antialias==="inherit"&&l&&(l=n.antialias),h.clipToViewport||(f=!1),!!!(h.compatibleRenderers&t.type)){u=!1;break}if(h.blendRequired&&!(t.backBuffer?.useBackBuffer??!0)){V("Blend filter requires backBuffer on WebGL renderer to be enabled. Set `useBackBuffer: true` in the renderer options."),u=!1;break}u=h.enabled||u,c||(c=h.blendRequired)}if(!u){i.skip=!0;return}if(f){const p=t.renderTarget.rootViewPort,h=t.renderTarget.renderTarget.resolution;s.fitBounds(0,p.width/h,0,p.height/h)}if(s.scale(o).ceil().scale(1/o).pad(d|0),!s.isPositive){i.skip=!0;return}i.skip=!1,i.bounds=s,i.blendRequired=c,i.container=e.container,i.filterEffect=e.filterEffect,i.previousRenderSurface=t.renderTarget.renderSurface,i.inputTexture=w.getOptimalTexture(s.width,s.height,o,l),t.renderTarget.bind(i.inputTexture,!0),t.globalUniforms.push({offset:s})}pop(){const e=this.renderer;this._filterStackIndex--;const t=this._filterStack[this._filterStackIndex];if(t.skip)return;this._activeFilterData=t;const r=t.inputTexture,i=t.bounds;let s=M.EMPTY;if(e.renderTarget.finishRenderPass(),t.blendRequired){const o=this._filterStackIndex>0?this._filterStack[this._filterStackIndex-1].bounds:null,d=e.renderTarget.getRenderTarget(t.previousRenderSurface);s=this.getBackTexture(d,i,o)}t.backTexture=s;const n=t.filterEffect.filters;if(this._globalFilterBindGroup.setResource(r.source.style,2),this._globalFilterBindGroup.setResource(s.source,3),e.globalUniforms.pop(),n.length===1)n[0].apply(this,r,t.previousRenderSurface,!1),w.returnTexture(r);else{let o=t.inputTexture,d=w.getOptimalTexture(i.width,i.height,o.source._resolution,!1),l=0;for(l=0;l<n.length-1;++l){n[l].apply(this,o,d,!0);const u=o;o=d,d=u}n[l].apply(this,o,t.previousRenderSurface,!1),w.returnTexture(o),w.returnTexture(d)}t.blendRequired&&w.returnTexture(s)}getBackTexture(e,t,r){const i=e.colorTexture.source._resolution,s=w.getOptimalTexture(t.width,t.height,i,!1);let n=t.minX,o=t.minY;r&&(n-=r.minX,o-=r.minY),n=Math.floor(n*i),o=Math.floor(o*i);const d=Math.ceil(t.width*i),l=Math.ceil(t.height*i);return this.renderer.renderTarget.copyToTexture(e,s,{x:n,y:o},{width:d,height:l},{x:0,y:0}),s}applyFilter(e,t,r,i){const s=this.renderer,n=this._filterStack[this._filterStackIndex],o=n.bounds,d=ot.shared,c=n.previousRenderSurface===r;let u=this.renderer.renderTarget.rootRenderTarget.colorTexture.source._resolution,f=this._filterStackIndex-1;for(;f>0&&this._filterStack[f].skip;)--f;f>0&&(u=this._filterStack[f].inputTexture.source._resolution);const p=this._filterGlobalUniforms,h=p.uniforms,m=h.uOutputFrame,_=h.uInputSize,x=h.uInputPixel,v=h.uInputClamp,S=h.uGlobalFrame,C=h.uOutputTexture;if(c){let R=this._filterStackIndex;for(;R>0;){R--;const y=this._filterStack[this._filterStackIndex-1];if(!y.skip){d.x=y.bounds.minX,d.y=y.bounds.minY;break}}m[0]=o.minX-d.x,m[1]=o.minY-d.y}else m[0]=0,m[1]=0;m[2]=t.frame.width,m[3]=t.frame.height,_[0]=t.source.width,_[1]=t.source.height,_[2]=1/_[0],_[3]=1/_[1],x[0]=t.source.pixelWidth,x[1]=t.source.pixelHeight,x[2]=1/x[0],x[3]=1/x[1],v[0]=.5*x[2],v[1]=.5*x[3],v[2]=t.frame.width*_[2]-.5*x[2],v[3]=t.frame.height*_[3]-.5*x[3];const D=this.renderer.renderTarget.rootRenderTarget.colorTexture;S[0]=d.x*u,S[1]=d.y*u,S[2]=D.source.width*u,S[3]=D.source.height*u;const G=this.renderer.renderTarget.getRenderTarget(r);if(s.renderTarget.bind(r,!!i),r instanceof M?(C[0]=r.frame.width,C[1]=r.frame.height):(C[0]=G.width,C[1]=G.height),C[2]=G.isRoot?-1:1,p.update(),s.renderPipes.uniformBatch){const R=s.renderPipes.uniformBatch.getUboResource(p);this._globalFilterBindGroup.setResource(R,0)}else this._globalFilterBindGroup.setResource(p,0);this._globalFilterBindGroup.setResource(t.source,1),this._globalFilterBindGroup.setResource(t.source.style,2),e.groups[0]=this._globalFilterBindGroup,s.encoder.draw({geometry:Rt,shader:e,state:e._state,topology:"triangle-list"}),s.type===J.WEBGL&&s.renderTarget.finishRenderPass()}_getFilterData(){return{skip:!1,inputTexture:null,bounds:new we,container:null,filterEffect:null,blendRequired:!1,previousRenderSurface:null}}calculateSpriteMatrix(e,t){const r=this._activeFilterData,i=e.set(r.inputTexture._source.width,0,0,r.inputTexture._source.height,r.bounds.minX,r.bounds.minY),s=t.worldTransform.copyTo(P.shared),n=t.renderGroup||t.parentRenderGroup;return n&&n.cacheToLocalTransform&&s.prepend(n.cacheToLocalTransform),s.invert(),i.prepend(s),i.scale(1/t.texture.frame.width,1/t.texture.frame.height),i.translate(t.anchor.x,t.anchor.y),i}}He.extension={type:[g.WebGLSystem,g.WebGPUSystem],name:"filter"};const De=class ze extends Q{constructor(...e){let t=e[0]??{};t instanceof Float32Array&&(Z(Be,"use new MeshGeometry({ positions, uvs, indices }) instead"),t={positions:t,uvs:e[1],indices:e[2]}),t={...ze.defaultOptions,...t};const r=t.positions||new Float32Array([0,0,1,0,1,1,0,1]);let i=t.uvs;i||(t.positions?i=new Float32Array(r.length):i=new Float32Array([0,0,1,0,1,1,0,1]));const s=t.indices||new Uint32Array([0,1,2,0,2,3]),n=t.shrinkBuffersToFit,o=new k({data:r,label:"attribute-mesh-positions",shrinkToFit:n,usage:B.VERTEX|B.COPY_DST}),d=new k({data:i,label:"attribute-mesh-uvs",shrinkToFit:n,usage:B.VERTEX|B.COPY_DST}),l=new k({data:s,label:"index-mesh-buffer",shrinkToFit:n,usage:B.INDEX|B.COPY_DST});super({attributes:{aPosition:{buffer:o,format:"float32x2",stride:2*4,offset:0},aUV:{buffer:d,format:"float32x2",stride:2*4,offset:0}},indexBuffer:l,topology:t.topology}),this.batchMode="auto"}get positions(){return this.attributes.aPosition.buffer.data}set positions(e){this.attributes.aPosition.buffer.data=e}get uvs(){return this.attributes.aUV.buffer.data}set uvs(e){this.attributes.aUV.buffer.data=e}get indices(){return this.indexBuffer.data}set indices(e){this.indexBuffer.data=e}};De.defaultOptions={topology:"triangle-list",shrinkBuffersToFit:!1};let re=De;function Mt(a){const e=a._stroke,t=a._fill,i=[`div { ${[`color: ${U.shared.setValue(t.color).toHex()}`,`font-size: ${a.fontSize}px`,`font-family: ${a.fontFamily}`,`font-weight: ${a.fontWeight}`,`font-style: ${a.fontStyle}`,`font-variant: ${a.fontVariant}`,`letter-spacing: ${a.letterSpacing}px`,`text-align: ${a.align}`,`padding: ${a.padding}px`,`white-space: ${a.whiteSpace==="pre"&&a.wordWrap?"pre-wrap":a.whiteSpace}`,...a.lineHeight?[`line-height: ${a.lineHeight}px`]:[],...a.wordWrap?[`word-wrap: ${a.breakWords?"break-all":"break-word"}`,`max-width: ${a.wordWrapWidth}px`]:[],...e?[Oe(e)]:[],...a.dropShadow?[We(a.dropShadow)]:[],...a.cssOverrides].join(";")} }`];return Ut(a.tagStyles,i),i.join(" ")}function We(a){const e=U.shared.setValue(a.color).setAlpha(a.alpha).toHexa(),t=Math.round(Math.cos(a.angle)*a.distance),r=Math.round(Math.sin(a.angle)*a.distance),i=`${t}px ${r}px`;return a.blur>0?`text-shadow: ${i} ${a.blur}px ${e}`:`text-shadow: ${i} ${e}`}function Oe(a){return[`-webkit-text-stroke-width: ${a.width}px`,`-webkit-text-stroke-color: ${U.shared.setValue(a.color).toHex()}`,`text-stroke-width: ${a.width}px`,`text-stroke-color: ${U.shared.setValue(a.color).toHex()}`,"paint-order: stroke"].join(";")}const ce={fontSize:"font-size: {{VALUE}}px",fontFamily:"font-family: {{VALUE}}",fontWeight:"font-weight: {{VALUE}}",fontStyle:"font-style: {{VALUE}}",fontVariant:"font-variant: {{VALUE}}",letterSpacing:"letter-spacing: {{VALUE}}px",align:"text-align: {{VALUE}}",padding:"padding: {{VALUE}}px",whiteSpace:"white-space: {{VALUE}}",lineHeight:"line-height: {{VALUE}}px",wordWrapWidth:"max-width: {{VALUE}}px"},he={fill:a=>`color: ${U.shared.setValue(a).toHex()}`,breakWords:a=>`word-wrap: ${a?"break-all":"break-word"}`,stroke:Oe,dropShadow:We};function Ut(a,e){for(const t in a){const r=a[t],i=[];for(const s in r)he[s]?i.push(he[s](r[s])):ce[s]&&i.push(ce[s].replace("{{VALUE}}",r[s]));e.push(`${t} { ${i.join(";")} }`)}}class ie extends j{constructor(e={}){super(e),this._cssOverrides=[],this.cssOverrides??(this.cssOverrides=e.cssOverrides),this.tagStyles=e.tagStyles??{}}set cssOverrides(e){this._cssOverrides=e instanceof Array?e:[e],this.update()}get cssOverrides(){return this._cssOverrides}_generateKey(){return this._styleKey=dt(this)+this._cssOverrides.join("-"),this._styleKey}update(){this._cssStyle=null,super.update()}clone(){return new ie({align:this.align,breakWords:this.breakWords,dropShadow:this.dropShadow?{...this.dropShadow}:null,fill:this._fill,fontFamily:this.fontFamily,fontSize:this.fontSize,fontStyle:this.fontStyle,fontVariant:this.fontVariant,fontWeight:this.fontWeight,letterSpacing:this.letterSpacing,lineHeight:this.lineHeight,padding:this.padding,stroke:this._stroke,whiteSpace:this.whiteSpace,wordWrap:this.wordWrap,wordWrapWidth:this.wordWrapWidth,cssOverrides:this.cssOverrides})}get cssStyle(){return this._cssStyle||(this._cssStyle=Mt(this)),this._cssStyle}addOverride(...e){const t=e.filter(r=>!this.cssOverrides.includes(r));t.length>0&&(this.cssOverrides.push(...t),this.update())}removeOverride(...e){const t=e.filter(r=>this.cssOverrides.includes(r));t.length>0&&(this.cssOverrides=this.cssOverrides.filter(r=>!t.includes(r)),this.update())}set fill(e){typeof e!="string"&&typeof e!="number"&&V("[HTMLTextStyle] only color fill is not supported by HTMLText"),super.fill=e}set stroke(e){e&&typeof e!="string"&&typeof e!="number"&&V("[HTMLTextStyle] only color stroke is not supported by HTMLText"),super.stroke=e}}const fe="http://www.w3.org/2000/svg",pe="http://www.w3.org/1999/xhtml";class Ve{constructor(){this.svgRoot=document.createElementNS(fe,"svg"),this.foreignObject=document.createElementNS(fe,"foreignObject"),this.domElement=document.createElementNS(pe,"div"),this.styleElement=document.createElementNS(pe,"style"),this.image=new Image;const{foreignObject:e,svgRoot:t,styleElement:r,domElement:i}=this;e.setAttribute("width","10000"),e.setAttribute("height","10000"),e.style.overflow="hidden",t.appendChild(e),e.appendChild(r),e.appendChild(i)}}let ge;function Gt(a,e,t,r){r||(r=ge||(ge=new Ve));const{domElement:i,styleElement:s,svgRoot:n}=r;i.innerHTML=`<style>${e.cssStyle};</style><div style='padding:0'>${a}</div>`,i.setAttribute("style","transform-origin: top left; display: inline-block"),t&&(s.textContent=t),document.body.appendChild(n);const o=i.getBoundingClientRect();n.remove();const d=e.padding*2;return{width:o.width-d,height:o.height-d}}class Ee{constructor(e,t){this.state=E.for2d(),this._graphicsBatchesHash=Object.create(null),this._destroyRenderableBound=this.destroyRenderable.bind(this),this.renderer=e,this._adaptor=t,this._adaptor.init(),this.renderer.renderableGC.addManagedHash(this,"_graphicsBatchesHash")}validateRenderable(e){const t=e.context,r=!!this._graphicsBatchesHash[e.uid],i=this.renderer.graphicsContext.updateGpuContext(t);return!!(i.isBatchable||r!==i.isBatchable)}addRenderable(e,t){const r=this.renderer.graphicsContext.updateGpuContext(e.context);e.didViewUpdate&&this._rebuild(e),r.isBatchable?this._addToBatcher(e,t):(this.renderer.renderPipes.batch.break(t),t.add(e))}updateRenderable(e){const t=this._graphicsBatchesHash[e.uid];if(t)for(let r=0;r<t.length;r++){const i=t[r];i._batcher.updateElement(i)}}destroyRenderable(e){this._graphicsBatchesHash[e.uid]&&this._removeBatchForRenderable(e.uid),e.off("destroyed",this._destroyRenderableBound)}execute(e){if(!e.isRenderable)return;const t=this.renderer,r=e.context;if(!t.graphicsContext.getGpuContext(r).batches.length)return;const s=r.customShader||this._adaptor.shader;this.state.blendMode=e.groupBlendMode;const n=s.resources.localUniforms.uniforms;n.uTransformMatrix=e.groupTransform,n.uRound=t._roundPixels|e._roundPixels,I(e.groupColorAlpha,n.uColor,0),this._adaptor.execute(this,e)}_rebuild(e){const t=!!this._graphicsBatchesHash[e.uid],r=this.renderer.graphicsContext.updateGpuContext(e.context);t&&this._removeBatchForRenderable(e.uid),r.isBatchable&&this._initBatchesForRenderable(e),e.batched=r.isBatchable}_addToBatcher(e,t){const r=this.renderer.renderPipes.batch,i=this._getBatchesForRenderable(e);for(let s=0;s<i.length;s++){const n=i[s];r.addToBatch(n,t)}}_getBatchesForRenderable(e){return this._graphicsBatchesHash[e.uid]||this._initBatchesForRenderable(e)}_initBatchesForRenderable(e){const t=e.context,r=this.renderer.graphicsContext.getGpuContext(t),i=this.renderer._roundPixels|e._roundPixels,s=r.batches.map(n=>{const o=b.get(ut);return n.copyTo(o),o.renderable=e,o.roundPixels=i,o});return this._graphicsBatchesHash[e.uid]===void 0&&e.on("destroyed",this._destroyRenderableBound),this._graphicsBatchesHash[e.uid]=s,s}_removeBatchForRenderable(e){this._graphicsBatchesHash[e].forEach(t=>{b.return(t)}),this._graphicsBatchesHash[e]=null}destroy(){this.renderer=null,this._adaptor.destroy(),this._adaptor=null,this.state=null;for(const e in this._graphicsBatchesHash)this._removeBatchForRenderable(e);this._graphicsBatchesHash=null}}Ee.extension={type:[g.WebGLPipes,g.WebGPUPipes,g.CanvasPipes],name:"graphics"};const Ie=class Le extends re{constructor(...e){super({});let t=e[0]??{};typeof t=="number"&&(Z(Be,"PlaneGeometry constructor changed please use { width, height, verticesX, verticesY } instead"),t={width:t,height:e[1],verticesX:e[2],verticesY:e[3]}),this.build(t)}build(e){e={...Le.defaultOptions,...e},this.verticesX=this.verticesX??e.verticesX,this.verticesY=this.verticesY??e.verticesY,this.width=this.width??e.width,this.height=this.height??e.height;const t=this.verticesX*this.verticesY,r=[],i=[],s=[],n=this.verticesX-1,o=this.verticesY-1,d=this.width/n,l=this.height/o;for(let u=0;u<t;u++){const f=u%this.verticesX,p=u/this.verticesX|0;r.push(f*d,p*l),i.push(f/n,p/o)}const c=n*o;for(let u=0;u<c;u++){const f=u%n,p=u/n|0,h=p*this.verticesX+f,m=p*this.verticesX+f+1,_=(p+1)*this.verticesX+f,x=(p+1)*this.verticesX+f+1;s.push(h,m,_,m,x,_)}this.buffers[0].data=new Float32Array(r),this.buffers[1].data=new Float32Array(i),this.indexBuffer.data=new Uint32Array(s),this.buffers[0].update(),this.buffers[1].update(),this.indexBuffer.update()}};Ie.defaultOptions={width:100,height:100,verticesX:10,verticesY:10};let Ft=Ie;class se{constructor(){this.batcherName="default",this.packAsQuad=!1,this.indexOffset=0,this.attributeOffset=0,this.roundPixels=0,this._batcher=null,this._batch=null,this._uvUpdateId=-1,this._textureMatrixUpdateId=-1}get blendMode(){return this.renderable.groupBlendMode}get topology(){return this._topology||this.geometry.topology}set topology(e){this._topology=e}reset(){this.renderable=null,this.texture=null,this._batcher=null,this._batch=null,this.geometry=null,this._uvUpdateId=-1,this._textureMatrixUpdateId=-1}setTexture(e){this.texture!==e&&(this.texture=e,this._textureMatrixUpdateId=-1)}get uvs(){const t=this.geometry.getBuffer("aUV"),r=t.data;let i=r;const s=this.texture.textureMatrix;return s.isSimple||(i=this._transformedUvs,(this._textureMatrixUpdateId!==s._updateID||this._uvUpdateId!==t._updateID)&&((!i||i.length<r.length)&&(i=this._transformedUvs=new Float32Array(r.length)),this._textureMatrixUpdateId=s._updateID,this._uvUpdateId=t._updateID,s.multiplyUvs(r,i))),i}get positions(){return this.geometry.positions}get indices(){return this.geometry.indices}get color(){return this.renderable.groupColorAlpha}get groupTransform(){return this.renderable.groupTransform}get attributeSize(){return this.geometry.positions.length/2}get indexSize(){return this.geometry.indices.length}}class $e{constructor(e,t){this.localUniforms=new A({uTransformMatrix:{value:new P,type:"mat3x3<f32>"},uColor:{value:new Float32Array([1,1,1,1]),type:"vec4<f32>"},uRound:{value:0,type:"f32"}}),this.localUniformsBindGroup=new Se({0:this.localUniforms}),this._meshDataHash=Object.create(null),this._gpuBatchableMeshHash=Object.create(null),this._destroyRenderableBound=this.destroyRenderable.bind(this),this.renderer=e,this._adaptor=t,this._adaptor.init(),e.renderableGC.addManagedHash(this,"_gpuBatchableMeshHash"),e.renderableGC.addManagedHash(this,"_meshDataHash")}validateRenderable(e){const t=this._getMeshData(e),r=t.batched,i=e.batched;if(t.batched=i,r!==i)return!0;if(i){const s=e._geometry;if(s.indices.length!==t.indexSize||s.positions.length!==t.vertexSize)return t.indexSize=s.indices.length,t.vertexSize=s.positions.length,!0;const n=this._getBatchableMesh(e);return!n._batcher.checkAndUpdateTexture(n,e.texture)}return!1}addRenderable(e,t){const r=this.renderer.renderPipes.batch,{batched:i}=this._getMeshData(e);if(i){const s=this._getBatchableMesh(e);s.texture=e._texture,s.geometry=e._geometry,r.addToBatch(s,t)}else r.break(t),t.add(e)}updateRenderable(e){if(e.batched){const t=this._gpuBatchableMeshHash[e.uid];t.setTexture(e._texture),t.geometry=e._geometry,t._batcher.updateElement(t)}}destroyRenderable(e){this._meshDataHash[e.uid]=null;const t=this._gpuBatchableMeshHash[e.uid];t&&(b.return(t),this._gpuBatchableMeshHash[e.uid]=null),e.off("destroyed",this._destroyRenderableBound)}execute(e){if(!e.isRenderable)return;e.state.blendMode=ee(e.groupBlendMode,e.texture._source);const t=this.localUniforms;t.uniforms.uTransformMatrix=e.groupTransform,t.uniforms.uRound=this.renderer._roundPixels|e._roundPixels,t.update(),I(e.groupColorAlpha,t.uniforms.uColor,0),this._adaptor.execute(this,e)}_getMeshData(e){return this._meshDataHash[e.uid]||this._initMeshData(e)}_initMeshData(e){return this._meshDataHash[e.uid]={batched:e.batched,indexSize:e._geometry.indices?.length,vertexSize:e._geometry.positions?.length},e.on("destroyed",this._destroyRenderableBound),this._meshDataHash[e.uid]}_getBatchableMesh(e){return this._gpuBatchableMeshHash[e.uid]||this._initBatchableMesh(e)}_initBatchableMesh(e){const t=b.get(se);return t.renderable=e,t.texture=e._texture,t.transform=e.groupTransform,t.roundPixels=this.renderer._roundPixels|e._roundPixels,this._gpuBatchableMeshHash[e.uid]=t,t}destroy(){for(const e in this._gpuBatchableMeshHash)this._gpuBatchableMeshHash[e]&&b.return(this._gpuBatchableMeshHash[e]);this._gpuBatchableMeshHash=null,this._meshDataHash=null,this.localUniforms=null,this.localUniformsBindGroup=null,this._adaptor.destroy(),this._adaptor=null,this.renderer=null}}$e.extension={type:[g.WebGLPipes,g.WebGPUPipes,g.CanvasPipes],name:"mesh"};class At{execute(e,t){const r=e.state,i=e.renderer,s=t.shader||e.defaultShader;s.resources.uTexture=t.texture._source,s.resources.uniforms=e.localUniforms;const n=i.gl,o=e.getBuffers(t);i.shader.bind(s),i.state.set(r),i.geometry.bind(o.geometry,s.glProgram);const l=o.geometry.indexBuffer.data.BYTES_PER_ELEMENT===2?n.UNSIGNED_SHORT:n.UNSIGNED_INT;n.drawElements(n.TRIANGLES,t.particleChildren.length*6,l,0)}}class kt{execute(e,t){const r=e.renderer,i=t.shader||e.defaultShader;i.groups[0]=r.renderPipes.uniformBatch.getUniformBindGroup(e.localUniforms,!0),i.groups[1]=r.texture.getTextureBindGroup(t.texture);const s=e.state,n=e.getBuffers(t);r.encoder.draw({geometry:n.geometry,shader:t.shader||e.defaultShader,state:s,size:t.particleChildren.length*6})}}function me(a,e=null){const t=a*6;if(t>65535?e||(e=new Uint32Array(t)):e||(e=new Uint16Array(t)),e.length!==t)throw new Error(`Out buffer length is incorrect, got ${e.length} and expected ${t}`);for(let r=0,i=0;r<t;r+=6,i+=4)e[r+0]=i+0,e[r+1]=i+1,e[r+2]=i+2,e[r+3]=i+0,e[r+4]=i+2,e[r+5]=i+3;return e}function Ht(a){return{dynamicUpdate:xe(a,!0),staticUpdate:xe(a,!1)}}function xe(a,e){const t=[];t.push(`
      
        var index = 0;

        for (let i = 0; i < ps.length; ++i)
        {
            const p = ps[i];

            `);let r=0;for(const s in a){const n=a[s];if(e!==n.dynamic)continue;t.push(`offset = index + ${r}`),t.push(n.code);const o=K(n.format);r+=o.stride/4}t.push(`
            index += stride * 4;
        }
    `),t.unshift(`
        var stride = ${r};
    `);const i=t.join(`
`);return new Function("ps","f32v","u32v",i)}class Dt{constructor(e){this._size=0,this._generateParticleUpdateCache={};const t=this._size=e.size??1e3,r=e.properties;let i=0,s=0;for(const c in r){const u=r[c],f=K(u.format);u.dynamic?s+=f.stride:i+=f.stride}this._dynamicStride=s/4,this._staticStride=i/4,this.staticAttributeBuffer=new z(t*4*i),this.dynamicAttributeBuffer=new z(t*4*s),this.indexBuffer=me(t);const n=new Q;let o=0,d=0;this._staticBuffer=new k({data:new Float32Array(1),label:"static-particle-buffer",shrinkToFit:!1,usage:B.VERTEX|B.COPY_DST}),this._dynamicBuffer=new k({data:new Float32Array(1),label:"dynamic-particle-buffer",shrinkToFit:!1,usage:B.VERTEX|B.COPY_DST});for(const c in r){const u=r[c],f=K(u.format);u.dynamic?(n.addAttribute(u.attributeName,{buffer:this._dynamicBuffer,stride:this._dynamicStride*4,offset:o*4,format:u.format}),o+=f.size):(n.addAttribute(u.attributeName,{buffer:this._staticBuffer,stride:this._staticStride*4,offset:d*4,format:u.format}),d+=f.size)}n.addIndex(this.indexBuffer);const l=this.getParticleUpdate(r);this._dynamicUpload=l.dynamicUpdate,this._staticUpload=l.staticUpdate,this.geometry=n}getParticleUpdate(e){const t=zt(e);return this._generateParticleUpdateCache[t]?this._generateParticleUpdateCache[t]:(this._generateParticleUpdateCache[t]=this.generateParticleUpdate(e),this._generateParticleUpdateCache[t])}generateParticleUpdate(e){return Ht(e)}update(e,t){e.length>this._size&&(t=!0,this._size=Math.max(e.length,this._size*1.5|0),this.staticAttributeBuffer=new z(this._size*this._staticStride*4*4),this.dynamicAttributeBuffer=new z(this._size*this._dynamicStride*4*4),this.indexBuffer=me(this._size),this.geometry.indexBuffer.setDataWithSize(this.indexBuffer,this.indexBuffer.byteLength,!0));const r=this.dynamicAttributeBuffer;if(this._dynamicUpload(e,r.float32View,r.uint32View),this._dynamicBuffer.setDataWithSize(this.dynamicAttributeBuffer.float32View,e.length*this._dynamicStride*4,!0),t){const i=this.staticAttributeBuffer;this._staticUpload(e,i.float32View,i.uint32View),this._staticBuffer.setDataWithSize(i.float32View,e.length*this._staticStride*4,!0)}}destroy(){this._staticBuffer.destroy(),this._dynamicBuffer.destroy(),this.geometry.destroy()}}function zt(a){const e=[];for(const t in a){const r=a[t];e.push(t,r.code,r.dynamic?"d":"s")}return e.join("_")}var Wt=`varying vec2 vUV;
varying vec4 vColor;

uniform sampler2D uTexture;

void main(void){
    vec4 color = texture2D(uTexture, vUV) * vColor;
    gl_FragColor = color;
}`,Ot=`attribute vec2 aVertex;
attribute vec2 aUV;
attribute vec4 aColor;

attribute vec2 aPosition;
attribute float aRotation;

uniform mat3 uTranslationMatrix;
uniform float uRound;
uniform vec2 uResolution;
uniform vec4 uColor;

varying vec2 vUV;
varying vec4 vColor;

vec2 roundPixels(vec2 position, vec2 targetSize)
{       
    return (floor(((position * 0.5 + 0.5) * targetSize) + 0.5) / targetSize) * 2.0 - 1.0;
}

void main(void){
    float cosRotation = cos(aRotation);
    float sinRotation = sin(aRotation);
    float x = aVertex.x * cosRotation - aVertex.y * sinRotation;
    float y = aVertex.x * sinRotation + aVertex.y * cosRotation;

    vec2 v = vec2(x, y);
    v = v + aPosition;

    gl_Position = vec4((uTranslationMatrix * vec3(v, 1.0)).xy, 0.0, 1.0);

    if(uRound == 1.0)
    {
        gl_Position.xy = roundPixels(gl_Position.xy, uResolution);
    }

    vUV = aUV;
    vColor = vec4(aColor.rgb * aColor.a, aColor.a) * uColor;
}
`,_e=`
struct ParticleUniforms {
  uProjectionMatrix:mat3x3<f32>,
  uColor:vec4<f32>,
  uResolution:vec2<f32>,
  uRoundPixels:f32,
};

@group(0) @binding(0) var<uniform> uniforms: ParticleUniforms;

@group(1) @binding(0) var uTexture: texture_2d<f32>;
@group(1) @binding(1) var uSampler : sampler;

struct VSOutput {
    @builtin(position) position: vec4<f32>,
    @location(0) uv : vec2<f32>,
    @location(1) color : vec4<f32>,
  };
@vertex
fn mainVertex(
  @location(0) aVertex: vec2<f32>,
  @location(1) aPosition: vec2<f32>,
  @location(2) aUV: vec2<f32>,
  @location(3) aColor: vec4<f32>,
  @location(4) aRotation: f32,
) -> VSOutput {
  
   let v = vec2(
       aVertex.x * cos(aRotation) - aVertex.y * sin(aRotation),
       aVertex.x * sin(aRotation) + aVertex.y * cos(aRotation)
   ) + aPosition;

   let position = vec4((uniforms.uProjectionMatrix * vec3(v, 1.0)).xy, 0.0, 1.0);

    let vColor = vec4(aColor.rgb * aColor.a, aColor.a) * uniforms.uColor;

  return VSOutput(
   position,
   aUV,
   vColor,
  );
}

@fragment
fn mainFragment(
  @location(0) uv: vec2<f32>,
  @location(1) color: vec4<f32>,
  @builtin(position) position: vec4<f32>,
) -> @location(0) vec4<f32> {

    var sample = textureSample(uTexture, uSampler, uv) * color;
   
    return sample;
}`;class Vt extends te{constructor(){const e=lt.from({vertex:Ot,fragment:Wt}),t=ct.from({fragment:{source:_e,entryPoint:"mainFragment"},vertex:{source:_e,entryPoint:"mainVertex"}});super({glProgram:e,gpuProgram:t,resources:{uTexture:M.WHITE.source,uSampler:new ht({}),uniforms:{uTranslationMatrix:{value:new P,type:"mat3x3<f32>"},uColor:{value:new U(16777215),type:"vec4<f32>"},uRound:{value:1,type:"f32"},uResolution:{value:[0,0],type:"vec2<f32>"}}}})}}class Ye{constructor(e,t){this.state=E.for2d(),this._gpuBufferHash=Object.create(null),this._destroyRenderableBound=this.destroyRenderable.bind(this),this.localUniforms=new A({uTranslationMatrix:{value:new P,type:"mat3x3<f32>"},uColor:{value:new Float32Array(4),type:"vec4<f32>"},uRound:{value:1,type:"f32"},uResolution:{value:[0,0],type:"vec2<f32>"}}),this.renderer=e,this.adaptor=t,this.defaultShader=new Vt,this.state=E.for2d()}validateRenderable(e){return!1}addRenderable(e,t){this.renderer.renderPipes.batch.break(t),t.add(e)}getBuffers(e){return this._gpuBufferHash[e.uid]||this._initBuffer(e)}_initBuffer(e){return this._gpuBufferHash[e.uid]=new Dt({size:e.particleChildren.length,properties:e._properties}),e.on("destroyed",this._destroyRenderableBound),this._gpuBufferHash[e.uid]}updateRenderable(e){}destroyRenderable(e){this._gpuBufferHash[e.uid].destroy(),this._gpuBufferHash[e.uid]=null,e.off("destroyed",this._destroyRenderableBound)}execute(e){const t=e.particleChildren;if(t.length===0)return;const r=this.renderer,i=this.getBuffers(e);e.texture||(e.texture=t[0].texture);const s=this.state;i.update(t,e._childrenDirty),e._childrenDirty=!1,s.blendMode=ee(e.blendMode,e.texture._source);const n=this.localUniforms.uniforms,o=n.uTranslationMatrix;e.worldTransform.copyTo(o),o.prepend(r.globalUniforms.globalUniformData.projectionMatrix),n.uResolution=r.globalUniforms.globalUniformData.resolution,n.uRound=r._roundPixels|e._roundPixels,I(e.groupColorAlpha,n.uColor,0),this.adaptor.execute(this,e)}destroy(){this.defaultShader&&(this.defaultShader.destroy(),this.defaultShader=null)}}class Xe extends Ye{constructor(e){super(e,new At)}}Xe.extension={type:[g.WebGLPipes],name:"particle"};class je extends Ye{constructor(e){super(e,new kt)}}je.extension={type:[g.WebGPUPipes],name:"particle"};const Ke=class Ne extends Ft{constructor(e={}){e={...Ne.defaultOptions,...e},super({width:e.width,height:e.height,verticesX:4,verticesY:4}),this.update(e)}update(e){this.width=e.width??this.width,this.height=e.height??this.height,this._originalWidth=e.originalWidth??this._originalWidth,this._originalHeight=e.originalHeight??this._originalHeight,this._leftWidth=e.leftWidth??this._leftWidth,this._rightWidth=e.rightWidth??this._rightWidth,this._topHeight=e.topHeight??this._topHeight,this._bottomHeight=e.bottomHeight??this._bottomHeight,this.updateUvs(),this.updatePositions()}updatePositions(){const e=this.positions,t=this._leftWidth+this._rightWidth,r=this.width>t?1:this.width/t,i=this._topHeight+this._bottomHeight,s=this.height>i?1:this.height/i,n=Math.min(r,s);e[9]=e[11]=e[13]=e[15]=this._topHeight*n,e[17]=e[19]=e[21]=e[23]=this.height-this._bottomHeight*n,e[25]=e[27]=e[29]=e[31]=this.height,e[2]=e[10]=e[18]=e[26]=this._leftWidth*n,e[4]=e[12]=e[20]=e[28]=this.width-this._rightWidth*n,e[6]=e[14]=e[22]=e[30]=this.width,this.getBuffer("aPosition").update()}updateUvs(){const e=this.uvs;e[0]=e[8]=e[16]=e[24]=0,e[1]=e[3]=e[5]=e[7]=0,e[6]=e[14]=e[22]=e[30]=1,e[25]=e[27]=e[29]=e[31]=1;const t=1/this._originalWidth,r=1/this._originalHeight;e[2]=e[10]=e[18]=e[26]=t*this._leftWidth,e[9]=e[11]=e[13]=e[15]=r*this._topHeight,e[4]=e[12]=e[20]=e[28]=1-t*this._rightWidth,e[17]=e[19]=e[21]=e[23]=1-r*this._bottomHeight,this.getBuffer("aUV").update()}};Ke.defaultOptions={width:100,height:100,leftWidth:10,topHeight:10,rightWidth:10,bottomHeight:10,originalWidth:100,originalHeight:100};let Et=Ke;class qe{constructor(e){this._gpuSpriteHash=Object.create(null),this._destroyRenderableBound=this.destroyRenderable.bind(this),this._renderer=e,this._renderer.renderableGC.addManagedHash(this,"_gpuSpriteHash")}addRenderable(e,t){const r=this._getGpuSprite(e);e.didViewUpdate&&this._updateBatchableSprite(e,r),this._renderer.renderPipes.batch.addToBatch(r,t)}updateRenderable(e){const t=this._gpuSpriteHash[e.uid];e.didViewUpdate&&this._updateBatchableSprite(e,t),t._batcher.updateElement(t)}validateRenderable(e){const t=this._getGpuSprite(e);return!t._batcher.checkAndUpdateTexture(t,e._texture)}destroyRenderable(e){const t=this._gpuSpriteHash[e.uid];b.return(t.geometry),b.return(t),this._gpuSpriteHash[e.uid]=null,e.off("destroyed",this._destroyRenderableBound)}_updateBatchableSprite(e,t){t.geometry.update(e),t.setTexture(e._texture)}_getGpuSprite(e){return this._gpuSpriteHash[e.uid]||this._initGPUSprite(e)}_initGPUSprite(e){const t=b.get(se);return t.geometry=b.get(Et),t.renderable=e,t.transform=e.groupTransform,t.texture=e._texture,t.roundPixels=this._renderer._roundPixels|e._roundPixels,this._gpuSpriteHash[e.uid]=t,e.didViewUpdate||this._updateBatchableSprite(e,t),e.on("destroyed",this._destroyRenderableBound),t}destroy(){for(const e in this._gpuSpriteHash)this._gpuSpriteHash[e].geometry.destroy();this._gpuSpriteHash=null,this._renderer=null}}qe.extension={type:[g.WebGLPipes,g.WebGPUPipes,g.CanvasPipes],name:"nineSliceSprite"};const It={name:"tiling-bit",vertex:{header:`
            struct TilingUniforms {
                uMapCoord:mat3x3<f32>,
                uClampFrame:vec4<f32>,
                uClampOffset:vec2<f32>,
                uTextureTransform:mat3x3<f32>,
                uSizeAnchor:vec4<f32>
            };

            @group(2) @binding(0) var<uniform> tilingUniforms: TilingUniforms;
            @group(2) @binding(1) var uTexture: texture_2d<f32>;
            @group(2) @binding(2) var uSampler: sampler;
        `,main:`
            uv = (tilingUniforms.uTextureTransform * vec3(uv, 1.0)).xy;

            position = (position - tilingUniforms.uSizeAnchor.zw) * tilingUniforms.uSizeAnchor.xy;
        `},fragment:{header:`
            struct TilingUniforms {
                uMapCoord:mat3x3<f32>,
                uClampFrame:vec4<f32>,
                uClampOffset:vec2<f32>,
                uTextureTransform:mat3x3<f32>,
                uSizeAnchor:vec4<f32>
            };

            @group(2) @binding(0) var<uniform> tilingUniforms: TilingUniforms;
            @group(2) @binding(1) var uTexture: texture_2d<f32>;
            @group(2) @binding(2) var uSampler: sampler;
        `,main:`

            var coord = vUV + ceil(tilingUniforms.uClampOffset - vUV);
            coord = (tilingUniforms.uMapCoord * vec3(coord, 1.0)).xy;
            var unclamped = coord;
            coord = clamp(coord, tilingUniforms.uClampFrame.xy, tilingUniforms.uClampFrame.zw);

            var bias = 0.;

            if(unclamped.x == coord.x && unclamped.y == coord.y)
            {
                bias = -32.;
            } 

            outColor = textureSampleBias(uTexture, uSampler, coord, bias);
        `}},Lt={name:"tiling-bit",vertex:{header:`
            uniform mat3 uTextureTransform;
            uniform vec4 uSizeAnchor;
        
        `,main:`
            uv = (uTextureTransform * vec3(aUV, 1.0)).xy;

            position = (position - uSizeAnchor.zw) * uSizeAnchor.xy;
        `},fragment:{header:`
            uniform sampler2D uTexture;
            uniform mat3 uMapCoord;
            uniform vec4 uClampFrame;
            uniform vec2 uClampOffset;
        `,main:`

        vec2 coord = vUV + ceil(uClampOffset - vUV);
        coord = (uMapCoord * vec3(coord, 1.0)).xy;
        vec2 unclamped = coord;
        coord = clamp(coord, uClampFrame.xy, uClampFrame.zw);
        
        outColor = texture(uTexture, coord, unclamped == coord ? 0.0 : -32.0);// lod-bias very negative to force lod 0
    
        `}};let L,$;class $t extends te{constructor(){L??(L=Ce({name:"tiling-sprite-shader",bits:[Bt,It,Re]})),$??($=Pe({name:"tiling-sprite-shader",bits:[Ct,Lt,Me]}));const e=new A({uMapCoord:{value:new P,type:"mat3x3<f32>"},uClampFrame:{value:new Float32Array([0,0,1,1]),type:"vec4<f32>"},uClampOffset:{value:new Float32Array([0,0]),type:"vec2<f32>"},uTextureTransform:{value:new P,type:"mat3x3<f32>"},uSizeAnchor:{value:new Float32Array([100,100,.5,.5]),type:"vec4<f32>"}});super({glProgram:$,gpuProgram:L,resources:{localUniforms:new A({uTransformMatrix:{value:new P,type:"mat3x3<f32>"},uColor:{value:new Float32Array([1,1,1,1]),type:"vec4<f32>"},uRound:{value:0,type:"f32"}}),tilingUniforms:e,uTexture:M.EMPTY.source,uSampler:M.EMPTY.source.style}})}updateUniforms(e,t,r,i,s,n){const o=this.resources.tilingUniforms,d=n.width,l=n.height,c=n.textureMatrix,u=o.uniforms.uTextureTransform;u.set(r.a*d/e,r.b*d/t,r.c*l/e,r.d*l/t,r.tx/e,r.ty/t),u.invert(),o.uniforms.uMapCoord=c.mapCoord,o.uniforms.uClampFrame=c.uClampFrame,o.uniforms.uClampOffset=c.uClampOffset,o.uniforms.uTextureTransform=u,o.uniforms.uSizeAnchor[0]=e,o.uniforms.uSizeAnchor[1]=t,o.uniforms.uSizeAnchor[2]=i,o.uniforms.uSizeAnchor[3]=s,n&&(this.resources.uTexture=n.source,this.resources.uSampler=n.source.style)}}class Yt extends re{constructor(){super({positions:new Float32Array([0,0,1,0,1,1,0,1]),uvs:new Float32Array([0,0,1,0,1,1,0,1]),indices:new Uint32Array([0,1,2,0,2,3])})}}function Xt(a,e){const t=a.anchor.x,r=a.anchor.y;e[0]=-t*a.width,e[1]=-r*a.height,e[2]=(1-t)*a.width,e[3]=-r*a.height,e[4]=(1-t)*a.width,e[5]=(1-r)*a.height,e[6]=-t*a.width,e[7]=(1-r)*a.height}function jt(a,e,t,r){let i=0;const s=a.length/e,n=r.a,o=r.b,d=r.c,l=r.d,c=r.tx,u=r.ty;for(t*=e;i<s;){const f=a[t],p=a[t+1];a[t]=n*f+d*p+c,a[t+1]=o*f+l*p+u,t+=e,i++}}function Kt(a,e){const t=a.texture,r=t.frame.width,i=t.frame.height;let s=0,n=0;a.applyAnchorToTexture&&(s=a.anchor.x,n=a.anchor.y),e[0]=e[6]=-s,e[2]=e[4]=1-s,e[1]=e[3]=-n,e[5]=e[7]=1-n;const o=P.shared;o.copyFrom(a._tileTransform.matrix),o.tx/=a.width,o.ty/=a.height,o.invert(),o.scale(a.width/r,a.height/i),jt(e,2,0,o)}const W=new Yt;class Qe{constructor(e){this._state=E.default2d,this._tilingSpriteDataHash=Object.create(null),this._destroyRenderableBound=this.destroyRenderable.bind(this),this._renderer=e,this._renderer.renderableGC.addManagedHash(this,"_tilingSpriteDataHash")}validateRenderable(e){const t=this._getTilingSpriteData(e),r=t.canBatch;this._updateCanBatch(e);const i=t.canBatch;if(i&&i===r){const{batchableMesh:s}=t;return!s._batcher.checkAndUpdateTexture(s,e.texture)}return r!==i}addRenderable(e,t){const r=this._renderer.renderPipes.batch;this._updateCanBatch(e);const i=this._getTilingSpriteData(e),{geometry:s,canBatch:n}=i;if(n){i.batchableMesh||(i.batchableMesh=new se);const o=i.batchableMesh;e.didViewUpdate&&(this._updateBatchableMesh(e),o.geometry=s,o.renderable=e,o.transform=e.groupTransform,o.setTexture(e._texture)),o.roundPixels=this._renderer._roundPixels|e._roundPixels,r.addToBatch(o,t)}else r.break(t),i.shader||(i.shader=new $t),this.updateRenderable(e),t.add(e)}execute(e){const{shader:t}=this._tilingSpriteDataHash[e.uid];t.groups[0]=this._renderer.globalUniforms.bindGroup;const r=t.resources.localUniforms.uniforms;r.uTransformMatrix=e.groupTransform,r.uRound=this._renderer._roundPixels|e._roundPixels,I(e.groupColorAlpha,r.uColor,0),this._state.blendMode=ee(e.groupBlendMode,e.texture._source),this._renderer.encoder.draw({geometry:W,shader:t,state:this._state})}updateRenderable(e){const t=this._getTilingSpriteData(e),{canBatch:r}=t;if(r){const{batchableMesh:i}=t;e.didViewUpdate&&this._updateBatchableMesh(e),i._batcher.updateElement(i)}else if(e.didViewUpdate){const{shader:i}=t;i.updateUniforms(e.width,e.height,e._tileTransform.matrix,e.anchor.x,e.anchor.y,e.texture)}}destroyRenderable(e){const t=this._getTilingSpriteData(e);t.batchableMesh=null,t.shader?.destroy(),this._tilingSpriteDataHash[e.uid]=null,e.off("destroyed",this._destroyRenderableBound)}_getTilingSpriteData(e){return this._tilingSpriteDataHash[e.uid]||this._initTilingSpriteData(e)}_initTilingSpriteData(e){const t=new re({indices:W.indices,positions:W.positions.slice(),uvs:W.uvs.slice()});return this._tilingSpriteDataHash[e.uid]={canBatch:!0,renderable:e,geometry:t},e.on("destroyed",this._destroyRenderableBound),this._tilingSpriteDataHash[e.uid]}_updateBatchableMesh(e){const t=this._getTilingSpriteData(e),{geometry:r}=t,i=e.texture.source.style;i.addressMode!=="repeat"&&(i.addressMode="repeat",i.update()),Kt(e,r.uvs),Xt(e,r.positions)}destroy(){for(const e in this._tilingSpriteDataHash)this.destroyRenderable(this._tilingSpriteDataHash[e].renderable);this._tilingSpriteDataHash=null,this._renderer=null}_updateCanBatch(e){const t=this._getTilingSpriteData(e),r=e.texture;let i=!0;return this._renderer.type===J.WEBGL&&(i=this._renderer.context.supports.nonPowOf2wrapping),t.canBatch=r.textureMatrix.isSimple&&(i||r.source.isPowerOfTwo),t.canBatch}}Qe.extension={type:[g.WebGLPipes,g.WebGPUPipes,g.CanvasPipes],name:"tilingSprite"};const Nt={name:"local-uniform-msdf-bit",vertex:{header:`
            struct LocalUniforms {
                uColor:vec4<f32>,
                uTransformMatrix:mat3x3<f32>,
                uDistance: f32,
                uRound:f32,
            }

            @group(2) @binding(0) var<uniform> localUniforms : LocalUniforms;
        `,main:`
            vColor *= localUniforms.uColor;
            modelMatrix *= localUniforms.uTransformMatrix;
        `,end:`
            if(localUniforms.uRound == 1)
            {
                vPosition = vec4(roundPixels(vPosition.xy, globalUniforms.uResolution), vPosition.zw);
            }
        `},fragment:{header:`
            struct LocalUniforms {
                uColor:vec4<f32>,
                uTransformMatrix:mat3x3<f32>,
                uDistance: f32
            }

            @group(2) @binding(0) var<uniform> localUniforms : LocalUniforms;
         `,main:` 
            outColor = vec4<f32>(calculateMSDFAlpha(outColor, localUniforms.uColor, localUniforms.uDistance));
        `}},qt={name:"local-uniform-msdf-bit",vertex:{header:`
            uniform mat3 uTransformMatrix;
            uniform vec4 uColor;
            uniform float uRound;
        `,main:`
            vColor *= uColor;
            modelMatrix *= uTransformMatrix;
        `,end:`
            if(uRound == 1.)
            {
                gl_Position.xy = roundPixels(gl_Position.xy, uResolution);
            }
        `},fragment:{header:`
            uniform float uDistance;
         `,main:` 
            outColor = vec4(calculateMSDFAlpha(outColor, vColor, uDistance));
        `}},Qt={name:"msdf-bit",fragment:{header:`
            fn calculateMSDFAlpha(msdfColor:vec4<f32>, shapeColor:vec4<f32>, distance:f32) -> f32 {
                
                // MSDF
                var median = msdfColor.r + msdfColor.g + msdfColor.b -
                    min(msdfColor.r, min(msdfColor.g, msdfColor.b)) -
                    max(msdfColor.r, max(msdfColor.g, msdfColor.b));
            
                // SDF
                median = min(median, msdfColor.a);

                var screenPxDistance = distance * (median - 0.5);
                var alpha = clamp(screenPxDistance + 0.5, 0.0, 1.0);
                if (median < 0.01) {
                    alpha = 0.0;
                } else if (median > 0.99) {
                    alpha = 1.0;
                }

                // Gamma correction for coverage-like alpha
                var luma: f32 = dot(shapeColor.rgb, vec3<f32>(0.299, 0.587, 0.114));
                var gamma: f32 = mix(1.0, 1.0 / 2.2, luma);
                var coverage: f32 = pow(shapeColor.a * alpha, gamma);

                return coverage;
             
            }
        `}},Jt={name:"msdf-bit",fragment:{header:`
            float calculateMSDFAlpha(vec4 msdfColor, vec4 shapeColor, float distance) {
                
                // MSDF
                float median = msdfColor.r + msdfColor.g + msdfColor.b -
                                min(msdfColor.r, min(msdfColor.g, msdfColor.b)) -
                                max(msdfColor.r, max(msdfColor.g, msdfColor.b));
               
                // SDF
                median = min(median, msdfColor.a);
            
                float screenPxDistance = distance * (median - 0.5);
                float alpha = clamp(screenPxDistance + 0.5, 0.0, 1.0);
           
                if (median < 0.01) {
                    alpha = 0.0;
                } else if (median > 0.99) {
                    alpha = 1.0;
                }

                // Gamma correction for coverage-like alpha
                float luma = dot(shapeColor.rgb, vec3(0.299, 0.587, 0.114));
                float gamma = mix(1.0, 1.0 / 2.2, luma);
                float coverage = pow(shapeColor.a * alpha, gamma);  
              
                return coverage;
            }
        `}};let Y,X;class Zt extends te{constructor(){const e=new A({uColor:{value:new Float32Array([1,1,1,1]),type:"vec4<f32>"},uTransformMatrix:{value:new P,type:"mat3x3<f32>"},uDistance:{value:4,type:"f32"},uRound:{value:0,type:"f32"}}),t=ft();Y??(Y=Ce({name:"sdf-shader",bits:[pt,gt(t),Nt,Qt,Re]})),X??(X=Pe({name:"sdf-shader",bits:[mt,xt(t),qt,Jt,Me]})),super({glProgram:X,gpuProgram:Y,resources:{localUniforms:e,batchSamplers:_t(t)}})}}class Je{constructor(e){this._gpuBitmapText={},this._destroyRenderableBound=this.destroyRenderable.bind(this),this._renderer=e,this._renderer.renderableGC.addManagedHash(this,"_gpuBitmapText")}validateRenderable(e){const t=this._getGpuBitmapText(e);return e._didTextUpdate&&(e._didTextUpdate=!1,this._updateContext(e,t)),this._renderer.renderPipes.graphics.validateRenderable(t)}addRenderable(e,t){const r=this._getGpuBitmapText(e);be(e,r),e._didTextUpdate&&(e._didTextUpdate=!1,this._updateContext(e,r)),this._renderer.renderPipes.graphics.addRenderable(r,t),r.context.customShader&&this._updateDistanceField(e)}destroyRenderable(e){e.off("destroyed",this._destroyRenderableBound),this._destroyRenderableByUid(e.uid)}_destroyRenderableByUid(e){const t=this._gpuBitmapText[e].context;t.customShader&&(b.return(t.customShader),t.customShader=null),b.return(this._gpuBitmapText[e]),this._gpuBitmapText[e]=null}updateRenderable(e){const t=this._getGpuBitmapText(e);be(e,t),this._renderer.renderPipes.graphics.updateRenderable(t),t.context.customShader&&this._updateDistanceField(e)}_updateContext(e,t){const{context:r}=t,i=bt.getFont(e.text,e._style);r.clear(),i.distanceField.type!=="none"&&(r.customShader||(r.customShader=b.get(Zt)));const s=Array.from(e.text),n=e._style;let o=i.baseLineOffset;const d=yt(s,n,i,!0);let l=0;const c=n.padding,u=d.scale;let f=d.width,p=d.height+d.offsetY;n._stroke&&(f+=n._stroke.width/u,p+=n._stroke.width/u),r.translate(-e._anchor._x*f-c,-e._anchor._y*p-c).scale(u,u);const h=i.applyFillAsTint?n._fill.color:16777215;for(let m=0;m<d.lines.length;m++){const _=d.lines[m];for(let x=0;x<_.charPositions.length;x++){const v=s[l++],S=i.chars[v];S?.texture&&r.texture(S.texture,h||"black",Math.round(_.charPositions[x]+S.xOffset),Math.round(o+S.yOffset))}o+=i.lineHeight}}_getGpuBitmapText(e){return this._gpuBitmapText[e.uid]||this.initGpuText(e)}initGpuText(e){const t=b.get(Tt);return this._gpuBitmapText[e.uid]=t,this._updateContext(e,t),e.on("destroyed",this._destroyRenderableBound),this._gpuBitmapText[e.uid]}_updateDistanceField(e){const t=this._getGpuBitmapText(e).context,r=e._style.fontFamily,i=N.get(`${r}-bitmap`),{a:s,b:n,c:o,d}=e.groupTransform,l=Math.sqrt(s*s+n*n),c=Math.sqrt(o*o+d*d),u=(Math.abs(l)+Math.abs(c))/2,f=i.baseRenderedFontSize/e._style.fontSize,p=u*i.distanceField.range*(1/f);t.customShader.resources.localUniforms.uniforms.uDistance=p}destroy(){for(const e in this._gpuBitmapText)this._destroyRenderableByUid(e);this._gpuBitmapText=null,this._renderer=null}}Je.extension={type:[g.WebGLPipes,g.WebGPUPipes,g.CanvasPipes],name:"bitmapText"};function be(a,e){e.groupTransform=a.groupTransform,e.groupColorAlpha=a.groupColorAlpha,e.groupColor=a.groupColor,e.groupBlendMode=a.groupBlendMode,e.globalDisplayStatus=a.globalDisplayStatus,e.groupTransform=a.groupTransform,e.localDisplayStatus=a.localDisplayStatus,e.groupAlpha=a.groupAlpha,e._roundPixels=a._roundPixels}function q(a,e){const{texture:t,bounds:r}=a;vt(r,e._anchor,t);const i=e._style.padding;r.minX-=i,r.minY-=i,r.maxX-=i,r.maxY-=i}class Ze{constructor(e){this._gpuText=Object.create(null),this._destroyRenderableBound=this.destroyRenderable.bind(this),this._renderer=e,this._renderer.runners.resolutionChange.add(this),this._renderer.renderableGC.addManagedHash(this,"_gpuText")}resolutionChange(){for(const e in this._gpuText){const t=this._gpuText[e];if(!t)continue;const r=t.batchableSprite.renderable;r._autoResolution&&(r._resolution=this._renderer.resolution,r.onViewUpdate())}}validateRenderable(e){const t=this._getGpuText(e),r=e._getKey();return t.textureNeedsUploading?(t.textureNeedsUploading=!1,!0):t.currentKey!==r}addRenderable(e,t){const i=this._getGpuText(e).batchableSprite;e._didTextUpdate&&this._updateText(e),this._renderer.renderPipes.batch.addToBatch(i,t)}updateRenderable(e){const r=this._getGpuText(e).batchableSprite;e._didTextUpdate&&this._updateText(e),r._batcher.updateElement(r)}destroyRenderable(e){e.off("destroyed",this._destroyRenderableBound),this._destroyRenderableById(e.uid)}_destroyRenderableById(e){const t=this._gpuText[e];this._renderer.htmlText.decreaseReferenceCount(t.currentKey),b.return(t.batchableSprite),this._gpuText[e]=null}_updateText(e){const t=e._getKey(),r=this._getGpuText(e),i=r.batchableSprite;r.currentKey!==t&&this._updateGpuText(e).catch(s=>{console.error(s)}),e._didTextUpdate=!1,q(i,e)}async _updateGpuText(e){e._didTextUpdate=!1;const t=this._getGpuText(e);if(t.generatingTexture)return;const r=e._getKey();this._renderer.htmlText.decreaseReferenceCount(t.currentKey),t.generatingTexture=!0,t.currentKey=r;const i=e.resolution??this._renderer.resolution,s=await this._renderer.htmlText.getManagedTexture(e.text,i,e._style,e._getKey()),n=t.batchableSprite;n.texture=t.texture=s,t.generatingTexture=!1,t.textureNeedsUploading=!0,e.onViewUpdate(),q(n,e)}_getGpuText(e){return this._gpuText[e.uid]||this.initGpuText(e)}initGpuText(e){const t={texture:M.EMPTY,currentKey:"--",batchableSprite:b.get(Ge),textureNeedsUploading:!1,generatingTexture:!1},r=t.batchableSprite;return r.renderable=e,r.transform=e.groupTransform,r.texture=M.EMPTY,r.bounds={minX:0,maxX:1,minY:0,maxY:0},r.roundPixels=this._renderer._roundPixels|e._roundPixels,e._resolution=e._autoResolution?this._renderer.resolution:e.resolution,this._gpuText[e.uid]=t,e.on("destroyed",this._destroyRenderableBound),t}destroy(){for(const e in this._gpuText)this._destroyRenderableById(e);this._gpuText=null,this._renderer=null}}Ze.extension={type:[g.WebGLPipes,g.WebGPUPipes,g.CanvasPipes],name:"htmlText"};function er(){const{userAgent:a}=Ue.get().getNavigator();return/^((?!chrome|android).)*safari/i.test(a)}const tr=new we;function et(a,e,t,r){const i=tr;i.minX=0,i.minY=0,i.maxX=a.width/r|0,i.maxY=a.height/r|0;const s=w.getOptimalTexture(i.width,i.height,r,!1);return s.source.uploadMethodId="image",s.source.resource=a,s.source.alphaMode="premultiply-alpha-on-upload",s.frame.width=e/r,s.frame.height=t/r,s.source.emit("update",s.source),s.updateUvs(),s}function rr(a,e){const t=e.fontFamily,r=[],i={},s=/font-family:([^;"\s]+)/g,n=a.match(s);function o(d){i[d]||(r.push(d),i[d]=!0)}if(Array.isArray(t))for(let d=0;d<t.length;d++)o(t[d]);else o(t);n&&n.forEach(d=>{const l=d.split(":")[1].trim();o(l)});for(const d in e.tagStyles){const l=e.tagStyles[d].fontFamily;o(l)}return r}async function ir(a){const t=await(await Ue.get().fetch(a)).blob(),r=new FileReader;return await new Promise((s,n)=>{r.onloadend=()=>s(r.result),r.onerror=n,r.readAsDataURL(t)})}async function ye(a,e){const t=await ir(e);return`@font-face {
        font-family: "${a.fontFamily}";
        src: url('${t}');
        font-weight: ${a.fontWeight};
        font-style: ${a.fontStyle};
    }`}const O=new Map;async function sr(a,e,t){const r=a.filter(i=>N.has(`${i}-and-url`)).map((i,s)=>{if(!O.has(i)){const{url:n}=N.get(`${i}-and-url`);s===0?O.set(i,ye({fontWeight:e.fontWeight,fontStyle:e.fontStyle,fontFamily:i},n)):O.set(i,ye({fontWeight:t.fontWeight,fontStyle:t.fontStyle,fontFamily:i},n))}return O.get(i)});return(await Promise.all(r)).join(`
`)}function ar(a,e,t,r,i){const{domElement:s,styleElement:n,svgRoot:o}=i;s.innerHTML=`<style>${e.cssStyle}</style><div style='padding:0;'>${a}</div>`,s.setAttribute("style",`transform: scale(${t});transform-origin: top left; display: inline-block`),n.textContent=r;const{width:d,height:l}=i.image;return o.setAttribute("width",d.toString()),o.setAttribute("height",l.toString()),new XMLSerializer().serializeToString(o)}function nr(a,e){const t=H.getOptimalCanvasAndContext(a.width,a.height,e),{context:r}=t;return r.clearRect(0,0,a.width,a.height),r.drawImage(a,0,0),t}function or(a,e,t){return new Promise(async r=>{t&&await new Promise(i=>setTimeout(i,100)),a.onload=()=>{r()},a.src=`data:image/svg+xml;charset=utf8,${encodeURIComponent(e)}`,a.crossOrigin="anonymous"})}class ae{constructor(e){this._activeTextures={},this._renderer=e,this._createCanvas=e.type===J.WEBGPU}getTexture(e){return this._buildTexturePromise(e.text,e.resolution,e.style)}getManagedTexture(e,t,r,i){if(this._activeTextures[i])return this._increaseReferenceCount(i),this._activeTextures[i].promise;const s=this._buildTexturePromise(e,t,r).then(n=>(this._activeTextures[i].texture=n,n));return this._activeTextures[i]={texture:null,promise:s,usageCount:1},s}async _buildTexturePromise(e,t,r){const i=b.get(Ve),s=rr(e,r),n=await sr(s,r,ie.defaultTextStyle),o=Gt(e,r,n,i),d=Math.ceil(Math.ceil(Math.max(1,o.width)+r.padding*2)*t),l=Math.ceil(Math.ceil(Math.max(1,o.height)+r.padding*2)*t),c=i.image,u=2;c.width=(d|0)+u,c.height=(l|0)+u;const f=ar(e,r,t,n,i);await or(c,f,er()&&s.length>0);const p=c;let h;this._createCanvas&&(h=nr(c,t));const m=et(h?h.canvas:p,c.width-u,c.height-u,t);return this._createCanvas&&(this._renderer.texture.initSource(m.source),H.returnCanvasAndContext(h)),b.return(i),m}_increaseReferenceCount(e){this._activeTextures[e].usageCount++}decreaseReferenceCount(e){const t=this._activeTextures[e];t&&(t.usageCount--,t.usageCount===0&&(t.texture?this._cleanUp(t):t.promise.then(r=>{t.texture=r,this._cleanUp(t)}).catch(()=>{V("HTMLTextSystem: Failed to clean texture")}),this._activeTextures[e]=null))}_cleanUp(e){w.returnTexture(e.texture),e.texture.source.resource=null,e.texture.source.uploadMethodId="unknown"}getReferenceCount(e){return this._activeTextures[e].usageCount}destroy(){this._activeTextures=null}}ae.extension={type:[g.WebGLSystem,g.WebGPUSystem,g.CanvasSystem],name:"htmlText"};ae.defaultFontOptions={fontFamily:"Arial",fontStyle:"normal",fontWeight:"normal"};class tt{constructor(e){this._gpuText=Object.create(null),this._destroyRenderableBound=this.destroyRenderable.bind(this),this._renderer=e,this._renderer.runners.resolutionChange.add(this),this._renderer.renderableGC.addManagedHash(this,"_gpuText")}resolutionChange(){for(const e in this._gpuText){const t=this._gpuText[e];if(!t)continue;const r=t.batchableSprite.renderable;r._autoResolution&&(r._resolution=this._renderer.resolution,r.onViewUpdate())}}validateRenderable(e){const t=this._getGpuText(e),r=e._getKey();return t.currentKey!==r}addRenderable(e,t){const i=this._getGpuText(e).batchableSprite;e._didTextUpdate&&this._updateText(e),this._renderer.renderPipes.batch.addToBatch(i,t)}updateRenderable(e){const r=this._getGpuText(e).batchableSprite;e._didTextUpdate&&this._updateText(e),r._batcher.updateElement(r)}destroyRenderable(e){e.off("destroyed",this._destroyRenderableBound),this._destroyRenderableById(e.uid)}_destroyRenderableById(e){const t=this._gpuText[e];this._renderer.canvasText.decreaseReferenceCount(t.currentKey),b.return(t.batchableSprite),this._gpuText[e]=null}_updateText(e){const t=e._getKey(),r=this._getGpuText(e),i=r.batchableSprite;r.currentKey!==t&&this._updateGpuText(e),e._didTextUpdate=!1,q(i,e)}_updateGpuText(e){const t=this._getGpuText(e),r=t.batchableSprite;t.texture&&this._renderer.canvasText.decreaseReferenceCount(t.currentKey),t.texture=r.texture=this._renderer.canvasText.getManagedTexture(e),t.currentKey=e._getKey(),r.texture=t.texture}_getGpuText(e){return this._gpuText[e.uid]||this.initGpuText(e)}initGpuText(e){const t={texture:null,currentKey:"--",batchableSprite:b.get(Ge)};return t.batchableSprite.renderable=e,t.batchableSprite.transform=e.groupTransform,t.batchableSprite.bounds={minX:0,maxX:1,minY:0,maxY:0},t.batchableSprite.roundPixels=this._renderer._roundPixels|e._roundPixels,this._gpuText[e.uid]=t,e._resolution=e._autoResolution?this._renderer.resolution:e.resolution,this._updateText(e),e.on("destroyed",this._destroyRenderableBound),t}destroy(){for(const e in this._gpuText)this._destroyRenderableById(e);this._gpuText=null,this._renderer=null}}tt.extension={type:[g.WebGLPipes,g.WebGPUPipes,g.CanvasPipes],name:"text"};function Te(a,e,t){for(let r=0,i=4*t*e;r<e;++r,i+=4)if(a[i+3]!==0)return!1;return!0}function ve(a,e,t,r,i){const s=4*e;for(let n=r,o=r*s+4*t;n<=i;++n,o+=s)if(a[o+3]!==0)return!1;return!0}function dr(a,e=1){const{width:t,height:r}=a,i=a.getContext("2d",{willReadFrequently:!0});if(i===null)throw new TypeError("Failed to get canvas 2D context");const n=i.getImageData(0,0,t,r).data;let o=0,d=0,l=t-1,c=r-1;for(;d<r&&Te(n,t,d);)++d;if(d===r)return de.EMPTY;for(;Te(n,t,c);)--c;for(;ve(n,t,o,d,c);)++o;for(;ve(n,t,l,d,c);)--l;return++l,++c,new de(o/e,d/e,(l-o)/e,(c-d)/e)}class rt{constructor(e){this._activeTextures={},this._renderer=e}getTextureSize(e,t,r){const i=F.measureText(e||" ",r);let s=Math.ceil(Math.ceil(Math.max(1,i.width)+r.padding*2)*t),n=Math.ceil(Math.ceil(Math.max(1,i.height)+r.padding*2)*t);return s=Math.ceil(s-1e-6),n=Math.ceil(n-1e-6),s=le(s),n=le(n),{width:s,height:n}}getTexture(e,t,r,i){typeof e=="string"&&(Z("8.0.0","CanvasTextSystem.getTexture: Use object TextOptions instead of separate arguments"),e={text:e,style:r,resolution:t}),e.style instanceof j||(e.style=new j(e.style));const{texture:s,canvasAndContext:n}=this.createTextureAndCanvas(e);return this._renderer.texture.initSource(s._source),H.returnCanvasAndContext(n),s}createTextureAndCanvas(e){const{text:t,style:r}=e,i=e.resolution??this._renderer.resolution,s=F.measureText(t||" ",r),n=Math.ceil(Math.ceil(Math.max(1,s.width)+r.padding*2)*i),o=Math.ceil(Math.ceil(Math.max(1,s.height)+r.padding*2)*i),d=H.getOptimalCanvasAndContext(n,o),{canvas:l}=d;this.renderTextToCanvas(t,r,i,d);const c=et(l,n,o,i);if(r.trim){const u=dr(l,i);c.frame.copyFrom(u),c.updateUvs()}return{texture:c,canvasAndContext:d}}getManagedTexture(e){e._resolution=e._autoResolution?this._renderer.resolution:e.resolution;const t=e._getKey();if(this._activeTextures[t])return this._increaseReferenceCount(t),this._activeTextures[t].texture;const{texture:r,canvasAndContext:i}=this.createTextureAndCanvas(e);return this._activeTextures[t]={canvasAndContext:i,texture:r,usageCount:1},r}_increaseReferenceCount(e){this._activeTextures[e].usageCount++}decreaseReferenceCount(e){const t=this._activeTextures[e];if(t.usageCount--,t.usageCount===0){H.returnCanvasAndContext(t.canvasAndContext),w.returnTexture(t.texture);const r=t.texture.source;r.resource=null,r.uploadMethodId="unknown",r.alphaMode="no-premultiply-alpha",this._activeTextures[e]=null}}getReferenceCount(e){return this._activeTextures[e].usageCount}renderTextToCanvas(e,t,r,i){const{canvas:s,context:n}=i,o=St(t),d=F.measureText(e||" ",t),l=d.lines,c=d.lineHeight,u=d.lineWidths,f=d.maxLineWidth,p=d.fontProperties,h=s.height;if(n.resetTransform(),n.scale(r,r),n.textBaseline=t.textBaseline,t._stroke?.width){const v=t._stroke;n.lineWidth=v.width,n.miterLimit=v.miterLimit,n.lineJoin=v.join,n.lineCap=v.cap}n.font=o;let m,_;const x=t.dropShadow?2:1;for(let v=0;v<x;++v){const S=t.dropShadow&&v===0,C=S?Math.ceil(Math.max(1,h)+t.padding*2):0,D=C*r;if(S){n.fillStyle="black",n.strokeStyle="black";const y=t.dropShadow,it=y.color,st=y.alpha;n.shadowColor=U.shared.setValue(it).setAlpha(st).toRgbaString();const at=y.blur*r,ne=y.distance*r;n.shadowBlur=at,n.shadowOffsetX=Math.cos(y.angle)*ne,n.shadowOffsetY=Math.sin(y.angle)*ne+D}else n.fillStyle=t._fill?ue(t._fill,n):null,t._stroke?.width&&(n.strokeStyle=ue(t._stroke,n)),n.shadowColor="black";let G=(c-p.fontSize)/2;c-p.fontSize<0&&(G=0);const R=t._stroke?.width??0;for(let y=0;y<l.length;y++)m=R/2,_=R/2+y*c+p.ascent+G,t.align==="right"?m+=f-u[y]:t.align==="center"&&(m+=(f-u[y])/2),t._stroke?.width&&this._drawLetterSpacing(l[y],t,i,m+t.padding,_+t.padding-C,!0),t._fill!==void 0&&this._drawLetterSpacing(l[y],t,i,m+t.padding,_+t.padding-C)}}_drawLetterSpacing(e,t,r,i,s,n=!1){const{context:o}=r,d=t.letterSpacing;let l=!1;if(F.experimentalLetterSpacingSupported&&(F.experimentalLetterSpacing?(o.letterSpacing=`${d}px`,o.textLetterSpacing=`${d}px`,l=!0):(o.letterSpacing="0px",o.textLetterSpacing="0px")),d===0||l){n?o.strokeText(e,i,s):o.fillText(e,i,s);return}let c=i;const u=F.graphemeSegmenter(e);let f=o.measureText(e).width,p=0;for(let h=0;h<u.length;++h){const m=u[h];n?o.strokeText(m,c,s):o.fillText(m,c,s);let _="";for(let x=h+1;x<u.length;++x)_+=u[x];p=o.measureText(_).width,c+=f-p+d,f=p}}destroy(){this._activeTextures=null}}rt.extension={type:[g.WebGLSystem,g.WebGPUSystem,g.CanvasSystem],name:"canvasText"};T.add(Fe);T.add(Ae);T.add(Ee);T.add(wt);T.add($e);T.add(Xe);T.add(je);T.add(rt);T.add(tt);T.add(Je);T.add(ae);T.add(Ze);T.add(Qe);T.add(qe);T.add(He);T.add(ke);
//# sourceMappingURL=init-CFUME-yJ.js.map
