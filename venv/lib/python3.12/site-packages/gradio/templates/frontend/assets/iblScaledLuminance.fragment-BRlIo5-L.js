import{j as i}from"./index-CwC3eD5q.js";import"./helperFunctions-2QL_6EXN.js";import"./index-tg5Kngjw.js";import"./svelte/svelte.js";const e="iblScaledLuminancePixelShader",o=`precision highp sampler2D;precision highp samplerCube;
#include<helperFunctions>
varying vec2 vUV;
#ifdef IBL_USE_CUBE_MAP
uniform samplerCube iblSource;
#else
uniform sampler2D iblSource;
#endif
uniform int iblWidth;uniform int iblHeight;float fetchLuminance(vec2 coords) {
#ifdef IBL_USE_CUBE_MAP
vec3 direction=equirectangularToCubemapDirection(coords);vec3 color=textureCubeLodEXT(iblSource,direction,0.0).rgb;
#else
vec3 color=textureLod(iblSource,coords,0.0).rgb;
#endif
return dot(color,LuminanceEncodeApprox);}
void main(void) {float deform=sin(vUV.y*PI);float luminance=fetchLuminance(vUV);gl_FragColor=vec4(vec3(deform*luminance),1.0);}`;i.ShadersStore[e]||(i.ShadersStore[e]=o);const t={name:e,shader:o};export{t as iblScaledLuminancePixelShader};
//# sourceMappingURL=iblScaledLuminance.fragment-BRlIo5-L.js.map
