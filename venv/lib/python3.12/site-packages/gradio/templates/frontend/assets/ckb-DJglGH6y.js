const o="کوردی",e={annotated_image:"وێنەی نیشانە کراو"},t={allow_recording_access:"تکایە ڕێگە بدە بە دەستگەیشتن بە مایکرۆفۆن بۆ تۆمارکردن",audio:"دەنگ",record_from_microphone:"تۆمارکردن لە مایکرۆفۆنەوە",stop_recording:"تۆمارکردن بوەستێنە",no_device_support:"ناتوانرێت دەستبگات بە ئامێرەکانی میدیا. دڵنیابە لەوەی کە لەسەر سەرچاوەیەکی پارێزراو (https) یان localhost کاردەکەیت (یان بڕوانامەیەکی SSL دروستت داوە)، و ڕێگەت داوە بە وێبگەڕەکە دەستی بگات بە ئامێرەکەت.",stop:"وەستان",resume:"بەردەوامبوون",record:"تۆمارکردن",no_microphone:"هیچ مایکرۆفۆنێک نەدۆزرایەوە",pause:"وەستاندنی کاتی",play:"لێدان",waiting:"چاوەڕوانی",drop_to_upload:"فایلی دەنگی لێرە دابنێ بۆ بارکردن"},r={connection_can_break:"لە مۆبایلدا، پەیوەندییەکە دەکرێت بپچڕێت ئەگەر ئەم تابە چالاک نەبێت یان ئامێرەکە بچێتە دۆخی پشوو، ئەمەش شوێنی خۆت لە ڕیزدا لەدەست دەدات.",long_requests_queue:"ڕیزێکی درێژی داواکاری هەیە. ئەم سپەیسە دووباد بکە بۆی چاوەڕوان نەبیت.",lost_connection:"پەیوەندی پچڕا بەهۆی جێهێشتنی پەیج. گەڕانەوە بۆ ڕیز...",waiting_for_inputs:"بەخێربێی بۆ فایل(ەکان) دەستپێکردن، هەڵبژارە دووبارە هەوڵ بدە."},a={checkbox:"بۆکسی هەڵبژاردن",checkbox_group:"گروپی بۆکسی هەڵبژاردن"},c={code:"کۆد"},_={color_picker:"هەڵبژاردنی ڕەنگ"},n={built_with:"دروستکراوە لەگەڵ...",built_with_gradio:"Gradio دروستکراوە بە",clear:"خاوێنکردنەوە",download:"دابەزاندن",edit:"بژارکردن",empty:"بەتاڵ",error:"هەڵە",hosted_on:"میوانداری کراوە لە",loading:"بارکردن",logo:"لۆگۆ",or:"یان",remove:"لابردن",settings:"ڕێکخستنەکان",share:"هاوبەشکردن",submit:"پێشکەشکردن",undo:"پووچکردنەوە",no_devices:"هیچ ئامێرێک نەدۆزرایەوە",language:"زمان",display_theme:"ڕووکاری نیشاندان",pwa:"بەرنامەی وێبی پێشکەوتوو"},d={incorrect_format:"فۆرماتێکی هەڵە، تەنها فایلەکانی CSV و TSV پشتگیری دەکرێن",new_column:"زیادکردنی ستوون",new_row:"ڕیزی نوێ",add_row_above:"زیادکردنی ڕیز لە سەرەوە",add_row_below:"زیادکردنی ڕیز لە خوارەوە",add_column_left:"زیادکردنی ستوون لە چەپەوە",add_column_right:"زیادکردنی ستوون لە ڕاستەوە",delete_row:"ڕووی ڕاکردن",delete_column:"کۆلۆنی ڕاکردن",sort_column:"کۆلۆنی ڕێکەوە کردن",sort_ascending:"بەرچاوەی ڕوونی",sort_descending:"سەرچاوەکە بەپاشی بەرگرێڕەوە بەڕێوە بکە",drop_to_upload:"فایلەکانی CSV یان TSV لەگەڵ ئەمە بکەوە تا داتا بەرچوون بێت لەوەی دەیتە فرەیمەکەدا",clear_sort:"ڕاژکردنی ڕوونکردن"},i={dropdown:"لیستی داکەوتوو"},l={build_error:"هەڵەیەک هەیە لە دروستکردندا",config_error:"هەڵەیەک هەیە لە ڕێکخستندا",contact_page_author:"تکایە پەیوەندی بکە بە نووسەری لاپەڕەکە بۆ ئاگادارکردنەوە.",no_app_file:"فایلی بەرنامە نییە",runtime_error:"هەڵەیەک هەیە لە کاتی جێبەجێکردندا",space_not_working:'"سپەیسەکە کار ناکات چونکە" {0}',space_paused:"سپەیسەکە وەستێنراوە",use_via_api:"بەکارهێنان لە ڕێگەی API"},s={uploading:"بارکردن..."},p={highlighted_text:"دەقی دیاریکراو"},u={allow_webcam_access:"تکایە ڕێگە بدە بە بەکارهێنانی کامێرای وێب بۆ تۆمارکردن.",brush_color:"ڕەنگی فڵچە",brush_radius:"تیژڕەوی فڵچە",image:"وێنە",remove_image:"لابردنی وێنە",select_brush_color:"ڕەنگی فڵچە هەڵبژێرە",start_drawing:"دەست بکە بە وێنەکێشان",use_brush:"فڵچە بەکاربهێنە",drop_to_upload:"فایل وێنەیەک هەروەها لەمەکە دەڕوونە بۆ بارکردنەوە"},g={label:"لەیبڵ"},m={enable_cookies:"ئەگەر تۆ سەردانی HuggingFace Space دەکەیت لە دۆخی نادیاردا، پێویستە کووکی لایەنی سێیەم چالاک بکەیت.",incorrect_credentials:"زانیاری چوونەژوورەوە هەڵەیە",username:"ناوی بەکارهێنەر",password:"وشەی نهێنی",login:"چوونە ژوورەوە"},b={number:"ژمارە"},h={plot:"هێڵکاری"},w={radio:"ڕادیۆ"},k={slider:"سلایدەر"},f={click_to_upload:"کلیک بکە بۆ بارکردن",drop_audio:"دەنگ لێرە دابنێ",drop_csv:"لێرەدا CSV دابنێ",drop_file:"فایل لێرە دابنێ",drop_image:"وێنە لێرەدا دابنێ",drop_video:"ڤیدیۆ لێرە دابنێ",drop_gallery:"میدیا لێرە دابنێ",paste_clipboard:"لکاندن لە کلیپبۆردەوە"},v={drop_to_upload:"فایل ویدیۆییەکەت هەنە بکەرەوە بۆ بارکردنەوە"},x={edit:"دەستکاری",retry:"هەوڵدانەوە",undo:"گەڕانەوە",submit:"ناردن",cancel:"هەڵوەشاندنەوە",like:"پەسەند",dislike:"ناپەسەند",clear:"پاککردنەوەی گفتوگۆ"},S={_name:o,"3D_model":{"3d_model":"مۆدێلی سێ ڕەهەندی",drop_to_upload:"فایل مۆدێلی ٣دی (.obj، .glb، .stl، .gltf، .splat یان .ply) لەمە هێرە بکە لەبەر بارکردنی."},annotated_image:e,audio:t,blocks:r,checkbox:a,code:c,color_picker:_,common:n,dataframe:d,dropdown:i,errors:l,file:s,highlighted_text:p,image:u,label:g,login:m,number:b,plot:h,radio:w,slider:k,upload_text:f,video:v,chatbot:x};export{o as _name,e as annotated_image,t as audio,r as blocks,x as chatbot,a as checkbox,c as code,_ as color_picker,n as common,d as dataframe,S as default,i as dropdown,l as errors,s as file,p as highlighted_text,u as image,g as label,m as login,b as number,h as plot,w as radio,k as slider,f as upload_text,v as video};
//# sourceMappingURL=ckb-DJglGH6y.js.map
