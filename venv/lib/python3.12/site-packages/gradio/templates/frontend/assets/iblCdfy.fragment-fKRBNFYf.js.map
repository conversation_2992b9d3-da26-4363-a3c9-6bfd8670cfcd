{"version": 3, "file": "iblCdfy.fragment-fKRBNFYf.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/ShadersWGSL/iblCdfy.fragment.js"], "sourcesContent": ["// Do not edit.\nimport { ShaderStore } from \"../Engines/shaderStore.js\";\nconst name = \"iblCdfyPixelShader\";\nconst shader = `varying vUV : vec2f;\n#include <helperFunctions>\n#ifdef IBL_USE_CUBE_MAP\nvar iblSourceSampler: sampler;var iblSource: texture_cube<f32>;\n#else\nvar iblSourceSampler: sampler;var iblSource: texture_2d<f32>;\n#endif\nuniform iblHeight: i32;\n#ifdef IBL_USE_CUBE_MAP\nfn fetchCube(uv: vec2f)->f32 {var direction: vec3f=equirectangularToCubemapDirection(uv);return sin(PI*uv.y) *\ndot(textureSampleLevel(iblSource,iblSourceSampler,direction,0.0)\n.rgb,\nLuminanceEncodeApprox);}\n#else\nfn fetchPanoramic(Coords: vec2i,envmapHeight: f32)->f32 {return sin(PI*(f32(Coords.y)+0.5)/envmapHeight) *\ndot(textureLoad(iblSource,Coords,0).rgb,LuminanceEncodeApprox);}\n#endif\n@fragment\nfn main(input: FragmentInputs)->FragmentOutputs {var coords: vec2i= vec2i(fragmentInputs.position.xy);var cdfy: f32=0.0;for (var y: i32=1; y<=coords.y; y++) {\n#ifdef IBL_USE_CUBE_MAP\nvar uv: vec2f= vec2f(input.vUV.x,( f32(y-1)+0.5)/ f32(uniforms.iblHeight));cdfy+=fetchCube(uv);\n#else\ncdfy+=fetchPanoramic( vec2i(coords.x,y-1), f32(uniforms.iblHeight));\n#endif\n}\nfragmentOutputs.color= vec4f(cdfy,0.0,0.0,1.0);}`;\n// Sideeffect\nif (!ShaderStore.ShadersStoreWGSL[name]) {\n    ShaderStore.ShadersStoreWGSL[name] = shader;\n}\n/** @internal */\nexport const iblCdfyPixelShaderWGSL = { name, shader };\n//# sourceMappingURL=iblCdfy.fragment.js.map"], "names": ["name", "shader", "ShaderStore", "iblCdfyPixelShaderWGSL"], "mappings": "+FAEA,MAAMA,EAAO,qBACPC,EAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kDA2BVC,EAAY,iBAAiBF,CAAI,IAClCE,EAAY,iBAAiBF,CAAI,EAAIC,GAG7B,MAACE,EAAyB,CAAE,KAAAH,EAAM,OAAAC,CAAM", "x_google_ignoreList": [0]}