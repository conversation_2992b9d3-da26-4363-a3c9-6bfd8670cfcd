{"version": 3, "file": "utils-Gtzs_Zla.js", "sources": ["../../../../js/image/shared/utils.ts"], "sourcesContent": ["export const get_coordinates_of_clicked_image = (\n\tevt: MouseEvent\n): [number, number] | null => {\n\tlet image;\n\tif (evt.currentTarget instanceof Element) {\n\t\timage = evt.currentTarget.querySelector(\"img\") as HTMLImageElement;\n\t} else {\n\t\treturn [NaN, NaN];\n\t}\n\n\tconst imageRect = image.getBoundingClientRect();\n\tconst xScale = image.naturalWidth / imageRect.width;\n\tconst yScale = image.naturalHeight / imageRect.height;\n\tif (xScale > yScale) {\n\t\tconst displayed_height = image.naturalHeight / xScale;\n\t\tconst y_offset = (imageRect.height - displayed_height) / 2;\n\t\tvar x = Math.round((evt.clientX - imageRect.left) * xScale);\n\t\tvar y = Math.round((evt.clientY - imageRect.top - y_offset) * xScale);\n\t} else {\n\t\tconst displayed_width = image.naturalWidth / yScale;\n\t\tconst x_offset = (imageRect.width - displayed_width) / 2;\n\t\tvar x = Math.round((evt.clientX - imageRect.left - x_offset) * yScale);\n\t\tvar y = Math.round((evt.clientY - imageRect.top) * yScale);\n\t}\n\tif (x < 0 || x >= image.naturalWidth || y < 0 || y >= image.naturalHeight) {\n\t\treturn null;\n\t}\n\treturn [x, y];\n};\n"], "names": ["get_coordinates_of_clicked_image", "evt", "image", "imageRect", "xScale", "yScale", "displayed_height", "y_offset", "x", "y", "displayed_width", "x_offset"], "mappings": "AAAa,MAAAA,EACZC,GAC6B,CACzB,IAAAC,EACA,GAAAD,EAAI,yBAAyB,QACxBC,EAAAD,EAAI,cAAc,cAAc,KAAK,MAEtC,OAAA,CAAC,IAAK,GAAG,EAGX,MAAAE,EAAYD,EAAM,wBAClBE,EAASF,EAAM,aAAeC,EAAU,MACxCE,EAASH,EAAM,cAAgBC,EAAU,OAC/C,GAAIC,EAASC,EAAQ,CACd,MAAAC,EAAmBJ,EAAM,cAAgBE,EACzCG,GAAYJ,EAAU,OAASG,GAAoB,EACzD,IAAIE,EAAI,KAAK,OAAOP,EAAI,QAAUE,EAAU,MAAQC,CAAM,EACtDK,EAAI,KAAK,OAAOR,EAAI,QAAUE,EAAU,IAAMI,GAAYH,CAAM,CAAA,KAC9D,CACA,MAAAM,EAAkBR,EAAM,aAAeG,EACvCM,GAAYR,EAAU,MAAQO,GAAmB,EACnD,IAAAF,EAAI,KAAK,OAAOP,EAAI,QAAUE,EAAU,KAAOQ,GAAYN,CAAM,EACjEI,EAAI,KAAK,OAAOR,EAAI,QAAUE,EAAU,KAAOE,CAAM,CAC1D,CACI,OAAAG,EAAI,GAAKA,GAAKN,EAAM,cAAgBO,EAAI,GAAKA,GAAKP,EAAM,cACpD,KAED,CAACM,EAAGC,CAAC,CACb"}