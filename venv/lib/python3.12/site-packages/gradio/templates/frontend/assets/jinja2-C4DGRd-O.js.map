{"version": 3, "file": "jinja2-C4DGRd-O.js", "sources": ["../../../../node_modules/.pnpm/@codemirror+legacy-modes@6.4.3/node_modules/@codemirror/legacy-modes/mode/jinja2.js"], "sourcesContent": ["var keywords = [\"and\", \"as\", \"block\", \"endblock\", \"by\", \"cycle\", \"debug\", \"else\", \"elif\",\n                \"extends\", \"filter\", \"endfilter\", \"firstof\", \"do\", \"for\",\n                \"endfor\", \"if\", \"endif\", \"ifchanged\", \"endifchanged\",\n                \"ifequal\", \"endifequal\", \"ifnotequal\", \"set\", \"raw\", \"endraw\",\n                \"endifnotequal\", \"in\", \"include\", \"load\", \"not\", \"now\", \"or\",\n                \"parsed\", \"regroup\", \"reversed\", \"spaceless\", \"call\", \"endcall\", \"macro\",\n                \"endmacro\", \"endspaceless\", \"ssi\", \"templatetag\", \"openblock\",\n                \"closeblock\", \"openvariable\", \"closevariable\", \"without\", \"context\",\n                \"openbrace\", \"closebrace\", \"opencomment\",\n                \"closecomment\", \"widthratio\", \"url\", \"with\", \"endwith\",\n                \"get_current_language\", \"trans\", \"endtrans\", \"noop\", \"blocktrans\",\n                \"endblocktrans\", \"get_available_languages\",\n                \"get_current_language_bidi\", \"pluralize\", \"autoescape\", \"endautoescape\"],\n    operator = /^[+\\-*&%=<>!?|~^]/,\n    sign = /^[:\\[\\(\\{]/,\n    atom = [\"true\", \"false\"],\n    number = /^(\\d[+\\-\\*\\/])?\\d+(\\.\\d+)?/;\n\nkeywords = new RegExp(\"((\" + keywords.join(\")|(\") + \"))\\\\b\");\natom = new RegExp(\"((\" + atom.join(\")|(\") + \"))\\\\b\");\n\nfunction tokenBase (stream, state) {\n  var ch = stream.peek();\n\n  //Comment\n  if (state.incomment) {\n    if(!stream.skipTo(\"#}\")) {\n      stream.skipToEnd();\n    } else {\n      stream.eatWhile(/\\#|}/);\n      state.incomment = false;\n    }\n    return \"comment\";\n    //Tag\n  } else if (state.intag) {\n    //After operator\n    if(state.operator) {\n      state.operator = false;\n      if(stream.match(atom)) {\n        return \"atom\";\n      }\n      if(stream.match(number)) {\n        return \"number\";\n      }\n    }\n    //After sign\n    if(state.sign) {\n      state.sign = false;\n      if(stream.match(atom)) {\n        return \"atom\";\n      }\n      if(stream.match(number)) {\n        return \"number\";\n      }\n    }\n\n    if(state.instring) {\n      if(ch == state.instring) {\n        state.instring = false;\n      }\n      stream.next();\n      return \"string\";\n    } else if(ch == \"'\" || ch == '\"') {\n      state.instring = ch;\n      stream.next();\n      return \"string\";\n    } else if (state.inbraces > 0 && ch ==\")\") {\n      stream.next()\n      state.inbraces--;\n    }\n    else if (ch == \"(\") {\n      stream.next()\n      state.inbraces++;\n    }\n    else if (state.inbrackets > 0 && ch ==\"]\") {\n      stream.next()\n      state.inbrackets--;\n    }\n    else if (ch == \"[\") {\n      stream.next()\n      state.inbrackets++;\n    } else if (!state.lineTag && (stream.match(state.intag + \"}\") || stream.eat(\"-\") && stream.match(state.intag + \"}\"))) {\n      state.intag = false;\n      return \"tag\";\n    } else if(stream.match(operator)) {\n      state.operator = true;\n      return \"operator\";\n    } else if(stream.match(sign)) {\n      state.sign = true;\n    } else {\n      if (stream.column() == 1 && state.lineTag && stream.match(keywords)) {\n        //allow nospace after tag before the keyword\n        return \"keyword\";\n      }\n      if(stream.eat(\" \") || stream.sol()) {\n        if(stream.match(keywords)) {\n          return \"keyword\";\n        }\n        if(stream.match(atom)) {\n          return \"atom\";\n        }\n        if(stream.match(number)) {\n          return \"number\";\n        }\n        if(stream.sol()) {\n          stream.next();\n        }\n      } else {\n        stream.next();\n      }\n\n    }\n    return \"variable\";\n  } else if (stream.eat(\"{\")) {\n    if (stream.eat(\"#\")) {\n      state.incomment = true;\n      if(!stream.skipTo(\"#}\")) {\n        stream.skipToEnd();\n      } else {\n        stream.eatWhile(/\\#|}/);\n        state.incomment = false;\n      }\n      return \"comment\";\n      //Open tag\n    } else if (ch = stream.eat(/\\{|%/)) {\n      //Cache close tag\n      state.intag = ch;\n      state.inbraces = 0;\n      state.inbrackets = 0;\n      if(ch == \"{\") {\n        state.intag = \"}\";\n      }\n      stream.eat(\"-\");\n      return \"tag\";\n    }\n    //Line statements\n  } else if (stream.eat('#')) {\n    if (stream.peek() == '#') {\n      stream.skipToEnd();\n      return \"comment\"\n    }\n    else if (!stream.eol()) {\n      state.intag = true;\n      state.lineTag = true;\n      state.inbraces = 0;\n      state.inbrackets = 0;\n      return \"tag\";\n    }\n  }\n  stream.next();\n};\n\nexport const jinja2 = {\n  name: \"jinja2\",\n  startState: function () {\n    return {tokenize: tokenBase, inbrackets: 0, inbraces: 0};\n  },\n  token: function(stream, state) {\n    var style = state.tokenize(stream, state);\n    if (stream.eol() && state.lineTag && !state.instring && state.inbraces == 0 && state.inbrackets == 0) {\n      //Close line statement at the EOL\n      state.intag = false\n      state.lineTag = false\n    }\n    return style;\n  },\n  languageData: {\n    commentTokens: {block: {open: \"{#\", close: \"#}\", line: \"##\"}}\n  }\n};\n"], "names": ["keywords", "operator", "sign", "atom", "number", "tokenBase", "stream", "state", "ch", "jinja2", "style"], "mappings": "AAAA,IAAIA,EAAW,CAAC,MAAO,KAAM,QAAS,WAAY,KAAM,QAAS,QAAS,OAAQ,OAClE,UAAW,SAAU,YAAa,UAAW,KAAM,MACnD,SAAU,KAAM,QAAS,YAAa,eACtC,UAAW,aAAc,aAAc,MAAO,MAAO,SACrD,gBAAiB,KAAM,UAAW,OAAQ,MAAO,MAAO,KACxD,SAAU,UAAW,WAAY,YAAa,OAAQ,UAAW,QACjE,WAAY,eAAgB,MAAO,cAAe,YAClD,aAAc,eAAgB,gBAAiB,UAAW,UAC1D,YAAa,aAAc,cAC3B,eAAgB,aAAc,MAAO,OAAQ,UAC7C,uBAAwB,QAAS,WAAY,OAAQ,aACrD,gBAAiB,0BACjB,4BAA6B,YAAa,aAAc,eAAe,EACnFC,EAAW,oBACXC,EAAO,aACPC,EAAO,CAAC,OAAQ,OAAO,EACvBC,EAAS,6BAEbJ,EAAW,IAAI,OAAO,KAAOA,EAAS,KAAK,KAAK,EAAI,OAAO,EAC3DG,EAAO,IAAI,OAAO,KAAOA,EAAK,KAAK,KAAK,EAAI,OAAO,EAEnD,SAASE,EAAWC,EAAQC,EAAO,CACjC,IAAIC,EAAKF,EAAO,OAGhB,GAAIC,EAAM,UACR,OAAID,EAAO,OAAO,IAAI,GAGpBA,EAAO,SAAS,MAAM,EACtBC,EAAM,UAAY,IAHlBD,EAAO,UAAS,EAKX,UAEF,GAAIC,EAAM,MAAO,CAEtB,GAAGA,EAAM,SAAU,CAEjB,GADAA,EAAM,SAAW,GACdD,EAAO,MAAMH,CAAI,EAClB,MAAO,OAET,GAAGG,EAAO,MAAMF,CAAM,EACpB,MAAO,QAEV,CAED,GAAGG,EAAM,KAAM,CAEb,GADAA,EAAM,KAAO,GACVD,EAAO,MAAMH,CAAI,EAClB,MAAO,OAET,GAAGG,EAAO,MAAMF,CAAM,EACpB,MAAO,QAEV,CAED,GAAGG,EAAM,SACP,OAAGC,GAAMD,EAAM,WACbA,EAAM,SAAW,IAEnBD,EAAO,KAAI,EACJ,SACF,GAAGE,GAAM,KAAOA,GAAM,IAC3B,OAAAD,EAAM,SAAWC,EACjBF,EAAO,KAAI,EACJ,SACF,GAAIC,EAAM,SAAW,GAAKC,GAAK,IACpCF,EAAO,KAAM,EACbC,EAAM,mBAECC,GAAM,IACbF,EAAO,KAAM,EACbC,EAAM,mBAECA,EAAM,WAAa,GAAKC,GAAK,IACpCF,EAAO,KAAM,EACbC,EAAM,qBAECC,GAAM,IACbF,EAAO,KAAM,EACbC,EAAM,iBACD,IAAI,CAACA,EAAM,UAAYD,EAAO,MAAMC,EAAM,MAAQ,GAAG,GAAKD,EAAO,IAAI,GAAG,GAAKA,EAAO,MAAMC,EAAM,MAAQ,GAAG,GAChH,OAAAA,EAAM,MAAQ,GACP,MACF,GAAGD,EAAO,MAAML,CAAQ,EAC7B,OAAAM,EAAM,SAAW,GACV,WACF,GAAGD,EAAO,MAAMJ,CAAI,EACzBK,EAAM,KAAO,OACR,CACL,GAAID,EAAO,OAAQ,GAAI,GAAKC,EAAM,SAAWD,EAAO,MAAMN,CAAQ,EAEhE,MAAO,UAET,GAAGM,EAAO,IAAI,GAAG,GAAKA,EAAO,IAAG,EAAI,CAClC,GAAGA,EAAO,MAAMN,CAAQ,EACtB,MAAO,UAET,GAAGM,EAAO,MAAMH,CAAI,EAClB,MAAO,OAET,GAAGG,EAAO,MAAMF,CAAM,EACpB,MAAO,SAENE,EAAO,OACRA,EAAO,KAAI,CAErB,MACQA,EAAO,KAAI,CAGd,EACD,MAAO,UACR,SAAUA,EAAO,IAAI,GAAG,EAAG,CAC1B,GAAIA,EAAO,IAAI,GAAG,EAChB,OAAAC,EAAM,UAAY,GACdD,EAAO,OAAO,IAAI,GAGpBA,EAAO,SAAS,MAAM,EACtBC,EAAM,UAAY,IAHlBD,EAAO,UAAS,EAKX,UAEF,GAAIE,EAAKF,EAAO,IAAI,MAAM,EAE/B,OAAAC,EAAM,MAAQC,EACdD,EAAM,SAAW,EACjBA,EAAM,WAAa,EAChBC,GAAM,MACPD,EAAM,MAAQ,KAEhBD,EAAO,IAAI,GAAG,EACP,KAGV,SAAUA,EAAO,IAAI,GAAG,EAAG,CAC1B,GAAIA,EAAO,KAAM,GAAI,IACnB,OAAAA,EAAO,UAAS,EACT,UAEJ,GAAI,CAACA,EAAO,MACf,OAAAC,EAAM,MAAQ,GACdA,EAAM,QAAU,GAChBA,EAAM,SAAW,EACjBA,EAAM,WAAa,EACZ,KAEV,CACDD,EAAO,KAAI,CACb,CAEY,MAACG,EAAS,CACpB,KAAM,SACN,WAAY,UAAY,CACtB,MAAO,CAAC,SAAUJ,EAAW,WAAY,EAAG,SAAU,CAAC,CACxD,EACD,MAAO,SAASC,EAAQC,EAAO,CAC7B,IAAIG,EAAQH,EAAM,SAASD,EAAQC,CAAK,EACxC,OAAID,EAAO,IAAG,GAAMC,EAAM,SAAW,CAACA,EAAM,UAAYA,EAAM,UAAY,GAAKA,EAAM,YAAc,IAEjGA,EAAM,MAAQ,GACdA,EAAM,QAAU,IAEXG,CACR,EACD,aAAc,CACZ,cAAe,CAAC,MAAO,CAAC,KAAM,KAAM,MAAO,KAAM,KAAM,IAAI,CAAC,CAC7D,CACH", "x_google_ignoreList": [0]}