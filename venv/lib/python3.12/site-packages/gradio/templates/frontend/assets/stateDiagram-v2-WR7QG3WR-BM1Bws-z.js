import{s as r,S as e,b as a,a as s}from"./chunk-4IRHCMPZ-2blT2GKm.js";import{_ as i}from"./mermaid.core-C-NdzACT.js";import"./chunk-2O5F6CEG-gNWW8U6S.js";import"./select-BigU4G0v.js";import"./index-tg5Kngjw.js";import"./svelte/svelte.js";import"./dispatch-kxCwF96_.js";import"./step-Ce-xBr2D.js";var b={parser:r,get db(){return new e(2)},renderer:a,styles:s,init:i(t=>{t.state||(t.state={}),t.state.arrowMarkerAbsolute=t.arrowMarkerAbsolute},"init")};export{b as diagram};
//# sourceMappingURL=stateDiagram-v2-WR7QG3WR-BM1Bws-z.js.map
