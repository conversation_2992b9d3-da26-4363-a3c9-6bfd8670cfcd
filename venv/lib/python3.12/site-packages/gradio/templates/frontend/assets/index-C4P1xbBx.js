import qe from"./StaticAudio-9JNeuaSM.js";import{I as Ge}from"./InteractiveAudio-DSX8c33d.js";import{S as $}from"./index-BnpaMODf.js";/* empty css                                                        */import"./StreamingBar.svelte_svelte_type_style_lang-DyfAPhwM.js";import{B as x}from"./Block-CJdXVpa7.js";import"./MarkdownCode.svelte_svelte_type_style_lang-D31a0_vL.js";import"./index-tg5Kngjw.js";import{U as Me}from"./UploadText-LzqOsquI.js";import{A as Mt}from"./AudioPlayer-SLNuKDl4.js";import{default as Dt}from"./Example-BQyGztrG.js";import"./utils-BsGrhMNe.js";import"./BlockLabel-3KxTaaiM.js";import"./IconButton-C_HS7fTi.js";import"./Empty-ZqppqzTN.js";import"./ShareButton-DLbTVDiY.js";import"./Community-Dw1micSV.js";import"./Download-DVtk-Jv3.js";import"./Music-CDm0RGMk.js";import"./IconButtonWrapper--EIOWuEM.js";import"./DownloadLink-QIttOhoR.js";import"./DownloadLink.svelte_svelte_type_style_lang-C_5UIfol.js";import"./file-url-DoxvUUVV.js";import"./svelte/svelte.js";import"./prism-python-BppifD2Y.js";import"./Trim-JQYgj7Jd.js";import"./Play-B0Q0U1Qz.js";import"./Undo-DCjBnnSO.js";import"./hls-CnVhpNcu.js";import"./Upload-BcrNQj7m.js";/* empty css                                             */import"./ModifyUpload-nKSY8dQH.js";import"./Clear-By3xiIwg.js";import"./Edit-BpRIf5rU.js";import"./SelectSource-DkKG-PgQ.js";import"./StreamingBar-JqJtcvLZ.js";const{SvelteComponent:Ve,add_flush_callback:P,assign:ee,bind:E,binding_callbacks:N,check_outros:De,create_component:z,destroy_component:B,detach:K,empty:Fe,flush:m,get_spread_object:te,get_spread_update:ie,group_outros:He,init:Ke,insert:L,mount_component:C,safe_not_equal:Le,space:se,transition_in:v,transition_out:k}=window.__gradio__svelte__internal,{afterUpdate:Qe,onMount:Xe}=window.__gradio__svelte__internal;function Ye(t){let e,n;return e=new x({props:{variant:t[0]===null&&t[25]==="upload"?"dashed":"solid",border_mode:t[27]?"focus":"base",padding:!1,allow_overflow:!1,elem_id:t[4],elem_classes:t[5],visible:t[6],container:t[12],scale:t[13],min_width:t[14],$$slots:{default:[$e]},$$scope:{ctx:t}}}),{c(){z(e.$$.fragment)},m(i,a){C(e,i,a),n=!0},p(i,a){const _={};a[0]&33554433&&(_.variant=i[0]===null&&i[25]==="upload"?"dashed":"solid"),a[0]&134217728&&(_.border_mode=i[27]?"focus":"base"),a[0]&16&&(_.elem_id=i[4]),a[0]&32&&(_.elem_classes=i[5]),a[0]&64&&(_.visible=i[6]),a[0]&4096&&(_.container=i[12]),a[0]&8192&&(_.scale=i[13]),a[0]&16384&&(_.min_width=i[14]),a[0]&536710927|a[2]&128&&(_.$$scope={dirty:a,ctx:i}),e.$set(_)},i(i){n||(v(e.$$.fragment,i),n=!0)},o(i){k(e.$$.fragment,i),n=!1},d(i){B(e,i)}}}function Ze(t){let e,n;return e=new x({props:{variant:"solid",border_mode:t[27]?"focus":"base",padding:!1,allow_overflow:!1,elem_id:t[4],elem_classes:t[5],visible:t[6],container:t[12],scale:t[13],min_width:t[14],$$slots:{default:[xe]},$$scope:{ctx:t}}}),{c(){z(e.$$.fragment)},m(i,a){C(e,i,a),n=!0},p(i,a){const _={};a[0]&134217728&&(_.border_mode=i[27]?"focus":"base"),a[0]&16&&(_.elem_id=i[4]),a[0]&32&&(_.elem_classes=i[5]),a[0]&64&&(_.visible=i[6]),a[0]&4096&&(_.container=i[12]),a[0]&8192&&(_.scale=i[13]),a[0]&16384&&(_.min_width=i[14]),a[0]&277842435|a[2]&128&&(_.$$scope={dirty:a,ctx:i}),e.$set(_)},i(i){n||(v(e.$$.fragment,i),n=!0)},o(i){k(e.$$.fragment,i),n=!1},d(i){B(e,i)}}}function ye(t){let e,n;return e=new Me({props:{i18n:t[23].i18n,type:"audio"}}),{c(){z(e.$$.fragment)},m(i,a){C(e,i,a),n=!0},p(i,a){const _={};a[0]&8388608&&(_.i18n=i[23].i18n),e.$set(_)},i(i){n||(v(e.$$.fragment,i),n=!0)},o(i){k(e.$$.fragment,i),n=!1},d(i){B(e,i)}}}function $e(t){let e,n,i,a,_,c,l,r,h;const g=[{autoscroll:t[23].autoscroll},{i18n:t[23].i18n},t[1]];let I={};for(let o=0;o<g.length;o+=1)I=ee(I,g[o]);e=new $({props:I}),e.$on("clear_status",t[45]);function U(o){t[48](o)}function J(o){t[49](o)}function O(o){t[50](o)}function R(o){t[51](o)}function b(o){t[52](o)}let w={label:t[9],show_label:t[11],show_download_button:t[16],value:t[0],root:t[10],sources:t[8],active_source:t[25],pending:t[20],streaming:t[21],loop:t[15],max_file_size:t[23].max_file_size,handle_reset_value:t[29],editable:t[18],i18n:t[23].i18n,waveform_settings:t[28],waveform_options:t[19],trim_region_settings:t[30],stream_every:t[22],upload:t[46],stream_handler:t[47],$$slots:{default:[ye]},$$scope:{ctx:t}};return t[2]!==void 0&&(w.recording=t[2]),t[27]!==void 0&&(w.dragging=t[27]),t[24]!==void 0&&(w.uploading=t[24]),t[26]!==void 0&&(w.modify_stream=t[26]),t[3]!==void 0&&(w.set_time_limit=t[3]),i=new Ge({props:w}),N.push(()=>E(i,"recording",U)),N.push(()=>E(i,"dragging",J)),N.push(()=>E(i,"uploading",O)),N.push(()=>E(i,"modify_stream",R)),N.push(()=>E(i,"set_time_limit",b)),i.$on("change",t[53]),i.$on("stream",t[54]),i.$on("drag",t[55]),i.$on("edit",t[56]),i.$on("play",t[57]),i.$on("pause",t[58]),i.$on("stop",t[59]),i.$on("start_recording",t[60]),i.$on("pause_recording",t[61]),i.$on("stop_recording",t[62]),i.$on("upload",t[63]),i.$on("clear",t[64]),i.$on("error",t[31]),i.$on("close_stream",t[65]),{c(){z(e.$$.fragment),n=se(),z(i.$$.fragment)},m(o,u){C(e,o,u),L(o,n,u),C(i,o,u),h=!0},p(o,u){const T=u[0]&8388610?ie(g,[u[0]&8388608&&{autoscroll:o[23].autoscroll},u[0]&8388608&&{i18n:o[23].i18n},u[0]&2&&te(o[1])]):{};e.$set(T);const f={};u[0]&512&&(f.label=o[9]),u[0]&2048&&(f.show_label=o[11]),u[0]&65536&&(f.show_download_button=o[16]),u[0]&1&&(f.value=o[0]),u[0]&1024&&(f.root=o[10]),u[0]&256&&(f.sources=o[8]),u[0]&33554432&&(f.active_source=o[25]),u[0]&1048576&&(f.pending=o[20]),u[0]&2097152&&(f.streaming=o[21]),u[0]&32768&&(f.loop=o[15]),u[0]&8388608&&(f.max_file_size=o[23].max_file_size),u[0]&262144&&(f.editable=o[18]),u[0]&8388608&&(f.i18n=o[23].i18n),u[0]&268435456&&(f.waveform_settings=o[28]),u[0]&524288&&(f.waveform_options=o[19]),u[0]&4194304&&(f.stream_every=o[22]),u[0]&8388608&&(f.upload=o[46]),u[0]&8388608&&(f.stream_handler=o[47]),u[0]&8388608|u[2]&128&&(f.$$scope={dirty:u,ctx:o}),!a&&u[0]&4&&(a=!0,f.recording=o[2],P(()=>a=!1)),!_&&u[0]&134217728&&(_=!0,f.dragging=o[27],P(()=>_=!1)),!c&&u[0]&16777216&&(c=!0,f.uploading=o[24],P(()=>c=!1)),!l&&u[0]&67108864&&(l=!0,f.modify_stream=o[26],P(()=>l=!1)),!r&&u[0]&8&&(r=!0,f.set_time_limit=o[3],P(()=>r=!1)),i.$set(f)},i(o){h||(v(e.$$.fragment,o),v(i.$$.fragment,o),h=!0)},o(o){k(e.$$.fragment,o),k(i.$$.fragment,o),h=!1},d(o){o&&K(n),B(e,o),B(i,o)}}}function xe(t){let e,n,i,a;const _=[{autoscroll:t[23].autoscroll},{i18n:t[23].i18n},t[1]];let c={};for(let l=0;l<_.length;l+=1)c=ee(c,_[l]);return e=new $({props:c}),e.$on("clear_status",t[39]),i=new qe({props:{i18n:t[23].i18n,show_label:t[11],show_download_button:t[16],show_share_button:t[17],value:t[0],label:t[9],loop:t[15],waveform_settings:t[28],waveform_options:t[19],editable:t[18]}}),i.$on("share",t[40]),i.$on("error",t[41]),i.$on("play",t[42]),i.$on("pause",t[43]),i.$on("stop",t[44]),{c(){z(e.$$.fragment),n=se(),z(i.$$.fragment)},m(l,r){C(e,l,r),L(l,n,r),C(i,l,r),a=!0},p(l,r){const h=r[0]&8388610?ie(_,[r[0]&8388608&&{autoscroll:l[23].autoscroll},r[0]&8388608&&{i18n:l[23].i18n},r[0]&2&&te(l[1])]):{};e.$set(h);const g={};r[0]&8388608&&(g.i18n=l[23].i18n),r[0]&2048&&(g.show_label=l[11]),r[0]&65536&&(g.show_download_button=l[16]),r[0]&131072&&(g.show_share_button=l[17]),r[0]&1&&(g.value=l[0]),r[0]&512&&(g.label=l[9]),r[0]&32768&&(g.loop=l[15]),r[0]&268435456&&(g.waveform_settings=l[28]),r[0]&524288&&(g.waveform_options=l[19]),r[0]&262144&&(g.editable=l[18]),i.$set(g)},i(l){a||(v(e.$$.fragment,l),v(i.$$.fragment,l),a=!0)},o(l){k(e.$$.fragment,l),k(i.$$.fragment,l),a=!1},d(l){l&&K(n),B(e,l),B(i,l)}}}function et(t){let e,n,i,a;const _=[Ze,Ye],c=[];function l(r,h){return r[7]?1:0}return e=l(t),n=c[e]=_[e](t),{c(){n.c(),i=Fe()},m(r,h){c[e].m(r,h),L(r,i,h),a=!0},p(r,h){let g=e;e=l(r),e===g?c[e].p(r,h):(He(),k(c[g],1,1,()=>{c[g]=null}),De(),n=c[e],n?n.p(r,h):(n=c[e]=_[e](r),n.c()),v(n,1),n.m(i.parentNode,i))},i(r){a||(v(n),a=!0)},o(r){k(n),a=!1},d(r){r&&K(i),c[e].d(r)}}}function tt(t,e,n){let{value_is_output:i=!1}=e,{elem_id:a=""}=e,{elem_classes:_=[]}=e,{visible:c=!0}=e,{interactive:l}=e,{value:r=null}=e,{sources:h}=e,{label:g}=e,{root:I}=e,{show_label:U}=e,{container:J=!0}=e,{scale:O=null}=e,{min_width:R=void 0}=e,{loading_status:b}=e,{autoplay:w=!1}=e,{loop:o=!1}=e,{show_download_button:u}=e,{show_share_button:T=!1}=e,{editable:f=!0}=e,{waveform_options:S={show_recording_waveform:!0}}=e,{pending:Q}=e,{streaming:X}=e,{stream_every:Y}=e,{input_ready:V}=e,{recording:W=!1}=e,j=!1,Z="closed",q;function ne(s){Z=s,q(s)}const oe=()=>Z;let{set_time_limit:G}=e,{gradio:d}=e,D=null,F,A=r;const re=()=>{A===null||r===A||n(0,r=A)};let M,p,H="darkorange";Xe(()=>{H=getComputedStyle(document?.documentElement).getPropertyValue("--color-accent"),ae(),n(28,p.waveColor=S.waveform_color||"#9ca3af",p),n(28,p.progressColor=S.waveform_progress_color||H,p),n(28,p.mediaControls=S.show_controls,p),n(28,p.sampleRate=S.sample_rate||44100,p)});const y={color:S.trim_region_color,drag:!0,resize:!0};function ae(){document.documentElement.style.setProperty("--trim-region-color",y.color||H)}function le({detail:s}){const[We,je]=s.includes("Invalid file type")?["warning","complete"]:["error","error"];n(1,b=b||{}),n(1,b.status=je,b),n(1,b.message=s,b),d.dispatch(We,s)}Qe(()=>{n(32,i=!1)});const _e=()=>d.dispatch("clear_status",b),ue=s=>d.dispatch("share",s.detail),fe=s=>d.dispatch("error",s.detail),me=()=>d.dispatch("play"),de=()=>d.dispatch("pause"),ge=()=>d.dispatch("stop"),ce=()=>d.dispatch("clear_status",b),he=(...s)=>d.client.upload(...s),be=(...s)=>d.client.stream(...s);function we(s){W=s,n(2,W)}function pe(s){M=s,n(27,M)}function ve(s){j=s,n(24,j)}function ke(s){q=s,n(26,q)}function Se(s){G=s,n(3,G)}const ze=({detail:s})=>n(0,r=s),Be=({detail:s})=>{n(0,r=s),d.dispatch("stream",r)},Ce=({detail:s})=>n(27,M=s),Ie=()=>d.dispatch("edit"),Ae=()=>d.dispatch("play"),Pe=()=>d.dispatch("pause"),Ee=()=>d.dispatch("stop"),Ne=()=>d.dispatch("start_recording"),Ue=()=>d.dispatch("pause_recording"),Je=s=>d.dispatch("stop_recording"),Oe=()=>d.dispatch("upload"),Re=()=>d.dispatch("clear"),Te=()=>d.dispatch("close_stream","stream");return t.$$set=s=>{"value_is_output"in s&&n(32,i=s.value_is_output),"elem_id"in s&&n(4,a=s.elem_id),"elem_classes"in s&&n(5,_=s.elem_classes),"visible"in s&&n(6,c=s.visible),"interactive"in s&&n(7,l=s.interactive),"value"in s&&n(0,r=s.value),"sources"in s&&n(8,h=s.sources),"label"in s&&n(9,g=s.label),"root"in s&&n(10,I=s.root),"show_label"in s&&n(11,U=s.show_label),"container"in s&&n(12,J=s.container),"scale"in s&&n(13,O=s.scale),"min_width"in s&&n(14,R=s.min_width),"loading_status"in s&&n(1,b=s.loading_status),"autoplay"in s&&n(34,w=s.autoplay),"loop"in s&&n(15,o=s.loop),"show_download_button"in s&&n(16,u=s.show_download_button),"show_share_button"in s&&n(17,T=s.show_share_button),"editable"in s&&n(18,f=s.editable),"waveform_options"in s&&n(19,S=s.waveform_options),"pending"in s&&n(20,Q=s.pending),"streaming"in s&&n(21,X=s.streaming),"stream_every"in s&&n(22,Y=s.stream_every),"input_ready"in s&&n(33,V=s.input_ready),"recording"in s&&n(2,W=s.recording),"set_time_limit"in s&&n(3,G=s.set_time_limit),"gradio"in s&&n(23,d=s.gradio)},t.$$.update=()=>{t.$$.dirty[0]&16777216&&n(33,V=!j),t.$$.dirty[0]&1|t.$$.dirty[1]&128&&r&&A===null&&n(38,A=r),t.$$.dirty[0]&8388609|t.$$.dirty[1]&66&&JSON.stringify(r)!==JSON.stringify(D)&&(n(37,D=r),d.dispatch("change"),i||d.dispatch("input")),t.$$.dirty[0]&33554688&&!F&&h&&n(25,F=h[0]),t.$$.dirty[1]&8&&n(28,p={height:50,barWidth:2,barGap:3,cursorWidth:2,cursorColor:"#ddd5e9",autoplay:w,barRadius:10,dragToSeek:!0,normalize:!0,minPxPerSec:20})},[r,b,W,G,a,_,c,l,h,g,I,U,J,O,R,o,u,T,f,S,Q,X,Y,d,j,F,q,M,p,re,y,le,i,V,w,ne,oe,D,A,_e,ue,fe,me,de,ge,ce,he,be,we,pe,ve,ke,Se,ze,Be,Ce,Ie,Ae,Pe,Ee,Ne,Ue,Je,Oe,Re,Te]}class it extends Ve{constructor(e){super(),Ke(this,e,tt,et,Le,{value_is_output:32,elem_id:4,elem_classes:5,visible:6,interactive:7,value:0,sources:8,label:9,root:10,show_label:11,container:12,scale:13,min_width:14,loading_status:1,autoplay:34,loop:15,show_download_button:16,show_share_button:17,editable:18,waveform_options:19,pending:20,streaming:21,stream_every:22,input_ready:33,recording:2,modify_stream_state:35,get_stream_state:36,set_time_limit:3,gradio:23},null,[-1,-1,-1])}get value_is_output(){return this.$$.ctx[32]}set value_is_output(e){this.$$set({value_is_output:e}),m()}get elem_id(){return this.$$.ctx[4]}set elem_id(e){this.$$set({elem_id:e}),m()}get elem_classes(){return this.$$.ctx[5]}set elem_classes(e){this.$$set({elem_classes:e}),m()}get visible(){return this.$$.ctx[6]}set visible(e){this.$$set({visible:e}),m()}get interactive(){return this.$$.ctx[7]}set interactive(e){this.$$set({interactive:e}),m()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),m()}get sources(){return this.$$.ctx[8]}set sources(e){this.$$set({sources:e}),m()}get label(){return this.$$.ctx[9]}set label(e){this.$$set({label:e}),m()}get root(){return this.$$.ctx[10]}set root(e){this.$$set({root:e}),m()}get show_label(){return this.$$.ctx[11]}set show_label(e){this.$$set({show_label:e}),m()}get container(){return this.$$.ctx[12]}set container(e){this.$$set({container:e}),m()}get scale(){return this.$$.ctx[13]}set scale(e){this.$$set({scale:e}),m()}get min_width(){return this.$$.ctx[14]}set min_width(e){this.$$set({min_width:e}),m()}get loading_status(){return this.$$.ctx[1]}set loading_status(e){this.$$set({loading_status:e}),m()}get autoplay(){return this.$$.ctx[34]}set autoplay(e){this.$$set({autoplay:e}),m()}get loop(){return this.$$.ctx[15]}set loop(e){this.$$set({loop:e}),m()}get show_download_button(){return this.$$.ctx[16]}set show_download_button(e){this.$$set({show_download_button:e}),m()}get show_share_button(){return this.$$.ctx[17]}set show_share_button(e){this.$$set({show_share_button:e}),m()}get editable(){return this.$$.ctx[18]}set editable(e){this.$$set({editable:e}),m()}get waveform_options(){return this.$$.ctx[19]}set waveform_options(e){this.$$set({waveform_options:e}),m()}get pending(){return this.$$.ctx[20]}set pending(e){this.$$set({pending:e}),m()}get streaming(){return this.$$.ctx[21]}set streaming(e){this.$$set({streaming:e}),m()}get stream_every(){return this.$$.ctx[22]}set stream_every(e){this.$$set({stream_every:e}),m()}get input_ready(){return this.$$.ctx[33]}set input_ready(e){this.$$set({input_ready:e}),m()}get recording(){return this.$$.ctx[2]}set recording(e){this.$$set({recording:e}),m()}get modify_stream_state(){return this.$$.ctx[35]}get get_stream_state(){return this.$$.ctx[36]}get set_time_limit(){return this.$$.ctx[3]}set set_time_limit(e){this.$$set({set_time_limit:e}),m()}get gradio(){return this.$$.ctx[23]}set gradio(e){this.$$set({gradio:e}),m()}}const jt=it;export{Dt as BaseExample,Ge as BaseInteractiveAudio,Mt as BasePlayer,qe as BaseStaticAudio,jt as default};
//# sourceMappingURL=index-C4P1xbBx.js.map
