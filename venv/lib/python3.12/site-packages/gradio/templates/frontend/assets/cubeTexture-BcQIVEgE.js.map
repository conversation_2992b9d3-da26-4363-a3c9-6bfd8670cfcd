{"version": 3, "file": "cubeTexture-BcQIVEgE.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Materials/Textures/cubeTexture.js"], "sourcesContent": ["import { __decorate } from \"../../tslib.es6.js\";\nimport { serialize, serializeAsMatrix, serializeAsVector3 } from \"../../Misc/decorators.js\";\nimport { Tools } from \"../../Misc/tools.js\";\nimport { Matrix, TmpVectors, Vector3 } from \"../../Maths/math.vector.js\";\nimport { BaseTexture } from \"../../Materials/Textures/baseTexture.js\";\nimport { Texture } from \"../../Materials/Textures/texture.js\";\n\nimport { GetClass, RegisterClass } from \"../../Misc/typeStore.js\";\nimport { Observable } from \"../../Misc/observable.js\";\nimport { SerializationHelper } from \"../../Misc/decorators.serialization.js\";\nimport \"../../Engines/AbstractEngine/abstractEngine.cubeTexture.js\";\n// The default scale applied to environment texture. This manages the range of LOD level used for IBL according to the roughness\nconst defaultLodScale = 0.8;\n/**\n * Class for creating a cube texture\n */\nexport class CubeTexture extends BaseTexture {\n    /**\n     * Gets or sets the size of the bounding box associated with the cube texture\n     * When defined, the cubemap will switch to local mode\n     * @see https://community.arm.com/graphics/b/blog/posts/reflections-based-on-local-cubemaps-in-unity\n     * @example https://www.babylonjs-playground.com/#RNASML\n     */\n    set boundingBoxSize(value) {\n        if (this._boundingBoxSize && this._boundingBoxSize.equals(value)) {\n            return;\n        }\n        this._boundingBoxSize = value;\n        const scene = this.getScene();\n        if (scene) {\n            scene.markAllMaterialsAsDirty(1);\n        }\n    }\n    /**\n     * Returns the bounding box size\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/materials/using/reflectionTexture#using-local-cubemap-mode\n     */\n    get boundingBoxSize() {\n        return this._boundingBoxSize;\n    }\n    /**\n     * Sets texture matrix rotation angle around Y axis in radians.\n     */\n    set rotationY(value) {\n        this._rotationY = value;\n        this.setReflectionTextureMatrix(Matrix.RotationY(this._rotationY));\n    }\n    /**\n     * Gets texture matrix rotation angle around Y axis radians.\n     */\n    get rotationY() {\n        return this._rotationY;\n    }\n    /**\n     * Are mip maps generated for this texture or not.\n     */\n    get noMipmap() {\n        return this._noMipmap;\n    }\n    /**\n     * Gets the forced extension (if any)\n     */\n    get forcedExtension() {\n        return this._forcedExtension;\n    }\n    /**\n     * Creates a cube texture from an array of image urls\n     * @param files defines an array of image urls\n     * @param scene defines the hosting scene\n     * @param noMipmap specifies if mip maps are not used\n     * @returns a cube texture\n     */\n    static CreateFromImages(files, scene, noMipmap) {\n        let rootUrlKey = \"\";\n        files.forEach((url) => (rootUrlKey += url));\n        return new CubeTexture(rootUrlKey, scene, null, noMipmap, files);\n    }\n    /**\n     * Creates and return a texture created from prefilterd data by tools like IBL Baker or Lys.\n     * @param url defines the url of the prefiltered texture\n     * @param scene defines the scene the texture is attached to\n     * @param forcedExtension defines the extension of the file if different from the url\n     * @param createPolynomials defines whether or not to create polynomial harmonics from the texture data if necessary\n     * @returns the prefiltered texture\n     */\n    static CreateFromPrefilteredData(url, scene, forcedExtension = null, createPolynomials = true) {\n        const oldValue = scene.useDelayedTextureLoading;\n        scene.useDelayedTextureLoading = false;\n        const result = new CubeTexture(url, scene, null, false, null, null, null, undefined, true, forcedExtension, createPolynomials);\n        scene.useDelayedTextureLoading = oldValue;\n        return result;\n    }\n    /**\n     * Creates a cube texture to use with reflection for instance. It can be based upon dds or six images as well\n     * as prefiltered data.\n     * @param rootUrl defines the url of the texture or the root name of the six images\n     * @param sceneOrEngine defines the scene or engine the texture is attached to\n     * @param extensionsOrOptions defines the suffixes add to the picture name in case six images are in use like _px.jpg or set of all options to create the cube texture\n     * @param noMipmap defines if mipmaps should be created or not\n     * @param files defines the six files to load for the different faces in that order: px, py, pz, nx, ny, nz\n     * @param onLoad defines a callback triggered at the end of the file load if no errors occurred\n     * @param onError defines a callback triggered in case of error during load\n     * @param format defines the internal format to use for the texture once loaded\n     * @param prefiltered defines whether or not the texture is created from prefiltered data\n     * @param forcedExtension defines the extensions to use (force a special type of file to load) in case it is different from the file name\n     * @param createPolynomials defines whether or not to create polynomial harmonics from the texture data if necessary\n     * @param lodScale defines the scale applied to environment texture. This manages the range of LOD level used for IBL according to the roughness\n     * @param lodOffset defines the offset applied to environment texture. This manages first LOD level used for IBL according to the roughness\n     * @param loaderOptions options to be passed to the loader\n     * @param useSRGBBuffer Defines if the texture must be loaded in a sRGB GPU buffer (if supported by the GPU) (default: false)\n     * @returns the cube texture\n     */\n    constructor(rootUrl, sceneOrEngine, extensionsOrOptions = null, noMipmap = false, files = null, onLoad = null, onError = null, format = 5, prefiltered = false, forcedExtension = null, createPolynomials = false, lodScale = defaultLodScale, lodOffset = 0, loaderOptions, useSRGBBuffer) {\n        super(sceneOrEngine);\n        /**\n         * Observable triggered once the texture has been loaded.\n         */\n        this.onLoadObservable = new Observable();\n        /**\n         * Gets or sets the center of the bounding box associated with the cube texture.\n         * It must define where the camera used to render the texture was set\n         * @see https://doc.babylonjs.com/features/featuresDeepDive/materials/using/reflectionTexture#using-local-cubemap-mode\n         */\n        this.boundingBoxPosition = Vector3.Zero();\n        this._rotationY = 0;\n        /** @internal */\n        this._files = null;\n        this._forcedExtension = null;\n        this._extensions = null;\n        this._textureMatrixRefraction = new Matrix();\n        this._buffer = null;\n        this.name = rootUrl;\n        this.url = rootUrl;\n        this._noMipmap = noMipmap;\n        this.hasAlpha = false;\n        this.isCube = true;\n        this._textureMatrix = Matrix.Identity();\n        this.coordinatesMode = Texture.CUBIC_MODE;\n        let extensions = null;\n        let buffer = null;\n        if (extensionsOrOptions !== null && !Array.isArray(extensionsOrOptions)) {\n            extensions = extensionsOrOptions.extensions ?? null;\n            this._noMipmap = extensionsOrOptions.noMipmap ?? false;\n            files = extensionsOrOptions.files ?? null;\n            buffer = extensionsOrOptions.buffer ?? null;\n            this._format = extensionsOrOptions.format ?? 5;\n            prefiltered = extensionsOrOptions.prefiltered ?? false;\n            forcedExtension = extensionsOrOptions.forcedExtension ?? null;\n            this._createPolynomials = extensionsOrOptions.createPolynomials ?? false;\n            this._lodScale = extensionsOrOptions.lodScale ?? defaultLodScale;\n            this._lodOffset = extensionsOrOptions.lodOffset ?? 0;\n            this._loaderOptions = extensionsOrOptions.loaderOptions;\n            this._useSRGBBuffer = extensionsOrOptions.useSRGBBuffer;\n            onLoad = extensionsOrOptions.onLoad ?? null;\n            onError = extensionsOrOptions.onError ?? null;\n        }\n        else {\n            this._noMipmap = noMipmap;\n            this._format = format;\n            this._createPolynomials = createPolynomials;\n            extensions = extensionsOrOptions;\n            this._loaderOptions = loaderOptions;\n            this._useSRGBBuffer = useSRGBBuffer;\n            this._lodScale = lodScale;\n            this._lodOffset = lodOffset;\n        }\n        if (!rootUrl && !files) {\n            return;\n        }\n        this.updateURL(rootUrl, forcedExtension, onLoad, prefiltered, onError, extensions, this.getScene()?.useDelayedTextureLoading, files, buffer);\n    }\n    /**\n     * Get the current class name of the texture useful for serialization or dynamic coding.\n     * @returns \"CubeTexture\"\n     */\n    getClassName() {\n        return \"CubeTexture\";\n    }\n    /**\n     * Update the url (and optional buffer) of this texture if url was null during construction.\n     * @param url the url of the texture\n     * @param forcedExtension defines the extension to use\n     * @param onLoad callback called when the texture is loaded  (defaults to null)\n     * @param prefiltered Defines whether the updated texture is prefiltered or not\n     * @param onError callback called if there was an error during the loading process (defaults to null)\n     * @param extensions defines the suffixes add to the picture name in case six images are in use like _px.jpg...\n     * @param delayLoad defines if the texture should be loaded now (false by default)\n     * @param files defines the six files to load for the different faces in that order: px, py, pz, nx, ny, nz\n     * @param buffer the buffer to use instead of loading from the url\n     */\n    updateURL(url, forcedExtension = null, onLoad = null, prefiltered = false, onError = null, extensions = null, delayLoad = false, files = null, buffer = null) {\n        if (!this.name || this.name.startsWith(\"data:\")) {\n            this.name = url;\n        }\n        this.url = url;\n        if (forcedExtension) {\n            this._forcedExtension = forcedExtension;\n        }\n        const lastDot = url.lastIndexOf(\".\");\n        const extension = forcedExtension ? forcedExtension : lastDot > -1 ? url.substring(lastDot).toLowerCase() : \"\";\n        const isDDS = extension.indexOf(\".dds\") === 0;\n        const isEnv = extension.indexOf(\".env\") === 0;\n        const isBasis = extension.indexOf(\".basis\") === 0;\n        if (isEnv) {\n            this.gammaSpace = false;\n            this._prefiltered = false;\n            this.anisotropicFilteringLevel = 1;\n        }\n        else {\n            this._prefiltered = prefiltered;\n            if (prefiltered) {\n                this.gammaSpace = false;\n                this.anisotropicFilteringLevel = 1;\n            }\n        }\n        if (files) {\n            this._files = files;\n        }\n        else {\n            if (!isBasis && !isEnv && !isDDS && !extensions) {\n                extensions = [\"_px.jpg\", \"_py.jpg\", \"_pz.jpg\", \"_nx.jpg\", \"_ny.jpg\", \"_nz.jpg\"];\n            }\n            this._files = this._files || [];\n            this._files.length = 0;\n            if (extensions) {\n                for (let index = 0; index < extensions.length; index++) {\n                    this._files.push(url + extensions[index]);\n                }\n                this._extensions = extensions;\n            }\n        }\n        this._buffer = buffer;\n        if (delayLoad) {\n            this.delayLoadState = 4;\n            this._delayedOnLoad = onLoad;\n            this._delayedOnError = onError;\n        }\n        else {\n            this._loadTexture(onLoad, onError);\n        }\n    }\n    /**\n     * Delays loading of the cube texture\n     * @param forcedExtension defines the extension to use\n     */\n    delayLoad(forcedExtension) {\n        if (this.delayLoadState !== 4) {\n            return;\n        }\n        if (forcedExtension) {\n            this._forcedExtension = forcedExtension;\n        }\n        this.delayLoadState = 1;\n        this._loadTexture(this._delayedOnLoad, this._delayedOnError);\n    }\n    /**\n     * Returns the reflection texture matrix\n     * @returns the reflection texture matrix\n     */\n    getReflectionTextureMatrix() {\n        return this._textureMatrix;\n    }\n    /**\n     * Sets the reflection texture matrix\n     * @param value Reflection texture matrix\n     */\n    setReflectionTextureMatrix(value) {\n        if (value.updateFlag === this._textureMatrix.updateFlag) {\n            return;\n        }\n        if (value.isIdentity() !== this._textureMatrix.isIdentity()) {\n            this.getScene()?.markAllMaterialsAsDirty(1, (mat) => mat.getActiveTextures().indexOf(this) !== -1);\n        }\n        this._textureMatrix = value;\n        if (!this.getScene()?.useRightHandedSystem) {\n            return;\n        }\n        const scale = TmpVectors.Vector3[0];\n        const quat = TmpVectors.Quaternion[0];\n        const trans = TmpVectors.Vector3[1];\n        this._textureMatrix.decompose(scale, quat, trans);\n        quat.z *= -1; // these two operations correspond to negating the x and y euler angles\n        quat.w *= -1;\n        Matrix.ComposeToRef(scale, quat, trans, this._textureMatrixRefraction);\n    }\n    /**\n     * Gets a suitable rotate/transform matrix when the texture is used for refraction.\n     * There's a separate function from getReflectionTextureMatrix because refraction requires a special configuration of the matrix in right-handed mode.\n     * @returns The refraction matrix\n     */\n    getRefractionTextureMatrix() {\n        return this.getScene()?.useRightHandedSystem ? this._textureMatrixRefraction : this._textureMatrix;\n    }\n    _loadTexture(onLoad = null, onError = null) {\n        const scene = this.getScene();\n        const oldTexture = this._texture;\n        this._texture = this._getFromCache(this.url, this._noMipmap, undefined, undefined, this._useSRGBBuffer, this.isCube);\n        const onLoadProcessing = () => {\n            this.onLoadObservable.notifyObservers(this);\n            if (oldTexture) {\n                oldTexture.dispose();\n                this.getScene()?.markAllMaterialsAsDirty(1);\n            }\n            if (onLoad) {\n                onLoad();\n            }\n        };\n        const errorHandler = (message, exception) => {\n            this._loadingError = true;\n            this._errorObject = { message, exception };\n            if (onError) {\n                onError(message, exception);\n            }\n            Texture.OnTextureLoadErrorObservable.notifyObservers(this);\n        };\n        if (!this._texture) {\n            if (this._prefiltered) {\n                this._texture = this._getEngine().createPrefilteredCubeTexture(this.url, scene, this._lodScale, this._lodOffset, onLoad, errorHandler, this._format, this._forcedExtension, this._createPolynomials);\n            }\n            else {\n                this._texture = this._getEngine().createCubeTexture(this.url, scene, this._files, this._noMipmap, onLoad, errorHandler, this._format, this._forcedExtension, false, this._lodScale, this._lodOffset, null, this._loaderOptions, !!this._useSRGBBuffer, this._buffer);\n            }\n            this._texture?.onLoadedObservable.add(() => this.onLoadObservable.notifyObservers(this));\n        }\n        else {\n            if (this._texture.isReady) {\n                Tools.SetImmediate(() => onLoadProcessing());\n            }\n            else {\n                this._texture.onLoadedObservable.add(() => onLoadProcessing());\n            }\n        }\n    }\n    /**\n     * Parses text to create a cube texture\n     * @param parsedTexture define the serialized text to read from\n     * @param scene defines the hosting scene\n     * @param rootUrl defines the root url of the cube texture\n     * @returns a cube texture\n     */\n    static Parse(parsedTexture, scene, rootUrl) {\n        const texture = SerializationHelper.Parse(() => {\n            let prefiltered = false;\n            if (parsedTexture.prefiltered) {\n                prefiltered = parsedTexture.prefiltered;\n            }\n            return new CubeTexture(rootUrl + (parsedTexture.url ?? parsedTexture.name), scene, parsedTexture.extensions, false, parsedTexture.files || null, null, null, undefined, prefiltered, parsedTexture.forcedExtension);\n        }, parsedTexture, scene);\n        // Local Cubemaps\n        if (parsedTexture.boundingBoxPosition) {\n            texture.boundingBoxPosition = Vector3.FromArray(parsedTexture.boundingBoxPosition);\n        }\n        if (parsedTexture.boundingBoxSize) {\n            texture.boundingBoxSize = Vector3.FromArray(parsedTexture.boundingBoxSize);\n        }\n        // Animations\n        if (parsedTexture.animations) {\n            for (let animationIndex = 0; animationIndex < parsedTexture.animations.length; animationIndex++) {\n                const parsedAnimation = parsedTexture.animations[animationIndex];\n                const internalClass = GetClass(\"BABYLON.Animation\");\n                if (internalClass) {\n                    texture.animations.push(internalClass.Parse(parsedAnimation));\n                }\n            }\n        }\n        return texture;\n    }\n    /**\n     * Makes a clone, or deep copy, of the cube texture\n     * @returns a new cube texture\n     */\n    clone() {\n        let uniqueId = 0;\n        const newCubeTexture = SerializationHelper.Clone(() => {\n            const cubeTexture = new CubeTexture(this.url, this.getScene() || this._getEngine(), this._extensions, this._noMipmap, this._files);\n            uniqueId = cubeTexture.uniqueId;\n            return cubeTexture;\n        }, this);\n        newCubeTexture.uniqueId = uniqueId;\n        return newCubeTexture;\n    }\n}\n__decorate([\n    serialize()\n], CubeTexture.prototype, \"url\", void 0);\n__decorate([\n    serializeAsVector3()\n], CubeTexture.prototype, \"boundingBoxPosition\", void 0);\n__decorate([\n    serializeAsVector3()\n], CubeTexture.prototype, \"boundingBoxSize\", null);\n__decorate([\n    serialize(\"rotationY\")\n], CubeTexture.prototype, \"rotationY\", null);\n__decorate([\n    serialize(\"files\")\n], CubeTexture.prototype, \"_files\", void 0);\n__decorate([\n    serialize(\"forcedExtension\")\n], CubeTexture.prototype, \"_forcedExtension\", void 0);\n__decorate([\n    serialize(\"extensions\")\n], CubeTexture.prototype, \"_extensions\", void 0);\n__decorate([\n    serializeAsMatrix(\"textureMatrix\")\n], CubeTexture.prototype, \"_textureMatrix\", void 0);\n__decorate([\n    serializeAsMatrix(\"textureMatrixRefraction\")\n], CubeTexture.prototype, \"_textureMatrixRefraction\", void 0);\nTexture._CubeTextureParser = CubeTexture.Parse;\n// Some exporters relies on Tools.Instantiate\nRegisterClass(\"BABYLON.CubeTexture\", CubeTexture);\n//# sourceMappingURL=cubeTexture.js.map"], "names": ["defaultLodScale", "CubeTexture", "BaseTexture", "value", "scene", "Matrix", "files", "noMipmap", "rootUrlKey", "url", "forcedExtension", "createPolynomials", "oldValue", "result", "rootUrl", "scene<PERSON><PERSON><PERSON><PERSON><PERSON>", "extensionsOrOptions", "onLoad", "onError", "format", "prefiltered", "lodScale", "lodOffset", "loaderOptions", "useSRGBBuffer", "Observable", "Vector3", "Texture", "extensions", "buffer", "delayLoad", "lastDot", "extension", "isDDS", "isEnv", "isBasis", "index", "mat", "scale", "TmpVectors", "quat", "trans", "oldTexture", "onLoadProcessing", "<PERSON><PERSON><PERSON><PERSON>", "message", "exception", "Tools", "parsedTexture", "texture", "SerializationHelper", "animationIndex", "parsedAnimation", "internalClass", "GetClass", "uniqueId", "newCubeTexture", "cubeTexture", "__decorate", "serialize", "serializeAsVector3", "serializeAsMatrix", "RegisterClass"], "mappings": "2OAYA,MAAMA,EAAkB,GAIjB,MAAMC,UAAoBC,CAAY,CAOzC,IAAI,gBAAgBC,EAAO,CACvB,GAAI,KAAK,kBAAoB,KAAK,iBAAiB,OAAOA,CAAK,EAC3D,OAEJ,KAAK,iBAAmBA,EACxB,MAAMC,EAAQ,KAAK,WACfA,GACAA,EAAM,wBAAwB,CAAC,CAEtC,CAKD,IAAI,iBAAkB,CAClB,OAAO,KAAK,gBACf,CAID,IAAI,UAAUD,EAAO,CACjB,KAAK,WAAaA,EAClB,KAAK,2BAA2BE,EAAO,UAAU,KAAK,UAAU,CAAC,CACpE,CAID,IAAI,WAAY,CACZ,OAAO,KAAK,UACf,CAID,IAAI,UAAW,CACX,OAAO,KAAK,SACf,CAID,IAAI,iBAAkB,CAClB,OAAO,KAAK,gBACf,CAQD,OAAO,iBAAiBC,EAAOF,EAAOG,EAAU,CAC5C,IAAIC,EAAa,GACjB,OAAAF,EAAM,QAASG,GAASD,GAAcC,CAAI,EACnC,IAAIR,EAAYO,EAAYJ,EAAO,KAAMG,EAAUD,CAAK,CAClE,CASD,OAAO,0BAA0BG,EAAKL,EAAOM,EAAkB,KAAMC,EAAoB,GAAM,CAC3F,MAAMC,EAAWR,EAAM,yBACvBA,EAAM,yBAA2B,GACjC,MAAMS,EAAS,IAAIZ,EAAYQ,EAAKL,EAAO,KAAM,GAAO,KAAM,KAAM,KAAM,OAAW,GAAMM,EAAiBC,CAAiB,EAC7H,OAAAP,EAAM,yBAA2BQ,EAC1BC,CACV,CAqBD,YAAYC,EAASC,EAAeC,EAAsB,KAAMT,EAAW,GAAOD,EAAQ,KAAMW,EAAS,KAAMC,EAAU,KAAMC,EAAS,EAAGC,EAAc,GAAOV,EAAkB,KAAMC,EAAoB,GAAOU,EAAWrB,EAAiBsB,EAAY,EAAGC,EAAeC,EAAe,CACxR,MAAMT,CAAa,EAInB,KAAK,iBAAmB,IAAIU,EAM5B,KAAK,oBAAsBC,EAAQ,OACnC,KAAK,WAAa,EAElB,KAAK,OAAS,KACd,KAAK,iBAAmB,KACxB,KAAK,YAAc,KACnB,KAAK,yBAA2B,IAAIrB,EACpC,KAAK,QAAU,KACf,KAAK,KAAOS,EACZ,KAAK,IAAMA,EACX,KAAK,UAAYP,EACjB,KAAK,SAAW,GAChB,KAAK,OAAS,GACd,KAAK,eAAiBF,EAAO,WAC7B,KAAK,gBAAkBsB,EAAQ,WAC/B,IAAIC,EAAa,KACbC,EAAS,KACTb,IAAwB,MAAQ,CAAC,MAAM,QAAQA,CAAmB,GAClEY,EAAaZ,EAAoB,YAAc,KAC/C,KAAK,UAAYA,EAAoB,UAAY,GACjDV,EAAQU,EAAoB,OAAS,KACrCa,EAASb,EAAoB,QAAU,KACvC,KAAK,QAAUA,EAAoB,QAAU,EAC7CI,EAAcJ,EAAoB,aAAe,GACjDN,EAAkBM,EAAoB,iBAAmB,KACzD,KAAK,mBAAqBA,EAAoB,mBAAqB,GACnE,KAAK,UAAYA,EAAoB,UAAYhB,EACjD,KAAK,WAAagB,EAAoB,WAAa,EACnD,KAAK,eAAiBA,EAAoB,cAC1C,KAAK,eAAiBA,EAAoB,cAC1CC,EAASD,EAAoB,QAAU,KACvCE,EAAUF,EAAoB,SAAW,OAGzC,KAAK,UAAYT,EACjB,KAAK,QAAUY,EACf,KAAK,mBAAqBR,EAC1BiB,EAAaZ,EACb,KAAK,eAAiBO,EACtB,KAAK,eAAiBC,EACtB,KAAK,UAAYH,EACjB,KAAK,WAAaC,GAElB,GAACR,GAAW,CAACR,IAGjB,KAAK,UAAUQ,EAASJ,EAAiBO,EAAQG,EAAaF,EAASU,EAAY,KAAK,SAAU,GAAE,yBAA0BtB,EAAOuB,CAAM,CAC9I,CAKD,cAAe,CACX,MAAO,aACV,CAaD,UAAUpB,EAAKC,EAAkB,KAAMO,EAAS,KAAMG,EAAc,GAAOF,EAAU,KAAMU,EAAa,KAAME,EAAY,GAAOxB,EAAQ,KAAMuB,EAAS,KAAM,EACtJ,CAAC,KAAK,MAAQ,KAAK,KAAK,WAAW,OAAO,KAC1C,KAAK,KAAOpB,GAEhB,KAAK,IAAMA,EACPC,IACA,KAAK,iBAAmBA,GAE5B,MAAMqB,EAAUtB,EAAI,YAAY,GAAG,EAC7BuB,EAAYtB,IAAoCqB,EAAU,GAAKtB,EAAI,UAAUsB,CAAO,EAAE,YAAW,EAAK,IACtGE,EAAQD,EAAU,QAAQ,MAAM,IAAM,EACtCE,EAAQF,EAAU,QAAQ,MAAM,IAAM,EACtCG,EAAUH,EAAU,QAAQ,QAAQ,IAAM,EAahD,GAZIE,GACA,KAAK,WAAa,GAClB,KAAK,aAAe,GACpB,KAAK,0BAA4B,IAGjC,KAAK,aAAed,EAChBA,IACA,KAAK,WAAa,GAClB,KAAK,0BAA4B,IAGrCd,EACA,KAAK,OAASA,UAGV,CAAC6B,GAAW,CAACD,GAAS,CAACD,GAAS,CAACL,IACjCA,EAAa,CAAC,UAAW,UAAW,UAAW,UAAW,UAAW,SAAS,GAElF,KAAK,OAAS,KAAK,QAAU,CAAA,EAC7B,KAAK,OAAO,OAAS,EACjBA,EAAY,CACZ,QAASQ,EAAQ,EAAGA,EAAQR,EAAW,OAAQQ,IAC3C,KAAK,OAAO,KAAK3B,EAAMmB,EAAWQ,CAAK,CAAC,EAE5C,KAAK,YAAcR,CACtB,CAEL,KAAK,QAAUC,EACXC,GACA,KAAK,eAAiB,EACtB,KAAK,eAAiBb,EACtB,KAAK,gBAAkBC,GAGvB,KAAK,aAAaD,EAAQC,CAAO,CAExC,CAKD,UAAUR,EAAiB,CACnB,KAAK,iBAAmB,IAGxBA,IACA,KAAK,iBAAmBA,GAE5B,KAAK,eAAiB,EACtB,KAAK,aAAa,KAAK,eAAgB,KAAK,eAAe,EAC9D,CAKD,4BAA6B,CACzB,OAAO,KAAK,cACf,CAKD,2BAA2BP,EAAO,CAQ9B,GAPIA,EAAM,aAAe,KAAK,eAAe,aAGzCA,EAAM,WAAY,IAAK,KAAK,eAAe,WAAU,GACrD,KAAK,SAAU,GAAE,wBAAwB,EAAIkC,GAAQA,EAAI,kBAAmB,EAAC,QAAQ,IAAI,IAAM,EAAE,EAErG,KAAK,eAAiBlC,EAClB,CAAC,KAAK,SAAU,GAAE,sBAClB,OAEJ,MAAMmC,EAAQC,EAAW,QAAQ,CAAC,EAC5BC,EAAOD,EAAW,WAAW,CAAC,EAC9BE,EAAQF,EAAW,QAAQ,CAAC,EAClC,KAAK,eAAe,UAAUD,EAAOE,EAAMC,CAAK,EAChDD,EAAK,GAAK,GACVA,EAAK,GAAK,GACVnC,EAAO,aAAaiC,EAAOE,EAAMC,EAAO,KAAK,wBAAwB,CACxE,CAMD,4BAA6B,CACzB,OAAO,KAAK,YAAY,qBAAuB,KAAK,yBAA2B,KAAK,cACvF,CACD,aAAaxB,EAAS,KAAMC,EAAU,KAAM,CACxC,MAAMd,EAAQ,KAAK,WACbsC,EAAa,KAAK,SACxB,KAAK,SAAW,KAAK,cAAc,KAAK,IAAK,KAAK,UAAW,OAAW,OAAW,KAAK,eAAgB,KAAK,MAAM,EACnH,MAAMC,EAAmB,IAAM,CAC3B,KAAK,iBAAiB,gBAAgB,IAAI,EACtCD,IACAA,EAAW,QAAO,EAClB,KAAK,SAAQ,GAAI,wBAAwB,CAAC,GAE1CzB,GACAA,GAEhB,EACc2B,EAAe,CAACC,EAASC,IAAc,CACzC,KAAK,cAAgB,GACrB,KAAK,aAAe,CAAE,QAAAD,EAAS,UAAAC,CAAS,EACpC5B,GACAA,EAAQ2B,EAASC,CAAS,EAE9BnB,EAAQ,6BAA6B,gBAAgB,IAAI,CACrE,EACa,KAAK,SAUF,KAAK,SAAS,QACdoB,EAAM,aAAa,IAAMJ,EAAgB,CAAE,EAG3C,KAAK,SAAS,mBAAmB,IAAI,IAAMA,EAAkB,CAAA,GAb7D,KAAK,aACL,KAAK,SAAW,KAAK,WAAY,EAAC,6BAA6B,KAAK,IAAKvC,EAAO,KAAK,UAAW,KAAK,WAAYa,EAAQ2B,EAAc,KAAK,QAAS,KAAK,iBAAkB,KAAK,kBAAkB,EAGnM,KAAK,SAAW,KAAK,WAAU,EAAG,kBAAkB,KAAK,IAAKxC,EAAO,KAAK,OAAQ,KAAK,UAAWa,EAAQ2B,EAAc,KAAK,QAAS,KAAK,iBAAkB,GAAO,KAAK,UAAW,KAAK,WAAY,KAAM,KAAK,eAAgB,CAAC,CAAC,KAAK,eAAgB,KAAK,OAAO,EAEvQ,KAAK,UAAU,mBAAmB,IAAI,IAAM,KAAK,iBAAiB,gBAAgB,IAAI,CAAC,EAU9F,CAQD,OAAO,MAAMI,EAAe5C,EAAOU,EAAS,CACxC,MAAMmC,EAAUC,EAAoB,MAAM,IAAM,CAC5C,IAAI9B,EAAc,GAClB,OAAI4B,EAAc,cACd5B,EAAc4B,EAAc,aAEzB,IAAI/C,EAAYa,GAAWkC,EAAc,KAAOA,EAAc,MAAO5C,EAAO4C,EAAc,WAAY,GAAOA,EAAc,OAAS,KAAM,KAAM,KAAM,OAAW5B,EAAa4B,EAAc,eAAe,CAC9N,EAAWA,EAAe5C,CAAK,EASvB,GAPI4C,EAAc,sBACdC,EAAQ,oBAAsBvB,EAAQ,UAAUsB,EAAc,mBAAmB,GAEjFA,EAAc,kBACdC,EAAQ,gBAAkBvB,EAAQ,UAAUsB,EAAc,eAAe,GAGzEA,EAAc,WACd,QAASG,EAAiB,EAAGA,EAAiBH,EAAc,WAAW,OAAQG,IAAkB,CAC7F,MAAMC,EAAkBJ,EAAc,WAAWG,CAAc,EACzDE,EAAgBC,EAAS,mBAAmB,EAC9CD,GACAJ,EAAQ,WAAW,KAAKI,EAAc,MAAMD,CAAe,CAAC,CAEnE,CAEL,OAAOH,CACV,CAKD,OAAQ,CACJ,IAAIM,EAAW,EACf,MAAMC,EAAiBN,EAAoB,MAAM,IAAM,CACnD,MAAMO,EAAc,IAAIxD,EAAY,KAAK,IAAK,KAAK,YAAc,KAAK,WAAY,EAAE,KAAK,YAAa,KAAK,UAAW,KAAK,MAAM,EACjI,OAAAsD,EAAWE,EAAY,SAChBA,CACV,EAAE,IAAI,EACP,OAAAD,EAAe,SAAWD,EACnBC,CACV,CACL,CACAE,EAAW,CACPC,EAAW,CACf,EAAG1D,EAAY,UAAW,MAAO,MAAM,EACvCyD,EAAW,CACPE,EAAoB,CACxB,EAAG3D,EAAY,UAAW,sBAAuB,MAAM,EACvDyD,EAAW,CACPE,EAAoB,CACxB,EAAG3D,EAAY,UAAW,kBAAmB,IAAI,EACjDyD,EAAW,CACPC,EAAU,WAAW,CACzB,EAAG1D,EAAY,UAAW,YAAa,IAAI,EAC3CyD,EAAW,CACPC,EAAU,OAAO,CACrB,EAAG1D,EAAY,UAAW,SAAU,MAAM,EAC1CyD,EAAW,CACPC,EAAU,iBAAiB,CAC/B,EAAG1D,EAAY,UAAW,mBAAoB,MAAM,EACpDyD,EAAW,CACPC,EAAU,YAAY,CAC1B,EAAG1D,EAAY,UAAW,cAAe,MAAM,EAC/CyD,EAAW,CACPG,EAAkB,eAAe,CACrC,EAAG5D,EAAY,UAAW,iBAAkB,MAAM,EAClDyD,EAAW,CACPG,EAAkB,yBAAyB,CAC/C,EAAG5D,EAAY,UAAW,2BAA4B,MAAM,EAC5D0B,EAAQ,mBAAqB1B,EAAY,MAEzC6D,EAAc,sBAAuB7D,CAAW", "x_google_ignoreList": [0]}