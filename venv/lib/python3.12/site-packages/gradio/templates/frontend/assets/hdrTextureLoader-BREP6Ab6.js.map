{"version": 3, "file": "hdrTextureLoader-BREP6Ab6.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Materials/Textures/Loaders/hdrTextureLoader.js"], "sourcesContent": ["import { RGBE_ReadHeader, RGBE_ReadPixels } from \"../../../Misc/HighDynamicRange/hdr.js\";\n\n/**\n * Implementation of the HDR Texture Loader.\n * @internal\n */\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport class _HDRTextureLoader {\n    constructor() {\n        /**\n         * Defines whether the loader supports cascade loading the different faces.\n         */\n        this.supportCascades = false;\n    }\n    /**\n     * Uploads the cube texture data to the WebGL texture. It has already been bound.\n     * Cube texture are not supported by .hdr files\n     */\n    loadCubeData() {\n        // eslint-disable-next-line no-throw-literal\n        throw \".hdr not supported in Cube.\";\n    }\n    /**\n     * Uploads the 2D texture data to the WebGL texture. It has already been bound once in the callback.\n     * @param data contains the texture data\n     * @param texture defines the BabylonJS internal texture\n     * @param callback defines the method to call once ready to upload\n     */\n    loadData(data, texture, callback) {\n        const uint8array = new Uint8Array(data.buffer, data.byteOffset, data.byteLength);\n        const hdrInfo = RGBE_ReadHeader(uint8array);\n        const pixelsDataRGB32 = RGBE_ReadPixels(uint8array, hdrInfo);\n        const pixels = hdrInfo.width * hdrInfo.height;\n        const pixelsDataRGBA32 = new Float32Array(pixels * 4);\n        for (let i = 0; i < pixels; i += 1) {\n            pixelsDataRGBA32[i * 4] = pixelsDataRGB32[i * 3];\n            pixelsDataRGBA32[i * 4 + 1] = pixelsDataRGB32[i * 3 + 1];\n            pixelsDataRGBA32[i * 4 + 2] = pixelsDataRGB32[i * 3 + 2];\n            pixelsDataRGBA32[i * 4 + 3] = 1;\n        }\n        callback(hdrInfo.width, hdrInfo.height, texture.generateMipMaps, false, () => {\n            const engine = texture.getEngine();\n            texture.type = 1;\n            texture.format = 5;\n            texture._gammaSpace = false;\n            engine._uploadDataToTextureDirectly(texture, pixelsDataRGBA32);\n        });\n    }\n}\n//# sourceMappingURL=hdrTextureLoader.js.map"], "names": ["_HDRTextureLoader", "data", "texture", "callback", "uint8array", "hdrInfo", "RGBE_ReadHeader", "pixelsDataRGB32", "RGBE_ReadPixels", "pixels", "pixelsDataRGBA32", "i", "engine"], "mappings": "gIAOO,MAAMA,CAAkB,CAC3B,aAAc,CAIV,KAAK,gBAAkB,EAC1B,CAKD,cAAe,CAEX,KAAM,6BACT,CAOD,SAASC,EAAMC,EAASC,EAAU,CAC9B,MAAMC,EAAa,IAAI,WAAWH,EAAK,OAAQA,EAAK,WAAYA,EAAK,UAAU,EACzEI,EAAUC,EAAgBF,CAAU,EACpCG,EAAkBC,EAAgBJ,EAAYC,CAAO,EACrDI,EAASJ,EAAQ,MAAQA,EAAQ,OACjCK,EAAmB,IAAI,aAAaD,EAAS,CAAC,EACpD,QAASE,EAAI,EAAGA,EAAIF,EAAQE,GAAK,EAC7BD,EAAiBC,EAAI,CAAC,EAAIJ,EAAgBI,EAAI,CAAC,EAC/CD,EAAiBC,EAAI,EAAI,CAAC,EAAIJ,EAAgBI,EAAI,EAAI,CAAC,EACvDD,EAAiBC,EAAI,EAAI,CAAC,EAAIJ,EAAgBI,EAAI,EAAI,CAAC,EACvDD,EAAiBC,EAAI,EAAI,CAAC,EAAI,EAElCR,EAASE,EAAQ,MAAOA,EAAQ,OAAQH,EAAQ,gBAAiB,GAAO,IAAM,CAC1E,MAAMU,EAASV,EAAQ,YACvBA,EAAQ,KAAO,EACfA,EAAQ,OAAS,EACjBA,EAAQ,YAAc,GACtBU,EAAO,6BAA6BV,EAASQ,CAAgB,CACzE,CAAS,CACJ,CACL", "x_google_ignoreList": [0]}