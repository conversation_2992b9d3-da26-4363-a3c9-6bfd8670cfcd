import{_ as e,l as o,I as i,j as n,K as p}from"./mermaid.core-C-NdzACT.js";import{p as m}from"./mermaid-parser.core-CkTt5U05.js";import"./index-tg5Kngjw.js";import"./svelte/svelte.js";import"./dispatch-kxCwF96_.js";import"./step-Ce-xBr2D.js";import"./select-BigU4G0v.js";import"./_baseUniq-kYHsoIXm.js";import"./_basePickBy-Dqt9CHqL.js";import"./clone-DO5wYzpV.js";var g={parse:e(async r=>{const a=await m("info",r);o.debug(a)},"parse")},v={version:p.version},d=e(()=>v.version,"getVersion"),c={getVersion:d},l=e((r,a,s)=>{o.debug(`rendering info diagram
`+r);const t=i(a);n(t,100,400,!0),t.append("g").append("text").attr("x",100).attr("y",40).attr("class","version").attr("font-size",32).style("text-anchor","middle").text(`v${s}`)},"draw"),f={draw:l},I={parser:g,db:c,renderer:f};export{I as diagram};
//# sourceMappingURL=infoDiagram-SDLB2J7W-B5F6Rmf5.js.map
