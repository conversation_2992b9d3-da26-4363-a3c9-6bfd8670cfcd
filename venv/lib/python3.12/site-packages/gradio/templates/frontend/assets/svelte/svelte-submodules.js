function t(){}const n=t=>t;function e(t,n){for(const e in n)t[e]=n[e];return t}function r(t){return t()}function o(t){return"function"==typeof t}function i(n,...e){if(null==n){for(const t of e)t(void 0);return t}const r=n.subscribe(...e);return r.unsubscribe?()=>r.unsubscribe():r}function s(t){let n;return i(t,t=>n=t)(),n}function a(t){const n="string"==typeof t&&t.match(/^\s*(-?[\d.]+)([^\s]*)\s*$/);return n?[parseFloat(n[1]),n[2]||"px"]:[t,"px"]}const u="undefined"!=typeof window;let c=u?()=>window.performance.now():()=>Date.now(),f=u?t=>requestAnimationFrame(t):t;const p=new Set;function l(t){p.forEach(n=>{n.c(t)||(p.delete(n),n.f())}),0!==p.size&&f(l)}function d(t){let n;return 0===p.size&&f(l),{promise:new Promise(e=>{p.add(n={c:t,f:e})}),abort(){p.delete(n)}}}function h(t){const n=2.5949095;return(t*=2)<1?t*t*((n+1)*t-n)*.5:.5*((t-=2)*t*((n+1)*t+n)+2)}function y(t){const n=1.70158;return t*t*((n+1)*t-n)}function g(t){const n=1.70158;return--t*t*((n+1)*t+n)+1}function m(t){const n=t*t;return t<4/11?7.5625*n:t<8/11?9.075*n-9.9*t+3.4:t<.9?4356/361*n-35442/1805*t+16061/1805:10.8*t*t-20.52*t+10.72}function $(t){return t<.5?.5*(1-m(1-2*t)):.5*m(2*t-1)+.5}function b(t){return 1-m(1-t)}function w(t){return(t*=2)<1?-.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1)}function M(t){return 1-Math.sqrt(1-t*t)}function x(t){return Math.sqrt(1- --t*t)}function v(t){return t<.5?4*t*t*t:.5*Math.pow(2*t-2,3)+1}function C(t){return t*t*t}function P(t){const n=t-1;return n*n*n+1}function k(t){return t<.5?.5*Math.sin(13*Math.PI/2*2*t)*Math.pow(2,10*(2*t-1)):.5*Math.sin(-13*Math.PI/2*(2*t-1+1))*Math.pow(2,-10*(2*t-1))+1}function A(t){return Math.sin(13*t*Math.PI/2)*Math.pow(2,10*(t-1))}function S(t){return Math.sin(-13*(t+1)*Math.PI/2)*Math.pow(2,-10*t)+1}function F(t){return 0===t||1===t?t:t<.5?.5*Math.pow(2,20*t-10):-.5*Math.pow(2,10-20*t)+1}function q(t){return 0===t?t:Math.pow(2,10*(t-1))}function E(t){return 1===t?t:1-Math.pow(2,-10*t)}function I(t){return(t/=.5)<1?.5*t*t:-.5*(--t*(t-2)-1)}function j(t){return t*t}function z(t){return-t*(t-2)}function D(t){return t<.5?8*Math.pow(t,4):-8*Math.pow(t-1,4)+1}function O(t){return Math.pow(t,4)}function T(t){return Math.pow(t-1,3)*(1-t)+1}function B(t){return(t*=2)<1?.5*t*t*t*t*t:.5*((t-=2)*t*t*t*t+2)}function W(t){return t*t*t*t*t}function L(t){return--t*t*t*t*t+1}function R(t){return-.5*(Math.cos(Math.PI*t)-1)}function _(t){const n=Math.cos(t*Math.PI*.5);return Math.abs(n)<1e-14?1:1-n}function U(t){return Math.sin(t*Math.PI/2)}function G(t,{delay:n=0,duration:e=400,easing:r=v,amount:o=5,opacity:i=0}={}){const s=getComputedStyle(t),u=+s.opacity,c="none"===s.filter?"":s.filter,f=u*(1-i),[p,l]=a(o);return{delay:n,duration:e,easing:r,css:(t,n)=>`opacity: ${u-f*n}; filter: ${c} blur(${n*p}${l});`}}function H(t,{delay:e=0,duration:r=400,easing:o=n}={}){const i=+getComputedStyle(t).opacity;return{delay:e,duration:r,easing:o,css:t=>"opacity: "+t*i}}function J(t,{delay:n=0,duration:e=400,easing:r=P,x:o=0,y:i=0,opacity:s=0}={}){const u=getComputedStyle(t),c=+u.opacity,f="none"===u.transform?"":u.transform,p=c*(1-s),[l,d]=a(o),[h,y]=a(i);return{delay:n,duration:e,easing:r,css:(t,n)=>`\n\t\t\ttransform: ${f} translate(${(1-t)*l}${d}, ${(1-t)*h}${y});\n\t\t\topacity: ${c-p*n}`}}function K(t,{delay:n=0,duration:e=400,easing:r=P,axis:o="y"}={}){const i=getComputedStyle(t),s=+i.opacity,a="y"===o?"height":"width",u=parseFloat(i[a]),c="y"===o?["top","bottom"]:["left","right"],f=c.map(t=>`${t[0].toUpperCase()}${t.slice(1)}`),p=parseFloat(i[`padding${f[0]}`]),l=parseFloat(i[`padding${f[1]}`]),d=parseFloat(i[`margin${f[0]}`]),h=parseFloat(i[`margin${f[1]}`]),y=parseFloat(i[`border${f[0]}Width`]),g=parseFloat(i[`border${f[1]}Width`]);return{delay:n,duration:e,easing:r,css:t=>`overflow: hidden;opacity: ${Math.min(20*t,1)*s};${a}: ${t*u}px;padding-${c[0]}: ${t*p}px;padding-${c[1]}: ${t*l}px;margin-${c[0]}: ${t*d}px;margin-${c[1]}: ${t*h}px;border-${c[0]}-width: ${t*y}px;border-${c[1]}-width: ${t*g}px;`}}function N(t,{delay:n=0,duration:e=400,easing:r=P,start:o=0,opacity:i=0}={}){const s=getComputedStyle(t),a=+s.opacity,u="none"===s.transform?"":s.transform,c=1-o,f=a*(1-i);return{delay:n,duration:e,easing:r,css:(t,n)=>`\n\t\t\ttransform: ${u} scale(${1-c*n});\n\t\t\topacity: ${a-f*n}\n\t\t`}}function Q(t,{delay:n=0,speed:e,duration:r,easing:o=v}={}){let i=t.getTotalLength();const s=getComputedStyle(t);return"butt"!==s.strokeLinecap&&(i+=parseInt(s.strokeWidth)),void 0===r?r=void 0===e?800:i/e:"function"==typeof r&&(r=r(i)),{delay:n,duration:r,easing:o,css:(t,n)=>`\n\t\t\tstroke-dasharray: ${i};\n\t\t\tstroke-dashoffset: ${n*i};\n\t\t`}}function V({fallback:t,...n}){const r=new Map,i=new Map;function s(r,i,s){return(a,u)=>(r.set(u.key,a),()=>{if(i.has(u.key)){const t=i.get(u.key);return i.delete(u.key),function(t,r,i){const{delay:s=0,duration:a=t=>30*Math.sqrt(t),easing:u=P}=e(e({},n),i),c=t.getBoundingClientRect(),f=r.getBoundingClientRect(),p=c.left-f.left,l=c.top-f.top,d=c.width/f.width,h=c.height/f.height,y=Math.sqrt(p*p+l*l),g=getComputedStyle(r),m="none"===g.transform?"":g.transform,$=+g.opacity;return{delay:s,duration:o(a)?a(y):a,easing:u,css:(t,n)=>`\n\t\t\t\topacity: ${t*$};\n\t\t\t\ttransform-origin: top left;\n\t\t\t\ttransform: ${m} translate(${n*p}px,${n*l}px) scale(${t+(1-t)*d}, ${t+(1-t)*h});\n\t\t\t`}}(t,a,u)}return r.delete(u.key),t&&t(a,u,s)})}return[s(i,r,!1),s(r,i,!0)]}const X=[];function Y(t,n){return{subscribe:Z(t,n).subscribe}}function Z(n,e=t){let r;const o=new Set;function i(t){if(i=t,((e=n)!=e?i==i:e!==i||e&&"object"==typeof e||"function"==typeof e)&&(n=t,r)){const t=!X.length;for(const t of o)t[1](),X.push(t,n);if(t){for(let t=0;t<X.length;t+=2)X[t][0](X[t+1]);X.length=0}}var e,i}function s(t){i(t(n))}return{set:i,update:s,subscribe:function(a,u=t){const c=[a,u];return o.add(c),1===o.size&&(r=e(i,s)||t),a(n),()=>{o.delete(c),0===o.size&&r&&(r(),r=null)}}}}function tt(n,e,s){const a=!Array.isArray(n),u=a?[n]:n;if(!u.every(Boolean))throw new Error("derived() expects stores as input, got a falsy value");const c=e.length<2;return Y(s,(n,s)=>{let f=!1;const p=[];let l=0,d=t;const h=()=>{if(l)return;d();const r=e(a?p[0]:p,n,s);c?n(r):d=o(r)?r:t},y=u.map((t,n)=>i(t,t=>{p[n]=t,l&=~(1<<n),f&&h()},()=>{l|=1<<n}));return f=!0,h(),function(){y.forEach(r),d(),f=!1}})}function nt(t){return{subscribe:t.subscribe.bind(t)}}function et(t){return"[object Date]"===Object.prototype.toString.call(t)}function rt(t,n,e,r){if("number"==typeof e||et(e)){const o=r-e,i=(e-n)/(t.dt||1/60),s=(i+(t.opts.stiffness*o-t.opts.damping*i)*t.inv_mass)*t.dt;return Math.abs(s)<t.opts.precision&&Math.abs(o)<t.opts.precision?r:(t.settled=!1,et(e)?new Date(e.getTime()+s):e+s)}if(Array.isArray(e))return e.map((o,i)=>rt(t,n[i],e[i],r[i]));if("object"==typeof e){const o={};for(const i in e)o[i]=rt(t,n[i],e[i],r[i]);return o}throw new Error(`Cannot spring ${typeof e} values`)}function ot(t,n={}){const e=Z(t),{stiffness:r=.15,damping:o=.8,precision:i=.01}=n;let s,a,u,f=t,p=t,l=1,h=0,y=!1;function g(n,r={}){p=n;const o=u={};if(null==t||r.hard||m.stiffness>=1&&m.damping>=1)return y=!0,s=c(),f=n,e.set(t=p),Promise.resolve();if(r.soft){const t=!0===r.soft?.5:+r.soft;h=1/(60*t),l=0}return a||(s=c(),y=!1,a=d(n=>{if(y)return y=!1,a=null,!1;l=Math.min(l+h,1);const r={inv_mass:l,opts:m,settled:!0,dt:60*(n-s)/1e3},o=rt(r,f,t,p);return s=n,f=t,e.set(t=o),r.settled&&(a=null),!r.settled})),new Promise(t=>{a.promise.then(()=>{o===u&&t()})})}const m={set:g,update:(n,e)=>g(n(p,t),e),subscribe:e.subscribe,stiffness:r,damping:o,precision:i};return m}function it(t,n){if(t===n||t!=t)return()=>t;const e=typeof t;if(e!==typeof n||Array.isArray(t)!==Array.isArray(n))throw new Error("Cannot interpolate values of different type");if(Array.isArray(t)){const e=n.map((n,e)=>it(t[e],n));return t=>e.map(n=>n(t))}if("object"===e){if(!t||!n)throw new Error("Object cannot be null");if(et(t)&&et(n)){t=t.getTime();const e=(n=n.getTime())-t;return n=>new Date(t+n*e)}const e=Object.keys(n),r={};return e.forEach(e=>{r[e]=it(t[e],n[e])}),t=>{const n={};return e.forEach(e=>{n[e]=r[e](t)}),n}}if("number"===e){const e=n-t;return n=>t+n*e}throw new Error(`Cannot interpolate ${e} values`)}function st(t,r={}){const o=Z(t);let i,s=t;function a(a,u){if(null==t)return o.set(t=a),Promise.resolve();s=a;let f=i,p=!1,{delay:l=0,duration:h=400,easing:y=n,interpolate:g=it}=e(e({},r),u);if(0===h)return f&&(f.abort(),f=null),o.set(t=s),Promise.resolve();const m=c()+l;let $;return i=d(n=>{if(n<m)return!0;p||($=g(t,a),"function"==typeof h&&(h=h(t,a)),p=!0),f&&(f.abort(),f=null);const e=n-m;return e>h?(o.set(t=a),!1):(o.set(t=$(y(e/h))),!0)}),i.promise}return{set:a,update:(n,e)=>a(n(s,t),e),subscribe:o.subscribe}}function at(t,{from:n,to:e},r={}){const i=getComputedStyle(t),s="none"===i.transform?"":i.transform,[a,u]=i.transformOrigin.split(" ").map(parseFloat),c=n.left+n.width*a/e.width-(e.left+a),f=n.top+n.height*u/e.height-(e.top+u),{delay:p=0,duration:l=t=>120*Math.sqrt(t),easing:d=P}=r;return{delay:p,duration:o(l)?l(Math.sqrt(c*c+f*f)):l,easing:d,css:(t,r)=>{const o=r*c,i=r*f,a=t+r*n.width/e.width,u=t+r*n.height/e.height;return`transform: ${s} translate(${o}px, ${i}px) scale(${a}, ${u});`}}}export{y as backIn,h as backInOut,g as backOut,G as blur,b as bounceIn,$ as bounceInOut,m as bounceOut,M as circIn,w as circInOut,x as circOut,V as crossfade,C as cubicIn,v as cubicInOut,P as cubicOut,tt as derived,Q as draw,A as elasticIn,k as elasticInOut,S as elasticOut,q as expoIn,F as expoInOut,E as expoOut,H as fade,at as flip,J as fly,s as get,n as linear,j as quadIn,I as quadInOut,z as quadOut,O as quartIn,D as quartInOut,T as quartOut,W as quintIn,B as quintInOut,L as quintOut,Y as readable,nt as readonly,N as scale,_ as sineIn,R as sineInOut,U as sineOut,K as slide,ot as spring,st as tweened,Z as writable};
