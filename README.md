# Google Chat Login Automation

This Python script automates the login process for Google Chat (chat.google.com) using Selenium WebDriver with Chrome browser.

## Features

- **Multi-step Authentication**: Handles Google's two-step login process (email first, then password)
- **Explicit Waits**: Uses WebDriverWait for reliable element detection instead of time.sleep()
- **Error Handling**: Comprehensive error handling for common issues
- **2FA Support**: Detects and handles 2FA prompts gracefully
- **Modular Design**: Clean, object-oriented code structure
- **Logging**: Detailed logging for debugging and monitoring
- **Headless Mode**: Optional headless browser operation

## Prerequisites

1. **Python 3.7+** installed on your system
2. **Google Chrome browser** installed
3. **ChromeDriver** (automatically managed by Selenium 4.x)

## Installation

1. Clone or download this repository
2. Install the required dependencies:

```bash
pip install -r requirements.txt
```

## Usage

### Basic Usage

Run the script directly:

```bash
python google_chat_login.py
```

### Headless Mode

To run in headless mode (no browser window), modify the script:

```python
login_automation = GoogleChatLogin(headless=True)
```

### Custom Timeout

To set a custom timeout (default is 30 seconds):

```python
login_automation = GoogleChatLogin(timeout=60)
```

## Configuration

The script is pre-configured with the following credentials:
- **Username**: <EMAIL>
- **Password**: Uni@2025!

To change credentials, modify the `__init__` method in the `GoogleChatLogin` class:

```python
self.username = "<EMAIL>"
self.password = "your-password"
```

## Script Flow

1. **Setup WebDriver**: Initializes Chrome WebDriver with optimized options
2. **Navigate to Google Chat**: Goes to chat.google.com
3. **Enter Email**: Fills in the email address and clicks Next
4. **Enter Password**: Fills in the password and clicks Next
5. **Handle 2FA**: Detects and handles two-factor authentication if required
6. **Verify Login**: Confirms successful login by checking for Google Chat interface elements
7. **Cleanup**: Properly closes the WebDriver

## Error Handling

The script handles various scenarios:
- Element not found errors
- Timeout exceptions
- WebDriver initialization failures
- Network connectivity issues
- 2FA prompts requiring manual intervention

## Security Considerations

- **Credentials**: Consider using environment variables for sensitive credentials
- **2FA**: The script will pause for manual 2FA completion when detected
- **Rate Limiting**: Google may implement rate limiting for automated logins

## Troubleshooting

### Common Issues

1. **ChromeDriver not found**: Selenium 4.x automatically manages ChromeDriver, but ensure Chrome browser is installed
2. **Element not found**: Google may update their UI; check element selectors
3. **Timeout errors**: Increase the timeout value for slower connections
4. **2FA required**: Complete 2FA manually when prompted

### Debug Mode

Enable debug logging by modifying the logging level:

```python
logging.basicConfig(level=logging.DEBUG)
```

## Legal and Ethical Considerations

- Use this script responsibly and in compliance with Google's Terms of Service
- Ensure you have proper authorization to automate login for the specified account
- Be mindful of rate limiting and avoid excessive automated requests

## License

This script is provided as-is for educational and automation purposes. Use at your own risk.
