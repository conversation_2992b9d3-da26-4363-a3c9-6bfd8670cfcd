#!/usr/bin/env python3
"""
Google Chat Messaging Automation Script

This script extends the Google Chat login automation to:
1. Read CSV data with contact information
2. <PERSON>gin to Google Chat
3. Send automated messages to each contact

Requirements:
- selenium package: pip install selenium
- pandas package: pip install pandas
- Chrome browser installed
- ChromeDriver (automatically managed by selenium 4.x)

Author: Auto-generated script
Date: 2025-07-29
"""

import time
import logging
import pandas as pd
import os
from collections import defaultdict
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import (
    TimeoutException,
    NoSuchElementException,
    WebDriverException,
    ElementClickInterceptedException
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class GoogleChatMessaging:
    """Class to handle Google Chat messaging automation"""
    
    def __init__(self, csv_file_path, headless=False, incognito=True, timeout=30):
        """
        Initialize the GoogleChatMessaging class

        Args:
            csv_file_path (str): Path to the CSV file with contact data
            headless (bool): Run browser in headless mode
            incognito (bool): Run browser in incognito mode (default: True)
            timeout (int): Default timeout for WebDriverWait
        """
        self.driver = None
        self.wait = None
        self.timeout = timeout
        self.headless = headless
        self.incognito = incognito
        self.csv_file_path = csv_file_path
        
        # Login credentials
        self.username = "<EMAIL>"
        self.password = "Uni@2025!"
        
        # URLs
        self.login_url = "https://accounts.google.com/v3/signin/identifier?continue=https%3A%2F%2Fchat.google.com%2F%3Freferrer%3D2&ec=wgc-chat-globalnav-signin&ifkv=AdBytiPQNtzlBi5QNev3dkFJbveZXstcPU8KpAg5otCcPHt5lRpV6oJhOp8b4NemjeStJS4lwCIWww&flowName=GlifWebSignIn&flowEntry=ServiceLogin&dsh=S303255065%3A1753782885323592"
        
        # Contact data
        self.contacts_data = {}
    
    def setup_driver(self):
        """Set up Chrome WebDriver with appropriate options"""
        try:
            if self.incognito:
                logger.info("Setting up Chrome WebDriver in Incognito mode...")
            else:
                logger.info("Setting up Chrome WebDriver in normal mode...")

            # Configure Chrome options
            chrome_options = Options()

            # Enable Incognito mode for privacy if requested
            if self.incognito:
                chrome_options.add_argument("--incognito")
                logger.info("Incognito mode enabled")

            if self.headless:
                chrome_options.add_argument("--headless")

            # Additional Chrome options for better stability
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--window-size=1920,1080")
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)

            # Additional privacy-focused options for Incognito mode
            chrome_options.add_argument("--disable-extensions")
            chrome_options.add_argument("--disable-plugins")
            chrome_options.add_argument("--no-first-run")
            chrome_options.add_argument("--no-default-browser-check")
            chrome_options.add_argument("--disable-default-apps")
            
            # Initialize WebDriver
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            # Set up WebDriverWait
            self.wait = WebDriverWait(self.driver, self.timeout)
            
            logger.info("Chrome WebDriver setup completed successfully")
            return True
            
        except WebDriverException as e:
            logger.error(f"Failed to setup WebDriver: {str(e)}")
            return False
    
    def read_csv_data(self):
        """Read and process CSV data"""
        try:
            logger.info(f"Reading CSV file: {self.csv_file_path}")
            
            # Check if file exists
            if not os.path.exists(self.csv_file_path):
                logger.error(f"CSV file not found: {self.csv_file_path}")
                return False
            
            # Read CSV file
            df = pd.read_csv(self.csv_file_path)
            logger.info(f"CSV file loaded with {len(df)} rows")
            
            # Print column names for debugging
            logger.info(f"CSV columns: {list(df.columns)}")
            
            # Process data - group by email
            contacts = defaultdict(lambda: {'nickname': '', 'phone_list': []})
            
            for _, row in df.iterrows():
                try:
                    email = str(row['Email']).strip()
                    user_ctv = str(row['2. User CTV Book']).strip()
                    phone = str(row['3. SĐT Phụ huynh']).strip()
                    
                    # Skip empty rows
                    if pd.isna(email) or email == '' or email == 'nan':
                        continue
                    
                    # Extract nickname
                    nickname = user_ctv
                    
                    # Add to contacts
                    if contacts[email]['nickname'] == '':
                        contacts[email]['nickname'] = nickname
                    
                    # Add phone number if not empty and not already in list
                    if phone != 'nan' and phone != '' and phone not in contacts[email]['phone_list']:
                        contacts[email]['phone_list'].append(phone)
                        
                except Exception as e:
                    logger.warning(f"Error processing row: {e}")
                    continue
            
            # Convert to regular dict
            self.contacts_data = dict(contacts)
            
            logger.info(f"Processed {len(self.contacts_data)} unique contacts")
            for email, data in self.contacts_data.items():
                logger.info(f"Contact: {email} -> Nickname: {data['nickname']}, Phones: {data['phone_list']}")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to read CSV data: {str(e)}")
            return False
    
    def login_to_google_chat(self):
        """Login to Google Chat using the existing login logic"""
        try:
            logger.info("Starting Google Chat login process...")
            
            # Navigate directly to Google login page
            logger.info("Navigating directly to Google login page...")
            self.driver.get(self.login_url)
            time.sleep(3)
            
            current_url = self.driver.current_url
            logger.info(f"Current URL after navigation: {current_url}")
            
            # Check if already logged in
            if (self.driver.current_url.startswith("https://chat.google.com") or 
                "mail.google.com/chat" in self.driver.current_url):
                logger.info("Already logged in, verifying access...")
                return self.verify_chat_interface()
            
            # Enter email
            if not self.enter_email():
                return False
            
            time.sleep(2)
            
            # Enter password
            if not self.enter_password():
                return False
            
            # Handle 2FA if needed
            if not self.handle_2fa_prompt():
                return False
            
            # Verify login success
            return self.verify_chat_interface()
            
        except Exception as e:
            logger.error(f"Login process failed: {str(e)}")
            return False
    
    def enter_email(self):
        """Enter email address in the first step of Google v3 signin"""
        try:
            logger.info("Entering email address...")
            
            # Wait for page to load
            time.sleep(2)
            
            # Try specific selectors for v3 signin email input field
            email_selectors = [
                (By.XPATH, "//input[@type='email' and @class='whsOnd zHQkBf' and @name='identifier' and @id='identifierId']"),
                (By.XPATH, "//input[@type='email' and @name='identifier' and @id='identifierId']"),
                (By.ID, "identifierId"),
                (By.NAME, "identifier"),
                (By.XPATH, "//input[@type='email']"),
            ]
            
            email_input = None
            for selector_type, selector_value in email_selectors:
                try:
                    email_input = WebDriverWait(self.driver, 10).until(
                        EC.element_to_be_clickable((selector_type, selector_value))
                    )
                    logger.info(f"Found email input with selector: {selector_value}")
                    break
                except TimeoutException:
                    continue
            
            if not email_input:
                logger.error("Could not find email input field")
                return False
            
            # Enter email
            email_input.clear()
            time.sleep(0.5)
            email_input.send_keys(self.username)
            logger.info(f"Email entered: {self.username}")
            
            # Submit email
            time.sleep(1)
            email_input.send_keys(Keys.RETURN)
            logger.info("Pressed Enter to submit email")
            time.sleep(3)
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to enter email: {str(e)}")
            return False
    
    def enter_password(self):
        """Enter password in the second step of Google v3 signin"""
        try:
            logger.info("Entering password...")
            
            # Wait for password page to load
            time.sleep(3)
            
            # Try specific selectors for v3 signin password input field
            password_selectors = [
                (By.XPATH, "//input[@type='password' and @class='whsOnd zHQkBf' and @name='Passwd']"),
                (By.NAME, "Passwd"),
                (By.NAME, "password"),
                (By.XPATH, "//input[@type='password']"),
            ]
            
            password_input = None
            for selector_type, selector_value in password_selectors:
                try:
                    password_input = WebDriverWait(self.driver, 10).until(
                        EC.element_to_be_clickable((selector_type, selector_value))
                    )
                    logger.info(f"Found password input with selector: {selector_value}")
                    break
                except TimeoutException:
                    continue
            
            if not password_input:
                logger.error("Could not find password input field")
                return False
            
            # Enter password
            password_input.clear()
            time.sleep(0.5)
            password_input.send_keys(self.password)
            logger.info("Password entered")
            
            # Submit password
            time.sleep(1)
            password_input.send_keys(Keys.RETURN)
            logger.info("Pressed Enter to submit password")
            time.sleep(5)
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to enter password: {str(e)}")
            return False

    def handle_2fa_prompt(self):
        """Handle potential 2FA or security challenges"""
        try:
            logger.info("Checking for 2FA or security challenges...")

            # Wait a bit to see if 2FA prompt appears
            time.sleep(3)

            # Check for various 2FA elements
            possible_2fa_selectors = [
                (By.ID, "totpPin"),
                (By.NAME, "totpPin"),
                (By.XPATH, "//input[@type='tel']"),
                (By.XPATH, "//input[@placeholder='Enter code']"),
            ]

            for selector_type, selector_value in possible_2fa_selectors:
                try:
                    element = self.driver.find_element(selector_type, selector_value)
                    if element.is_displayed():
                        logger.warning("2FA prompt detected. Manual intervention required.")
                        input("Please complete 2FA manually and press Enter to continue...")
                        return True
                except NoSuchElementException:
                    continue

            logger.info("No 2FA prompt detected")
            return True

        except Exception as e:
            logger.error(f"Error handling 2FA prompt: {str(e)}")
            return False

    def verify_chat_interface(self):
        """Verify successful login by checking for Google Chat interface"""
        try:
            logger.info("Verifying Google Chat interface...")

            # Wait for redirect to Google Chat
            logger.info("Waiting for redirect to Google Chat...")
            try:
                WebDriverWait(self.driver, 60).until(
                    lambda driver: (driver.current_url.startswith("https://chat.google.com") or
                                  "mail.google.com/chat" in driver.current_url)
                )
                logger.info("Successfully redirected to Google Chat!")
            except TimeoutException:
                logger.error("Timeout waiting for redirect to Google Chat")
                return False

            # Verify we're on Google Chat
            if not (self.driver.current_url.startswith("https://chat.google.com") or
                    "mail.google.com/chat" in self.driver.current_url):
                logger.error(f"Not on Google Chat. Current URL: {self.driver.current_url}")
                return False

            # Wait for interface to load
            logger.info("Waiting for Google Chat interface to load...")
            time.sleep(5)

            logger.info("Google Chat interface verified successfully!")
            return True

        except Exception as e:
            logger.error(f"Error verifying chat interface: {str(e)}")
            return False

    def activate_search_mode(self):
        """Activate search mode in Google Chat"""
        try:
            logger.info("Attempting to activate search mode...")

            # Debug: Look for all clickable elements that might be search-related
            try:
                all_buttons = self.driver.find_elements(By.XPATH, "//button | //div[@role='button'] | //*[@role='button']")
                logger.info(f"Found {len(all_buttons)} clickable elements on the page")

                search_candidates = []
                for i, button in enumerate(all_buttons):
                    try:
                        aria_label = button.get_attribute("aria-label") or ""
                        title = button.get_attribute("title") or ""
                        text_content = button.text or ""
                        class_name = button.get_attribute("class") or ""

                        # Check if element might be search-related
                        search_keywords = ["search", "tìm", "Search", "Tìm", "kiếm"]
                        if any(keyword in attr for attr in [aria_label, title, text_content, class_name] for keyword in search_keywords):
                            search_candidates.append((i, button, aria_label, title, text_content))
                            logger.info(f"Search candidate {i}: aria-label='{aria_label}', title='{title}', text='{text_content}'")
                    except Exception as e:
                        logger.debug(f"Error processing button {i}: {e}")
                        continue

                # Try clicking on search candidates
                for i, button, aria_label, title, text_content in search_candidates:
                    try:
                        if button.is_displayed():
                            logger.info(f"Trying to click search candidate {i}")
                            button.click()
                            logger.info(f"Clicked search candidate {i}")
                            time.sleep(2)
                            return True
                    except (ElementClickInterceptedException, Exception) as e:
                        logger.debug(f"Failed to click search candidate {i}: {e}")
                        continue

            except Exception as e:
                logger.debug(f"Error in search candidate detection: {e}")

            # Try to find and click search activation buttons/icons
            search_activation_selectors = [
                # Specific Vietnamese search selectors
                (By.XPATH, "//button[@aria-label='Tìm kiếm cuộc trò chuyện']"),
                (By.XPATH, "//*[@aria-label='Tìm kiếm cuộc trò chuyện']"),
                (By.XPATH, "//div[@role='button'][@aria-label='Tìm kiếm cuộc trò chuyện']"),
                (By.XPATH, "//*[contains(@aria-label, 'Tìm kiếm cuộc trò chuyện')]"),
                (By.XPATH, "//*[text()='Tìm kiếm cuộc trò chuyện']"),
                (By.XPATH, "//*[contains(text(), 'Tìm kiếm cuộc trò chuyện')]"),
                # General Vietnamese search selectors
                (By.XPATH, "//button[contains(@aria-label, 'Tìm kiếm') or contains(@aria-label, 'tìm kiếm')]"),
                (By.XPATH, "//div[@role='button'][contains(@aria-label, 'Tìm kiếm')]"),
                (By.CSS_SELECTOR, "button[aria-label*='Tìm kiếm']"),
                (By.CSS_SELECTOR, "*[aria-label*='Tìm kiếm']"),
                # English search selectors
                (By.XPATH, "//button[contains(@aria-label, 'Search') or contains(@aria-label, 'search')]"),
                (By.XPATH, "//div[@role='button'][contains(@aria-label, 'Search')]"),
                (By.CSS_SELECTOR, "button[aria-label*='search']"),
                (By.CSS_SELECTOR, "button[aria-label*='Search']"),
                # Search icon (magnifying glass)
                (By.XPATH, "//svg[contains(@viewBox, '24')]//path[contains(@d, 'search') or contains(@d, 'M20.49,19')]"),
                (By.XPATH, "//button[.//svg[contains(@viewBox, '24')]]"),
            ]

            for selector_type, selector_value in search_activation_selectors:
                try:
                    search_button = self.driver.find_element(selector_type, selector_value)
                    if search_button.is_displayed():
                        logger.info(f"Found search activation button: {selector_value}")
                        search_button.click()
                        logger.info("Clicked search activation button")
                        time.sleep(2)
                        return True
                except (NoSuchElementException, ElementClickInterceptedException):
                    continue

            # Try keyboard shortcut as fallback
            try:
                logger.info("Trying keyboard shortcut Ctrl+K to activate search...")
                actions = ActionChains(self.driver)
                actions.key_down(Keys.CONTROL).send_keys('k').key_up(Keys.CONTROL).perform()
                time.sleep(2)
                return True
            except Exception as e:
                logger.debug(f"Keyboard shortcut failed: {e}")

            logger.warning("Could not activate search mode")
            return False

        except Exception as e:
            logger.error(f"Error activating search mode: {str(e)}")
            return False

    def validate_search_form(self, form_element):
        """Validate if a form element is the search form"""
        try:
            # Check if form contains search-related attributes
            form_aria_label = form_element.get_attribute("aria-label") or ""
            form_role = form_element.get_attribute("role") or ""
            form_id = form_element.get_attribute("id") or ""
            form_class = form_element.get_attribute("class") or ""

            # Check for search indicators in form attributes
            search_indicators = ["search", "tìm kiếm", "tìm", "Search", "Tìm kiếm", "Tìm"]
            has_search_attribute = any(indicator in attr for attr in [form_aria_label, form_role, form_id, form_class] for indicator in search_indicators)

            if has_search_attribute:
                logger.info(f"Form has search-related attributes: aria-label='{form_aria_label}', role='{form_role}', id='{form_id}'")
                return True

            # Check if form contains text input elements
            text_inputs = form_element.find_elements(By.XPATH, ".//input[@type='text']")
            if text_inputs:
                logger.info(f"Form contains {len(text_inputs)} text input elements")

                # Check if any input has search-related attributes
                for input_elem in text_inputs:
                    try:
                        input_aria_label = input_elem.get_attribute("aria-label") or ""
                        input_placeholder = input_elem.get_attribute("placeholder") or ""
                        input_name = input_elem.get_attribute("name") or ""

                        has_search_input = any(indicator in attr for attr in [input_aria_label, input_placeholder, input_name] for indicator in search_indicators)
                        if has_search_input:
                            logger.info(f"Found search input: aria-label='{input_aria_label}', placeholder='{input_placeholder}', name='{input_name}'")
                            return True
                    except:
                        continue

            # Check for characteristic nested table structure
            tables = form_element.find_elements(By.XPATH, ".//table")
            if tables:
                logger.info(f"Form contains {len(tables)} table elements (potential search structure)")
                # Look for specific classes mentioned in HTML structure
                for table in tables:
                    table_class = table.get_attribute("class") or ""
                    if "gssb_m" in table_class or "gstl_" in table_class:
                        logger.info(f"Found table with search-related class: {table_class}")
                        return True

            return False

        except Exception as e:
            logger.debug(f"Error validating search form: {e}")
            return False

    def search_and_select_contact(self, email):
        """Search for a contact and select them"""
        iframe_switched = False
        try:
            logger.info(f"Searching for contact: {email}")

            # Debug: Print current URL
            logger.info(f"Current URL: {self.driver.current_url}")

            # Wait a moment for interface to stabilize after modal dismissal
            time.sleep(2)

            # Step 1: Check for iframe and switch context if needed
            try:
                logger.info("Checking for iframe with ID 'gtn-brain-iframe-id'...")
                iframe = self.driver.find_element(By.XPATH, "//*[@id='gtn-brain-iframe-id']")
                if iframe:
                    logger.info("Found iframe 'gtn-brain-iframe-id', switching context...")
                    self.driver.switch_to.frame(iframe)
                    iframe_switched = True
                    logger.info("Successfully switched to iframe context")
                    time.sleep(1)
            except NoSuchElementException:
                logger.info("Iframe 'gtn-brain-iframe-id' not found, continuing in main context")
            except Exception as e:
                logger.warning(f"Error switching to iframe: {e}")

            # Step 2: Check for search form by ID (now in correct context)
            try:
                search_form = self.driver.find_element(By.ID, "aso_search_form_anchor")
                if search_form:
                    logger.info(f"Search form found by ID: visible={search_form.is_displayed()}")
                    if search_form.is_displayed():
                        search_form.click()
                        logger.info("Clicked on search form to activate it")
                        time.sleep(1)

                        # Step 3: Look for search input field
                        search_input = None
                        search_selectors = [
                            (By.CSS_SELECTOR, "form#aso_search_form_anchor input.gb_se[name='q'][role='combobox']"),
                            (By.CSS_SELECTOR, "input.gb_se[aria-label='Tìm kiếm cuộc trò chuyện'][name='q']"),
                            (By.CSS_SELECTOR, "input.gb_se[placeholder='Tìm kiếm cuộc trò chuyện'][type='text']"),
                            (By.CSS_SELECTOR, "form#aso_search_form_anchor input[type='text']"),
                            (By.XPATH, "//form[@id='aso_search_form_anchor']//input[@class='gb_se' and @name='q']"),
                            (By.CSS_SELECTOR, "input[name='q'][role='combobox']"),
                            (By.CSS_SELECTOR, "input.gb_se"),
                        ]

                        for selector_type, selector_value in search_selectors:
                            try:
                                search_input = WebDriverWait(self.driver, 5).until(
                                    EC.element_to_be_clickable((selector_type, selector_value))
                                )
                                logger.info(f"Found search input with selector: {selector_value}")
                                break
                            except TimeoutException:
                                continue

                        if search_input:
                            # Step 4: Enter search term
                            search_input.clear()
                            time.sleep(0.5)
                            search_input.send_keys(email)
                            logger.info(f"Email entered: {email}")

                            # Wait for search results
                            time.sleep(3)

                            # Step 5: Look for and click search results
                            result_selectors = [
                                (By.CSS_SELECTOR, "table#gs_sbt50 tr[role='option'] div[role='button']"),
                                (By.CSS_SELECTOR, "tbody.GXilEb tr[role='option'] div[role='button']"),
                                (By.CSS_SELECTOR, "div.IyhRae.YDAV2"),
                                (By.XPATH, f"//div[@role='button'][contains(., '{email}')]"),
                                (By.XPATH, f"//tr[@role='option'][contains(., '{email}')]//div[@role='button']"),
                                (By.CSS_SELECTOR, "tr[role='option']"),
                                (By.XPATH, "//div[@role='option']"),
                            ]

                            for selector_type, selector_value in result_selectors:
                                try:
                                    result = WebDriverWait(self.driver, 5).until(
                                        EC.element_to_be_clickable((selector_type, selector_value))
                                    )
                                    logger.info(f"Found search result with selector: {selector_value}")
                                    result.click()
                                    logger.info(f"Clicked on search result for: {email}")
                                    time.sleep(2)
                                    return True
                                except TimeoutException:
                                    continue

                            logger.warning(f"No search results found for: {email}")
                        else:
                            logger.error("No search input found")
                else:
                    logger.warning("Search form found but not visible")
            except NoSuchElementException:
                logger.info("Search form not found by ID 'aso_search_form_anchor'")

            return False

        except Exception as e:
            logger.error(f"Failed to search for contact {email}: {str(e)}")
            return False
        finally:
            # Step 6: Always switch back to default content if we switched to iframe
            if iframe_switched:
                try:
                    self.driver.switch_to.default_content()
                    logger.info("Switched back to default content")
                except Exception as e:
                    logger.warning(f"Error switching back to default content: {e}")

    def send_message(self, nickname, phone_list):
        """Send message to the selected contact"""
        iframe_switched = False
        try:
            logger.info(f"Sending message to {nickname}")

            # Create message content
            phone_numbers = '\n'.join(phone_list)
            message = f"""Chào {nickname},
Bạn có những số demo done sau chưa chuyển, vui lòng check CRM và chuyển số trước 14h, sau 14h L5 của bạn sẽ không được tính nữa:

📱 List số:
{phone_numbers}"""

            logger.info(f"Message content:\n{message}")

            # Step 1: Switch to iframe context for chat input field
            try:
                logger.info("Switching to iframe context for chat input...")
                iframe = self.driver.find_element(By.XPATH, "//*[@id='gtn-brain-iframe-id']")
                if iframe:
                    self.driver.switch_to.frame(iframe)
                    iframe_switched = True
                    logger.info("Successfully switched to iframe context for messaging")
                    time.sleep(1)
            except NoSuchElementException:
                logger.warning("Iframe 'gtn-brain-iframe-id' not found for messaging, continuing in main context")
            except Exception as e:
                logger.warning(f"Error switching to iframe for messaging: {e}")

            # Step 2: Find chat input field with updated selectors
            input_selectors = [
                # Primary selector for the specific chat input field
                (By.XPATH, "//*[@id='ow19']"),
                # Fallback selectors for different interface versions
                (By.CSS_SELECTOR, "div[role='textbox'][aria-label='Đã bật lịch sử']"),
                (By.CSS_SELECTOR, "div.hj99tb.KRoqRc.editable"),
                (By.CSS_SELECTOR, "div[role='textbox']"),
                (By.CSS_SELECTOR, "div[contenteditable='true']"),
                (By.XPATH, "//div[@role='textbox']"),
                (By.XPATH, "//div[@contenteditable='true']"),
                # Additional selectors for iframe context
                (By.XPATH, "//*[@contenteditable='true']"),
                (By.XPATH, "//*[@role='textbox']"),
                (By.CSS_SELECTOR, "*[contenteditable='true']"),
            ]

            chat_input = None
            for selector_type, selector_value in input_selectors:
                try:
                    chat_input = WebDriverWait(self.driver, 10).until(
                        EC.element_to_be_clickable((selector_type, selector_value))
                    )
                    logger.info(f"Found chat input with selector: {selector_value}")
                    break
                except TimeoutException:
                    logger.debug(f"Chat input selector failed: {selector_value}")
                    continue

            if not chat_input:
                logger.error("Could not find chat input field")
                return False

            # Step 3: Click on input field and enter message
            chat_input.click()
            time.sleep(0.5)

            # Clear any existing content
            try:
                chat_input.clear()
            except Exception as e:
                logger.debug(f"Could not clear chat input: {e}")

            # Type the message
            chat_input.send_keys(message)
            logger.info("Message typed into chat input")

            # Step 4: Send the message
            time.sleep(1)
            chat_input.send_keys(Keys.RETURN)
            logger.info("Message sent successfully")

            # Wait before next message
            time.sleep(3)
            return True

        except Exception as e:
            logger.error(f"Failed to send message: {str(e)}")
            return False
        finally:
            # Step 5: Always switch back to default content if we switched to iframe
            if iframe_switched:
                try:
                    self.driver.switch_to.default_content()
                    logger.info("Switched back to default content after messaging")
                except Exception as e:
                    logger.warning(f"Error switching back to default content after messaging: {e}")

    def send_messages_to_all_contacts(self):
        """Send messages to all contacts in the CSV data"""
        try:
            logger.info("Starting to send messages to all contacts...")

            if not self.contacts_data:
                logger.error("No contact data available")
                return False

            success_count = 0
            failed_count = 0

            for email, contact_info in self.contacts_data.items():
                try:
                    logger.info(f"Processing contact {success_count + failed_count + 1}/{len(self.contacts_data)}: {email}")

                    nickname = contact_info['nickname']
                    phone_list = contact_info['phone_list']

                    # Skip if no phone numbers
                    if not phone_list:
                        logger.warning(f"No phone numbers for {email}, skipping...")
                        failed_count += 1
                        continue

                    # Search for contact
                    if not self.search_and_select_contact(email):
                        logger.error(f"Failed to find contact: {email}")
                        failed_count += 1
                        continue

                    # Send message
                    if self.send_message(nickname, phone_list):
                        logger.info(f"Successfully sent message to {email}")
                        success_count += 1
                    else:
                        logger.error(f"Failed to send message to {email}")
                        failed_count += 1

                    # Delay between contacts to avoid rate limiting
                    logger.info("Waiting before next contact...")
                    time.sleep(5)

                except Exception as e:
                    logger.error(f"Error processing contact {email}: {str(e)}")
                    failed_count += 1
                    continue

            logger.info(f"Messaging completed. Success: {success_count}, Failed: {failed_count}")
            return success_count > 0

        except Exception as e:
            logger.error(f"Failed to send messages to contacts: {str(e)}")
            return False

    def run_automation(self):
        """Main method to run the complete automation"""
        try:
            logger.info("Starting Google Chat messaging automation...")

            # Step 1: Read CSV data
            if not self.read_csv_data():
                logger.error("Failed to read CSV data")
                return False

            # Step 2: Setup WebDriver
            if not self.setup_driver():
                logger.error("Failed to setup WebDriver")
                return False

            # Step 3: Login to Google Chat
            if not self.login_to_google_chat():
                logger.error("Failed to login to Google Chat")
                return False

            # Step 5: Wait for interface to fully load after modal dismissal
            logger.info("Waiting for Google Chat interface to fully load...")
            time.sleep(5)

            # Step 6: Send messages to all contacts
            if not self.send_messages_to_all_contacts():
                logger.error("Failed to send messages")
                return False

            logger.info("Google Chat messaging automation completed successfully!")
            return True

        except Exception as e:
            logger.error(f"Automation failed: {str(e)}")
            return False

    def cleanup(self):
        """Clean up WebDriver resources"""
        if self.driver:
            logger.info("Cleaning up WebDriver...")
            self.driver.quit()
            logger.info("WebDriver cleanup completed")

def main():
    """Main function to run the Google Chat messaging automation"""

    # Configuration
    csv_file_path = "/home/<USER>/Downloads/File gửi số cho sale book - Trang tính1.csv"

    # Check if CSV file exists, if not use sample data
    if not os.path.exists(csv_file_path):
        logger.warning(f"CSV file not found at {csv_file_path}")
        csv_file_path = "sample_data.csv"
        logger.info(f"Using sample CSV file: {csv_file_path}")

    # Initialize automation
    automation = GoogleChatMessaging(
        csv_file_path=csv_file_path,
        headless=False,  # Set to True for headless mode
        incognito=True,  # Set to False to disable incognito mode
        timeout=30
    )

    try:
        # Run automation
        success = automation.run_automation()

        if success:
            logger.info("All messaging completed successfully!")
            input("Press Enter to close the browser...")
        else:
            logger.error("Messaging automation failed!")

        return success

    except KeyboardInterrupt:
        logger.info("Script interrupted by user")
        return False
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        return False
    finally:
        # Ensure cleanup happens
        automation.cleanup()

if __name__ == "__main__":
    main()
