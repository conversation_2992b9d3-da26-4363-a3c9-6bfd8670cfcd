#!/usr/bin/env python3
"""
Google Chat Messaging Automation Script

This script extends the Google Chat login automation to:
1. Read CSV data with contact information
2. Login to Google Chat
3. Send automated messages to each contact

Requirements:
- selenium package: pip install selenium
- pandas package: pip install pandas
- Chrome browser installed
- ChromeDriver (automatically managed by selenium 4.x)

Author: Auto-generated script
Date: 2025-07-29
"""

import time
import logging
import pandas as pd
import os
from collections import defaultdict
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import (
    TimeoutException, 
    NoSuchElementException, 
    WebDriverException,
    ElementClickInterceptedException
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class GoogleChatMessaging:
    """Class to handle Google Chat messaging automation"""
    
    def __init__(self, csv_file_path, headless=False, incognito=True, timeout=30):
        """
        Initialize the GoogleChatMessaging class

        Args:
            csv_file_path (str): Path to the CSV file with contact data
            headless (bool): Run browser in headless mode
            incognito (bool): Run browser in incognito mode (default: True)
            timeout (int): Default timeout for WebDriverWait
        """
        self.driver = None
        self.wait = None
        self.timeout = timeout
        self.headless = headless
        self.incognito = incognito
        self.csv_file_path = csv_file_path
        
        # Login credentials
        self.username = "<EMAIL>"
        self.password = "Uni@2025!"
        
        # URLs
        self.login_url = "https://accounts.google.com/v3/signin/identifier?continue=https%3A%2F%2Fchat.google.com%2F%3Freferrer%3D2&ec=wgc-chat-globalnav-signin&ifkv=AdBytiPQNtzlBi5QNev3dkFJbveZXstcPU8KpAg5otCcPHt5lRpV6oJhOp8b4NemjeStJS4lwCIWww&flowName=GlifWebSignIn&flowEntry=ServiceLogin&dsh=S303255065%3A1753782885323592"
        
        # Contact data
        self.contacts_data = {}
    
    def setup_driver(self):
        """Set up Chrome WebDriver with appropriate options"""
        try:
            if self.incognito:
                logger.info("Setting up Chrome WebDriver in Incognito mode...")
            else:
                logger.info("Setting up Chrome WebDriver in normal mode...")

            # Configure Chrome options
            chrome_options = Options()

            # Enable Incognito mode for privacy if requested
            if self.incognito:
                chrome_options.add_argument("--incognito")
                logger.info("Incognito mode enabled")

            if self.headless:
                chrome_options.add_argument("--headless")

            # Additional Chrome options for better stability
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--window-size=1920,1080")
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)

            # Additional privacy-focused options for Incognito mode
            chrome_options.add_argument("--disable-extensions")
            chrome_options.add_argument("--disable-plugins")
            chrome_options.add_argument("--no-first-run")
            chrome_options.add_argument("--no-default-browser-check")
            chrome_options.add_argument("--disable-default-apps")
            
            # Initialize WebDriver
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            # Set up WebDriverWait
            self.wait = WebDriverWait(self.driver, self.timeout)
            
            logger.info("Chrome WebDriver setup completed successfully")
            return True
            
        except WebDriverException as e:
            logger.error(f"Failed to setup WebDriver: {str(e)}")
            return False
    
    def read_csv_data(self):
        """Read and process CSV data"""
        try:
            logger.info(f"Reading CSV file: {self.csv_file_path}")
            
            # Check if file exists
            if not os.path.exists(self.csv_file_path):
                logger.error(f"CSV file not found: {self.csv_file_path}")
                return False
            
            # Read CSV file
            df = pd.read_csv(self.csv_file_path)
            logger.info(f"CSV file loaded with {len(df)} rows")
            
            # Print column names for debugging
            logger.info(f"CSV columns: {list(df.columns)}")
            
            # Process data - group by email
            contacts = defaultdict(lambda: {'nickname': '', 'phone_list': []})
            
            for _, row in df.iterrows():
                try:
                    email = str(row['Email']).strip()
                    user_ctv = str(row['2. User CTV Book']).strip()
                    phone = str(row['3. SĐT Phụ huynh']).strip()
                    
                    # Skip empty rows
                    if pd.isna(email) or email == '' or email == 'nan':
                        continue
                    
                    # Extract nickname (remove .saleclass suffix)
                    nickname = user_ctv.replace('.saleclass', '') if user_ctv != 'nan' else email.split('@')[0]
                    
                    # Add to contacts
                    if contacts[email]['nickname'] == '':
                        contacts[email]['nickname'] = nickname
                    
                    # Add phone number if not empty and not already in list
                    if phone != 'nan' and phone != '' and phone not in contacts[email]['phone_list']:
                        contacts[email]['phone_list'].append(phone)
                        
                except Exception as e:
                    logger.warning(f"Error processing row: {e}")
                    continue
            
            # Convert to regular dict
            self.contacts_data = dict(contacts)
            
            logger.info(f"Processed {len(self.contacts_data)} unique contacts")
            for email, data in self.contacts_data.items():
                logger.info(f"Contact: {email} -> Nickname: {data['nickname']}, Phones: {data['phone_list']}")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to read CSV data: {str(e)}")
            return False
    
    def login_to_google_chat(self):
        """Login to Google Chat using the existing login logic"""
        try:
            logger.info("Starting Google Chat login process...")
            
            # Navigate directly to Google login page
            logger.info("Navigating directly to Google login page...")
            self.driver.get(self.login_url)
            time.sleep(3)
            
            current_url = self.driver.current_url
            logger.info(f"Current URL after navigation: {current_url}")
            
            # Check if already logged in
            if (self.driver.current_url.startswith("https://chat.google.com") or 
                "mail.google.com/chat" in self.driver.current_url):
                logger.info("Already logged in, verifying access...")
                return self.verify_chat_interface()
            
            # Enter email
            if not self.enter_email():
                return False
            
            time.sleep(2)
            
            # Enter password
            if not self.enter_password():
                return False
            
            # Handle 2FA if needed
            if not self.handle_2fa_prompt():
                return False
            
            # Verify login success
            return self.verify_chat_interface()
            
        except Exception as e:
            logger.error(f"Login process failed: {str(e)}")
            return False
    
    def enter_email(self):
        """Enter email address in the first step of Google v3 signin"""
        try:
            logger.info("Entering email address...")
            
            # Wait for page to load
            time.sleep(2)
            
            # Try specific selectors for v3 signin email input field
            email_selectors = [
                (By.XPATH, "//input[@type='email' and @class='whsOnd zHQkBf' and @name='identifier' and @id='identifierId']"),
                (By.XPATH, "//input[@type='email' and @name='identifier' and @id='identifierId']"),
                (By.ID, "identifierId"),
                (By.NAME, "identifier"),
                (By.XPATH, "//input[@type='email']"),
            ]
            
            email_input = None
            for selector_type, selector_value in email_selectors:
                try:
                    email_input = WebDriverWait(self.driver, 10).until(
                        EC.element_to_be_clickable((selector_type, selector_value))
                    )
                    logger.info(f"Found email input with selector: {selector_value}")
                    break
                except TimeoutException:
                    continue
            
            if not email_input:
                logger.error("Could not find email input field")
                return False
            
            # Enter email
            email_input.clear()
            time.sleep(0.5)
            email_input.send_keys(self.username)
            logger.info(f"Email entered: {self.username}")
            
            # Submit email
            time.sleep(1)
            email_input.send_keys(Keys.RETURN)
            logger.info("Pressed Enter to submit email")
            time.sleep(3)
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to enter email: {str(e)}")
            return False
    
    def enter_password(self):
        """Enter password in the second step of Google v3 signin"""
        try:
            logger.info("Entering password...")
            
            # Wait for password page to load
            time.sleep(3)
            
            # Try specific selectors for v3 signin password input field
            password_selectors = [
                (By.XPATH, "//input[@type='password' and @class='whsOnd zHQkBf' and @name='Passwd']"),
                (By.NAME, "Passwd"),
                (By.NAME, "password"),
                (By.XPATH, "//input[@type='password']"),
            ]
            
            password_input = None
            for selector_type, selector_value in password_selectors:
                try:
                    password_input = WebDriverWait(self.driver, 10).until(
                        EC.element_to_be_clickable((selector_type, selector_value))
                    )
                    logger.info(f"Found password input with selector: {selector_value}")
                    break
                except TimeoutException:
                    continue
            
            if not password_input:
                logger.error("Could not find password input field")
                return False
            
            # Enter password
            password_input.clear()
            time.sleep(0.5)
            password_input.send_keys(self.password)
            logger.info("Password entered")
            
            # Submit password
            time.sleep(1)
            password_input.send_keys(Keys.RETURN)
            logger.info("Pressed Enter to submit password")
            time.sleep(5)
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to enter password: {str(e)}")
            return False

    def handle_2fa_prompt(self):
        """Handle potential 2FA or security challenges"""
        try:
            logger.info("Checking for 2FA or security challenges...")

            # Wait a bit to see if 2FA prompt appears
            time.sleep(3)

            # Check for various 2FA elements
            possible_2fa_selectors = [
                (By.ID, "totpPin"),
                (By.NAME, "totpPin"),
                (By.XPATH, "//input[@type='tel']"),
                (By.XPATH, "//input[@placeholder='Enter code']"),
            ]

            for selector_type, selector_value in possible_2fa_selectors:
                try:
                    element = self.driver.find_element(selector_type, selector_value)
                    if element.is_displayed():
                        logger.warning("2FA prompt detected. Manual intervention required.")
                        input("Please complete 2FA manually and press Enter to continue...")
                        return True
                except NoSuchElementException:
                    continue

            logger.info("No 2FA prompt detected")
            return True

        except Exception as e:
            logger.error(f"Error handling 2FA prompt: {str(e)}")
            return False

    def verify_chat_interface(self):
        """Verify successful login by checking for Google Chat interface"""
        try:
            logger.info("Verifying Google Chat interface...")

            # Wait for redirect to Google Chat
            logger.info("Waiting for redirect to Google Chat...")
            try:
                WebDriverWait(self.driver, 60).until(
                    lambda driver: (driver.current_url.startswith("https://chat.google.com") or
                                  "mail.google.com/chat" in driver.current_url)
                )
                logger.info("Successfully redirected to Google Chat!")
            except TimeoutException:
                logger.error("Timeout waiting for redirect to Google Chat")
                return False

            # Verify we're on Google Chat
            if not (self.driver.current_url.startswith("https://chat.google.com") or
                    "mail.google.com/chat" in self.driver.current_url):
                logger.error(f"Not on Google Chat. Current URL: {self.driver.current_url}")
                return False

            # Wait for interface to load
            logger.info("Waiting for Google Chat interface to load...")
            time.sleep(5)

            logger.info("Google Chat interface verified successfully!")
            return True

        except Exception as e:
            logger.error(f"Error verifying chat interface: {str(e)}")
            return False

    def handle_modal_popup(self):
        """Handle modal popup that appears after login"""
        try:
            logger.info("Checking for modal popup after login...")

            # Wait a moment for any modal to appear
            time.sleep(3)

            # Look for modal containers first
            modal_selectors = [
                (By.XPATH, "//div[@role='dialog']"),
                (By.XPATH, "//div[@role='alertdialog']"),
                (By.XPATH, "//div[contains(@class, 'modal')]"),
                (By.XPATH, "//div[contains(@class, 'dialog')]"),
                (By.XPATH, "//div[contains(@class, 'popup')]"),
                (By.CSS_SELECTOR, "[role='dialog']"),
                (By.CSS_SELECTOR, "[role='alertdialog']"),
            ]

            modal_found = False
            for selector_type, selector_value in modal_selectors:
                try:
                    modal = self.driver.find_element(selector_type, selector_value)
                    if modal.is_displayed():
                        logger.info(f"Found modal popup with selector: {selector_value}")
                        modal_found = True
                        break
                except NoSuchElementException:
                    continue

            if not modal_found:
                logger.info("No modal popup detected, proceeding...")
                return True

            # Look for cancel/close buttons in the modal
            cancel_button_selectors = [
                # Text-based selectors (English)
                (By.XPATH, "//button[text()='Cancel']"),
                (By.XPATH, "//button[contains(text(), 'Cancel')]"),
                (By.XPATH, "//button[text()='Close']"),
                (By.XPATH, "//button[contains(text(), 'Close')]"),
                (By.XPATH, "//button[text()='Dismiss']"),
                (By.XPATH, "//button[contains(text(), 'Dismiss')]"),
                (By.XPATH, "//button[text()='Skip']"),
                (By.XPATH, "//button[contains(text(), 'Skip')]"),

                # Text-based selectors (Vietnamese)
                (By.XPATH, "//button[text()='Hủy']"),
                (By.XPATH, "//button[contains(text(), 'Hủy')]"),
                (By.XPATH, "//button[text()='Đóng']"),
                (By.XPATH, "//button[contains(text(), 'Đóng')]"),
                (By.XPATH, "//button[text()='Bỏ qua']"),
                (By.XPATH, "//button[contains(text(), 'Bỏ qua')]"),

                # Aria-label based selectors
                (By.XPATH, "//button[@aria-label='Cancel']"),
                (By.XPATH, "//button[@aria-label='Close']"),
                (By.XPATH, "//button[@aria-label='Dismiss']"),
                (By.XPATH, "//button[@aria-label='Skip']"),
                (By.XPATH, "//button[contains(@aria-label, 'Cancel')]"),
                (By.XPATH, "//button[contains(@aria-label, 'Close')]"),

                # Role-based selectors
                (By.XPATH, "//div[@role='button'][text()='Cancel']"),
                (By.XPATH, "//div[@role='button'][contains(text(), 'Cancel')]"),
                (By.XPATH, "//div[@role='button'][text()='Hủy']"),
                (By.XPATH, "//div[@role='button'][contains(text(), 'Hủy')]"),

                # Class-based selectors (common patterns)
                (By.XPATH, "//button[contains(@class, 'cancel')]"),
                (By.XPATH, "//button[contains(@class, 'close')]"),
                (By.XPATH, "//button[contains(@class, 'dismiss')]"),
                (By.XPATH, "//button[contains(@class, 'skip')]"),

                # Generic close button (X symbol)
                (By.XPATH, "//button[text()='×']"),
                (By.XPATH, "//button[text()='✕']"),
                (By.XPATH, "//button[contains(@class, 'close-button')]"),

                # Last resort - any button in modal
                (By.XPATH, "//div[@role='dialog']//button"),
                (By.XPATH, "//div[contains(@class, 'modal')]//button"),
            ]

            button_clicked = False
            for selector_type, selector_value in cancel_button_selectors:
                try:
                    button = WebDriverWait(self.driver, 2).until(
                        EC.element_to_be_clickable((selector_type, selector_value))
                    )
                    logger.info(f"Found cancel/close button with selector: {selector_value}")
                    button.click()
                    logger.info("Successfully clicked cancel/close button")
                    button_clicked = True
                    break
                except TimeoutException:
                    continue
                except Exception as e:
                    logger.debug(f"Error clicking button with selector {selector_value}: {e}")
                    continue

            if not button_clicked:
                logger.warning("Could not find or click cancel/close button in modal")
                # Try pressing Escape key as fallback
                try:
                    from selenium.webdriver.common.keys import Keys
                    self.driver.find_element(By.TAG_NAME, "body").send_keys(Keys.ESCAPE)
                    logger.info("Pressed Escape key to dismiss modal")
                except Exception as e:
                    logger.debug(f"Error pressing Escape key: {e}")

            # Wait for modal to disappear
            time.sleep(2)

            # Verify modal is gone
            try:
                for selector_type, selector_value in modal_selectors:
                    try:
                        modal = self.driver.find_element(selector_type, selector_value)
                        if modal.is_displayed():
                            logger.warning("Modal still visible after dismiss attempt")
                            return False
                    except NoSuchElementException:
                        continue
                logger.info("Modal successfully dismissed")
            except Exception as e:
                logger.debug(f"Error verifying modal dismissal: {e}")

            return True

        except Exception as e:
            logger.error(f"Error handling modal popup: {str(e)}")
            # Continue anyway, modal handling is not critical
            return True

    def search_and_select_contact(self, email):
        """Search for a contact and select them"""
        try:
            logger.info(f"Searching for contact: {email}")

            # Debug: Print current URL and look for all input fields
            logger.info(f"Current URL: {self.driver.current_url}")

            # Wait a moment for interface to stabilize after modal dismissal
            time.sleep(2)

            # Look for all input fields for debugging
            try:
                all_inputs = self.driver.find_elements(By.TAG_NAME, "input")
                logger.info(f"Found {len(all_inputs)} input fields on the page")
                for i, input_elem in enumerate(all_inputs):
                    try:
                        input_type = input_elem.get_attribute("type")
                        input_placeholder = input_elem.get_attribute("placeholder")
                        input_aria_label = input_elem.get_attribute("aria-label")
                        input_class = input_elem.get_attribute("class")
                        input_id = input_elem.get_attribute("id")
                        is_visible = input_elem.is_displayed()
                        logger.info(f"Input {i}: type='{input_type}', placeholder='{input_placeholder}', aria-label='{input_aria_label}', class='{input_class}', id='{input_id}', visible={is_visible}")
                    except:
                        pass
            except Exception as e:
                logger.error(f"Error debugging inputs: {e}")

            # Also look for any clickable elements that might trigger search
            try:
                search_triggers = self.driver.find_elements(By.XPATH, "//*[contains(@aria-label, 'search') or contains(@aria-label, 'Search') or contains(@aria-label, 'tìm') or contains(@aria-label, 'Tìm')]")
                logger.info(f"Found {len(search_triggers)} potential search trigger elements")
                for i, elem in enumerate(search_triggers):
                    try:
                        tag_name = elem.tag_name
                        aria_label = elem.get_attribute("aria-label")
                        class_name = elem.get_attribute("class")
                        is_visible = elem.is_displayed()
                        logger.info(f"Search trigger {i}: tag='{tag_name}', aria-label='{aria_label}', class='{class_name}', visible={is_visible}")
                    except:
                        pass
            except Exception as e:
                logger.error(f"Error debugging search triggers: {e}")

            # First, try to activate search form by clicking on form container or search triggers
            search_form_triggers = [
                # Search form container
                (By.ID, "aso_search_form_anchor"),
                (By.CSS_SELECTOR, "form.gb_jd.iPxMob"),
                (By.CSS_SELECTOR, "form[role='search'][aria-label='Tìm kiếm cuộc trò chuyện']"),
                # General search triggers
                (By.XPATH, "//*[@aria-label='Search' or @aria-label='search']"),
                (By.XPATH, "//*[contains(@aria-label, 'Search') or contains(@aria-label, 'search')]"),
                (By.XPATH, "//*[@aria-label='Tìm kiếm' or @aria-label='tìm kiếm']"),
                (By.XPATH, "//*[contains(@aria-label, 'Tìm') or contains(@aria-label, 'tìm')]"),
                (By.XPATH, "//button[contains(@class, 'search')]"),
                (By.XPATH, "//div[contains(@class, 'search')][@role='button']"),
            ]

            for selector_type, selector_value in search_form_triggers:
                try:
                    trigger = self.driver.find_element(selector_type, selector_value)
                    if trigger.is_displayed():
                        logger.info(f"Found search form trigger: {selector_value}")
                        trigger.click()
                        logger.info("Clicked search form trigger")
                        time.sleep(1)
                        break
                except (NoSuchElementException, ElementClickInterceptedException):
                    continue

            # Find search input field with specific HTML structure-based selectors
            search_selectors = [
                # Specific selectors based on provided HTML structure
                (By.CSS_SELECTOR, "input[name='q'][role='combobox']"),
                (By.CSS_SELECTOR, "input.gb_se[aria-label='Tìm kiếm cuộc trò chuyện']"),
                (By.CSS_SELECTOR, "form#aso_search_form_anchor input[type='text']"),
                (By.CSS_SELECTOR, "input[placeholder='Tìm kiếm cuộc trò chuyện']"),
                (By.CSS_SELECTOR, "input.gb_se"),
                (By.CSS_SELECTOR, "form.gb_jd.iPxMob input"),
                # Vietnamese selectors
                (By.CSS_SELECTOR, "input[aria-label='Tìm kiếm cuộc trò chuyện']"),
                (By.XPATH, "//input[contains(@aria-label, 'Tìm kiếm')]"),
                (By.XPATH, "//input[contains(@placeholder, 'Tìm kiếm')]"),
                # English selectors
                (By.CSS_SELECTOR, "input[aria-label='Search conversations']"),
                (By.CSS_SELECTOR, "input[placeholder='Search conversations']"),
                (By.XPATH, "//input[contains(@aria-label, 'Search')]"),
                (By.XPATH, "//input[contains(@placeholder, 'Search')]"),
                # Form-based selectors
                (By.XPATH, "//form[@role='search']//input[@type='text']"),
                (By.XPATH, "//form[@id='aso_search_form_anchor']//input"),
                # Generic selectors as fallback
                (By.XPATH, "//input[@type='text']"),
                (By.XPATH, "//input[@type='search']"),
                (By.CSS_SELECTOR, "input[type='text']"),
                (By.CSS_SELECTOR, "input[type='search']"),
            ]

            search_input = None
            for selector_type, selector_value in search_selectors:
                try:
                    search_input = WebDriverWait(self.driver, 3).until(
                        EC.element_to_be_clickable((selector_type, selector_value))
                    )
                    logger.info(f"Found search input with selector: {selector_value}")
                    break
                except TimeoutException:
                    continue

            # If no specific search input found, try form-specific error handling
            if not search_input:
                logger.warning("Search input not found with specific selectors, trying form-based approach...")

                # Check if search form exists but input is not interactable
                try:
                    search_form = self.driver.find_element(By.ID, "aso_search_form_anchor")
                    if search_form.is_displayed():
                        logger.info("Search form found, trying to activate it...")
                        # Try clicking on the form to activate it
                        search_form.click()
                        time.sleep(1)

                        # Try to find input again after activation
                        for selector_type, selector_value in search_selectors[:5]:  # Try top 5 selectors again
                            try:
                                search_input = WebDriverWait(self.driver, 2).until(
                                    EC.element_to_be_clickable((selector_type, selector_value))
                                )
                                logger.info(f"Found search input after form activation: {selector_value}")
                                break
                            except TimeoutException:
                                continue
                    else:
                        logger.warning("Search form not visible")
                except NoSuchElementException:
                    logger.warning("Search form not found")

                # Last resort: try any visible and interactable text input
                if not search_input:
                    try:
                        all_inputs = self.driver.find_elements(By.XPATH, "//input[@type='text']")
                        for input_elem in all_inputs:
                            try:
                                if input_elem.is_displayed() and input_elem.is_enabled():
                                    # Check if it's not a hidden field
                                    input_id = input_elem.get_attribute("id")
                                    if input_id != "hist_state":  # Skip the hidden history state field
                                        search_input = input_elem
                                        logger.info(f"Found visible and interactable text input (id: {input_id})")
                                        break
                            except:
                                continue

                        if not search_input:
                            logger.error("No interactable search input found after all attempts")
                            return False

                    except Exception as e:
                        logger.error(f"Error in fallback input search: {e}")
                        return False

            # Clear and enter email
            search_input.clear()
            time.sleep(0.5)
            search_input.send_keys(email)
            logger.info(f"Entered search term: {email}")

            # Wait for search results
            time.sleep(3)

            # Look for search results based on the HTML table structure
            result_selectors = [
                # Specific selectors based on provided HTML structure
                (By.CSS_SELECTOR, "tr[role='option']"),
                (By.CSS_SELECTOR, "table.gssb_m.NFaP4e tr[role='option']"),
                (By.CSS_SELECTOR, "div.IyhRae.YDAV2"),
                (By.XPATH, "//tr[@role='option']"),
                (By.XPATH, "//table[contains(@class, 'gssb_m')]//tr[@role='option']"),
                # Search for specific email in results
                (By.XPATH, f"//tr[@role='option'][contains(., '{email}')]"),
                (By.XPATH, f"//div[contains(@class, 'IyhRae')][contains(., '{email}')]"),
                (By.XPATH, f"//td[contains(text(), '{email}')]"),
                (By.XPATH, f"//span[contains(text(), '{email}')]"),
                # Generic result selectors
                (By.XPATH, f"//div[contains(text(), '{email}')]"),
                (By.XPATH, "//div[@role='option']"),
                (By.XPATH, "//div[@role='button']"),
                (By.XPATH, "//div[contains(@class, 'search-result')]"),
                (By.XPATH, "//div[contains(@class, 'conversation')]"),
            ]

            for selector_type, selector_value in result_selectors:
                try:
                    result = WebDriverWait(self.driver, 5).until(
                        EC.element_to_be_clickable((selector_type, selector_value))
                    )
                    logger.info(f"Found search result with selector: {selector_value}")
                    result.click()
                    logger.info(f"Clicked on search result for: {email}")
                    time.sleep(2)
                    return True
                except TimeoutException:
                    continue
                except ElementClickInterceptedException:
                    # Try to scroll to element and click again
                    try:
                        self.driver.execute_script("arguments[0].scrollIntoView(true);", result)
                        time.sleep(1)
                        result.click()
                        logger.info(f"Clicked on search result after scroll for: {email}")
                        time.sleep(2)
                        return True
                    except Exception as e:
                        logger.debug(f"Error clicking result after scroll: {e}")
                        continue

            logger.warning(f"No search results found for: {email}")
            return False

        except Exception as e:
            logger.error(f"Failed to search for contact {email}: {str(e)}")
            return False

    def send_message(self, nickname, phone_list):
        """Send message to the selected contact"""
        try:
            logger.info(f"Sending message to {nickname}")

            # Create message content
            phone_numbers = '\n'.join(phone_list)
            message = f"""Chào {nickname},
Bạn có những số demo done sau chưa chuyển, vui lòng check CRM và chuyển số trước 14h, sau 14h L5 của bạn sẽ không được tính nữa:

📱 List số:
{phone_numbers}"""

            logger.info(f"Message content:\n{message}")

            # Find chat input field
            input_selectors = [
                (By.CSS_SELECTOR, "div[role='textbox'][aria-label='Đã bật lịch sử']"),
                (By.CSS_SELECTOR, "div.hj99tb.KRoqRc.editable"),
                (By.CSS_SELECTOR, "div[role='textbox']"),
                (By.CSS_SELECTOR, "div[contenteditable='true']"),
                (By.XPATH, "//div[@role='textbox']"),
                (By.XPATH, "//div[@contenteditable='true']"),
            ]

            chat_input = None
            for selector_type, selector_value in input_selectors:
                try:
                    chat_input = WebDriverWait(self.driver, 10).until(
                        EC.element_to_be_clickable((selector_type, selector_value))
                    )
                    logger.info(f"Found chat input with selector: {selector_value}")
                    break
                except TimeoutException:
                    continue

            if not chat_input:
                logger.error("Could not find chat input field")
                return False

            # Click on input field and enter message
            chat_input.click()
            time.sleep(0.5)

            # Clear any existing content
            chat_input.clear()

            # Type the message
            chat_input.send_keys(message)
            logger.info("Message typed into chat input")

            # Send the message
            time.sleep(1)
            chat_input.send_keys(Keys.RETURN)
            logger.info("Message sent successfully")

            # Wait before next message
            time.sleep(3)
            return True

        except Exception as e:
            logger.error(f"Failed to send message: {str(e)}")
            return False

    def send_messages_to_all_contacts(self):
        """Send messages to all contacts in the CSV data"""
        try:
            logger.info("Starting to send messages to all contacts...")

            if not self.contacts_data:
                logger.error("No contact data available")
                return False

            success_count = 0
            failed_count = 0

            for email, contact_info in self.contacts_data.items():
                try:
                    logger.info(f"Processing contact {success_count + failed_count + 1}/{len(self.contacts_data)}: {email}")

                    nickname = contact_info['nickname']
                    phone_list = contact_info['phone_list']

                    # Skip if no phone numbers
                    if not phone_list:
                        logger.warning(f"No phone numbers for {email}, skipping...")
                        failed_count += 1
                        continue

                    # Search for contact
                    if not self.search_and_select_contact(email):
                        logger.error(f"Failed to find contact: {email}")
                        failed_count += 1
                        continue

                    # Send message
                    if self.send_message(nickname, phone_list):
                        logger.info(f"Successfully sent message to {email}")
                        success_count += 1
                    else:
                        logger.error(f"Failed to send message to {email}")
                        failed_count += 1

                    # Delay between contacts to avoid rate limiting
                    logger.info("Waiting before next contact...")
                    time.sleep(5)

                except Exception as e:
                    logger.error(f"Error processing contact {email}: {str(e)}")
                    failed_count += 1
                    continue

            logger.info(f"Messaging completed. Success: {success_count}, Failed: {failed_count}")
            return success_count > 0

        except Exception as e:
            logger.error(f"Failed to send messages to contacts: {str(e)}")
            return False

    def run_automation(self):
        """Main method to run the complete automation"""
        try:
            logger.info("Starting Google Chat messaging automation...")

            # Step 1: Read CSV data
            if not self.read_csv_data():
                logger.error("Failed to read CSV data")
                return False

            # Step 2: Setup WebDriver
            if not self.setup_driver():
                logger.error("Failed to setup WebDriver")
                return False

            # Step 3: Login to Google Chat
            if not self.login_to_google_chat():
                logger.error("Failed to login to Google Chat")
                return False

            # Step 4: Handle modal popup after login
            if not self.handle_modal_popup():
                logger.warning("Modal popup handling failed, but continuing...")

            # Step 5: Wait for interface to fully load after modal dismissal
            logger.info("Waiting for Google Chat interface to fully load...")
            time.sleep(5)

            # Step 6: Send messages to all contacts
            if not self.send_messages_to_all_contacts():
                logger.error("Failed to send messages")
                return False

            logger.info("Google Chat messaging automation completed successfully!")
            return True

        except Exception as e:
            logger.error(f"Automation failed: {str(e)}")
            return False

    def cleanup(self):
        """Clean up WebDriver resources"""
        if self.driver:
            logger.info("Cleaning up WebDriver...")
            self.driver.quit()
            logger.info("WebDriver cleanup completed")

def main():
    """Main function to run the Google Chat messaging automation"""

    # Configuration
    csv_file_path = "/home/<USER>/Downloads/File gửi số cho sale book - Trang tính1.csv"

    # Check if CSV file exists, if not use sample data
    if not os.path.exists(csv_file_path):
        logger.warning(f"CSV file not found at {csv_file_path}")
        csv_file_path = "sample_data.csv"
        logger.info(f"Using sample CSV file: {csv_file_path}")

    # Initialize automation
    automation = GoogleChatMessaging(
        csv_file_path=csv_file_path,
        headless=False,  # Set to True for headless mode
        incognito=True,  # Set to False to disable incognito mode
        timeout=30
    )

    try:
        # Run automation
        success = automation.run_automation()

        if success:
            logger.info("All messaging completed successfully!")
            input("Press Enter to close the browser...")
        else:
            logger.error("Messaging automation failed!")

        return success

    except KeyboardInterrupt:
        logger.info("Script interrupted by user")
        return False
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        return False
    finally:
        # Ensure cleanup happens
        automation.cleanup()

if __name__ == "__main__":
    main()
