#!/usr/bin/env python3
"""
Gradio Web Application for Google Chat Messaging Automation
Integrates with the existing GoogleChatMessaging class to provide a user-friendly interface
"""

import gradio as gr
import pandas as pd
import os
import threading
import time
import logging
from io import StringIO
import sys
from datetime import datetime
from google_chat_messaging import GoogleChatMessaging

# Configure logging for the web app
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class GradioGoogleChatApp:
    def __init__(self):
        self.automation = None
        self.is_running = False
        self.progress_callback = None
        self.log_callback = None
        self.current_logs = []
        
    def validate_csv_file(self, file_path):
        """Validate uploaded CSV file format"""
        try:
            if not file_path:
                return False, "No file uploaded", None
                
            # Read CSV file
            df = pd.read_csv(file_path)
            
            # Check required columns
            required_columns = [
                'Email', '2. User CTV Book', '3. SĐT Phụ huynh', 
                '4. <PERSON><PERSON><PERSON> học sinh', '5. <PERSON>ớ<PERSON>', '6. Môn', 
                '7. <PERSON><PERSON><PERSON><PERSON> b<PERSON> h<PERSON>c', '7. Gi<PERSON> học demo buổi đầu', 
                '9. Ngày học buổi đầu tiên'
            ]
            
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                return False, f"Missing required columns: {', '.join(missing_columns)}", None
            
            # Check if file has data
            if len(df) == 0:
                return False, "CSV file is empty", None
                
            return True, f"✅ Valid CSV file with {len(df)} rows", df
            
        except Exception as e:
            return False, f"Error reading CSV file: {str(e)}", None
    
    def preview_csv_data(self, file_path):
        """Preview uploaded CSV data"""
        if not file_path:
            return "No file uploaded", None
            
        is_valid, message, df = self.validate_csv_file(file_path)
        
        if not is_valid:
            return message, None
            
        # Return preview message and dataframe for display
        preview_msg = f"📊 CSV Preview - {len(df)} contacts found\n\n"
        preview_msg += f"Columns: {', '.join(df.columns.tolist())}\n\n"
        preview_msg += "✅ File is ready for processing!"
        
        return preview_msg, df.head(10)  # Show first 10 rows
    
    def generate_message_preview(self, file_path):
        """Generate preview of messages that will be sent"""
        if not file_path:
            return "No file uploaded"
            
        is_valid, message, df = self.validate_csv_file(file_path)
        if not is_valid:
            return f"❌ {message}"
            
        try:
            # Create temporary automation instance to process data
            temp_automation = GoogleChatMessaging(file_path)
            
            if not temp_automation.contacts_data:
                return "❌ No valid contacts found in CSV"
            
            # Generate preview for first contact
            first_email = list(temp_automation.contacts_data.keys())[0]
            first_contact = temp_automation.contacts_data[first_email]
            
            nickname = first_contact['nickname']
            phone_list = first_contact['phone_list']
            phone_numbers = '\n'.join(phone_list)
            
            sample_message = f"""
Chào {nickname},

Bạn có những số demo đã hoàn thành nhưng chưa chuyển, vui lòng kiểm tra CRM và chuyển số trước 14h. Sau 14h, L5 của bạn sẽ không được tính nữa.

---

Danh sách số:

{phone_numbers}
"""
            
            preview_text = f"📝 Message Preview (Sample for {first_email}):\n\n"
            preview_text += "=" * 50 + "\n"
            preview_text += sample_message
            preview_text += "=" * 50 + "\n\n"
            preview_text += f"📊 Total contacts to message: {len(temp_automation.contacts_data)}\n"
            preview_text += f"📱 Total phone numbers: {sum(len(contact['phone_list']) for contact in temp_automation.contacts_data.values())}"
            
            return preview_text
            
        except Exception as e:
            return f"❌ Error generating preview: {str(e)}"
    
    def start_automation(self, file_path, username, password, headless, incognito, progress=gr.Progress()):
        """Start the Google Chat messaging automation"""
        if self.is_running:
            return "❌ Automation is already running!", "", ""
            
        if not file_path:
            return "❌ Please upload a CSV file first!", "", ""
            
        if not username or not password:
            return "❌ Please provide both username and password!", "", ""
        
        # Validate file
        is_valid, message, df = self.validate_csv_file(file_path)
        if not is_valid:
            return f"❌ {message}", "", ""
        
        self.is_running = True
        self.current_logs = []
        
        try:
            # Create automation instance
            self.automation = GoogleChatMessaging(file_path)
            
            # Configure automation settings
            self.automation.username = username
            self.automation.password = password
            self.automation.headless_mode = headless
            self.automation.incognito_mode = incognito
            
            # Start automation in a separate thread
            def run_automation():
                try:
                    # Capture logs
                    log_capture = StringIO()
                    handler = logging.StreamHandler(log_capture)
                    handler.setLevel(logging.INFO)
                    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
                    handler.setFormatter(formatter)
                    
                    # Add handler to capture logs
                    automation_logger = logging.getLogger('google_chat_messaging')
                    automation_logger.addHandler(handler)
                    automation_logger.setLevel(logging.INFO)
                    
                    # Run the automation
                    success = self.automation.run_automation()
                    
                    # Get captured logs
                    log_content = log_capture.getvalue()
                    self.current_logs.append(log_content)
                    
                    # Remove handler
                    automation_logger.removeHandler(handler)
                    
                    self.is_running = False
                    
                    if success:
                        return "✅ Automation completed successfully!"
                    else:
                        return "❌ Automation failed. Check logs for details."
                        
                except Exception as e:
                    self.is_running = False
                    error_msg = f"❌ Automation error: {str(e)}"
                    self.current_logs.append(error_msg)
                    return error_msg
            
            # Start automation thread
            automation_thread = threading.Thread(target=run_automation)
            automation_thread.daemon = True
            automation_thread.start()
            
            # Update progress
            total_contacts = len(self.automation.contacts_data)
            for i in range(total_contacts):
                if not self.is_running:
                    break
                progress((i + 1) / total_contacts, f"Processing contact {i + 1}/{total_contacts}")
                time.sleep(2)  # Simulate progress updates
            
            # Wait for completion
            automation_thread.join(timeout=300)  # 5 minute timeout
            
            if self.is_running:
                self.is_running = False
                return "⚠️ Automation timed out after 5 minutes", "", ""
            
            # Generate results summary
            if self.automation:
                results = self.generate_results_summary()
                logs = '\n'.join(self.current_logs)
                return "✅ Automation completed!", results, logs
            else:
                return "❌ Automation failed to initialize", "", ""
                
        except Exception as e:
            self.is_running = False
            error_msg = f"❌ Error starting automation: {str(e)}"
            return error_msg, "", error_msg
    
    def stop_automation(self):
        """Stop the running automation"""
        if not self.is_running:
            return "ℹ️ No automation is currently running"
        
        self.is_running = False
        if self.automation and hasattr(self.automation, 'driver') and self.automation.driver:
            try:
                self.automation.driver.quit()
            except:
                pass
        
        return "🛑 Automation stopped by user"
    
    def generate_results_summary(self):
        """Generate a summary of automation results"""
        if not self.automation:
            return "No automation data available"
        
        try:
            total_contacts = len(self.automation.contacts_data)
            # This would need to be tracked during automation
            # For now, we'll provide a basic summary
            
            summary = f"""
📊 AUTOMATION RESULTS SUMMARY
{'=' * 40}

📧 Total Contacts Processed: {total_contacts}
📱 Total Phone Numbers: {sum(len(contact['phone_list']) for contact in self.automation.contacts_data.values())}

⏰ Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

📋 Contact Details:
"""
            
            for email, contact in self.automation.contacts_data.items():
                summary += f"  • {email} ({contact['nickname']}) - {len(contact['phone_list'])} numbers\n"
            
            return summary
            
        except Exception as e:
            return f"Error generating summary: {str(e)}"
    
    def get_automation_status(self):
        """Get current automation status"""
        if self.is_running:
            return "🔄 Running...", "🛑 Stop Automation"
        else:
            return "⏹️ Stopped", "▶️ Start Automation"
    
    def create_interface(self):
        """Create the Gradio interface"""
        with gr.Blocks(title="Google Chat Messaging Automation", theme=gr.themes.Soft()) as interface:
            gr.Markdown("""
            # 🚀 Google Chat Messaging Automation
            
            Upload your CSV file and automatically send personalized messages to your contacts via Google Chat.
            
            ## 📋 CSV Format Requirements:
            Your CSV file must contain these columns:
            - `Email` - Contact email addresses
            - `2. User CTV Book` - User nickname
            - `3. SĐT Phụ huynh` - Phone numbers
            - Other columns as per sample format
            """)
            
            with gr.Tab("📁 File Upload & Preview"):
                with gr.Row():
                    with gr.Column(scale=1):
                        file_upload = gr.File(
                            label="📎 Upload CSV File",
                            file_types=[".csv"],
                            type="filepath"
                        )
                        
                        preview_btn = gr.Button("👀 Preview Data", variant="secondary")
                        message_preview_btn = gr.Button("📝 Preview Messages", variant="secondary")
                    
                    with gr.Column(scale=2):
                        file_status = gr.Textbox(
                            label="📊 File Status",
                            placeholder="Upload a CSV file to see status...",
                            lines=3
                        )
                
                csv_preview = gr.Dataframe(
                    label="📋 CSV Data Preview"
                )
                
                message_preview = gr.Textbox(
                    label="📝 Message Preview",
                    placeholder="Click 'Preview Messages' to see sample message...",
                    lines=15
                )
            
            with gr.Tab("🔐 Configuration & Control"):
                with gr.Row():
                    with gr.Column():
                        gr.Markdown("### 🔑 Login Credentials")
                        username = gr.Textbox(
                            label="📧 Username/Email",
                            placeholder="<EMAIL>",
                            value="<EMAIL>",
                            type="email"
                        )
                        password = gr.Textbox(
                            label="🔒 Password",
                            placeholder="Your password",
                            value="Uni@2025!",
                            type="password"
                        )
                    
                    with gr.Column():
                        gr.Markdown("### ⚙️ Browser Settings")
                        headless = gr.Checkbox(
                            label="🖥️ Headless Mode (Run in background)",
                            value=True
                        )
                        incognito = gr.Checkbox(
                            label="🕵️ Incognito Mode",
                            value=True
                        )
                
                with gr.Row():
                    start_btn = gr.Button("▶️ Start Automation", variant="primary", size="lg")
                    stop_btn = gr.Button("🛑 Stop Automation", variant="stop", size="lg")
                
                automation_status = gr.Textbox(
                    label="📊 Automation Status",
                    value="⏹️ Ready to start",
                    interactive=False
                )
            
            with gr.Tab("📊 Progress & Results"):
                with gr.Row():
                    with gr.Column():
                        progress_display = gr.Textbox(
                            label="🔄 Current Progress",
                            placeholder="Automation progress will appear here...",
                            lines=5
                        )
                        
                        results_summary = gr.Textbox(
                            label="📈 Results Summary",
                            placeholder="Results will appear after automation completes...",
                            lines=10
                        )
                    
                    with gr.Column():
                        logs_display = gr.Textbox(
                            label="📝 Automation Logs",
                            placeholder="Detailed logs will appear here during automation...",
                            lines=20,
                            max_lines=50
                        )
                
                download_logs = gr.File(
                    label="💾 Download Logs",
                    visible=False
                )
            
            # Event handlers
            file_upload.change(
                fn=self.preview_csv_data,
                inputs=[file_upload],
                outputs=[file_status, csv_preview]
            )
            
            preview_btn.click(
                fn=self.preview_csv_data,
                inputs=[file_upload],
                outputs=[file_status, csv_preview]
            )
            
            message_preview_btn.click(
                fn=self.generate_message_preview,
                inputs=[file_upload],
                outputs=[message_preview]
            )
            
            start_btn.click(
                fn=self.start_automation,
                inputs=[file_upload, username, password, headless, incognito],
                outputs=[automation_status, results_summary, logs_display]
            )
            
            stop_btn.click(
                fn=self.stop_automation,
                outputs=[automation_status]
            )
        
        return interface

def main():
    """Main function to run the Gradio app"""
    app = GradioGoogleChatApp()
    interface = app.create_interface()
    
    # Launch the interface
    interface.launch(
        server_name="0.0.0.0",
        server_port=7861,
        share=True,
        debug=True,
        show_error=True
    )

if __name__ == "__main__":
    main()
