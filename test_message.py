#!/usr/bin/env python3
"""
Test script to verify the messaging functionality works
"""

import time
import logging
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_message_sending():
    """Test message sending with simple ASCII characters"""
    
    # Simple message without emojis
    nickname = "test"
    phone_numbers = "123456789"
    message = f"""Chào {nickname},
Bạn có những số demo done sau chưa chuyển, vui lòng check CRM và chuyển số trước 14h, sau 14h L5 của bạn sẽ không được tính nữa:

* List số:
{phone_numbers}"""
    
    logger.info(f"Test message content:\n{message}")
    
    # Test if message contains only BMP characters
    try:
        message.encode('utf-16')
        logger.info("Message encoding test: PASSED - All characters are BMP compatible")
        return True
    except UnicodeEncodeError as e:
        logger.error(f"Message encoding test: FAILED - {e}")
        return False

if __name__ == "__main__":
    test_message_sending()
