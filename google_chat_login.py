#!/usr/bin/env python3
"""
Google Chat Login Automation Script using Selenium WebDriver

This script automates the login process for Google Chat (chat.google.com)
using Selenium WebDriver with Chrome browser.

Requirements:
- selenium package: pip install selenium
- Chrome browser installed
- ChromeDriver (automatically managed by selenium 4.x)

Author: Auto-generated script
Date: 2025-07-29
"""

import time
import logging
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.common.exceptions import (
    TimeoutException,
    NoSuchElementException,
    WebDriverException,
    ElementClickInterceptedException
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class GoogleChatLogin:
    """Class to handle Google Chat login automation"""
    
    def __init__(self, headless=False, timeout=30):
        """
        Initialize the GoogleChatLogin class
        
        Args:
            headless (bool): Run browser in headless mode
            timeout (int): Default timeout for WebDriverWait
        """
        self.driver = None
        self.wait = None
        self.timeout = timeout
        self.headless = headless
        
        # Login credentials
        self.username = "<EMAIL>"
        self.password = "Uni@2025!"
        
        # URLs
        self.chat_url = "https://chat.google.com"
        self.login_url = "https://accounts.google.com/v3/signin/identifier?continue=https%3A%2F%2Fchat.google.com%2F%3Freferrer%3D2&ec=wgc-chat-globalnav-signin&ifkv=AdBytiPQNtzlBi5QNev3dkFJbveZXstcPU8KpAg5otCcPHt5lRpV6oJhOp8b4NemjeStJS4lwCIWww&flowName=GlifWebSignIn&flowEntry=ServiceLogin&dsh=S303255065%3A1753782885323592"
    
    def setup_driver(self):
        """Set up Chrome WebDriver with appropriate options"""
        try:
            logger.info("Setting up Chrome WebDriver...")
            
            # Configure Chrome options
            chrome_options = Options()
            
            if self.headless:
                chrome_options.add_argument("--headless")
            
            # Additional Chrome options for better stability
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--window-size=1920,1080")
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            # Initialize WebDriver
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            # Set up WebDriverWait
            self.wait = WebDriverWait(self.driver, self.timeout)
            
            logger.info("Chrome WebDriver setup completed successfully")
            return True
            
        except WebDriverException as e:
            logger.error(f"Failed to setup WebDriver: {str(e)}")
            return False
    
    def navigate_to_google_chat(self):
        """Navigate directly to Google login page"""
        try:
            logger.info("Navigating directly to Google login page...")
            self.driver.get(self.login_url)

            # Wait for page to load
            time.sleep(3)
            current_url = self.driver.current_url

            logger.info(f"Current URL after navigation: {current_url}")
            logger.info(f"Page title: {self.driver.title}")

            if "accounts.google.com" in current_url:
                logger.info("Successfully navigated to Google login page")
                return True
            elif "chat.google.com" in current_url:
                logger.info("Already logged in, redirected to chat page")
                return self.verify_login_success()
            else:
                logger.warning(f"Unexpected URL after direct navigation: {current_url}")
                return False

        except Exception as e:
            logger.error(f"Failed to navigate to Google login page: {str(e)}")
            return False


    
    def enter_email(self):
        """Enter email address in the first step of Google v3 signin"""
        try:
            logger.info("Entering email address on v3 signin page...")

            # Debug: Print current URL and page title
            logger.info(f"Current URL: {self.driver.current_url}")
            logger.info(f"Page title: {self.driver.title}")

            # Wait for page to fully load
            time.sleep(2)

            # Debug: Look for all input fields on the page
            try:
                all_inputs = self.driver.find_elements(By.TAG_NAME, "input")
                logger.info(f"Found {len(all_inputs)} input fields on the page")
                for i, input_elem in enumerate(all_inputs):
                    try:
                        input_type = input_elem.get_attribute("type")
                        input_name = input_elem.get_attribute("name")
                        input_id = input_elem.get_attribute("id")
                        input_class = input_elem.get_attribute("class")
                        input_aria_label = input_elem.get_attribute("aria-label")
                        logger.info(f"Input {i}: type='{input_type}', name='{input_name}', id='{input_id}', class='{input_class}', aria-label='{input_aria_label}'")
                    except:
                        pass
            except Exception as e:
                logger.error(f"Error debugging inputs: {e}")

            # Try specific selectors for v3 signin email input field
            email_selectors = [
                # Most specific selector first - exact match for v3 signin
                (By.XPATH, "//input[@type='email' and @class='whsOnd zHQkBf' and @name='identifier' and @id='identifierId']"),
                (By.XPATH, "//input[@type='email' and @name='identifier' and @id='identifierId']"),
                (By.XPATH, "//input[@type='email' and contains(@class, 'whsOnd') and @name='identifier']"),
                (By.XPATH, "//input[@aria-label='Email or phone' and @type='email']"),
                (By.XPATH, "//input[@aria-label='Email or phone']"),
                # Fallback selectors
                (By.ID, "identifierId"),
                (By.NAME, "identifier"),
                (By.XPATH, "//input[@type='email']"),
                (By.XPATH, "//input[contains(@class, 'whsOnd')]"),
                (By.CSS_SELECTOR, "input[type='email'][name='identifier']#identifierId"),
                (By.CSS_SELECTOR, "input.whsOnd.zHQkBf[type='email']"),
                (By.CSS_SELECTOR, "input#identifierId"),
            ]

            email_input = None
            for selector_type, selector_value in email_selectors:
                try:
                    # Use longer timeout for the first few specific selectors
                    timeout = 10 if email_selectors.index((selector_type, selector_value)) < 5 else 3
                    wait = WebDriverWait(self.driver, timeout)
                    email_input = wait.until(
                        EC.element_to_be_clickable((selector_type, selector_value))
                    )
                    logger.info(f"Found email input with selector: {selector_value}")
                    break
                except TimeoutException:
                    logger.debug(f"Selector failed: {selector_value}")
                    continue

            if not email_input:
                logger.error("Could not find email input field with any selector")
                # Save screenshot for debugging
                try:
                    self.driver.save_screenshot("debug_email_page.png")
                    logger.info("Screenshot saved as debug_email_page.png")
                except:
                    pass
                return False

            # Clear and enter email
            email_input.clear()
            time.sleep(0.5)  # Small delay to ensure field is ready
            email_input.send_keys(self.username)
            logger.info(f"Email entered: {self.username}")

            # Wait a moment for the input to register
            time.sleep(1)

            # Try multiple methods to submit the email form
            logger.info("Attempting to submit email form...")

            # Method 1: Try pressing Enter key
            try:
                email_input.send_keys(Keys.RETURN)
                logger.info("Pressed Enter key to submit email")
                time.sleep(2)

                # Check if we moved to password page
                if self.driver.current_url != self.login_url or "password" in self.driver.current_url.lower():
                    logger.info("Successfully submitted email via Enter key")
                    return True
            except Exception as e:
                logger.debug(f"Enter key method failed: {e}")

            # Method 2: Try clicking Next button
            next_selectors = [
                (By.ID, "identifierNext"),
                (By.XPATH, "//div[@id='identifierNext']"),
                (By.XPATH, "//button[@id='identifierNext']"),
                (By.XPATH, "//div[@role='button' and @id='identifierNext']"),
                (By.XPATH, "//button[contains(text(), 'Next')]"),
                (By.XPATH, "//button[contains(text(), 'Tiếp theo')]"),  # Vietnamese
                (By.XPATH, "//span[text()='Next']/parent::*"),
                (By.XPATH, "//span[text()='Tiếp theo']/parent::*"),  # Vietnamese
                (By.CSS_SELECTOR, "#identifierNext"),
            ]

            for selector_type, selector_value in next_selectors:
                try:
                    next_button = WebDriverWait(self.driver, 3).until(
                        EC.element_to_be_clickable((selector_type, selector_value))
                    )
                    logger.info(f"Found Next button with selector: {selector_value}")
                    next_button.click()
                    logger.info("Clicked Next button after email entry")
                    time.sleep(2)

                    # Check if we moved to password page
                    if self.driver.current_url != self.login_url:
                        logger.info("Successfully submitted email via Next button")
                        return True
                    break
                except TimeoutException:
                    continue
                except Exception as e:
                    logger.debug(f"Next button click failed: {e}")
                    continue

            # Method 3: Try submitting the form directly
            try:
                form = email_input.find_element(By.XPATH, "./ancestor::form")
                form.submit()
                logger.info("Submitted form directly")
                time.sleep(2)
                return True
            except Exception as e:
                logger.debug(f"Form submit failed: {e}")

            logger.error("All email submission methods failed")
            return False

        except Exception as e:
            logger.error(f"Failed to enter email: {str(e)}")
            return False
    
    def enter_password(self):
        """Enter password in the second step of Google v3 signin"""
        try:
            logger.info("Entering password on v3 signin page...")

            # Debug: Print current URL and page title
            logger.info(f"Current URL: {self.driver.current_url}")
            logger.info(f"Page title: {self.driver.title}")

            # Wait for password page to load
            time.sleep(3)

            # Debug: Look for all input fields on the password page
            try:
                all_inputs = self.driver.find_elements(By.TAG_NAME, "input")
                logger.info(f"Found {len(all_inputs)} input fields on password page")
                for i, input_elem in enumerate(all_inputs):
                    try:
                        input_type = input_elem.get_attribute("type")
                        input_name = input_elem.get_attribute("name")
                        input_id = input_elem.get_attribute("id")
                        input_class = input_elem.get_attribute("class")
                        input_aria_label = input_elem.get_attribute("aria-label")
                        logger.info(f"Input {i}: type='{input_type}', name='{input_name}', id='{input_id}', class='{input_class}', aria-label='{input_aria_label}'")
                    except:
                        pass
            except Exception as e:
                logger.error(f"Error debugging password inputs: {e}")

            # Try specific selectors for v3 signin password input field
            password_selectors = [
                # Most specific selector first - exact match for v3 signin
                (By.XPATH, "//input[@type='password' and @class='whsOnd zHQkBf' and @name='Passwd']"),
                (By.XPATH, "//input[@type='password' and contains(@class, 'whsOnd') and @name='Passwd']"),
                (By.XPATH, "//input[@aria-label='Enter your password' and @type='password']"),
                (By.XPATH, "//input[@aria-label='Enter your password']"),
                (By.NAME, "Passwd"),
                # Fallback selectors
                (By.NAME, "password"),
                (By.XPATH, "//input[@type='password']"),
                (By.XPATH, "//input[contains(@class, 'whsOnd')][@type='password']"),
                (By.CSS_SELECTOR, "input[type='password'][name='Passwd']"),
                (By.CSS_SELECTOR, "input.whsOnd.zHQkBf[type='password']"),
                (By.CSS_SELECTOR, "input[type='password']"),
            ]

            password_input = None
            for selector_type, selector_value in password_selectors:
                try:
                    # Use longer timeout for the first few specific selectors
                    timeout = 10 if password_selectors.index((selector_type, selector_value)) < 5 else 3
                    wait = WebDriverWait(self.driver, timeout)
                    password_input = wait.until(
                        EC.element_to_be_clickable((selector_type, selector_value))
                    )
                    logger.info(f"Found password input with selector: {selector_value}")
                    break
                except TimeoutException:
                    logger.debug(f"Password selector failed: {selector_value}")
                    continue

            if not password_input:
                logger.error("Could not find password input field with any selector")
                # Save screenshot for debugging
                try:
                    self.driver.save_screenshot("debug_password_page.png")
                    logger.info("Screenshot saved as debug_password_page.png")
                except:
                    pass
                return False

            # Clear and enter password
            password_input.clear()
            time.sleep(0.5)  # Small delay to ensure field is ready
            password_input.send_keys(self.password)
            logger.info("Password entered")

            # Wait a moment for the input to register
            time.sleep(1)

            # Try multiple methods to submit the password form
            logger.info("Attempting to submit password form...")

            # Method 1: Try pressing Enter key
            try:
                password_input.send_keys(Keys.RETURN)
                logger.info("Pressed Enter key to submit password")
                time.sleep(3)

                # Check if we moved away from password page
                if "password" not in self.driver.current_url.lower() or "chat.google.com" in self.driver.current_url:
                    logger.info("Successfully submitted password via Enter key")
                    return True
            except Exception as e:
                logger.debug(f"Enter key method failed: {e}")

            # Method 2: Try clicking Next button
            next_selectors = [
                (By.ID, "passwordNext"),
                (By.XPATH, "//div[@id='passwordNext']"),
                (By.XPATH, "//button[@id='passwordNext']"),
                (By.XPATH, "//div[@role='button' and @id='passwordNext']"),
                (By.XPATH, "//button[contains(text(), 'Next')]"),
                (By.XPATH, "//button[contains(text(), 'Tiếp theo')]"),  # Vietnamese
                (By.XPATH, "//span[text()='Next']/parent::*"),
                (By.XPATH, "//span[text()='Tiếp theo']/parent::*"),  # Vietnamese
                (By.CSS_SELECTOR, "#passwordNext"),
            ]

            for selector_type, selector_value in next_selectors:
                try:
                    next_button = WebDriverWait(self.driver, 3).until(
                        EC.element_to_be_clickable((selector_type, selector_value))
                    )
                    logger.info(f"Found Next button with selector: {selector_value}")
                    next_button.click()
                    logger.info("Clicked Next button after password entry")
                    time.sleep(3)

                    # Check if we moved away from password page
                    if "password" not in self.driver.current_url.lower():
                        logger.info("Successfully submitted password via Next button")
                        return True
                    break
                except TimeoutException:
                    continue
                except Exception as e:
                    logger.debug(f"Next button click failed: {e}")
                    continue

            # Method 3: Try submitting the form directly
            try:
                form = password_input.find_element(By.XPATH, "./ancestor::form")
                form.submit()
                logger.info("Submitted password form directly")
                time.sleep(3)
                return True
            except Exception as e:
                logger.debug(f"Form submit failed: {e}")

            logger.error("All password submission methods failed")
            return False

        except Exception as e:
            logger.error(f"Failed to enter password: {str(e)}")
            return False
    
    def handle_2fa_prompt(self):
        """Handle potential 2FA or security challenges"""
        try:
            logger.info("Checking for 2FA or security challenges...")
            
            # Wait a bit to see if 2FA prompt appears
            time.sleep(3)
            
            # Check for various 2FA elements
            possible_2fa_selectors = [
                (By.ID, "totpPin"),
                (By.NAME, "totpPin"),
                (By.XPATH, "//input[@type='tel']"),
                (By.XPATH, "//input[@placeholder='Enter code']"),
            ]
            
            for selector_type, selector_value in possible_2fa_selectors:
                try:
                    element = self.driver.find_element(selector_type, selector_value)
                    if element.is_displayed():
                        logger.warning("2FA prompt detected. Manual intervention required.")
                        input("Please complete 2FA manually and press Enter to continue...")
                        return True
                except NoSuchElementException:
                    continue
            
            logger.info("No 2FA prompt detected")
            return True
            
        except Exception as e:
            logger.error(f"Error handling 2FA prompt: {str(e)}")
            return False
    
    def verify_login_success(self):
        """Verify successful login by checking for Google Chat interface elements"""
        try:
            logger.info("Verifying login success...")

            # Debug: Print current URL
            logger.info(f"Current URL during verification: {self.driver.current_url}")

            # Wait longer for redirect to Google Chat (up to 60 seconds)
            logger.info("Waiting for redirect to Google Chat...")
            try:
                WebDriverWait(self.driver, 60).until(
                    lambda driver: (driver.current_url.startswith("https://chat.google.com") or
                                  "mail.google.com/chat" in driver.current_url)
                )
                logger.info("Successfully redirected to Google Chat!")
            except TimeoutException:
                logger.warning("Timeout waiting for redirect to Google Chat")
                # Check if we're still on a Google accounts page
                if "accounts.google.com" in self.driver.current_url:
                    logger.info("Still on Google accounts page, checking for additional steps...")
                    # Sometimes there are additional consent or setup pages
                    # Try to find and click continue/skip buttons
                    continue_selectors = [
                        (By.XPATH, "//button[contains(text(), 'Continue')]"),
                        (By.XPATH, "//button[contains(text(), 'Skip')]"),
                        (By.XPATH, "//button[contains(text(), 'Next')]"),
                        (By.XPATH, "//a[contains(text(), 'Continue')]"),
                        (By.XPATH, "//div[@role='button'][contains(text(), 'Continue')]"),
                    ]

                    for selector_type, selector_value in continue_selectors:
                        try:
                            button = WebDriverWait(self.driver, 5).until(
                                EC.element_to_be_clickable((selector_type, selector_value))
                            )
                            logger.info(f"Found continue button: {selector_value}")
                            button.click()
                            time.sleep(3)
                            break
                        except TimeoutException:
                            continue

                    # Try waiting again for redirect
                    try:
                        WebDriverWait(self.driver, 30).until(
                            lambda driver: (driver.current_url.startswith("https://chat.google.com") or
                                          "mail.google.com/chat" in driver.current_url)
                        )
                        logger.info("Successfully redirected to Google Chat after clicking continue!")
                    except TimeoutException:
                        logger.error("Failed to redirect to Google Chat even after clicking continue")
                        return False

            # Now verify we're actually on Google Chat (either chat.google.com or mail.google.com/chat)
            if not (self.driver.current_url.startswith("https://chat.google.com") or
                    "mail.google.com/chat" in self.driver.current_url):
                logger.error(f"Not on Google Chat. Current URL: {self.driver.current_url}")
                return False

            # Wait for Google Chat interface to load
            logger.info("Waiting for Google Chat interface to load...")
            time.sleep(5)

            # Check for Google Chat interface elements
            chat_indicators = [
                (By.XPATH, "//div[@data-topic-id]"),  # Chat rooms
                (By.XPATH, "//div[contains(@class, 'chat')]"),  # Chat interface
                (By.XPATH, "//button[@aria-label='Main menu']"),  # Main menu
                (By.XPATH, "//div[@role='main']"),  # Main content area
                (By.XPATH, "//div[contains(@class, 'conversation')]"),  # Conversation area
                (By.XPATH, "//div[@role='navigation']"),  # Navigation area
            ]

            for selector_type, selector_value in chat_indicators:
                try:
                    element = WebDriverWait(self.driver, 10).until(
                        EC.presence_of_element_located((selector_type, selector_value))
                    )
                    if element:
                        logger.info(f"Found Google Chat interface element: {selector_value}")
                        logger.info("Successfully logged into Google Chat!")
                        return True
                except TimeoutException:
                    continue

            # If no specific elements found, but we're on Google Chat, consider it success
            if (self.driver.current_url.startswith("https://chat.google.com") or
                "mail.google.com/chat" in self.driver.current_url):
                logger.info("On Google Chat domain - considering login successful")
                return True

            logger.warning("Login may have failed - Google Chat interface not detected")
            return False

        except Exception as e:
            logger.error(f"Error verifying login success: {str(e)}")
            return False
    
    def login(self):
        """Main login method that orchestrates the entire login process"""
        try:
            logger.info("Starting Google Chat login process...")

            # Step 1: Navigate directly to Google login page
            if not self.navigate_to_google_chat():
                return False

            # Check if we're already logged in (on Google Chat domain)
            if (self.driver.current_url.startswith("https://chat.google.com") or
                "mail.google.com/chat" in self.driver.current_url):
                logger.info("Already logged in, verifying access...")
                return self.verify_login_success()

            # Check if we're on the login page and need to enter credentials
            if "accounts.google.com" not in self.driver.current_url:
                logger.error("Not on Google accounts page, cannot proceed with login")
                return False

            # We're on the login page, proceed with credential entry
            logger.info("On Google login page, proceeding with credential entry...")

            # Step 2: Enter email
            logger.info("Proceeding with email entry...")
            if not self.enter_email():
                return False

            # Wait a bit for page transition
            time.sleep(2)

            # Step 3: Enter password
            logger.info("Proceeding with password entry...")
            if not self.enter_password():
                return False

            # Step 4: Handle potential 2FA
            if not self.handle_2fa_prompt():
                return False

            # Step 5: Verify login success
            logger.info("Verifying login success...")
            return self.verify_login_success()

        except Exception as e:
            logger.error(f"Login process failed: {str(e)}")
            return False
    
    def cleanup(self):
        """Clean up WebDriver resources"""
        if self.driver:
            logger.info("Cleaning up WebDriver...")
            self.driver.quit()
            logger.info("WebDriver cleanup completed")

def main():
    """Main function to run the Google Chat login automation"""
    login_automation = GoogleChatLogin(headless=False)  # Set to True for headless mode
    
    try:
        # Setup WebDriver
        if not login_automation.setup_driver():
            logger.error("Failed to setup WebDriver. Exiting.")
            return False
        
        # Perform login
        success = login_automation.login()
        
        if success:
            logger.info("Google Chat login completed successfully!")
            input("Press Enter to close the browser...")
        else:
            logger.error("Google Chat login failed!")
            
        return success
        
    except KeyboardInterrupt:
        logger.info("Script interrupted by user")
        return False
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        return False
    finally:
        # Ensure cleanup happens
        login_automation.cleanup()

if __name__ == "__main__":
    main()
