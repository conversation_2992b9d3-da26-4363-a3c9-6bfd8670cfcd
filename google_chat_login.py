#!/usr/bin/env python3
"""
Google Chat Login Automation Script using Selenium WebDriver

This script automates the login process for Google Chat (chat.google.com)
using Selenium WebDriver with Chrome browser.

Requirements:
- selenium package: pip install selenium
- Chrome browser installed
- ChromeDriver (automatically managed by selenium 4.x)

Author: Auto-generated script
Date: 2025-07-29
"""

import time
import logging
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.common.exceptions import (
    TimeoutException, 
    NoSuchElementException, 
    WebDriverException,
    ElementClickInterceptedException
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class GoogleChatLogin:
    """Class to handle Google Chat login automation"""
    
    def __init__(self, headless=False, timeout=30):
        """
        Initialize the GoogleChatLogin class
        
        Args:
            headless (bool): Run browser in headless mode
            timeout (int): Default timeout for WebDriverWait
        """
        self.driver = None
        self.wait = None
        self.timeout = timeout
        self.headless = headless
        
        # Login credentials
        self.username = "<EMAIL>"
        self.password = "Uni@2025!"
        
        # URLs
        self.chat_url = "https://chat.google.com"
        self.login_url = "https://accounts.google.com/signin"
    
    def setup_driver(self):
        """Set up Chrome WebDriver with appropriate options"""
        try:
            logger.info("Setting up Chrome WebDriver...")
            
            # Configure Chrome options
            chrome_options = Options()
            
            if self.headless:
                chrome_options.add_argument("--headless")
            
            # Additional Chrome options for better stability
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--window-size=1920,1080")
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            # Initialize WebDriver
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            # Set up WebDriverWait
            self.wait = WebDriverWait(self.driver, self.timeout)
            
            logger.info("Chrome WebDriver setup completed successfully")
            return True
            
        except WebDriverException as e:
            logger.error(f"Failed to setup WebDriver: {str(e)}")
            return False
    
    def navigate_to_google_chat(self):
        """Navigate to Google Chat and handle initial redirect"""
        try:
            logger.info("Navigating to Google Chat...")
            self.driver.get(self.chat_url)

            # Wait for page to load and check if we're redirected to login
            time.sleep(3)
            current_url = self.driver.current_url

            if "accounts.google.com" in current_url:
                logger.info("Redirected to Google login page")
                return True
            elif "chat.google.com" in current_url:
                logger.info("Already logged in or on chat page")
                return self.verify_login_success()
            elif "workspace.google.com" in current_url:
                logger.info("Redirected to workspace page, looking for sign in button...")
                return self.handle_workspace_redirect()
            else:
                logger.warning(f"Unexpected URL: {current_url}")
                # Try to find and click sign in button anyway
                return self.find_and_click_signin()

        except Exception as e:
            logger.error(f"Failed to navigate to Google Chat: {str(e)}")
            return False

    def handle_workspace_redirect(self):
        """Handle redirect to workspace.google.com and find sign in button"""
        try:
            logger.info("Handling workspace redirect...")

            # Try direct navigation to login first
            logger.info("Trying direct login URL...")
            self.driver.get("https://accounts.google.com/signin/v2/identifier?service=chat&continue=https://chat.google.com")
            time.sleep(3)

            if "accounts.google.com" in self.driver.current_url:
                logger.info("Successfully navigated to login page")
                return True

            # If direct navigation doesn't work, look for sign in buttons with shorter timeout
            logger.info("Direct navigation failed, looking for sign in button...")
            signin_selectors = [
                (By.XPATH, "//a[contains(text(), 'Sign in')]"),
                (By.XPATH, "//button[contains(text(), 'Sign in')]"),
                (By.XPATH, "//a[contains(@href, 'accounts.google.com')]"),
                (By.LINK_TEXT, "Sign in"),
                (By.PARTIAL_LINK_TEXT, "Sign in"),
            ]

            # Use shorter timeout for each selector
            short_wait = WebDriverWait(self.driver, 5)

            for selector_type, selector_value in signin_selectors:
                try:
                    element = short_wait.until(
                        EC.element_to_be_clickable((selector_type, selector_value))
                    )
                    logger.info(f"Found sign in button: {selector_value}")
                    element.click()

                    # Wait for redirect to login page
                    time.sleep(3)
                    if "accounts.google.com" in self.driver.current_url:
                        logger.info("Successfully redirected to login page")
                        return True

                except (TimeoutException, NoSuchElementException):
                    continue

            logger.warning("Could not find sign in button or navigate to login page")
            return False

        except Exception as e:
            logger.error(f"Error handling workspace redirect: {str(e)}")
            return False

    def find_and_click_signin(self):
        """Generic method to find and click sign in button"""
        try:
            logger.info("Looking for sign in button...")

            signin_selectors = [
                (By.XPATH, "//a[contains(text(), 'Sign in')]"),
                (By.XPATH, "//button[contains(text(), 'Sign in')]"),
                (By.LINK_TEXT, "Sign in"),
                (By.PARTIAL_LINK_TEXT, "Sign in"),
            ]

            for selector_type, selector_value in signin_selectors:
                try:
                    element = self.driver.find_element(selector_type, selector_value)
                    if element.is_displayed():
                        element.click()
                        time.sleep(2)
                        return "accounts.google.com" in self.driver.current_url
                except NoSuchElementException:
                    continue

            return False

        except Exception as e:
            logger.error(f"Error finding sign in button: {str(e)}")
            return False
    
    def enter_email(self):
        """Enter email address in the first step of Google login"""
        try:
            logger.info("Entering email address...")

            # Debug: Print current URL and page title
            logger.info(f"Current URL: {self.driver.current_url}")
            logger.info(f"Page title: {self.driver.title}")

            # Debug: Look for all input fields on the page
            try:
                all_inputs = self.driver.find_elements(By.TAG_NAME, "input")
                logger.info(f"Found {len(all_inputs)} input fields on the page")
                for i, input_elem in enumerate(all_inputs):
                    try:
                        input_type = input_elem.get_attribute("type")
                        input_name = input_elem.get_attribute("name")
                        input_id = input_elem.get_attribute("id")
                        input_class = input_elem.get_attribute("class")
                        logger.info(f"Input {i}: type='{input_type}', name='{input_name}', id='{input_id}', class='{input_class}'")
                    except:
                        pass
            except Exception as e:
                logger.error(f"Error debugging inputs: {e}")

            # Try multiple selectors for email input field
            email_selectors = [
                (By.ID, "identifierId"),
                (By.NAME, "identifier"),
                (By.XPATH, "//input[@type='email']"),
                (By.XPATH, "//input[@name='identifier']"),
                (By.XPATH, "//input[@id='identifierId']"),
                (By.XPATH, "//input[contains(@class, 'whsOnd')]"),
                (By.XPATH, "//input[@autocomplete='username']"),
                (By.XPATH, "//input"),  # Try any input field as last resort
            ]

            email_input = None
            for selector_type, selector_value in email_selectors:
                try:
                    # Use shorter timeout for debugging
                    short_wait = WebDriverWait(self.driver, 5)
                    email_input = short_wait.until(
                        EC.element_to_be_clickable((selector_type, selector_value))
                    )
                    logger.info(f"Found email input with selector: {selector_value}")
                    break
                except TimeoutException:
                    continue

            if not email_input:
                logger.error("Could not find email input field with any selector")
                # Save screenshot for debugging
                try:
                    self.driver.save_screenshot("debug_login_page.png")
                    logger.info("Screenshot saved as debug_login_page.png")
                except:
                    pass
                return False

            # Clear and enter email
            email_input.clear()
            email_input.send_keys(self.username)
            logger.info(f"Email entered: {self.username}")

            # Try multiple selectors for Next button
            next_selectors = [
                (By.ID, "identifierNext"),
                (By.XPATH, "//button[@id='identifierNext']"),
                (By.XPATH, "//button[contains(text(), 'Next')]"),
                (By.XPATH, "//button[@type='submit']"),
                (By.XPATH, "//div[@id='identifierNext']"),
                (By.XPATH, "//span[text()='Next']/parent::button"),
            ]

            next_button = None
            for selector_type, selector_value in next_selectors:
                try:
                    next_button = self.wait.until(
                        EC.element_to_be_clickable((selector_type, selector_value))
                    )
                    logger.info(f"Found Next button with selector: {selector_value}")
                    break
                except TimeoutException:
                    continue

            if not next_button:
                logger.error("Could not find Next button with any selector")
                return False

            next_button.click()
            logger.info("Clicked Next button after email entry")

            return True

        except Exception as e:
            logger.error(f"Failed to enter email: {str(e)}")
            return False
    
    def enter_password(self):
        """Enter password in the second step of Google login"""
        try:
            logger.info("Entering password...")

            # Try multiple selectors for password input field
            password_selectors = [
                (By.NAME, "password"),
                (By.XPATH, "//input[@type='password']"),
                (By.XPATH, "//input[@name='password']"),
                (By.XPATH, "//input[contains(@class, 'whsOnd')][@type='password']"),
                (By.XPATH, "//input[@autocomplete='current-password']"),
            ]

            password_input = None
            for selector_type, selector_value in password_selectors:
                try:
                    password_input = self.wait.until(
                        EC.element_to_be_clickable((selector_type, selector_value))
                    )
                    logger.info(f"Found password input with selector: {selector_value}")
                    break
                except TimeoutException:
                    continue

            if not password_input:
                logger.error("Could not find password input field with any selector")
                return False

            # Clear and enter password
            password_input.clear()
            password_input.send_keys(self.password)
            logger.info("Password entered")

            # Try multiple selectors for Next button
            next_selectors = [
                (By.ID, "passwordNext"),
                (By.XPATH, "//button[@id='passwordNext']"),
                (By.XPATH, "//button[contains(text(), 'Next')]"),
                (By.XPATH, "//button[@type='submit']"),
                (By.XPATH, "//div[@id='passwordNext']"),
                (By.XPATH, "//span[text()='Next']/parent::button"),
            ]

            next_button = None
            for selector_type, selector_value in next_selectors:
                try:
                    next_button = self.wait.until(
                        EC.element_to_be_clickable((selector_type, selector_value))
                    )
                    logger.info(f"Found Next button with selector: {selector_value}")
                    break
                except TimeoutException:
                    continue

            if not next_button:
                logger.error("Could not find Next button with any selector")
                return False

            next_button.click()
            logger.info("Clicked Next button after password entry")

            return True

        except Exception as e:
            logger.error(f"Failed to enter password: {str(e)}")
            return False
    
    def handle_2fa_prompt(self):
        """Handle potential 2FA or security challenges"""
        try:
            logger.info("Checking for 2FA or security challenges...")
            
            # Wait a bit to see if 2FA prompt appears
            time.sleep(3)
            
            # Check for various 2FA elements
            possible_2fa_selectors = [
                (By.ID, "totpPin"),
                (By.NAME, "totpPin"),
                (By.XPATH, "//input[@type='tel']"),
                (By.XPATH, "//input[@placeholder='Enter code']"),
            ]
            
            for selector_type, selector_value in possible_2fa_selectors:
                try:
                    element = self.driver.find_element(selector_type, selector_value)
                    if element.is_displayed():
                        logger.warning("2FA prompt detected. Manual intervention required.")
                        input("Please complete 2FA manually and press Enter to continue...")
                        return True
                except NoSuchElementException:
                    continue
            
            logger.info("No 2FA prompt detected")
            return True
            
        except Exception as e:
            logger.error(f"Error handling 2FA prompt: {str(e)}")
            return False
    
    def verify_login_success(self):
        """Verify successful login by checking for Google Chat interface elements"""
        try:
            logger.info("Verifying login success...")
            
            # Wait for redirect to Google Chat
            self.wait.until(lambda driver: "chat.google.com" in driver.current_url)
            
            # Check for Google Chat interface elements
            chat_indicators = [
                (By.XPATH, "//div[@data-topic-id]"),  # Chat rooms
                (By.XPATH, "//div[contains(@class, 'chat')]"),  # Chat interface
                (By.XPATH, "//button[@aria-label='Main menu']"),  # Main menu
                (By.XPATH, "//div[@role='main']"),  # Main content area
            ]
            
            for selector_type, selector_value in chat_indicators:
                try:
                    element = self.wait.until(
                        EC.presence_of_element_located((selector_type, selector_value))
                    )
                    if element:
                        logger.info("Successfully logged into Google Chat!")
                        return True
                except TimeoutException:
                    continue
            
            logger.warning("Login may have failed - Google Chat interface not detected")
            return False
            
        except Exception as e:
            logger.error(f"Error verifying login success: {str(e)}")
            return False
    
    def login(self):
        """Main login method that orchestrates the entire login process"""
        try:
            logger.info("Starting Google Chat login process...")
            
            # Step 1: Navigate to Google Chat
            if not self.navigate_to_google_chat():
                return False
            
            # If already logged in, return success
            if "chat.google.com" in self.driver.current_url and self.verify_login_success():
                return True
            
            # Step 2: Enter email
            if not self.enter_email():
                return False
            
            # Step 3: Enter password
            if not self.enter_password():
                return False
            
            # Step 4: Handle potential 2FA
            if not self.handle_2fa_prompt():
                return False
            
            # Step 5: Verify login success
            return self.verify_login_success()
            
        except Exception as e:
            logger.error(f"Login process failed: {str(e)}")
            return False
    
    def cleanup(self):
        """Clean up WebDriver resources"""
        if self.driver:
            logger.info("Cleaning up WebDriver...")
            self.driver.quit()
            logger.info("WebDriver cleanup completed")

def main():
    """Main function to run the Google Chat login automation"""
    login_automation = GoogleChatLogin(headless=False)  # Set to True for headless mode
    
    try:
        # Setup WebDriver
        if not login_automation.setup_driver():
            logger.error("Failed to setup WebDriver. Exiting.")
            return False
        
        # Perform login
        success = login_automation.login()
        
        if success:
            logger.info("Google Chat login completed successfully!")
            input("Press Enter to close the browser...")
        else:
            logger.error("Google Chat login failed!")
            
        return success
        
    except KeyboardInterrupt:
        logger.info("Script interrupted by user")
        return False
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        return False
    finally:
        # Ensure cleanup happens
        login_automation.cleanup()

if __name__ == "__main__":
    main()
